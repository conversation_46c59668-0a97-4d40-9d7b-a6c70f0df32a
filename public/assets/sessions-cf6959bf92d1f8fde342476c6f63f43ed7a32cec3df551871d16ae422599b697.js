(function() {
  var clearOtpFields, generateOtp, getOtpValue, getTimer, verifyOtp;

  $(function() {
    $('#send_otp_form').on('submit', function(e) {
      e.preventDefault();
      $('.otp-sent-phone').html($('#phone').val());
      return $.ajax(generateOtp($('#phone').val(), $(this).attr('action'), false));
    });
    $('.closebtn').on('click', function() {
      $('#error-message').hide();
      return $('#otp-error-message').hide();
    });
    $('#verify_otp_form').on('submit', function(e) {
      ga('set', 'dimension12', 'logged in with otp');
      $(".overlay").show();
      $(".progress_img1").show();
      e.preventDefault();
      $('#user_phone').val($('#phone').val());
      $('#one_time_password').val(getOtpValue());
      return $.ajax(verifyOtp($('#phone').val(), getOtpValue(), $(this).attr('action')));
    });
    $('#new_account').on('submit', function(e) {
      $(".overlay").show();
      $(".progress_img1").show();
      return ga('set', 'dimension12', 'logged in with email');
    });
    $('#resend_otp').on('click', function() {
      $.ajax(generateOtp($('#phone').val(), '/accounts/send_otp', true));
      getTimer();
      $('.resend-tag').hide();
      return $('.timer').show();
    });
    $('#email-login-link').on('click', function() {
      $('.mobile-number-login').hide();
      $('.email-login').show();
      return ga('set', 'dimension12', 'email login form view');
    });
    return $('#number-login-link').on('click', function() {
      $('.email-login').hide();
      $('.mobile-number-login').show();
      return ga('set', 'dimension12', 'otp login form view');
    });
  });

  verifyOtp = function(phone, otp, url) {
    return {
      type: 'POST',
      data: {
        user_phone: phone,
        one_time_password: otp
      },
      url: url,
      dataType: 'JSON',
      success: function(data, status) {
        if (data['error']) {
          $(".overlay").hide();
          $(".progress_img1").hide();
          $('#otp-error-message').show();
          return clearOtpFields();
        } else {
          return window.location.href = data['location'];
        }
      }
    };
  };

  generateOtp = function(phone, url, resent) {
    return {
      type: 'POST',
      data: {
        phone: phone
      },
      url: url,
      dataType: 'JSON',
      success: function(data, status, jqxhr) {
        if (data['error'] === true) {
          return $('#error-message').show();
        } else if (data['sms_sent'] === true && resent === false) {
          $('.new-login-form').hide();
          $('.verify-otp-form').show();
          $('.otp-sent-phone').text(phone);
          return getTimer();
        }
      }
    };
  };

  getOtpValue = function() {
    var otp;
    otp = $('#otpBox1').val() + $('#otpBox2').val() + $('#otpBox3').val() + $('#otpBox4').val();
    return otp;
  };

  getTimer = function() {
    var countdown, seconds;
    seconds = document.getElementById('countdown-timer').textContent;
    return countdown = setInterval((function() {
      seconds--;
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      document.getElementById('countdown-timer').textContent = seconds;
      $('.resend-tag').hide();
      $('.timer').show();
      if (seconds <= 0) {
        clearInterval(countdown);
        $('.resend-tag').show();
        $('.timer').hide();
        document.getElementById('countdown-timer').textContent = 15;
      }
    }), 1000);
  };

  clearOtpFields = function() {
    $('#otpBox1').val('');
    $('#otpBox2').val('');
    $('#otpBox3').val('');
    $('#otpBox4').val('');
    return $('#otpBox1').focus();
  };

}).call(this);
