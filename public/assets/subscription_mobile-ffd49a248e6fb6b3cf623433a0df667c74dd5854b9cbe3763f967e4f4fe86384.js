(function() {
  $(function() {
    var checkExitIntent, checkUpScrollPercent, getScrollPercent, show_subscription_banner, sub_view, subscribe_email;
    checkUpScrollPercent = function(scrollStart) {
      var interval;
      return interval = setInterval((function() {
        var upScrollPercent;
        upScrollPercent = getScrollPercent(scrollStart);
        if (upScrollPercent > parseFloat(10) / 100) {
          clearInterval(interval);
          setTimeout(show_subscription_banner, 1000);
        }
      }), 1000);
    };
    getScrollPercent = function(scrollStart) {
      var scrollAmount;
      scrollAmount = $(window).scrollTop() - scrollStart;
      if (scrollAmount < 0) {
        scrollAmount = 0;
        scrollStart = $(window).scrollTop();
      }
      return parseFloat(scrollAmount) / parseFloat($(document).height());
    };
    checkExitIntent = (function() {
      var pageHeight, scrollStart;
      scrollStart = $(window).scrollTop();
      pageHeight = $(document).height();
      return function() {
        var interval;
        if (pageHeight > 0) {
          return interval = setInterval((function() {
            var downScrollPercent;
            downScrollPercent = getScrollPercent(scrollStart);
            if (downScrollPercent > parseFloat(60) / 100 && $('.pages_home').length > 0) {
              clearInterval(interval);
              checkUpScrollPercent(scrollStart);
            }
          }), 1000);
        }
      };
    })();
    checkExitIntent();
    subscribe_email = function(user_email) {
      var data, url;
      url = '/subscriptions';
      data = {
        subscriptions: {
          source_url: window.location.href,
          email: user_email,
          appsource: 'mobile'
        }
      };
      $.post(url, data, (function(response) {
        if (response.error) {
          $('.sub-msg').text(response.error_message);
        } else {
          if (response.data.coupon_code) {
            $('.sub-msg').text(response.data.message + response.data.coupon_code);
            $('#mobile-subscribe-window').foundation('reveal', 'close');
            $('.coupon_code').html(response.data.coupon_code);
            $('.offer_msg').html(response.data.message);
            $('#notice_banner').slideDown(500, function() {
              setTimeout((function() {
                $('#notice_banner').slideUp(500, function() {
                  $('#notice_banner').remove();
                });
              }), 5000);
            });
          } else {
            $('.sub-msg').text(response.data);
          }
          $('.subscribe_text_message').hide();
          if (!response.already_subscribed.length) {
            setTimeout((function() {
              $('#mobile-subscribe-window').foundation('reveal', 'close');
            }), 5000);
            $('.sub-msg').addClass('subscribe_success');
            $('#mobile-subscribe-window input').hide();
            ga('send', 'event', 'Subscription', 'subscription_successfull', {
              'nonInteraction': 1,
              'metric1': 1
            });
          } else {
            $('.sub-msg').text('We noticed.!!!!You have already subscribed!');
            setTimeout((function() {
              $('#mobile-subscribe-window').foundation('reveal', 'close');
            }), 5000);
          }
        }
      }), 'json');
    };
    show_subscription_banner = function() {
      var sub_view;
      if (!getCookie('subscribe').length && !/cart*|order*|account*/i.test(window.location.href)) {
        $('#mobile-subscribe-window').foundation('reveal', 'open');
        if (!sub_view) {
          sub_view = true;
          ga('send', 'event', 'Subscription', 'subscription_form_view', {
            'nonInteraction': 1,
            'metric2': 1
          });
        }
      }
    };
    $(document).on('click', '#email-subscribe-button', function(e) {
      var regex, user_email;
      e.preventDefault();
      if (typeof $('#subscribe-input').val() !== 'undefined') {
        user_email = $('#subscribe-input').val();
        regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
        if (user_email.trim() && !user_email.endsWith('.co') && regex.test(user_email)) {
          return subscribe_email(user_email);
        } else {
          return $('.sub-msg').text('Please enter a valid Email address.');
        }
      }
    });
    $(document).on('click', '#subscribe-input', function(e) {
      return $('#modal-subscribe-box').animate({
        scrollTop: $(this).offset().top
      }, 'slow');
    });
    $(document).foundation('reveal');
    sub_view = false;
    $(document).on('click', '#notice_close_btn', function() {
      return $('#notice_banner').slideUp(500, function() {
        return $('#notice_banner').remove();
      });
    });
    return $(document).on('click', '.close_subscribe, #email-cancel-button', function() {
      $('#mobile-subscribe-window').foundation('reveal', 'close');
      setCookie('subscribe', 'closed', 7);
      ga('send', 'event', 'Subscription', 'subscription_window_close', {
        'nonInteraction': 1,
        'metric3': 1
      });
    });
  });

}).call(this);
