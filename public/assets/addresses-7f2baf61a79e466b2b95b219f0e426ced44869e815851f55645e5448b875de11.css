/* line 7, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address label, .shipping_address label {
  color: white;
}
/* line 10, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address .save_btn, .shipping_address .save_btn {
  background-color: #0e9a7d;
}
/* line 13, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address table, .shipping_address table {
  background: inherit;
  border: none;
  margin-bottom: 0em;
}
/* line 18, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address table tr:nth-of-type(even), .shipping_address table tr:nth-of-type(even) {
  background: inherit;
}
/* line 21, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address table tr td, .shipping_address table tr td {
  color: inherit;
  padding: 0.1em;
}
/* line 27, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.billing_address a, .shipping_address a {
  margin-bottom: 0em;
}

/* line 31, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
input[type='submit'] {
  margin-top: 1em;
}

/* line 35, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.payment_options label {
  color: inherit;
}

/* line 40, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/addresses.scss */
.payment_btn .cont_payment {
  background-color: #0e9a7d;
}
