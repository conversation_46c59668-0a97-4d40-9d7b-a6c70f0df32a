(function(){this.OtherPayment=function(){function t(t){i=i.bind(this),n=n.bind(this),a=a.bind(this),this.$container=$(t),this.$initialListInputs=this.$container.find("[type=radio]"),this.$remainingListInputs=$(".payment-other-remaining-list select"),i()}var i,n,a,e,r;return i=function(){return this.$initialListInputs.on("change",a),this.$remainingListInputs.on("change",function(){return function(t){var i;return i=$(t.target),"Default"!==i.val()?n():void 0}}(this))},n=function(){return r(),this.$initialListInputs.filter(":checked").attr("checked",!1)&&this.$remainingListInputs.find(":selected").attr("selected",!0),$(".cvv-input").attr("type","hidden"),$(".payment-upi-id input").attr("type","hidden")},a=function(){return this.$remainingListInputs.find(":selected").attr("selected",!1),this.$remainingListInputs.each(function(){var t;return t=$(this),t.val(t.data("default"))})},r=function(){var t,i,n,a,r;return n=$(".accordion-navigation.active"),r=n.data("symbol"),t=n.data("grandtotal"),i=n.data("prepaidDiscount"),a=n.data("shipping"),$("#wallet_discount_order_page").show(),1===$(".accordion-navigation.active").length?("available"===n.data("prepaidShippingPromo")?($("#shipping_charge").html("Shipping : "+"FREE".fontcolor("green").bold()),e(r,t,i,0,a)):($("#shipping_charge").html("Shipping : "+r+" "+n.data("shipping")),e(r,t,i,0,0)),$(".prepaid_discount").show(),$("#prepaid_discount").val(gon.prepaid_discount),$(".grand_total").show(),$(".grand_total_with_cod.hide").hide(),$(".cod_charges").hide()):(n=$(".accordion-navigation"),t=n.data("grandtotal"),r=n.data("symbol"),a=n.data("shipping"),e(r,t,0,0,0),$("#shipping_charge").html("Shipping : "+a),$(".prepaid_discount").hide(),$("#prepaid_discount").val(0)),$("#mastercard_discount").val("0"),$(".mastercard_discount").hide(),$(".card_message").hide(),$(".saved_card_message").hide()},e=function(t,i,n,a,e){var r,s;return r=i-n-a-e,s=t+" "+r,$(".grand_total").html("Grand Total : "+s),$(".grand_total.top").html(s)},t.prototype.isValid=function(){return this.$initialListInputs.filter(":checked").length>0||"Default"!==this.$remainingListInputs.find(":selected").val()&&0!==this.$remainingListInputs.find(":selected").length},t.prototype.clear=function(){return n(),a()},t}()}).call(this);