(function() {
  var extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    hasProp = {}.hasOwnProperty;

  this.SavedCard = (function(superClass) {
    var bindings, initialize, updateInputType;

    extend(SavedCard, superClass);

    initialize = function() {
      return this.$IDInput.each(function() {
        if ($(this).data("type") === "AMEX") {
          return new Cleave(this, {
            blocks: [4]
          });
        } else {
          return new Cleave(this, {
            blocks: [3]
          });
        }
      });
    };

    bindings = function() {
      return this.$typeDeciderElements.on('change', (function(_this) {
        return function(event) {
          var $selector;
          $selector = $(event.target);
          if ($selector.context.nodeName === "SELECT") {
            $selector = $selector.find(':selected');
          }
          return updateInputType.call(_this, $selector.data("token"));
        };
      })(this));
    };

    updateInputType = function(token) {
      var message;
      if (this.cleave !== void 0) {
        this.cleave.destroy();
      }
      this.$IDInput.val('');
      if (token !== void 0) {
        message = "CVV";
        this.$IDInput.attr("disabled", true);
        this.$IDInput.attr("type", "hidden");
        $('.' + token).attr("disabled", false);
        $('.' + token).attr("type", "password");
      } else {
        this.$IDInput.attr("disabled", true);
        this.$IDInput.attr("type", "hidden");
      }
      return this.$IDInput.attr("placeholder", message);
    };

    function SavedCard(container) {
      SavedCard.__super__.constructor.call(this, container);
      this.$IDInput = $('.cvv-input');
      this.$typeDeciderElements = $('.radio-container').find('input[type=radio], select');
      initialize.call(this);
      updateInputType.call(this);
      bindings.call(this);
    }

    return SavedCard;

  })(OtherPayment);

}).call(this);
