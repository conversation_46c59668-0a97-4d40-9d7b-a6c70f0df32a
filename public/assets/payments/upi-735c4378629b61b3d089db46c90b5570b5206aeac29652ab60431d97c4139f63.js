(function(){var t=function(t,n){function i(){this.constructor=t}for(var r in n)e.call(n,r)&&(t[r]=n[r]);return i.prototype=n.prototype,t.prototype=new i,t.__super__=n.prototype,t},e={}.hasOwnProperty;this.UPIPayment=function(e){function n(t){n.__super__.constructor.call(this,t),this.$IDInput=$(".payment-upi-id input"),this.$typeDeciderElements=this.$container.find("input[type=radio], select"),r.call(this),i.call(this)}var i,r;return t(n,e),i=function(){return this.$typeDeciderElements.on("change",function(t){return function(e){var n;return n=$(e.target),"SELECT"===n.context.nodeName&&(n=n.find(":selected")),r.call(t,n.data("type"))}}(this))},r=function(t){var e;switch(void 0!==this.cleave&&this.cleave.destroy(),this.$IDInput.val(""),t){case"vpa":e="Enter VPA",this.$IDInput.attr("disabled",!1),this.$IDInput.attr("type","text");break;case"mobile":e="Enter phone number",this.cleave=new Cleave(this.$IDInput.get()[0],{phone:!0,phoneRegionCode:"IN"}),this.$IDInput.attr("disabled",!1),this.$IDInput.attr("type","text");break;default:e="Please select an option",this.$IDInput.attr("disabled",!0)}return this.$IDInput.attr("placeholder",e)},n}(OtherPayment)}).call(this);