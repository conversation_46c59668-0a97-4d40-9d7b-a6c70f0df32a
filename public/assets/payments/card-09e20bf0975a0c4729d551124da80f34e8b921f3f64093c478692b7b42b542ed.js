!function(){function t(t,n){var e=t.split("."),r=T;e[0]in r||!r.execScript||r.execScript("var "+e[0]);for(var i;e.length&&(i=e.shift());)e.length||void 0===n?r=r[i]?r[i]:r[i]={}:r[i]=n}function n(t,n){function e(){}e.prototype=n.prototype,t.M=n.prototype,t.prototype=new e,t.prototype.constructor=t,t.N=function(t,e,r){for(var i=Array(arguments.length-2),l=2;l<arguments.length;l++)i[l-2]=arguments[l];return n.prototype[e].apply(t,i)}}function e(t,n){null!=t&&this.a.apply(this,arguments)}function r(t){t.b=""}function i(t,n){t.sort(n||l)}function l(t,n){return t>n?1:n>t?-1:0}function a(t){var n,e=[],r=0;for(n in t)e[r++]=t[n];return e}function u(t,n){this.b=t,this.a={};for(var e=0;e<n.length;e++){var r=n[e];this.a[r.b]=r}}function o(t){return t=a(t.a),i(t,function(t,n){return t.b-n.b}),t}function s(t,n){switch(this.b=t,this.g=!!n.G,this.a=n.c,this.j=n.type,this.h=!1,this.a){case k:case J:case K:case L:case O:case Y:case U:this.h=!0}this.f=n.defaultValue}function f(){this.a={},this.f=this.i().a,this.b=this.g=null}function c(t,n){for(var e=o(t.i()),r=0;r<e.length;r++){var i=e[r],l=i.b;if(null!=n.a[l]){t.b&&delete t.b[i.b];var a=11==i.a||10==i.a;if(i.g)for(var i=p(n,l)||[],u=0;u<i.length;u++){var s=t,f=l,h=a?i[u].clone():i[u];s.a[f]||(s.a[f]=[]),s.a[f].push(h),s.b&&delete s.b[f]}else i=p(n,l),a?(a=p(t,l))?c(a,i):b(t,l,i.clone()):b(t,l,i)}}}function p(t,n){var e=t.a[n];if(null==e)return null;if(t.g){if(!(n in t.b)){var r=t.g,i=t.f[n];if(null!=e)if(i.g){for(var l=[],a=0;a<e.length;a++)l[a]=r.b(i,e[a]);e=l}else e=r.b(i,e);return t.b[n]=e}return t.b[n]}return e}function h(t,n,e){var r=p(t,n);return t.f[n].g?r[e||0]:r}function g(t,n){var e;if(null!=t.a[n])e=h(t,n,void 0);else t:{if(e=t.f[n],void 0===e.f){var r=e.j;if(r===Boolean)e.f=!1;else if(r===Number)e.f=0;else{if(r!==String){e=new r;break t}e.f=e.h?"0":""}}e=e.f}return e}function d(t,n){return t.f[n].g?null!=t.a[n]?t.a[n].length:0:null!=t.a[n]?1:0}function b(t,n,e){t.a[n]=e,t.b&&(t.b[n]=e)}function m(t,n){var e,r=[];for(e in n)0!=e&&r.push(new s(e,n[e]));return new u(t,r)}/*

 Protocol Buffer 2 Copyright 2008 Google Inc.
 All other code copyright its respective owners.
 Copyright (C) 2010 The Libphonenumber Authors

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
function y(){f.call(this)}function v(){f.call(this)}function _(){f.call(this)}function $(){}function S(){}function w(){}/*

 Copyright (C) 2010 The Libphonenumber Authors.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
function x(){this.a={}}function N(t,n){if(null==n)return null;n=n.toUpperCase();var e=t.a[n];if(null==e){if(e=tt[n],null==e)return null;e=(new w).a(_.i(),e),t.a[n]=e}return e}function A(t){return t=X[t],null==t?"ZZ":t[0]}function j(t){this.H=RegExp(" "),this.B="",this.m=new e,this.v="",this.h=new e,this.u=new e,this.j=!0,this.w=this.o=this.D=!1,this.F=x.b(),this.s=0,this.b=new e,this.A=!1,this.l="",this.a=new e,this.f=[],this.C=t,this.J=this.g=E(this,this.C)}function E(t,n){var e;if(null!=n&&isNaN(n)&&n.toUpperCase()in tt){if(e=N(t.F,n),null==e)throw"Invalid region code: "+n;e=g(e,10)}else e=0;return e=N(t.F,A(e)),null!=e?e:lt}function I(t){for(var n=t.f.length,e=0;n>e;++e){var i=t.f[e],l=g(i,1);if(t.v==l)return!1;var a;a=t;var u=i,o=g(u,1);if(-1!=o.indexOf("|"))a=!1;else{o=o.replace(at,"\\d"),o=o.replace(ut,"\\d"),r(a.m);var s;s=a;var u=g(u,2),f="999999999999999".match(o)[0];f.length<s.a.b.length?s="":(s=f.replace(new RegExp(o,"g"),u),s=s.replace(RegExp("9","g")," ")),0<s.length?(a.m.a(s),a=!0):a=!1}if(a)return t.v=l,t.A=st.test(h(i,4)),t.s=0,!0}return t.j=!1}function R(t,n){for(var e=[],r=n.length-3,i=t.f.length,l=0;i>l;++l){var a=t.f[l];0==d(a,3)?e.push(t.f[l]):(a=h(a,3,Math.min(r,d(a,3)-1)),0==n.search(a)&&e.push(t.f[l]))}t.f=e}function C(t,n){t.h.a(n);var e=n;if(rt.test(e)||1==t.h.b.length&&et.test(e)){var i,e=n;"+"==e?(i=e,t.u.a(e)):(i=nt[e],t.u.a(i),t.a.a(i)),n=i}else t.j=!1,t.D=!0;if(!t.j){if(!t.D)if(P(t)){if(V(t))return F(t)}else if(0<t.l.length&&(e=t.a.toString(),r(t.a),t.a.a(t.l),t.a.a(e),e=t.b.toString(),i=e.lastIndexOf(t.l),r(t.b),t.b.a(e.substring(0,i))),t.l!=H(t))return t.b.a(" "),F(t);return t.h.toString()}switch(t.u.b.length){case 0:case 1:case 2:return t.h.toString();case 3:if(!P(t))return t.l=H(t),M(t);t.w=!0;default:return t.w?(V(t)&&(t.w=!1),t.b.toString()+t.a.toString()):0<t.f.length?(e=q(t,n),i=B(t),0<i.length?i:(R(t,t.a.toString()),I(t)?G(t):t.j?D(t,e):t.h.toString())):M(t)}}function F(t){return t.j=!0,t.w=!1,t.f=[],t.s=0,r(t.m),t.v="",M(t)}function B(t){for(var n=t.a.toString(),e=t.f.length,r=0;e>r;++r){var i=t.f[r],l=g(i,1);if(new RegExp("^(?:"+l+")$").test(n))return t.A=st.test(h(i,4)),n=n.replace(new RegExp(l,"g"),h(i,2)),D(t,n)}return""}function D(t,n){var e=t.b.b.length;return t.A&&e>0&&" "!=t.b.toString().charAt(e-1)?t.b+" "+n:t.b+n}function M(t){var n=t.a.toString();if(3<=n.length){for(var e=t.o&&0<d(t.g,20)?p(t.g,20)||[]:p(t.g,19)||[],r=e.length,i=0;r>i;++i){var l,a=e[i];(l=null==t.g.a[12]||t.o||h(a,6))||(l=g(a,4),l=0==l.length||it.test(l)),l&&ot.test(g(a,2))&&t.f.push(a)}return R(t,n),n=B(t),0<n.length?n:I(t)?G(t):t.h.toString()}return D(t,n)}function G(t){var n=t.a.toString(),e=n.length;if(e>0){for(var r="",i=0;e>i;i++)r=q(t,n.charAt(i));return t.j?D(t,r):t.h.toString()}return t.b.toString()}function H(t){var n,e=t.a.toString(),i=0;return 1!=h(t.g,10)?n=!1:(n=t.a.toString(),n="1"==n.charAt(0)&&"0"!=n.charAt(1)&&"1"!=n.charAt(1)),n?(i=1,t.b.a("1").a(" "),t.o=!0):null!=t.g.a[15]&&(n=new RegExp("^(?:"+h(t.g,15)+")"),n=e.match(n),null!=n&&null!=n[0]&&0<n[0].length&&(t.o=!0,i=n[0].length,t.b.a(e.substring(0,i)))),r(t.a),t.a.a(e.substring(i)),e.substring(0,i)}function P(t){var n=t.u.toString(),e=new RegExp("^(?:\\+|"+h(t.g,11)+")"),e=n.match(e);return null!=e&&null!=e[0]&&0<e[0].length?(t.o=!0,e=e[0].length,r(t.a),t.a.a(n.substring(e)),r(t.b),t.b.a(n.substring(0,e)),"+"!=n.charAt(0)&&t.b.a(" "),!0):!1}function V(t){if(0==t.a.b.length)return!1;var n,i=new e;t:{if(n=t.a.toString(),0!=n.length&&"0"!=n.charAt(0))for(var l,a=n.length,u=1;3>=u&&a>=u;++u)if(l=parseInt(n.substring(0,u),10),l in X){i.a(n.substring(u)),n=l;break t}n=0}return 0==n?!1:(r(t.a),t.a.a(i.toString()),i=A(n),"001"==i?t.g=N(t.F,""+n):i!=t.C&&(t.g=E(t,i)),t.b.a(""+n).a(" "),t.l="",!0)}function q(t,n){var e=t.m.toString();if(0<=e.substring(t.s).search(t.H)){var i=e.search(t.H),e=e.replace(t.H,n);return r(t.m),t.m.a(e),t.s=i,e.substring(0,t.s+1)}return 1==t.f.length&&(t.j=!1),t.v="",t.h.toString()}var T=this;e.prototype.b="",e.prototype.set=function(t){this.b=""+t},e.prototype.a=function(t,n,e){if(this.b+=String(t),null!=n)for(var r=1;r<arguments.length;r++)this.b+=arguments[r];return this},e.prototype.toString=function(){return this.b};var U=1,Y=2,k=3,J=4,K=6,L=16,O=18;f.prototype.set=function(t,n){b(this,t.b,n)},f.prototype.clone=function(){var t=new this.constructor;return t!=this&&(t.a={},t.b&&(t.b={}),c(t,this)),t};var Z;n(y,f);var z;n(v,f);var Q;n(_,f),y.prototype.i=function(){return Z||(Z=m(y,{0:{name:"NumberFormat",I:"i18n.phonenumbers.NumberFormat"},1:{name:"pattern",required:!0,c:9,type:String},2:{name:"format",required:!0,c:9,type:String},3:{name:"leading_digits_pattern",G:!0,c:9,type:String},4:{name:"national_prefix_formatting_rule",c:9,type:String},6:{name:"national_prefix_optional_when_formatting",c:8,type:Boolean},5:{name:"domestic_carrier_code_formatting_rule",c:9,type:String}})),Z},y.ctor=y,y.ctor.i=y.prototype.i,v.prototype.i=function(){return z||(z=m(v,{0:{name:"PhoneNumberDesc",I:"i18n.phonenumbers.PhoneNumberDesc"},2:{name:"national_number_pattern",c:9,type:String},3:{name:"possible_number_pattern",c:9,type:String},6:{name:"example_number",c:9,type:String},7:{name:"national_number_matcher_data",c:12,type:String},8:{name:"possible_number_matcher_data",c:12,type:String}})),z},v.ctor=v,v.ctor.i=v.prototype.i,_.prototype.i=function(){return Q||(Q=m(_,{0:{name:"PhoneMetadata",I:"i18n.phonenumbers.PhoneMetadata"},1:{name:"general_desc",c:11,type:v},2:{name:"fixed_line",c:11,type:v},3:{name:"mobile",c:11,type:v},4:{name:"toll_free",c:11,type:v},5:{name:"premium_rate",c:11,type:v},6:{name:"shared_cost",c:11,type:v},7:{name:"personal_number",c:11,type:v},8:{name:"voip",c:11,type:v},21:{name:"pager",c:11,type:v},25:{name:"uan",c:11,type:v},27:{name:"emergency",c:11,type:v},28:{name:"voicemail",c:11,type:v},24:{name:"no_international_dialling",c:11,type:v},9:{name:"id",required:!0,c:9,type:String},10:{name:"country_code",c:5,type:Number},11:{name:"international_prefix",c:9,type:String},17:{name:"preferred_international_prefix",c:9,type:String},12:{name:"national_prefix",c:9,type:String},13:{name:"preferred_extn_prefix",c:9,type:String},15:{name:"national_prefix_for_parsing",c:9,type:String},16:{name:"national_prefix_transform_rule",c:9,type:String},18:{name:"same_mobile_and_fixed_line_pattern",c:8,defaultValue:!1,type:Boolean},19:{name:"number_format",G:!0,c:11,type:y},20:{name:"intl_number_format",G:!0,c:11,type:y},22:{name:"main_country_for_code",c:8,defaultValue:!1,type:Boolean},23:{name:"leading_digits",c:9,type:String},26:{name:"leading_zero_possible",c:8,defaultValue:!1,type:Boolean}})),Q},_.ctor=_,_.ctor.i=_.prototype.i,$.prototype.a=function(t){throw new t.b,Error("Unimplemented")},$.prototype.b=function(t,n){if(11==t.a||10==t.a)return n instanceof f?n:this.a(t.j.prototype.i(),n);if(14==t.a){if("string"==typeof n&&W.test(n)){var e=Number(n);if(e>0)return e}return n}if(!t.h)return n;if(e=t.j,e===String){if("number"==typeof n)return String(n)}else if(e===Number&&"string"==typeof n&&("Infinity"===n||"-Infinity"===n||"NaN"===n||W.test(n)))return Number(n);return n};var W=/^-?[0-9]+$/;n(S,$),S.prototype.a=function(t,n){var e=new t.b;return e.g=this,e.a=n,e.b={},e},n(w,S),w.prototype.b=function(t,n){return 8==t.a?!!n:$.prototype.b.apply(this,arguments)},w.prototype.a=function(t,n){return w.M.a.call(this,t,n)};/*

 Copyright (C) 2010 The Libphonenumber Authors

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
var X={91:["IN"]},tt={IN:[null,[null,null,"1\\d{7,12}|[2-9]\\d{9,10}","\\d{6,13}"],[null,null,"(?:11|2[02]|33|4[04]|79)[2-7]\\d{7}|80[2-467]\\d{7}|(?:1(?:2[0-249]|3[0-25]|4[145]|[59][14]|6[014]|7[1257]|8[01346])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|[36][25]|22|4[28]|5[12]|[78]1|9[15])|6(?:12|[2345]1|57|6[13]|7[14]|80)|7(?:12|2[14]|3[134]|4[47]|5[15]|[67]1|88)|8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91))[2-7]\\d{6}|(?:(?:1(?:2[35-8]|3[346-9]|4[236-9]|[59][0235-9]|6[235-9]|7[34689]|8[257-9])|2(?:1[134689]|3[24-8]|4[2-8]|5[25689]|6[2-4679]|7[13-79]|8[2-479]|9[235-9])|3(?:01|1[79]|2[1-5]|4[25-8]|5[125689]|6[235-7]|7[157-9]|8[2-467])|4(?:1[14578]|2[5689]|3[2-467]|5[4-7]|6[35]|73|8[2689]|9[2389])|5(?:[16][146-9]|2[14-8]|3[1346]|4[14-69]|5[46]|7[2-4]|8[2-8]|9[246])|6(?:1[1358]|2[2457]|3[2-4]|4[235-7]|[57][2-689]|6[24-578]|8[1-6])|8(?:1[1357-9]|2[235-8]|3[03-57-9]|4[0-24-9]|5\\d|6[2457-9]|7[1-6]|8[1256]|9[2-4]))\\d|7(?:(?:1[013-9]|2[0235-9]|3[2679]|4[1-35689]|5[2-46-9]|[67][02-9]|9\\d)\\d|8(?:2[0-6]|[013-8]\\d)))[2-7]\\d{5}","\\d{6,10}",null,null,"1123456789"],[null,null,"(?:7(?:0\\d{3}|2(?:[0235679]\\d|[14][017-9]|8[0-59]|9[389])\\d|3(?:[05-8]\\d{2}|1(?:[089]\\d|7[5-8])|2(?:[5-8]\\d|[01][089])|3[17-9]\\d|4[789]\\d|9[01689]\\d)|4(?:0[1-9]\\d|1(?:[015-9]\\d|4[08])|[29][89]\\d|39\\d|8[389]\\d)|5(?:[034678]\\d|2[03-9]|5[017-9]|9[7-9])\\d|6(?:0[0-47]|1[0-257-9]|2[0-4]|3[19]|5[4589]|[6-9]\\d)\\d|7(?:0[2-9]|[1-79]\\d|8[1-9])\\d|8[0-79]\\d{2}|99[4-9]\\d)|8(?:0(?:[01589]\\d|6[67])|1(?:[02-57-9]\\d|1[0135-9])|2(?:[236-9]\\d|5[1-9])|3(?:[0357-9]\\d|4[1-9])|[45]\\d{2}|6[02457-9]\\d|7(?:07|[1-69]\\d)|8(?:[0-26-9]\\d|44|5[2-9])|9(?:[035-9]\\d|2[2-9]|4[0-8]))\\d|9\\d{4})\\d{5}","\\d{10}",null,null,"9123456789"],[null,null,"1(?:600\\d{6}|80(?:0\\d{4,9}|3\\d{9}))","\\d{8,13}",null,null,"1800123456"],[null,null,"186[12]\\d{9}","\\d{13}",null,null,"1861123456789"],[null,null,"1860\\d{7}","\\d{11}",null,null,"18603451234"],[null,null,"NA","NA"],[null,null,"NA","NA"],"IN",91,"00","0",null,null,"0",null,null,null,[[null,"(\\d{5})(\\d{5})","$1 $2",["7(?:[02357]|4[0-389]|6[0-35-9]|8[0-79]|99)|8(?:0[015689]|1[0-57-9]|2[2356-9]|3[0-57-9]|[45]|6[02457-9]|7[01-69]|8[0-24-9]|9[02-9])|9","7(?:0|2(?:[0235679]|[14][017-9]|8[0-59]|9[389])|3(?:[05-8]|1[07-9]|2[015-8]|3[17-9]|4[789]|9[01689])|4(?:0[1-9]|1[014-9]|[29][89]|39|8[389])|5(?:[034678]|2[03-9]|5[017-9]|9[7-9])|6(?:0[0-47]|1[0-257-9]|2[0-4]|3[19]|5[4589]|[6-9])|7(?:0[2-9]|[1-79]|8[1-9])|8[0-79]|99[4-9])|8(?:0(?:[01589]|6[67])|1(?:[02-57-9]|1[0135-9])|2(?:[236-9]|5[1-9])|3(?:[0357-9]|4[1-9])|[45]|6[02457-9]|7(?:07|[1-69])|8(?:[0-26-9]|44|5[2-9])|9(?:[035-9]|2[2-9]|4[0-8]))|9","7(?:0|2(?:[0235679]|[14][017-9]|8[0-59]|9[389])|3(?:[05-8]|1(?:[089]|7[5-9])|2(?:[5-8]|[01][089])|3[17-9]|4[789]|9[01689])|4(?:0[1-9]|1(?:[015-9]|4[08])|[29][89]|39|8[389])|5(?:[034678]|2[03-9]|5[017-9]|9[7-9])|6(?:0[0-47]|1[0-257-9]|2[0-4]|3[19]|5[4589]|[6-9])|7(?:0[2-9]|[1-79]|8[1-9])|8[0-79]|99[4-9])|8(?:0(?:[01589]|6[67])|1(?:[02-57-9]|1[0135-9])|2(?:[236-9]|5[1-9])|3(?:[0357-9]|4[1-9])|[45]|6[02457-9]|7(?:07|[1-69])|8(?:[0-26-9]|44|5[2-9])|9(?:[035-9]|2[2-9]|4[0-8]))|9"],"0$1",null,1],[null,"(\\d{2})(\\d{4})(\\d{4})","$1 $2 $3",["11|2[02]|33|4[04]|79|80[2-46]"],"0$1",null,1],[null,"(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["1(?:2[0-249]|3[0-25]|4[145]|[569][14]|7[1257]|8[1346]|[68][1-9])|2(?:1[257]|3[013]|4[01]|5[0137]|6[0158]|78|8[1568]|9[14])|3(?:26|4[1-3]|5[34]|6[01489]|7[02-46]|8[159])|4(?:1[36]|2[1-47]|3[15]|5[12]|6[0-26-9]|7[0-24-9]|8[013-57]|9[014-7])|5(?:1[025]|[36][25]|22|4[28]|5[12]|[78]1|9[15])|6(?:12|[2345]1|57|6[13]|7[14]|80)"],"0$1",null,1],[null,"(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["7(?:12|2[14]|3[134]|4[47]|5[15]|[67]1|88)","7(?:12|2[14]|3[134]|4[47]|5(?:1|5[2-6])|[67]1|88)"],"0$1",null,1],[null,"(\\d{3})(\\d{3})(\\d{4})","$1 $2 $3",["8(?:16|2[014]|3[126]|6[136]|7[078]|8[34]|91)"],"0$1",null,1],[null,"(\\d{4})(\\d{3})(\\d{3})","$1 $2 $3",["1(?:[23579]|[468][1-9])|[2-8]"],"0$1",null,1],[null,"(1600)(\\d{2})(\\d{4})","$1 $2 $3",["160","1600"],"$1",null,1],[null,"(1800)(\\d{4,5})","$1 $2",["180","1800"],"$1",null,1],[null,"(18[06]0)(\\d{2,4})(\\d{4})","$1 $2 $3",["18[06]","18[06]0"],"$1",null,1],[null,"(140)(\\d{3})(\\d{4})","$1 $2 $3",["140"],"$1",null,1],[null,"(\\d{4})(\\d{3})(\\d{3})(\\d{3})","$1 $2 $3 $4",["18[06]","18(?:0[03]|6[12])"],"$1",null,1]],null,[null,null,"NA","NA"],null,null,[null,null,"1(?:600\\d{6}|8(?:0(?:0\\d{4,9}|3\\d{9})|6(?:0\\d{7}|[12]\\d{9})))","\\d{8,13}",null,null,"1800123456"],[null,null,"140\\d{7}","\\d{10}",null,null,"1409305260"],null,null,[null,null,"NA","NA"]]};x.b=function(){return x.a?x.a:x.a=new x};var nt={0:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9","０":"0","１":"1","２":"2","３":"3","４":"4","５":"5","６":"6","７":"7","８":"8","９":"9","٠":"0","١":"1","٢":"2","٣":"3","٤":"4","٥":"5","٦":"6","٧":"7","٨":"8","٩":"9","۰":"0","۱":"1","۲":"2","۳":"3","۴":"4","۵":"5","۶":"6","۷":"7","۸":"8","۹":"9"},et=RegExp("[+＋]+"),rt=RegExp("([0-9０-９٠-٩۰-۹])"),it=/^\(?\$1\)?$/,lt=new _;b(lt,11,"NA");var at=/\[([^\[\]])*\]/g,ut=/\d(?=[^,}][^,}])/g,ot=RegExp("^[-x‐-―−ー－-／  ­​⁠　()（）［］.\\[\\]/~⁓∼～]*(\\$\\d[-x‐-―−ー－-／  ­​⁠　()（）［］.\\[\\]/~⁓∼～]*)+$"),st=/[- ]/;j.prototype.K=function(){this.B="",r(this.h),r(this.u),r(this.m),this.s=0,this.v="",r(this.b),this.l="",r(this.a),this.j=!0,this.w=this.o=this.D=!1,this.f=[],this.A=!1,this.g!=this.J&&(this.g=E(this,this.C))},j.prototype.L=function(t){return this.B=C(this,t)},t("Cleave.AsYouTypeFormatter",j),t("Cleave.AsYouTypeFormatter.prototype.inputDigit",j.prototype.L),t("Cleave.AsYouTypeFormatter.prototype.clear",j.prototype.K)}.call("object"==typeof global&&global?global:window);
(function() {
  this.CardPayment = (function() {
    var changeCardIcon, computeGrandTotal, getApiURL, getCardNumber, getCvv, getEncodedBody, getMerchantID, getMonth, getYear, initialize, mastercardDiscount, onCardChange;

    initialize = function() {
      this.cvvCleave = new Cleave(this.cvvSelector, {
        blocks: [3]
      });
      this.cardCleave = new Cleave(this.cardSelector, {
        creditCard: true,
        creditCardStrictMode: true,
        onCreditCardTypeChanged: onCardChange
      });
      return this.expiryCleave = new Cleave(this.expirySelector, {
        date: true,
        datePattern: ['m', 'y']
      });
    };

    onCardChange = function(type) {
      this.cvvCleave.destroy();
      if (type === "amex") {
        this.cvvCleave = new Cleave(this.cvvSelector, {
          blocks: [4]
        });
      } else {
        this.cvvCleave = new Cleave(this.cvvSelector, {
          blocks: [3]
        });
        mastercardDiscount(type);
      }
      return changeCardIcon(type);
    };

    mastercardDiscount = function(type) {
      var grandtotal, mastercard_discount, prepaid_discount, selected_option, shipping_discount, symbol;
      selected_option = $(".accordion-navigation.active");
      mastercard_discount = selected_option.data('mastercardDiscount');
      if (mastercard_discount === 0) {

      } else {
        symbol = selected_option.data('symbol');
        grandtotal = selected_option.data('grandtotal');
        prepaid_discount = selected_option.data('prepaidDiscount');
        shipping_discount = selected_option.data('shipping');
        if (type === "mastercard") {
          $('.card_message').show();
          $('.card_message').html('Mastercard Discount is applied!');
          $('.prepaid_discount').hide();
          $('#prepaid_discount').val(0);
          $('.mastercard_discount').show();
          $('#mastercard_discount').val(gon.mastercard_discount);
          if (selected_option.data('prepaidShippingPromo') === 'available') {
            $('#shipping_charge').html('Shipping : ' + 'FREE'.fontcolor('green').bold());
            return computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, shipping_discount);
          } else {
            $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount);
            return computeGrandTotal(symbol, grandtotal, 0, mastercard_discount, 0);
          }
        } else {
          $('.card_message').hide();
          $('.mastercard_discount').hide();
          if ($('.prepaid_discount').length > 0) {
            $('.prepaid_discount').show();
            $('#prepaid_discount').val(gon.prepaid_discount);
          }
          $('#mastercard_discount').val("0");
          if (selected_option.data('prepaidShippingPromo') === 'available') {
            $('#shipping_charge').html('Shipping : ' + 'FREE'.fontcolor('green').bold());
            return computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount);
          } else {
            $('#shipping_charge').html('Shipping : ' + symbol + ' ' + shipping_discount);
            return computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0);
          }
        }
      }
    };

    computeGrandTotal = function(symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount) {
      var total, total_with_symbol;
      total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount;
      total_with_symbol = symbol + ' ' + total;
      $('.grand_total').html('Grand Total : ' + total_with_symbol);
      return $('.grand_total.top').html(total_with_symbol);
    };

    changeCardIcon = function(type) {
      $(this.cardIconSelector).removeClass(function(index, className) {
        return (className.match(/pf-.+/) || []).join('');
      });
      return $(this.cardIconSelector).addClass("pf-" + type);
    };

    getCardNumber = function() {
      return this.cardCleave.getRawValue();
    };

    getCvv = function() {
      return this.cvvCleave.getRawValue();
    };

    getMonth = function() {
      return this.expiryCleave.getFormattedValue().split('/')[0];
    };

    getYear = function() {
      return this.expiryCleave.getFormattedValue().split('/')[1];
    };

    getMerchantID = function() {
      return $('#merchant-id').val();
    };

    getApiURL = function() {
      return $('#apiURL').val();
    };

    getEncodedBody = function() {
      var cardDetails, encodedKey, encodedValue, formBody, property;
      cardDetails = {
        "card_number": getCardNumber(),
        "card_exp_year": getYear(),
        "card_exp_month": getMonth(),
        "card_security_code": getCvv(),
        "merchant_id": getMerchantID()
      };
      formBody = [];
      for (property in cardDetails) {
        encodedKey = encodeURIComponent(property);
        encodedValue = encodeURIComponent(cardDetails[property]);
        formBody.push(encodedKey + '=' + encodedValue);
      }
      return formBody = formBody.join('&');
    };

    CardPayment.prototype.isSelected = function() {
      return this.accordionNavigation.hasClass('active');
    };

    CardPayment.prototype.clear = function() {
      this.cardCleave.setRawValue('');
      this.cvvCleave.setRawValue('');
      return this.expiryCleave.setRawValue('');
    };

    CardPayment.prototype.isValid = function() {
      return getCardNumber().length >= 14 && getCvv().length >= 3 && getMonth().length > 0 && getYear().length > 0;
    };

    function CardPayment(cardSelector, expirySelector, cvvSelector, cardIconSelector, accordionNavigation) {
      initialize = initialize.bind(this);
      onCardChange = onCardChange.bind(this);
      changeCardIcon = changeCardIcon.bind(this);
      getCardNumber = getCardNumber.bind(this);
      getCvv = getCvv.bind(this);
      getMonth = getMonth.bind(this);
      getYear = getYear.bind(this);
      getMerchantID = getMerchantID.bind(this);
      getEncodedBody = getEncodedBody.bind(this);
      getApiURL = getApiURL.bind(this);
      this.apiURL = getApiURL() + "/card/tokenize";
      this.cardSelector = cardSelector;
      this.expirySelector = expirySelector;
      this.cvvSelector = cvvSelector;
      this.cardIconSelector = cardIconSelector;
      this.accordionNavigation = accordionNavigation;
      initialize();
    }

    CardPayment.prototype.makePayment = function() {
      return fetch(this.apiURL, {
        method: 'POST',
        body: getEncodedBody(),
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        }
      }).then(function(res) {
        return res.json();
      }).then(function(data) {
        if (data['status'] === "error") {
          if (data['error_message'].includes('[card_security_code]')) {
            $('#card-error').val("CVV is not present");
          } else {
            $('#card-error').val(data['error_message']);
          }
        } else {
          $('#card-token').val(data['token']);
        }
        return $("#new_order").trigger('submit');
      })["catch"](function(error) {
        return console.log(error);
      });
    };

    return CardPayment;

  })();

}).call(this);
