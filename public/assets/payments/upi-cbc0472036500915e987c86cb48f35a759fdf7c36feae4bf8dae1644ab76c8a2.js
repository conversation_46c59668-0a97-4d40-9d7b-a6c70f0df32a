(function() {
  var extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },
    hasProp = {}.hasOwnProperty;

  this.UPIPayment = (function(superClass) {
    var bindings, updateInputType;

    extend(UPIPayment, superClass);

    bindings = function() {
      return this.$typeDeciderElements.on('change', (function(_this) {
        return function(event) {
          var $selector;
          $selector = $(event.target);
          if ($selector.context.nodeName === "SELECT") {
            $selector = $selector.find(':selected');
          }
          return updateInputType.call(_this, $selector.data("type"));
        };
      })(this));
    };

    updateInputType = function(type) {
      var message;
      if (this.cleave !== void 0) {
        this.cleave.destroy();
      }
      this.$IDInput.val('');
      switch (type) {
        case "vpa":
          message = "Enter VPA";
          this.$IDInput.attr("disabled", false);
          this.$IDInput.attr("type", "text");
          break;
        case "mobile":
          message = "Enter phone number";
          this.cleave = new Cleave(this.$IDInput.get()[0], {
            phone: true,
            phoneRegionCode: 'IN'
          });
          this.$IDInput.attr("disabled", false);
          this.$IDInput.attr("type", "text");
          break;
        default:
          message = "Please select an option";
          this.$IDInput.attr("disabled", true);
      }
      return this.$IDInput.attr("placeholder", message);
    };

    function UPIPayment(container) {
      UPIPayment.__super__.constructor.call(this, container);
      this.$IDInput = $('.payment-upi-id input');
      this.$typeDeciderElements = this.$container.find('input[type=radio], select');
      updateInputType.call(this);
      bindings.call(this);
    }

    return UPIPayment;

  })(OtherPayment);

}).call(this);
