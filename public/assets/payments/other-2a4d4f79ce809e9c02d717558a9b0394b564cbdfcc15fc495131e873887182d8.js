(function() {
  this.OtherPayment = (function() {
    var bindings, clearInitialListInputs, clearRemainingListInputs, computeGrandTotal, togglePrepaidPromotion;

    bindings = function() {
      this.$initialListInputs.on("change", clearRemainingListInputs);
      return this.$remainingListInputs.on("change", (function(_this) {
        return function(event) {
          var $targetInput;
          $targetInput = $(event.target);
          if ($targetInput.val() === "Default") {
            return;
          }
          return clearInitialListInputs();
        };
      })(this));
    };

    clearInitialListInputs = function() {
      togglePrepaidPromotion();
      this.$initialListInputs.filter(":checked").attr('checked', false) && this.$remainingListInputs.find(":selected").attr('selected', true);
      $('.cvv-input').attr("type", "hidden");
      return $('.payment-upi-id input').attr("type", "hidden");
    };

    clearRemainingListInputs = function() {
      this.$remainingListInputs.find(":selected").attr('selected', false);
      return this.$remainingListInputs.each(function() {
        var $el;
        $el = $(this);
        return $el.val($el.data("default"));
      });
    };

    togglePrepaidPromotion = function() {
      var grandtotal, prepaid_discount, selected_option, shipping_discount, symbol;
      selected_option = $(".accordion-navigation.active");
      symbol = selected_option.data('symbol');
      grandtotal = selected_option.data('grandtotal');
      prepaid_discount = selected_option.data('prepaidDiscount');
      shipping_discount = selected_option.data('shipping');
      $('#wallet_discount_order_page').show();
      if ($(".accordion-navigation.active").length === 1) {
        if (selected_option.data('prepaidShippingPromo') === 'available') {
          $('#shipping_charge').html('Shipping : ' + 'FREE'.fontcolor('green').bold());
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, shipping_discount);
        } else {
          $('#shipping_charge').html('Shipping : ' + symbol + ' ' + selected_option.data('shipping'));
          computeGrandTotal(symbol, grandtotal, prepaid_discount, 0, 0);
        }
        $('.prepaid_discount').show();
        $('#prepaid_discount').val(gon.prepaid_discount);
        $('.grand_total').show();
        $('.grand_total_with_cod.hide').hide();
        $('.cod_charges').hide();
      } else {
        selected_option = $(".accordion-navigation");
        grandtotal = selected_option.data('grandtotal');
        symbol = selected_option.data('symbol');
        shipping_discount = selected_option.data('shipping');
        computeGrandTotal(symbol, grandtotal, 0, 0, 0);
        $('#shipping_charge').html('Shipping : ' + shipping_discount);
        $('.prepaid_discount').hide();
        $('#prepaid_discount').val(0);
      }
      $('#mastercard_discount').val("0");
      $('.mastercard_discount').hide();
      $('.card_message').hide();
      return $('.saved_card_message').hide();
    };

    computeGrandTotal = function(symbol, grandtotal, prepaid_discount, mastercard_discount, shipping_discount) {
      var total, total_with_symbol;
      total = grandtotal - prepaid_discount - mastercard_discount - shipping_discount;
      total_with_symbol = symbol + ' ' + total;
      $('.grand_total').html('Grand Total : ' + total_with_symbol);
      return $('.grand_total.top').html(total_with_symbol);
    };

    OtherPayment.prototype.isValid = function() {
      return this.$initialListInputs.filter(":checked").length > 0 || (this.$remainingListInputs.find(":selected").val() !== "Default" && this.$remainingListInputs.find(":selected").length !== 0);
    };

    function OtherPayment(container) {
      bindings = bindings.bind(this);
      clearInitialListInputs = clearInitialListInputs.bind(this);
      clearRemainingListInputs = clearRemainingListInputs.bind(this);
      this.$container = $(container);
      this.$initialListInputs = this.$container.find('[type=radio]');
      this.$remainingListInputs = $('.payment-other-remaining-list select');
      bindings();
    }

    OtherPayment.prototype.clear = function() {
      clearInitialListInputs();
      return clearRemainingListInputs();
    };

    return OtherPayment;

  })();

}).call(this);
