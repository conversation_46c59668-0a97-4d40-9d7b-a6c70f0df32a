!function(n,e){function t(e){var t,o=n("<div></div>").css({width:"100%"});return e.append(o),t=e.width()-o.width(),o.remove(),t}function o(r,i){var a=r.getBoundingClientRect(),s=a.top,c=a.bottom,l=a.left,u=a.right,d=n.extend({tolerance:0,viewport:e},i),p=!1,f=d.viewport.jquery?d.viewport:n(d.viewport);f.length||(console.warn("isInViewport: The viewport selector you have provided matches no element on page."),console.warn("isInViewport: Defaulting to viewport as window"),f=n(e));var w=f.height(),h=f.width(),v=f.get(0).toString();if(f[0]!==e&&"[object Window]"!==v&&"[object DOMWindow]"!==v){var g=f.get(0).getBoundingClientRect();s-=g.top,c-=g.top,l-=g.left,u-=g.left,o.scrollBarWidth=o.scrollBarWidth||t(f),h-=o.scrollBarWidth}return d.tolerance=~~Math.round(parseFloat(d.tolerance)),d.tolerance<0&&(d.tolerance=w+d.tolerance),0>=u||l>=h?p:p=d.tolerance?!!(s<=d.tolerance&&c>=d.tolerance):!!(c>0&&w>=s)}String.prototype.hasOwnProperty("trim")||(String.prototype.trim=function(){return this.replace(/^\s*(.*?)\s*$/,"$1")});var r=function(e){if(1===arguments.length&&"function"==typeof e&&(e=[e]),!(e instanceof Array))throw new SyntaxError("isInViewport: Argument(s) passed to .do/.run should be a function or an array of functions");for(var t=0;t<e.length;t++)if("function"==typeof e[t])for(var o=0;o<this.length;o++)e[t].call(n(this[o]));else console.warn("isInViewport: Argument(s) passed to .do/.run should be a function or an array of functions"),console.warn("isInViewport: Ignoring non-function values in array and moving on");return this};n.fn["do"]=function(n){return console.warn("isInViewport: .do causes issues in IE and some browsers since its a reserved. Use $.fn.run instead i.e., $(el).run(fn)."),r(n)},n.fn.run=r,n.extend(n.expr[":"],{"in-viewport":function(n,e,t){if(t[3]){var r=t[3].split(",");return 1===r.length&&isNaN(r[0])&&(r[1]=r[0],r[0]=void 0),o(n,{tolerance:r[0]?r[0].trim():void 0,viewport:r[1]?r[1].trim():void 0})}return o(n)}})}(jQuery,window);