/*! lightslider - v1.1.3 - 2015-04-14
* https://github.com/sachinchoolur/lightslider
* Copyright (c) 2015 Sachin N; Licensed MIT */
/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper, .lSSlideWrapper .lSFade {
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide, .lSSlideWrapper.usingCss .lSFade > * {
  -webkit-transition-timing-function: inherit !important;
  transition-timing-function: inherit !important;
  -webkit-transition-duration: inherit !important;
  transition-duration: inherit !important;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter, .lSSlideOuter .lSPager.lSGallery {
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideWrapper > .lightSlider:after {
  clear: both;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter {
  overflow: hidden;
  user-select: none;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider:after, .lightSlider:before {
  content: " ";
  display: table;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider {
  overflow: hidden;
  margin: 0;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper {
  max-width: 100%;
  overflow: hidden;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: all 1s;
  -webkit-transition-property: -webkit-transform,height;
  -moz-transition-property: -moz-transform,height;
  transition-property: transform,height;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > * {
  position: absolute !important;
  top: 0;
  left: 0;
  z-index: 9;
  margin-right: 0;
  width: 100%;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > * {
  opacity: 0;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-transition-property: opacity;
  transition-property: opacity;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > .active {
  z-index: 10;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > .active {
  opacity: 1;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg {
  margin: 10px 0 0;
  padding: 0;
  text-align: center;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li {
  cursor: pointer;
  display: inline-block;
  padding: 0 5px;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li a {
  background-color: #222;
  border-radius: 30px;
  display: inline-block;
  height: 8px;
  overflow: hidden;
  text-indent: -999em;
  width: 8px;
  position: relative;
  z-index: 99;
  -webkit-transition: all .5s linear 0s;
  transition: all .5s linear 0s;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li.active a, .lSSlideOuter .lSPager.lSpg > li:hover a {
  background-color: #428bca;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media {
  opacity: .8;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media.active {
  opacity: 1;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery {
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  user-select: none;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li {
  overflow: hidden;
  -webkit-transition: border-radius .12s linear 0s .35s linear 0s;
  transition: border-radius .12s linear 0s .35s linear 0s;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li.active, .lSSlideOuter .lSPager.lSGallery li:hover {
  border-radius: 5px;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery img {
  display: block;
  height: auto;
  max-width: 100%;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideOuter .lSPager.lSGallery:before {
  content: " ";
  display: table;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a {
  width: 32px;
  display: block;
  top: 50%;
  height: 32px;
  background-image: url(/assets/controls-f4c3996ad997182376dddc345b138b599bae61a44ac41dadb19bc18710908226.png);
  cursor: pointer;
  position: absolute;
  z-index: 98;
  margin-top: -16px;
  opacity: .5;
  -webkit-transition: opacity .35s linear 0s;
  transition: opacity .35s linear 0s;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a:hover {
  opacity: 1;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSPrev {
  background-position: 0 0;
  left: 10px;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSNext {
  background-position: -32px 0;
  right: 10px;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a.disabled {
  pointer-events: none;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.cS-hidden {
  /*height:1px;opacity:0;*/
  filter: alpha(opacity=0);
  overflow: hidden;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical {
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical.noPager {
  padding-right: 0 !important;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSGallery {
  position: absolute !important;
  right: 0;
  top: 0;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lightSlider > * {
  width: 100% !important;
  max-width: none !important;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > a {
  left: 50%;
  margin-left: -14px;
  margin-top: 0;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSNext {
  background-position: 31px -31px;
  bottom: 10px;
  top: auto;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSPrev {
  background-position: 0 -31px;
  bottom: auto;
  top: 10px;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl {
  direction: rtl;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager, .lSSlideOuter .lightSlider {
  padding-left: 0;
  list-style: none;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSPager, .lSSlideOuter.lSrtl .lightSlider {
  padding-right: 0;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSGallery li, .lSSlideOuter .lightSlider > * {
  float: left;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSGallery li, .lSSlideOuter.lSrtl .lightSlider > * {
  float: right !important;
}

@-webkit-keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@-webkit-keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@-webkit-keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@-webkit-keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
@keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .rightEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .leftEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .rightEnd {
  -webkit-animation: topEnd .3s;
  animation: topEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .leftEnd {
  -webkit-animation: bottomEnd .3s;
  animation: bottomEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .rightEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .leftEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrab > * {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: -o-grab;
  cursor: -ms-grab;
  cursor: grab;
}

/* line 4, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrabbing > * {
  cursor: move;
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: -o-grabbing;
  cursor: -ms-grabbing;
  cursor: grabbing;
}
