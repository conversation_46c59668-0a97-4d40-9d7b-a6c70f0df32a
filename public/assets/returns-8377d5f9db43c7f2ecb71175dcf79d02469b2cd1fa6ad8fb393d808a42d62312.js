(function() {
  var CheckAccountNumber, checkBoxCheckEvent, checkTrackingNumber, verifyIFSC;

  checkBoxCheckEvent = function(id) {
    var check_box;
    check_box = $('input[value=' + id + ']');
    if (check_box.is(':checked')) {
      check_box[0].checked = false;
      $('#item_' + id).addClass('hide');
    } else {
      check_box[0].checked = true;
      $('#item_' + id).removeClass('hide');
    }
    if ($("input[type=checkbox]:checked").length !== 0) {
      $('.submit_reason').removeClass('hide');
      return $('#reason_' + id).attr('required', true);
    } else {
      $('.submit_reason').addClass('hide');
      return $('#reason_' + id).removeAttr('required');
    }
  };

  $(function() {
    return $(document).on('click', '.img-check', function() {
      var id, msg, text_msg;
      id = this.id.substring(6);
      msg = $(this).attr('data-notice');
      if (msg === '') {
        $('.error-alert').remove();
        $(this).toggleClass('check');
        $('#done_step_' + id).toggleClass('done');
        return checkBoxCheckEvent(id);
      } else {
        $('.error-alert').remove();
        text_msg = "<div data-alert class='alert-box row warning error-alert radius'>" + msg + "<a href='#' class='close'></a></div>";
        return $('#new_return').prepend(text_msg);
      }
    });
  });

  $(document).on('change', '#new_return .quantity', function() {
    var item_id;
    item_id = $(this).attr('data-item-id');
    return $('#total_' + item_id).text((Number($('#quantity_' + item_id + ' option:selected').text()) * Number($('#item_price_' + item_id).text())).toFixed(2));
  });

  $(function() {
    return $(document).on('change', '#new_return .item_reason', function() {
      var i, id, len, reason, ref, return_reason, return_reason_hash;
      id = this.id.substring(7);
      return_reason_hash = $(this).data('return-reason-hash');
      $('#upload_panel_' + id).addClass('hide');
      $('#uploadimage_' + id).removeAttr('required');
      $('#reason_details_panel_' + id).addClass('hide');
      $('#reason_details_' + id).find('option').remove();
      $('#uploadimage_' + id).val('');
      if ($.inArray($(this).val(), ['Print /Design /Color Mismatch', 'Damaged Product', 'Wrong Product']) !== -1) {
        $('#upload_panel_' + id).removeClass('hide');
        $('#uploadimage_' + id).attr('required', true);
      } else {
        ref = return_reason_hash[$(this).val()];
        for (i = 0, len = ref.length; i < len; i++) {
          reason = ref[i];
          return_reason = $('<option>');
          return_reason.attr('value', reason).text(reason);
          $('#reason_details_' + id).append(return_reason);
        }
        $('#reason_details_panel_' + id).removeClass('hide');
      }
    });
  });

  CheckAccountNumber = function() {
    return {
      type: 'GET',
      data: {
        bank_name: $('#return_bank_name').val()
      },
      url: '/returns/get_account_no_length',
      datatype: 'json',
      success: function(data) {
        var error_label;
        error_label = $('#return_error_label_for_refund');
        if (data.length !== $('#return_account_number').val().length) {
          error_label.text("Account number should be of " + data.length + " digits");
          return error_label.addClass('label alert');
        } else {
          if (error_label.text().match(/Account number/) !== null) {
            error_label.text('');
            return error_label.removeClass('label alert');
          }
        }
      }
    };
  };

  verifyIFSC = function() {
    var error_label, ifsc_regex;
    ifsc_regex = /^[a-zA-Z]{4}[a-zA-Z0-9]{7}$/;
    error_label = $('#return_error_label_for_refund');
    if (ifsc_regex.test($('#return_ifsc_code').val())) {
      if (error_label.text().match(/IFSC Code/) !== null) {
        error_label.text('');
        error_label.removeClass('label alert');
      }
      return $.ajax(CheckAccountNumber());
    } else {
      error_label.addClass('label alert');
      return error_label.text("IFSC Code should be of 11 charachters only with first four letters.");
    }
  };

  $(function() {
    $(document).on('click', '.edit_buttons', function(e) {
      verifyIFSC();
      if ($('#return_error_label_for_refund').text() !== '') {
        e.preventDefault();
        return false;
      }
    });
    $(document).on('change', '.refund_type_dropdown', function(e) {
      var value;
      value = $('.refund_type_dropdown option:selected').text();
      $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number, #return_user_paypal_email').removeAttr('required');
      if (value === 'Refund') {
        $('.bank_details_form').removeClass('hide');
        $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').attr('required', true);
        return $('.bank_details_not_needed').addClass('hide');
      } else {
        $('.bank_details_form').find('input:text').val('');
        $('#return_user_paypal_email').attr('required', true).val('');
        $('.bank_details_not_needed').removeClass('hide');
        return $('.bank_details_form').addClass('hide');
      }
    });
    $(document).on('click', '.account_details', function(e) {
      if ($(this).text() === 'Paypal Account') {
        $('#paypal_detail').removeClass('hide');
        $('#bank_detail').addClass('hide');
        $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').removeAttr('required').val('');
        return $('#return_user_paypal_email').attr('required', true);
      } else {
        $('#paypal_detail').addClass('hide');
        $('#bank_detail').removeClass('hide');
        $('#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number').attr('required', true);
        return $('#return_user_paypal_email').removeAttr('required').val('');
      }
    });
    $(document).on('keyup', '#return_ifsc_code', function() {
      return verifyIFSC();
    });
    return $(document).on('keyup', '#return_account_number', function() {
      return $.ajax(CheckAccountNumber());
    });
  });

  $(function() {
    return $(document).on('click', '.user_return_panel .no_button', function() {
      var id;
      id = $(this).data('id');
      $('body').removeClass('modal-open');
      return $("#cancel_order_" + id).foundation('reveal', 'close');
    });
  });

  checkTrackingNumber = function(rdoid, tracking_number) {
    return {
      type: 'GET',
      data: {
        rdo_id: rdoid,
        tracking_number: tracking_number
      },
      url: '/returns/check_tracking_number',
      datatype: 'json',
      success: function(data) {
        if (data.new_track !== 'correct') {
          $('.same_track_notice').removeClass('hidden');
          $('.same_track_notice').text("* Please enter tracking number of package you will return to us.");
          return false;
        } else {
          $('.same_track_notice').addClass('hidden');
          $('.same_track_notice').text('');
          $(".return_designer_order_event").submit();
          return true;
        }
      }
    };
  };

  $(function() {
    $(document).on('click', '.designer_well .track_submit', function(e) {
      var rdoid, tracking_number;
      rdoid = $(this).attr('data-rdo-id');
      tracking_number = $('input[id="tracking_number_' + rdoid + '"]').val();
      if (tracking_number !== '') {
        e.preventDefault();
        return $.ajax(checkTrackingNumber(rdoid, tracking_number));
      }
    });
    $(document).on('click', '.policy_button', function() {
      return $('body').addClass('modal-open');
    });
    $(document).on('click', '.close-reveal-modal', function(e) {
      return $('body').removeClass('modal-open');
    });
    return $(document).on('change', '.item_image', function() {
      var allowedExtension, extError, extErrorMessage, inputFile, maxExceededMessage, maxFileSize, sizeExceeded;
      inputFile = $(this);
      maxExceededMessage = 'This file exceeds the maximum allowed file size (3 MB)';
      extErrorMessage = 'Only image file with extension: .jpg, .jpeg, .gif or .png is allowed';
      allowedExtension = ['jpg', 'jpeg', 'gif', 'png'];
      maxFileSize = inputFile.data('max-file-size');
      sizeExceeded = false;
      extError = false;
      $.each(this.files, function() {
        var extName;
        if (this.size && maxFileSize && this.size > parseInt(maxFileSize)) {
          sizeExceeded = true;
        }
        extName = this.name.split('.').pop();
        if ($.inArray(extName, allowedExtension) === -1) {
          extError = true;
        }
      });
      if (sizeExceeded) {
        window.alert(maxExceededMessage);
        $(inputFile).val('');
      }
      if (extError) {
        window.alert(extErrorMessage);
        $(inputFile).val('');
      }
    });
  });

}).call(this);
