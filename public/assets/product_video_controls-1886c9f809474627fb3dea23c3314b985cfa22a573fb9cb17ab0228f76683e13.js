(function() {
  var mute_unmute_switch, pause_Video, play_Video, product_video;

  product_video = document.getElementById('product_video');

  play_Video = function() {
    product_video.play();
  };

  pause_Video = function() {
    product_video.pause();
  };

  mute_unmute_switch = function() {
    product_video.muted = true;
  };

  $('#design_images').on('after-slide-change.fndtn.orbit', function(e) {
    var current_caraousel;
    current_caraousel = document.getElementsByClassName('active')[0];
    if (current_caraousel.contains(product_video)) {
      return play_Video();
    } else {
      if (product_video) {
        pause_Video();
        return mute_unmute_switch();
      }
    }
  });

  return;

}).call(this);
