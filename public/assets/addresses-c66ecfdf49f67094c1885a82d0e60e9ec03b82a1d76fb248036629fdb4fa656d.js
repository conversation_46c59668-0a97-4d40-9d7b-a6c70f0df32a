(function() {
  $(function() {
    var createStateDropDown, paramsGetStates;
    $('.mark_as_default').on('change', function(e) {
      return $(this).parent().submit();
    });
    if ($('#address_country').length > 0) {
      createStateDropDown = function(state_list) {
        var options;
        options = '';
        $.each(state_list, function(index, state) {
          return options += "<option value = '" + state + "'>" + state + "</option>";
        });
        return options;
      };
      paramsGetStates = function(country_code, id, selected_value) {
        return {
          type: 'GET',
          url: '/country/' + country_code + '/get_states',
          success: function(data, status, jqhxr) {
            var html_data, options;
            options = createStateDropDown(data);
            if (options === '') {
              html_data = '<input id="address_state" name="address[state]" size="30" required=true placeholder="Enter Your State" type="text">';
            } else {
              options = "<option value = ''>Please Select</option>" + options;
              html_data = "<select id=address_state name =address[state] required=true>" + options + "</select>";
            }
            $('#address_state').replaceWith(html_data);
            if (typeof selected_value !== 'undefined') {
              return $('#address_state').val(selected_value);
            }
          }
        };
      };
      $('#address_country').on('change', function(e) {
        if ($(this).val() !== '') {
          return $.ajax(paramsGetStates($(this).val(), $(this)[0].id));
        }
      });
      if ($('#address_country').val() !== '') {
        $.ajax(paramsGetStates($('#address_country').val(), $('#address_country')[0].id));
      }
    }
    return $('#shipping_address').on('click', function(e) {
      var ship_value;
      ship_value = 0;
      if ($('#shipping_address:checked').length > 0) {
        ship_value = 1;
      }
      return $('#ship_to_same_address').attr('value', ship_value);
    });
  });

}).call(this);
