/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 4, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
table tr {
  background: none !important;
}

/* line 7, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
table.table-borderless {
  background: none;
  border: none;
}

/* line 11, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.table-borderless td,
.table-borderless th {
  border: 0 !important;
  background: none;
  color: #303030;
}

/* line 18, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 23, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.order_return_links {
  background-color: #f4f4f4;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.order_return_links.active {
  background-color: #670b19;
}
/* line 27, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.order_return_links.active a {
  color: white !important;
}
/* line 31, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.order_return_links a, .order_return_links a:focus, .order_return_links a:hover {
  color: #303030;
}

/* line 36, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.text_dark_red {
  color: #670b19;
  font-size: large;
}

/* line 41, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.reveal-modal .close-reveal-modal {
  color: #303030;
}

/* line 51, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.cancel_modal {
  background-color: #191919;
  padding-top: 50px;
  min-height: 0 !important;
  height: 250px;
  top: 10% !important;
}
/* line 57, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.cancel_modal #cancellation_message {
  font-size: 1.3em;
}
/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.cancel_modal .close_cross {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}
/* line 64, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.cancel_modal .footer {
  margin-top: 40px;
  float: right;
}

/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel {
  font-size: 0.75rem;
}
/* line 73, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block {
  background: #f4f4f4;
}
/* line 75, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .panel_content {
  font-size: 0.75rem;
}
/* line 77, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .panel_content .design-details {
  display: inline-flex;
  padding: 15px 0px;
}
/* line 80, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .panel_content .design-details .design-image {
  padding: 0px;
}
/* line 83, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .panel_content .design-details .design-title {
  vertical-align: top;
  padding: 0px 15px;
}
/* line 88, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .panel_content a {
  font-weight: bold;
  color: #303030;
}
/* line 93, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .return_order {
  background: transparent;
  color: #308cba;
  border: 1px solid #308cba;
  border-radius: 5px;
}
/* line 99, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_block .track_order {
  background: #abcfb8;
  color: #43ac6a;
  border: 1px solid #43ac6a;
  border-radius: 5px;
  font-weight: bold;
  margin-left: 15px;
}
/* line 108, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
  font-size: 12px;
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}
@-webkit-keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
/* line 130, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .img-check {
  cursor: pointer;
}
/* line 133, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .check {
  opacity: 0.3;
  color: #428bca;
}
/* line 138, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel #new_return b {
  font-size: 12px;
  color: #303030;
}
/* line 143, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .done label:after {
  content: '\2713';
  display: inline-block;
  color: #43AC6A;
  padding: 0 6px 0 0;
  font-size: x-large;
  font-weight: bolder;
  position: relative;
  top: -6.5em;
  left: 2.5em;
}
/* line 154, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .designer_well {
  padding: 5px;
  margin: 5px 5px 5px 5px;
  border-radius: 4px;
  border: none;
}
/* line 160, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .designer_well table {
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 162, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .designer_well table td {
  text-align: center;
  color: #303030;
}
/* line 168, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .designer_well .small-5, .user_return_panel .designer_well .small-2 {
  padding: 10px;
  font-size: large;
}
/* line 173, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .designer_well label {
  color: #303030;
}
/* line 177, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .well-legend {
  font-size: 14px;
  text-align: center;
  width: auto;
  padding: 5px 20px 5px 20px;
  margin-bottom: 20px;
  color: #4c4b4b;
  /*background: #010710;*/
  border: 1px solid #e3e3e3;
  border-radius: 2px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
/* line 191, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .item_block {
  /*border: 0.1em solid #4d4d4d;*/
  margin-bottom: 1em;
  padding: 0.5em 0em;
  box-shadow: 0 0 0.5em #2d2d2d;
  background-color: #ffffff;
  margin-left: 0;
  margin-right: 0;
  padding-left: 1em;
}
/* line 201, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .item_block .columns {
  padding-left: 0.4em;
}
/* line 206, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .status_notice {
  background-color: #E8C55B;
  border: 2px solid #D4A22E;
  border-radius: 5px;
  margin-top: 2%;
  height: auto;
  padding: 8px 15px;
  color: black;
  font-size: small;
}
/* line 217, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .notice_div {
  color: orange !important;
  padding: 0 10px 5px 10px;
  line-height: 120%;
  font-size: small;
}
/* line 224, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel #return_policy {
  color: #303030;
  display: none;
  padding: 10px;
  text-align: justify;
}
/* line 230, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel #return_policy ul li {
  margin-top: 10px;
  text-align: justify;
}
/* line 237, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .account_details:hover {
  cursor: pointer;
}
/* line 246, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .return_block {
  background: #f4f4f4;
  box-shadow: 0 0 0.5em #2d2d2d;
  margin-bottom: 2em;
}
/* line 251, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .return_block .return_desc {
  font-size: small;
  color: #303030;
}
/* line 256, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .return_block hr {
  border-color: #848080;
}
/* line 261, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .return_block:hover {
  cursor: pointer;
}
/* line 265, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block {
  color: #303030;
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 269, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block .expand {
  display: none;
  float: right;
}
/* line 274, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block .collapse {
  float: right;
}
/* line 278, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block p {
  padding: 10px;
  margin-bottom: 0;
}
/* line 283, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block a {
  color: #303030;
}
/* line 285, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .listing_panel_block a:hover, .user_return_panel .listing_panel_block a:focus {
  color: #303030;
}
/* line 291, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
.user_return_panel .panel_footer, .user_return_panel .panel_footer p {
  font-size: small;
  color: #303030;
}

@media only screen and (max-width: 40em) {
  /* line 299, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/returns_red.scss */
  .orbit-container .orbit-next, .orbit-container .orbit-prev {
    display: block !important;
  }
}
