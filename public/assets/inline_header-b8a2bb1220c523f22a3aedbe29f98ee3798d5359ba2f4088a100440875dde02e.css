/* line 2, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
html, body {
  height: 100%;
}

/* line 6, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
html {
  box-sizing: border-box;
}

/* line 10, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
* {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
*:before, *:after {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}

/* line 21, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
body, html {
  height: 100%;
  width: 100%;
}

/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
html {
  font-size: 100%;
}

/* line 30, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
body {
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  position: relative;
}

/* line 41, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
a {
  line-height: inherit;
  text-decoration: none;
}
/* line 44, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
a img {
  border: none;
}

/* line 49, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
#menu-side-nav {
  display: none;
}

/* line 56, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .cart-icon {
  background-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
  background-repeat: no-repeat;
  display: inline-block;
  width: 2em;
  height: 2em;
  vertical-align: middle;
  background-size: 2em;
}
/* line 65, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .search-box-margin {
  margin-top: 0.25em;
}
/* line 68, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar #cart_count {
  margin: 0.5em -2em;
  padding: 0.4em;
  background-color: red;
  color: white;
  font-weight: bolder;
}
/* line 76, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .logo {
  background-image: url("data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7");
  background-repeat: no-repeat;
  display: inline-block;
  width: 6em;
  height: 2em;
  vertical-align: middle;
  background-size: 6em 2em;
}
/* line 86, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .download-app {
  height: 1.7em;
  width: 6.8em;
  background-color: #399b44;
  padding: 5px 8px;
  color: #fff;
  border-radius: 10%/45%;
  box-shadow: 1px 1px 3px #444, inset 0px 0px 6px #262;
  text-shadow: 0px 0px 2px #444;
  display: inline;
  font-size: 0.7rem;
}
/* line 101, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll {
  width: 95%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
/* line 109, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll nav {
  margin: -7px 4px -3px 4px;
}
/* line 111, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll nav i.fi-burst-new {
  font-size: 22px;
  vertical-align: middle;
  margin: -4px 0px;
  color: #de6b23;
  position: absolute;
}
/* line 120, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box {
  background: white;
  display: none;
  padding: 0px 15px 2px 15px;
  border-radius: 0px 0px 2px 2px;
}
/* line 125, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box .trending-results-text {
  border-bottom: 1px solid #d4d4d4;
  color: #272626;
  font-weight: 600;
}
/* line 129, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box .trending-results-text .trending-icon {
  color: #292929;
}
/* line 133, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box ul#trending-results {
  margin: 0px;
}
/* line 135, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box ul#trending-results li {
  list-style-type: none;
  font-size: 16px;
  text-transform: capitalize;
  padding: 10px 0px;
}
/* line 140, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #trending-results-box ul#trending-results li a {
  color: #271f35;
}
/* line 146, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin {
  width: 96%;
  margin: 0 auto;
  margin-top: -15px;
  padding: 6px 0px;
  display: none;
}
/* line 152, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text'] {
  border-radius: 2px 0px 0px 2px;
  padding-left: 18px;
  margin-bottom: 0px;
  border-bottom: none;
  background-color: white;
}
/* line 158, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:focus {
  box-shadow: none;
  border-bottom: none;
}
/* line 162, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 166, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 170, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 174, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 179, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin button.submit_btn {
  border-radius: 0px 2px 2px 0px;
  padding: 0px;
  margin-bottom: 0px;
}
/* line 188, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .scroll-button {
  position: absolute;
  font-size: 20px;
  line-height: 31px;
  background-color: #655d5d;
  color: #dedbdb;
  text-align: center;
  height: 33px;
}
/* line 198, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-right {
  display: none;
  top: 0px;
  z-index: 1;
  width: 8%;
}
/* line 203, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-right:after {
  content: '';
  width: 10px;
  height: 10px;
  position: absolute;
  transform: rotate(-45deg);
  border-left: 1px solid white;
  border-top: 1px solid white;
  top: 10px;
  left: 10px;
}
/* line 216, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-left {
  top: 0px;
  right: 1px;
  width: 8%;
}
/* line 220, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-left:after {
  content: '';
  width: 10px;
  height: 10px;
  position: absolute;
  transform: rotate(-45deg);
  border-right: 1px solid white;
  border-bottom: 1px solid white;
  top: 10px;
  right: 10px;
}
/* line 233, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .tab-fix {
  font-size: 0.8rem !important;
  padding: 0.5rem 0.8rem !important;
  width: fill-content;
  border-radius: 0px;
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
  margin-left: -3px;
}
/* line 244, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .tab-fix:hover, .off-canvas-wrap .inner-wrap div.tab-bar .tab-fix:focus {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
}
/* line 249, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .medium-tab-button {
  width: 100%;
}
/* line 254, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap .search_margin {
  margin-top: -15px;
}
/* line 256, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .inner-wrap .search_margin input[type='text'] {
  color: #383737;
}
/* line 262, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .submit_btn {
  background-color: white;
  border-color: transparent;
  border-left-color: #737373;
}
/* line 266, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .submit_btn i.fi-magnifying-glass {
  color: #1b1b1b;
  font-size: 18px;
}
/* line 271, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .main-section {
  margin-top: 7vh;
  overflow: hidden;
  position: relative;
  -webkit-overflow-scrolling: touch;
}
/* line 278, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap #main-section {
  width: 105vw;
  height: 99%;
  overflow: auto;
  padding-right: 1em;
}
/* line 285, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap ul.off-canvas-list li label {
  padding: 0.4em 0.6em;
}
/* line 290, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .left-submenu .back > a {
  padding: 0.1em 0.6em;
}
/* line 295, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap #container {
  width: 96vw;
  overflow-y: hidden;
  padding: 1em 0em;
  margin: 2%;
  margin-top: 5em;
  overflow-x: hidden;
}
/* line 304, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap .left-off-canvas-menu, .off-canvas-wrap .right-off-canvas-menu {
  min-height: 200vh;
}

/* line 308, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.no-scroll-background {
  overflow: hidden !important;
}

/* line 311, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.static-nav-bar {
  -webkit-transform: translate3d(0px, -33px, 0px);
  -moz-transform: translate3d(0px, -33px, 0px);
  -ms-transform: translate(-33px, 0);
  -ms-transform: translate3d(0px, -33px, 0px);
  -o-transform: translate3d(0px, -33px, 0px);
  transform: translate3d(0px, -33px, 0px);
  transition: transform .3s ease-in-out;
  width: 70% !important;
}

/* line 321, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
section.main-section {
  display: none;
}

@media only screen and (min-width: 320px) and (max-width: 568px) {
  /* line 328, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .download-app {
    font-size: 0.5rem !important;
    padding: 6px 5px !important;
  }
}
@media only screen and (min-width: 64.063em) {
  /* line 334, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .show-for-small-up.search_margin {
    width: 40% !important;
    margin-top: -98px !important;
  }

  /* line 338, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  div#search-tab {
    height: 2.3125rem !important;
  }
  /* line 340, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  div#search-tab #trending-results-box {
    position: fixed;
    width: 40%;
  }
}
/* line 347, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.left {
  float: left !important;
}

/* line 351, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.right {
  float: right !important;
}

/* line 355, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap {
  -webkit-backface-visibility: hidden;
  position: relative;
  width: 100%;
  overflow: hidden;
}
/* line 360, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.off-canvas-wrap.move-right, .off-canvas-wrap.move-left {
  min-height: 100%;
  -webkit-overflow-scrolling: touch;
}

/* line 366, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.inner-wrap {
  position: relative;
  width: 100%;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
}
/* line 374, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.inner-wrap:before {
  content: " ";
  display: table;
}
/* line 378, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.inner-wrap:after {
  content: " ";
  display: table;
  clear: both;
}

/* line 385, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.fixed {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
}

/* line 393, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-bar {
  -webkit-backface-visibility: hidden;
  background: #333333;
  color: #FFFFFF;
  height: 2.8125rem;
  line-height: 2.8125rem;
  position: relative;
}

/* line 402, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-bar-section {
  height: 2.8125rem;
  padding: 0 0.625rem;
  position: absolute;
  text-align: center;
  top: 0;
}
/* line 408, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-bar-section.right {
  text-align: right;
  left: 2.8125rem;
  right: 0;
}

/* line 415, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.left-small {
  height: 2.8125rem;
  position: absolute;
  top: 0;
  width: 2.8125rem;
  border-right: solid 1px #1a1a1a;
  left: 0;
}

/* line 424, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-bar .menu-icon {
  color: #FFFFFF;
  display: block;
  height: 2.8125rem;
  padding: 0;
  position: relative;
  text-indent: 2.1875rem;
  transform: translate3d(0, 0, 0);
  width: 2.8125rem;
}
/* line 433, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-bar .menu-icon span::after {
  content: "";
  display: block;
  height: 0;
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
  left: 0.90625rem;
  box-shadow: 0 0 0 1px #FFFFFF, 0 7px 0 1px #FFFFFF, 0 14px 0 1px #FFFFFF;
  width: 1rem;
}

/* line 446, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.page {
  height: 100%;
}

/* line 450, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.android_fix {
  overflow: auto !important;
}

/* line 454, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.safari_seven_fix {
  overflow: scroll !important;
}

/* line 458, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.tab-custom-fix {
  font-size: 0.8rem !important;
  padding: 0.5rem 0rem 0.5rem 0.2rem !important;
}

/* line 463, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.safari_login_fix {
  margin-top: 35px;
}

/* line 467, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.android_menu_fix {
  position: relative;
}

/* line 471, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.android_top_margin {
  margin-top: -35px;
}

/* line 475, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.orbit-slide-number {
  display: none;
}

/* line 479, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
button, .button {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 1rem 2rem 1.0625rem 2rem;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}

/* line 502, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
button.tiny, .button.tiny {
  padding: 0.625rem 1.25rem 0.6875rem 1.25rem;
  font-size: 0.6875rem;
}

/* line 507, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
button.round, .button.round {
  border-radius: 1000px;
}

/* line 511, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
button.secondary, .button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}

/* line 518, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button.secondary:hover, .button.secondary:focus {
  background-color: #b9b9b9;
  color: #333333;
}

/* line 524, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group {
  list-style: none;
  margin: 0;
  left: 0;
}
/* line 528, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group:before {
  content: " ";
  display: table;
}
/* line 532, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 537, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group.even-5 li {
  display: inline-block;
  margin: 0 -2px;
  width: 20%;
}
/* line 541, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group.even-5 li > button, .button-group.even-5 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 546, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group.even-5 li:first-child button, .button-group.even-5 li:first-child .button {
  border-left: 0;
}
/* line 550, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group.even-5 li button, .button-group.even-5 li .button {
  width: 100%;
}
/* line 554, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group > li {
  display: inline-block;
  margin: 0 -2px;
}
/* line 557, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group > li > button, .button-group > li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 562, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.button-group > li:first-child button, .button-group > li:first-child .button {
  border-left: 0;
}

/* line 569, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.column, .columns {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  width: 100%;
  float: left;
}

/* line 576, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
.column + .column:last-child, .columns + .column:last-child, .column + .columns:last-child, .columns + .columns:last-child {
  float: right;
}

@media only screen {
  /* line 581, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .column, .columns {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 588, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .hide-for-medium-up {
    display: inherit !important;
  }

  /* line 592, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .show-for-medium-up {
    display: none !important;
  }

  /* line 596, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .small-2 {
    width: 16.66667%;
  }

  /* line 599, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .small-3 {
    width: 25%;
  }

  /* line 602, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .small-4 {
    width: 33.33333%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 608, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .medium-1 {
    width: 8.33333%;
  }

  /* line 611, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .medium-2 {
    width: 16.66667%;
  }

  /* line 614, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header.scss */
  .medium-3 {
    width: 25%;
  }
}
