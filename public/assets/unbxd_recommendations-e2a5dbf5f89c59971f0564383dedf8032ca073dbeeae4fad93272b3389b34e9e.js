(function() {
  var data, i, isCurrentlyVisible, len, oldQueue, root;

  root = typeof exports !== "undefined" && exports !== null ? exports : this;

  root.loadRecommendation = function(data) {
    var currency_code;
    currency_code = data.symbol || 'INR';
    $.ajax({
      url: "/unbxd_recommendations/" + data.unbxdContainer,
      dataType: 'script',
      cache: true,
      data: {
        url_params: {
          currency_code: currency_code,
          uid: data.user,
          pid: $('#unbxd-data-div').data('pid'),
          category: $('#unbxd-data-div').data('category') || data.category,
          brand: $('#unbxd-data-div').data('brand'),
          product_count: data.productCount,
          box_name: data.boxName
        }
      },
      success: function(response) {
        response;
        $('#box-' + data.unbxdContainer).fadeIn('slow');
        gaImpressions();
        ga('send', 'event', 'UX', 'load', 'Unbxd-Widget-Loaded');
        if ($('#no-' + data.boxName.toLowerCase()).length > 0) {
          return setTimeout((function() {
            return $('#' + data.boxName.toLowerCase()).fadeOut('slow');
          }), 3000);
        }
      },
      error: function(xhr) {
        $("div[data-unbxd-container ='" + data.unbxdContainer + "']").replaceWith(("<div class='container recommended-design-box' id='" + data.boxName + "'><div class='title-block'><h1>") + data.boxName.toLowerCase().replace(/_/g, ' ') + ("</h1></div><div class='row'><div class='no-design-box' id='no-" + data.boxName + "'>Sorry Not Able to Find Recommendations</div></div></div>"));
        return setTimeout((function() {
          return $('#' + data.boxName).fadeOut('slow');
        }), 3000);
      }
    });
  };

  if (!root.bindView || root.bindView instanceof Array) {
    oldQueue = root.bindView || [];
  }

  isCurrentlyVisible = function($element) {
    var bottom_of_element, bottom_of_screen, top_of_element, top_of_screen;
    top_of_element = $element.offset().top;
    bottom_of_element = $element.offset().top + $element.outerHeight();
    bottom_of_screen = $(window).scrollTop() + window.innerHeight;
    top_of_screen = $(window).scrollTop();
    return (bottom_of_screen > top_of_element) && (top_of_screen < bottom_of_element);
  };

  root.bindView = {
    push: function(data) {
      var $element;
      $element = $('div[data-unbxd-container="' + data + '"]');
      if ($element.length > 0) {
        if (isCurrentlyVisible($element)) {
          return loadRecommendation($element.data());
        } else {
          return $(document).one('scroll', function() {
            return loadRecommendation($element.data());
          });
        }
      }
    }
  };

  for (i = 0, len = oldQueue.length; i < len; i++) {
    data = oldQueue[i];
    bindView.push(data);
  }

  oldQueue = [];

  $(function() {});

}).call(this);
