/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 5, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
html {
  height: 100%;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

/* line 11, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
* {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}
/* line 15, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
*:before, *:after {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}

/* line 22, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
body, html {
  height: 100%;
  width: 100%;
}

/* line 27, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
html {
  font-size: 100%;
}

/* line 31, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
body {
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;
}

/* line 41, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
body.branch-banner-is-active {
  margin-top: 0 !important;
}

/* line 43, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
::selection {
  background-color: #b11f2d;
}

/* line 46, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
a {
  line-height: inherit;
  text-decoration: none;
}
/* line 49, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
a img {
  border: none;
}

/* line 53, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.no-scroll-background {
  overflow: hidden !important;
}

/* The typing effect */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
#menu-side-nav {
  display: none;
}

/* line 68, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.pages_home .off-canvas-wrap #container {
  width: 100%;
  margin: 0;
  background: #f4f4f4;
}

/* line 77, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap .fixed {
  box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.2);
}
/* line 81, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .cart-icon {
  background-image: url(/assets/cart-1-490913f24db4c82ff17d18930c42c5f817f05ce380e5a31b1c556fb456568c08.png);
  background-repeat: no-repeat;
  display: inline-block;
  width: 1.25em;
  height: 1.25em;
  vertical-align: sub;
}
/* line 91, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .cart_count {
  margin: 0.5em 0em;
  color: #303030;
  letter-spacing: 1px;
  font-size: 0.8125rem;
  vertical-align: super;
  color: #670b19;
  line-height: 2.2rem;
}
/* line 100, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .user-wishlist {
  font-size: 1.6rem;
  margin-right: -0.3rem;
}
/* line 103, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .user-wishlist .fi-heart {
  vertical-align: middle;
}
/* line 108, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .logo {
  background-image: url(/assets/logo-white1-f9d3e9294f56d9e17d252825ceef038e47e0a23536b6f46327e17ddaf427360f.png);
  background-repeat: no-repeat;
  display: inline-block;
  width: 5.5em;
  height: 2em;
  vertical-align: middle;
  background-size: 5.5em 2em;
  margin-left: 0.9375rem;
}
/* line 119, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .download-app {
  color: #303030;
  font-size: 0.675rem;
  margin-left: 3px;
}
/* line 124, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap nav.tab-bar .app-download {
  background: url(/assets/download-app1-8c2e391ee3464f35317703277c130dbaee4031d4c8e79c7d534effa682576770.png);
  background-size: 12px;
  width: 14px;
  height: 14px;
  background-repeat: no-repeat;
  margin: 10px 0px 0px 0px;
}
/* line 135, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll {
  width: 92%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  border: 1px solid #d8d8d8;
}
/* line 144, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll nav {
  margin: -8px 0px -6px 0px;
}
/* line 146, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar header.scroll nav i.fi-burst-new {
  font-size: 22px;
  vertical-align: middle;
  margin: -4px 0px;
  color: #de6b23;
  position: absolute;
}
/* line 155, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box {
  background: white;
  display: none;
  padding: 0px 15px 2px 15px;
  border-radius: 0px 0px 2px 2px;
  text-align: left;
  z-index: 2 !important;
  position: absolute;
  width: inherit;
}
/* line 164, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box .trending-results-text {
  border-bottom: 1px solid #d8d8d8;
  color: #272626;
  font-weight: 600;
}
/* line 168, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box .trending-results-text .trending-icon {
  color: #292929;
}
/* line 172, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box ul#trending-results {
  margin: 0px;
}
/* line 174, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box ul#trending-results li {
  list-style-type: none;
  font-size: 16px;
  text-transform: capitalize;
  padding: 10px 0px;
}
/* line 179, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .trending-results-box ul#trending-results li a {
  color: #271f35;
}
/* line 185, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin {
  width: 96%;
  margin: 0 auto;
  padding: 6px 0px;
  display: none;
}
/* line 190, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text'] {
  padding-left: 18px;
  margin-bottom: 0px;
  border-bottom: none;
  background-color: white;
}
/* line 195, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:focus {
  box-shadow: none;
  border-bottom: none;
}
/* line 199, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 203, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 207, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:-ms-input-placeholder {
  /* IE 10+ */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 211, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin input[type='text']:-moz-placeholder {
  /* Firefox 18- */
  color: rgba(39, 31, 53, 0.67);
  font-size: 15px;
}
/* line 216, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .show-for-small-up.search_margin button.submit_btn {
  padding: 0px;
  margin-bottom: 0px;
}
/* line 224, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .scroll-button {
  position: absolute;
  font-size: 20px;
  line-height: 31px;
  background-color: #303030;
  color: #ffffff;
  text-align: center;
  height: 33px;
}
/* line 234, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-right {
  display: none;
  top: 0px;
  z-index: 1;
  width: 8%;
}
/* line 239, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-right:after {
  content: '';
  width: 10px;
  height: 10px;
  position: absolute;
  transform: rotate(-45deg);
  border-left: 1px solid white;
  border-top: 1px solid white;
  top: 10px;
  left: 10px;
}
/* line 252, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-left {
  top: 0px;
  right: 1px;
  width: 8%;
}
/* line 256, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar #scroll-btn-left:after {
  content: '';
  width: 10px;
  height: 10px;
  position: absolute;
  transform: rotate(-45deg);
  border-right: 1px solid white;
  border-bottom: 1px solid white;
  top: 10px;
  right: 10px;
}
/* line 269, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .tab-fix {
  font-size: 0.8rem !important;
  padding: 0.5rem 0.8rem !important;
  width: fill-content;
  border-radius: 0px;
  color: #303030;
  background-color: white;
  margin-left: -5px;
  margin-bottom: 0px !important;
  letter-spacing: 1px;
  border-left: 1px solid #d8d8d8;
}
/* line 282, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .tab-fix:hover, .off-canvas-wrap .inner-wrap div.tab-bar .tab-fix:focus {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
}
/* line 287, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap div.tab-bar .medium-tab-button {
  width: 100%;
}
/* line 293, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .inner-wrap .search_margin input[type='text'] {
  color: #383737;
}
/* line 299, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .submit_btn {
  background-color: #670b19;
  border-color: transparent;
}
/* line 302, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .submit_btn i.fi-magnifying-glass {
  color: #ffffff;
  font-size: 18px;
}
/* line 307, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap i.fi-download {
  color: #303030;
  font-size: 18px;
}
/* line 311, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .main-section {
  overflow: hidden;
  position: relative;
  -webkit-overflow-scrolling: touch;
}
/* line 316, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .no-search-bar {
  margin-top: 4rem;
}
/* line 319, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .with-menu {
  margin-top: 7rem;
}
/* line 322, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap #main-section {
  width: 100%;
  height: 99%;
  overflow: auto;
}
/* line 328, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap ul.off-canvas-list li label {
  padding: 0.4em 0.6em;
}
/* line 333, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .left-submenu .back > a {
  padding: 0.1em 0.6em;
}
/* line 338, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap #container {
  overflow-y: hidden;
  margin: 1%;
  overflow-x: hidden;
}
/* line 344, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap .left-off-canvas-menu, .off-canvas-wrap .right-off-canvas-menu {
  min-height: 200vh;
}

@media only screen and (min-width: 400px) {
  /* line 350, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .static-nav-bar {
    width: 80% !important;
  }
}
@media only screen and (min-width: 300px) and (max-width: 400px) {
  /* line 357, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .static-nav-bar {
    width: 78% !important;
  }
}
@media only screen and (min-width: 200px) and (max-width: 300px) {
  /* line 364, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .static-nav-bar {
    width: 75% !important;
  }
}
/* line 369, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
section.main-section {
  display: none;
}

@media only screen and (min-width: 320px) and (max-width: 568px) {
  /* line 376, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .download-app {
    font-size: 0.75rem !important;
  }
}
@media only screen and (min-width: 64.063em) {
  /* line 381, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .show-for-small-up.search_margin {
    width: 40% !important;
    margin-top: -83px !important;
  }

  /* line 385, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  div.search-tab {
    height: 2.3125rem !important;
  }
  /* line 387, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  div.search-tab .trending-results-box {
    position: fixed;
    width: 40%;
  }
}
/* line 394, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.left {
  float: left !important;
}

/* line 398, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.right {
  float: right !important;
}

/* line 402, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap {
  -webkit-backface-visibility: hidden;
  position: relative;
  width: 100%;
  overflow: hidden;
}
/* line 407, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.off-canvas-wrap.move-right, .off-canvas-wrap.move-left, .off-canvas-wrap.offcanvas-overlap {
  min-height: 100%;
  -webkit-overflow-scrolling: touch;
}

/* line 413, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.inner-wrap {
  position: relative;
  width: 100%;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
}
/* line 421, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.inner-wrap:before {
  content: " ";
  display: table;
}
/* line 425, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.inner-wrap:after {
  content: " ";
  display: table;
  clear: both;
}

/* line 432, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.border-top {
  border-top: 0.3125rem solid #670b19;
  background: white;
}

/* line 436, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.fixed-header {
  display: none;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.35);
  -webkit-transform: translateY(-50px);
  transform: translateY(-50px);
  will-change: transform;
  -webkit-transition: -webkit-transform .25s ease-out;
  transition: -webkit-transform .25s ease-out;
  transition: transform .25s ease-out;
  transition: transform .25s ease-out,-webkit-transform .25s ease-out;
}

/* line 452, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.fixed {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
}

/* line 460, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-bar {
  -webkit-backface-visibility: hidden;
  background: white !important;
  color: #303030;
  height: 2rem;
  line-height: 2rem;
  position: relative;
  margin: 0.625rem 0px;
}

/* line 469, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.search-tab {
  line-height: 2.8125rem !important;
}

/* line 472, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-bar-section {
  height: 2rem;
  padding: 0 0.375rem !important;
  position: absolute;
  text-align: center;
  top: 0;
}
/* line 478, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-bar-section.right {
  text-align: right;
  left: 2rem;
  right: 0;
}

/* line 485, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.left-small {
  height: 2rem;
  position: absolute;
  top: 0;
  width: 2rem;
  left: 6px !important;
  background: #670b19;
}

/* line 493, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.noleftpadding {
  padding-left: 0px !important;
}

/* line 496, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.nopadding {
  padding: 0px !important;
}

/* line 499, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-bar .menu-icon {
  color: #ffffff;
  display: block;
  height: 2rem;
  padding: 0;
  position: relative;
  text-indent: 2.1875rem;
  transform: translate3d(0, 0, 0);
  width: 2rem;
}
/* line 508, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-bar .menu-icon span::after {
  content: "";
  display: block;
  height: 0;
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
  left: 0.5rem;
  box-shadow: 0 0 0 1px #FFFFFF, 0 7px 0 1px #FFFFFF, 0 14px 0 1px #FFFFFF;
  width: 1rem;
}

/* line 521, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.static-tab-bar .search-tab {
  width: 100%;
  position: absolute;
  margin: 0;
}
/* line 526, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.static-tab-bar .show-for-small-up {
  margin-top: -0.15em !important;
  margin-bottom: 0 !important;
  margin-left: 0.8rem !important;
}

/* line 533, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.page {
  height: 100%;
}

/* line 537, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.android_fix {
  overflow: auto !important;
}

/* line 541, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.safari_seven_fix {
  overflow: scroll !important;
}

/* line 545, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.tab-custom-fix {
  font-size: 0.8rem !important;
  padding: 0.5rem 0rem 0.5rem 0.2rem !important;
}

/* line 550, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.safari_login_fix {
  margin-top: 35px;
}

/* line 554, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.android_menu_fix {
  position: relative;
}

/* line 558, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.android_top_margin {
  margin-top: -35px;
}

/* line 562, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.orbit-slide-number {
  display: none;
}

/* line 566, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
button, .button {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 0.5rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 1rem 2rem 1.0625rem 2rem;
  font-size: 1rem;
  background-color: #670b19;
  border-color: #670b19;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}

/* line 588, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
button.tiny, .button.tiny {
  padding: 0.625rem 1.25rem 0.6875rem 1.25rem;
  font-size: 0.75rem;
}

/* line 593, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
button.round, .button.round {
  border-radius: 1000px;
}

/* line 597, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
button.secondary, .button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}

/* line 604, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button.secondary:hover, .button.secondary:focus {
  background-color: #b9b9b9;
  color: #333333;
}

/* line 610, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group {
  list-style: none;
  margin: 0;
  left: 0;
  border: 1px solid #d8d8d8;
}
/* line 615, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group:before {
  content: " ";
  display: table;
}
/* line 619, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 624, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group.even-5 li {
  display: inline-block;
  margin: 0 -2px;
  width: 20%;
}
/* line 628, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group.even-5 li > button, .button-group.even-5 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 633, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group.even-5 li:first-child button, .button-group.even-5 li:first-child .button {
  border-left: 0;
}
/* line 637, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group.even-5 li button, .button-group.even-5 li .button {
  width: 100%;
}
/* line 641, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group > li {
  display: inline-block;
  margin: 0 -2px;
}
/* line 644, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group > li > button, .button-group > li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 649, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.button-group > li:first-child button, .button-group > li:first-child .button {
  border-left: 0;
}

/* line 656, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.column, .columns {
  width: 100%;
  float: left;
}

/* line 661, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.column + .column:last-child, .columns + .column:last-child, .column + .columns:last-child, .columns + .columns:last-child {
  float: right;
}

/* line 665, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.back-button {
  height: 2rem;
  position: absolute;
  top: 0;
  width: 2rem;
  left: 20px !important;
  color: #670b19 !important;
  z-index: 1;
}
/* line 673, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.back-button .left-arrow {
  border: solid #670b19;
  border-width: 0 3px 3px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(135deg);
  -webkit-transform: rotate(135deg);
}

@media only screen {
  /* line 685, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .column, .columns {
    position: relative;
    float: left;
  }

  /* line 690, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .hide-for-medium-up {
    display: inherit !important;
  }

  /* line 694, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .show-for-medium-up {
    display: none !important;
  }

  /* line 698, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .small-2 {
    width: 16.66667%;
  }

  /* line 701, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .small-3 {
    width: 25%;
  }

  /* line 704, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .small-4 {
    width: 33.33333%;
  }

  /* line 707, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .small-5 {
    width: 41.66667%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 713, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .medium-1 {
    width: 8.33333%;
  }

  /* line 716, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .medium-2 {
    width: 16.66667%;
  }

  /* line 719, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
  .medium-3 {
    width: 25%;
  }
}
/* line 724, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/inline_header_red.scss */
.cc-window {
  box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.64);
}
