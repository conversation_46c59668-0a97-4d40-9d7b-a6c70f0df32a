(function() {
  $(function() {
    var hideKeyboard, input, myAwesomeComplete, qas, questions;
    questions = $('#questions_hash');
    if (questions.length === 1) {
      input = document.getElementById('search_list');
      qas = questions.data('attr-id');
      myAwesomeComplete = new Awesomplete(input, {
        list: Object.keys(qas)
      });
      input.addEventListener('awesomplete-select', function(e) {
        var answer_result, question_result;
        question_result = $('#search_ques');
        answer_result = $('#search_answer');
        question_result.empty();
        answer_result.empty();
        answer_result.removeClass('search_res navigation');
        hideKeyboard($('#search_list'));
        if (!answer_result.is(':visible')) {
          answer_result.show();
        }
        if (qas[e.text['label']]) {
          question_result.append('*  ' + e.text['label']);
          answer_result.addClass('search_res');
          answer_result.append(qas[e.text['label']]);
          if (answer_result.innerHeight() > 100) {
            return answer_result.addClass('navigation');
          }
        }
      });
    }
    $('#search_ques').click(function() {
      var answer;
      answer = $('#search_answer');
      if (answer.is(':visible')) {
        return answer.hide();
      } else {
        return answer.show();
      }
    });
    hideKeyboard = function(element) {
      element.attr('readonly', 'readonly');
      element.attr('disabled', 'true');
      setTimeout((function() {
        element.blur();
        element.removeAttr('readonly');
        element.removeAttr('disabled');
        element.val('');
      }), 100);
    };
    $('.quick_link').click(function() {
      var id;
      id = $(this).data('attr-id');
      questions = $("#priority_questions-" + id);
      if (questions.is(':visible')) {
        questions.hide();
        return $(this).removeClass('faq_link_selected');
      } else {
        $('.view_more_ques').hide();
        $('.view_more_hidden').show();
        $('.view_more').show();
        $('.faq_answer').removeClass('answer_selected');
        $('.quick_link').removeClass('faq_link_selected');
        $(this).addClass('faq_link_selected');
        $('.faq_answer').hide();
        $('.faq_question').removeClass('question_selected').addClass('question_li_content');
        $('.faqs_all').hide();
        return questions.show();
      }
    });
    $('.faq_question').click(function() {
      var answer, id;
      id = $(this).data('attr-id');
      answer = $('#faq_answer-' + id);
      if (answer.is(':visible')) {
        answer.hide();
        $('.faq_question').removeClass('question_selected').addClass('question_li_content');
        $(this).addClass('question_li_content').removeClass('question_selected');
        return answer.removeClass('answer_selected');
      } else {
        $('.faq_answer').hide();
        $('.faq_question').removeClass('question_selected').addClass('question_li_content');
        $(this).removeClass('question_li_content').addClass('question_selected');
        answer.addClass('answer_selected');
        return answer.show();
      }
    });
    $('.view_more').click(function() {
      var more_ques;
      more_ques = $('.view_more_ques');
      more_ques.show();
      $(this).addClass('.view_more_hidden');
      return $(this).hide();
    });
    return $('#order_track_form').submit(function() {
      var order_number;
      order_number = $('#order_number_field').val();
      return window.open("/orders/" + order_number, "_blank");
    });
  });

  $(function() {
    $('.order_menu').click(function() {
      var selected_elem;
      selected_elem = $(this);
      $('.order_menu').not(selected_elem).hide(200);
      return $('.category_query').show(200);
    });
    $('.category-links').click(function() {
      var selected_elem, selected_one;
      selected_elem = $(this);
      $('.category-links, #faq_direct, #track_order').hide();
      $('.category-links').removeClass('selected');
      selected_elem.addClass('selected');
      selected_one = $('.selected').data('attrId');
      $('#' + selected_one).prev('#category_title').show(200);
      if (selected_elem.text().trim() === "Order Queries") {
        $('.category_query').hide();
      } else {
        $('.category_query').show(200);
      }
      $('#' + selected_one).show(200);
      $('#main').css({
        'padding-left': '0.85rem',
        'width': '77%'
      });
      return $('#nav_arrow').show();
    });
    $('.category_query').click(function() {
      var selected_elem;
      selected_elem = $(this);
      selected_elem.children('.answers_category_query').fadeToggle(200);
      selected_elem.siblings('.category_query_level_two').fadeToggle(200);
      $('.answers_category_query, .answer_category_query').not(selected_elem.children()).hide(200);
      $('.category_query_level_two, .answer_category_query').not(selected_elem.siblings()).hide(200);
      return $('.category_query').not(selected_elem).hide();
    });
    $('.category_query_level_two').click(function() {
      var selected_elem;
      selected_elem = $(this);
      selected_elem.next('.answer_category_query').fadeToggle(200);
      return $('.answer_category_query').not(selected_elem.next('.answer_category_query')).hide(200);
    });
    $('.closeEbtn').click(function() {
      return $('#emailUs').foundation('reveal', 'close');
    });
    $('#nav_arrow').click(function() {
      if ($('.category_query:visible').length === 1) {
        $('.category_query').show();
        return $('.category_query_level_two, .answer_category_query, .answers_category_query').hide();
      } else if ($('.order_menu:visible').length === 1) {
        $('.order_menu').show(200);
        return $('.category_query').hide();
      } else if ($('.category_query:visible').length > 1 && $('.order_menu:visible').length === 0 || $('.category_query').is(':hidden') || $('.order_menu:visible').length > 1 || $('.login_req').is(':visible')) {
        $('#main').css({
          'padding-left': '3rem',
          'width': '87%'
        });
        $('.helpcenter_qna, #category_title, #nav_arrow').hide();
        return $('.category-links, #faq_direct, #track_order').show();
      }
    });
    $(document).on('click', '#email_us', function() {
      var customAttribute, email_data, email_us, i, index, issue_text, len, taggingValues, value;
      email_us = $(this);
      issue_text = "";
      taggingValues = ['options', 'sub_option_1', 'sub_option_2'];
      for (index = i = 0, len = taggingValues.length; i < len; index = ++i) {
        value = taggingValues[index];
        customAttribute = value.replace(/_/g, '-');
        email_data = email_us.data(customAttribute);
        if (email_data !== void 0) {
          $('#' + value).val(email_data);
          issue_text += $('#' + value).val() + ' > ';
        }
      }
      $('#issue_text_help_center').val(issue_text.slice(0, -2));
      $('#order_number_text').val($('.order_menu:visible').find('#product_no').text().trim());
      $('#support_text_id').val(email_us.data('support-text-id'));
      if ($('#account_presence').data('account-present')) {
        $('#email_id_help_center').val($('#account_presence').data('account-email-id')).attr('readonly', true);
      }
      if ($('.order_menu').is(':visible')) {
        return $('#order_number_label').show();
      } else {
        return $('#order_number_label').hide();
      }
    });
    $('.track_btn').click(function() {
      var orderNo;
      orderNo = $(this).parent().siblings().children('#product_no').text().trim();
      return window.open("/orders/" + orderNo, "_blank");
    });
    $('.close_modal_btn').click(function() {
      $('html, body').animate({
        scrollTop: 180
      }, 10);
      return $('#list_of_order_modal').foundation('reveal', 'close');
    });
    return $('#order_confirmation_mail').click(function() {
      var order_number;
      order_number = $('#order_number_text').val();
      return $.ajax({
        type: 'GET',
        data: {
          number: order_number
        },
        url: '/orders/send_order_ack_email',
        beforeSend: function() {
          return $('#loadingImage').show();
        },
        complete: function() {
          return $('#loadingImage').hide();
        },
        success: function(data, success, jqhxr) {
          if (data.error === true) {
            $('#mail_message').append('Order not found');
          } else {
            if (data.mail_sent !== true) {
              $('#mail_message').append('Mail Already Sent');
            } else {
              $('#mail_message').append('Mail Sent Successfully');
            }
          }
          return setTimeout((function() {
            $('#mail_message').empty();
          }), 1000);
        },
        error: function() {
          return alert('Something went wrong');
        }
      });
    });
  });

}).call(this);
