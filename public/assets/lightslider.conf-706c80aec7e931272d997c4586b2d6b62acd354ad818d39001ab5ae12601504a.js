$(document).ready(function() {
  $('#design_images').lightSlider({
      loop: true,
      gallery:true,
      item:1,
      speed:500,
      slideMargin:5,
      useCSS: true,
      freeMove:true,
      auto:false,
      thumbItem:6,
      onSliderLoad: function(el) {
          $('#design_images').removeClass('cS-hidden');
      } 
  });

// #####################FULL SCREEN BACK BUTTON HACK#####################
  if (window.history && window.history.pushState) {

    $(window).on('popstate', function() {
      var hashLocation = location.hash;
      var hashSplit = hashLocation.split("#!/");
      var hashName = hashSplit[1];

      if (hashName !== '') {
        var hash = window.location.hash;
        if (hash === '' && (typeof $('.lg-close').attr('class') !== 'undefined')) {
          $('.lg-close').trigger("click");
        }
      }
    });

      window.history.pushState('forward', null, './#forward');
  }

  if(typeof $('.lg-close').attr('class') === 'undefined'){
    window.history.back();
  }
  $(document).on("click", ".design_image", function(){
    window.location.href="#forward";
  });

  $(document).on("click", ".lg-close", function(){
    $('#design_images').toggleClass('back_to_previous');
  });

  $('.lSGallery').css('margin-left', '17%');

});
