/* line 4, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button {
  width: 100%;
}
/* line 7, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook {
  background: #3b5998;
  text-transform: capitalize;
}
/* line 11, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook span {
  background: #2d4373;
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1c4";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter {
  background: #55acee;
  text-transform: capitalize;
}
/* line 29, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter span {
  background: #2795e9;
}
/* line 32, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1e4";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}
/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google {
  background: #d50f25;
  text-transform: capitalize;
}
/* line 48, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google span {
  background: #a50c1d;
}
/* line 51, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1ca";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}

/* line 63, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.bordered_block label {
  color: white;
}
/* line 66, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.bordered_block .login_btn {
  background-color: #0e9a7d;
}

/* line 70, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
hr {
  border: #bdb3b3 solid;
  border-width: 0.1em 0 0;
  margin-top: 0.4em;
}

/* line 77, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
#new_account label {
  color: #383737;
}
