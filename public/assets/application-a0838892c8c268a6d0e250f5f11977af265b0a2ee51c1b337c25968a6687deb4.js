/*!
 * jQuery JavaScript Library v1.11.2
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright 2005, 2014 jQuery Foundation, Inc. and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2014-12-17T15:27Z
 */
function getUrlParams(e){var t,n,i,r;for(t={},n=e.split("&"),i=0;i<n.length;)r=n[i].split("="),t[r[0]]=r[1],i++;return t}function applyColourFromLink(e){var t;if($("div.pages_home.page").length>0){var n=e.find(".active_clock").data("link");if(void 0!=n){var i=getUrlParams(n);e.find(".countdown").css("color","#"+i.timer_txt_color),t="#"+i.timer_bg_color}}return t}function gaOrder(e){$.each(e.data,function(e,t){ga("ec:addProduct",t)}),ga("ec:setAction",e.action_name,e.action_data)}function inspectletJs(){var e;e=function(){var e,t;e=document.createElement("script"),e.type="text/javascript",e.async=!0,e.id="inspsync",e.src=("https:"===document.location.protocol?"https":"http")+"://cdn.inspectlet.com/inspectlet.js",t=document.getElementsByTagName("script")[0],t.parentNode.insertBefore(e,t)},window.attachEvent?window.attachEvent("onload",e):window.addEventListener("load",e,!1)}function pushWidInspectlet(){return window.__insp=window.__insp||[],__insp.push(["wid",1062788080])}function pushTagSessionInspectlet(){return __insp.push(["tagSession","orders_new"])}function paRequest(e){var t,n;window._pa=window._pa||{},_pa.productId=e,t=document.createElement("script"),t.type="text/javascript",t.async=!0,t.src=("https:"===document.location.protocol?"https:":"http:")+"//tag.perfectaudience.com/serve/5426ae17eff9a47c01000038.js",n=document.getElementsByTagName("script")[0],n.parentNode.insertBefore(t,n)}function plusOne(){window.___gcfg={lang:"en-US",parsetags:"onload"},function(){var e,t;e=document.createElement("script"),e.type="text/javascript",e.async=!0,e.src="https://apis.google.com/js/plusone.js",t=document.getElementsByTagName("script")[0],t.parentNode.insertBefore(e,t)}()}function gaPageview(e){e&&(new_path=window.location.pathname+window.location.search,search_term=getSearchTerm(),search_term&&(new_path+="&search_term="+search_term),ga("set","page",new_path)),ga("send","pageview")}function getSearchTerm(){if(isSearchPage()){var e=/.*?q=(.*?)(?:&|$)/,t=e.exec(window.location.search);return t&&t[1]}}function isSearchPage(){return"/search"==window.location.pathname}function getCookieValue(e){for(var t=e+"=",n=document.cookie.split(";"),i=0;i<n.length;i++){for(var r=n[i];" "==r.charAt(0);)r=r.substring(1);if(0==r.indexOf(t))return r.substring(t.length,r.length)}return""}function preloadImage(e,t){e.setAttribute(t,e.dataset.original),"srcset"==t&&e.classList.remove("js-lazy"),e.onload=function(){e.classList.remove("js-lazy")}}function loadInViewImage(){inView.offset({top:-50,right:-10,bottom:0,left:-10}),inView(".js-lazy").on("enter",function(e){preloadImage(e)})}function onIntersection(e){e.forEach(function(e){e.intersectionRatio>0&&(observer.unobserve(e.target),e.target.srcset?preloadImage(e.target,"srcset"):preloadImage(e.target,"src"))})}!function(e,t){"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){function n(e){var t=e.length,n=rt.type(e);return"function"===n||rt.isWindow(e)?!1:1===e.nodeType&&t?!0:"array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e}function i(e,t,n){if(rt.isFunction(t))return rt.grep(e,function(e,i){return!!t.call(e,i,e)!==n});if(t.nodeType)return rt.grep(e,function(e){return e===t!==n});if("string"==typeof t){if(pt.test(t))return rt.filter(t,e,n);t=rt.filter(t,e)}return rt.grep(e,function(e){return rt.inArray(e,t)>=0!==n})}function r(e,t){do e=e[t];while(e&&1!==e.nodeType);return e}function o(e){var t=_t[e]={};return rt.each(e.match(bt)||[],function(e,n){t[n]=!0}),t}function a(){ht.addEventListener?(ht.removeEventListener("DOMContentLoaded",s,!1),e.removeEventListener("load",s,!1)):(ht.detachEvent("onreadystatechange",s),e.detachEvent("onload",s))}function s(){(ht.addEventListener||"load"===event.type||"complete"===ht.readyState)&&(a(),rt.ready())}function l(e,t,n){if(void 0===n&&1===e.nodeType){var i="data-"+t.replace(Tt,"-$1").toLowerCase();if(n=e.getAttribute(i),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:kt.test(n)?rt.parseJSON(n):n}catch(r){}rt.data(e,t,n)}else n=void 0}return n}function c(e){var t;for(t in e)if(("data"!==t||!rt.isEmptyObject(e[t]))&&"toJSON"!==t)return!1;return!0}function u(e,t,n,i){if(rt.acceptData(e)){var r,o,a=rt.expando,s=e.nodeType,l=s?rt.cache:e,c=s?e[a]:e[a]&&a;if(c&&l[c]&&(i||l[c].data)||void 0!==n||"string"!=typeof t)return c||(c=s?e[a]=X.pop()||rt.guid++:a),l[c]||(l[c]=s?{}:{toJSON:rt.noop}),("object"==typeof t||"function"==typeof t)&&(i?l[c]=rt.extend(l[c],t):l[c].data=rt.extend(l[c].data,t)),o=l[c],i||(o.data||(o.data={}),o=o.data),void 0!==n&&(o[rt.camelCase(t)]=n),"string"==typeof t?(r=o[t],null==r&&(r=o[rt.camelCase(t)])):r=o,r}}function d(e,t,n){if(rt.acceptData(e)){var i,r,o=e.nodeType,a=o?rt.cache:e,s=o?e[rt.expando]:rt.expando;if(a[s]){if(t&&(i=n?a[s]:a[s].data)){rt.isArray(t)?t=t.concat(rt.map(t,rt.camelCase)):t in i?t=[t]:(t=rt.camelCase(t),t=t in i?[t]:t.split(" ")),r=t.length;for(;r--;)delete i[t[r]];if(n?!c(i):!rt.isEmptyObject(i))return}(n||(delete a[s].data,c(a[s])))&&(o?rt.cleanData([e],!0):nt.deleteExpando||a!=a.window?delete a[s]:a[s]=null)}}}function p(){return!0}function f(){return!1}function h(){try{return ht.activeElement}catch(e){}}function m(e){var t=Rt.split("|"),n=e.createDocumentFragment();if(n.createElement)for(;t.length;)n.createElement(t.pop());return n}function g(e,t){var n,i,r=0,o=typeof e.getElementsByTagName!==Ct?e.getElementsByTagName(t||"*"):typeof e.querySelectorAll!==Ct?e.querySelectorAll(t||"*"):void 0;if(!o)for(o=[],n=e.childNodes||e;null!=(i=n[r]);r++)!t||rt.nodeName(i,t)?o.push(i):rt.merge(o,g(i,t));return void 0===t||t&&rt.nodeName(e,t)?rt.merge([e],o):o}function v(e){Lt.test(e.type)&&(e.defaultChecked=e.checked)}function y(e,t){return rt.nodeName(e,"table")&&rt.nodeName(11!==t.nodeType?t:t.firstChild,"tr")?e.getElementsByTagName("tbody")[0]||e.appendChild(e.ownerDocument.createElement("tbody")):e}function b(e){return e.type=(null!==rt.find.attr(e,"type"))+"/"+e.type,e}function _(e){var t=Zt.exec(e.type);return t?e.type=t[1]:e.removeAttribute("type"),e}function w(e,t){for(var n,i=0;null!=(n=e[i]);i++)rt._data(n,"globalEval",!t||rt._data(t[i],"globalEval"))}function x(e,t){if(1===t.nodeType&&rt.hasData(e)){var n,i,r,o=rt._data(e),a=rt._data(t,o),s=o.events;if(s){delete a.handle,a.events={};for(n in s)for(i=0,r=s[n].length;r>i;i++)rt.event.add(t,n,s[n][i])}a.data&&(a.data=rt.extend({},a.data))}}function C(e,t){var n,i,r;if(1===t.nodeType){if(n=t.nodeName.toLowerCase(),!nt.noCloneEvent&&t[rt.expando]){r=rt._data(t);for(i in r.events)rt.removeEvent(t,i,r.handle);t.removeAttribute(rt.expando)}"script"===n&&t.text!==e.text?(b(t).text=e.text,_(t)):"object"===n?(t.parentNode&&(t.outerHTML=e.outerHTML),nt.html5Clone&&e.innerHTML&&!rt.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):"input"===n&&Lt.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):"option"===n?t.defaultSelected=t.selected=e.defaultSelected:("input"===n||"textarea"===n)&&(t.defaultValue=e.defaultValue)}}function k(t,n){var i,r=rt(n.createElement(t)).appendTo(n.body),o=e.getDefaultComputedStyle&&(i=e.getDefaultComputedStyle(r[0]))?i.display:rt.css(r[0],"display");return r.detach(),o}function T(e){var t=ht,n=Qt[e];return n||(n=k(e,t),"none"!==n&&n||(Jt=(Jt||rt("<iframe frameborder='0' width='0' height='0'/>")).appendTo(t.documentElement),t=(Jt[0].contentWindow||Jt[0].contentDocument).document,t.write(),t.close(),n=k(e,t),Jt.detach()),Qt[e]=n),n}function S(e,t){return{get:function(){var n=e();if(null!=n)return n?void delete this.get:(this.get=t).apply(this,arguments)}}}function $(e,t){if(t in e)return t;for(var n=t.charAt(0).toUpperCase()+t.slice(1),i=t,r=fn.length;r--;)if(t=fn[r]+n,t in e)return t;return i}function E(e,t){for(var n,i,r,o=[],a=0,s=e.length;s>a;a++)i=e[a],i.style&&(o[a]=rt._data(i,"olddisplay"),n=i.style.display,t?(o[a]||"none"!==n||(i.style.display=""),""===i.style.display&&Et(i)&&(o[a]=rt._data(i,"olddisplay",T(i.nodeName)))):(r=Et(i),(n&&"none"!==n||!r)&&rt._data(i,"olddisplay",r?n:rt.css(i,"display"))));for(a=0;s>a;a++)i=e[a],i.style&&(t&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=t?o[a]||"":"none"));return e}function D(e,t,n){var i=cn.exec(t);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):t}function L(e,t,n,i,r){for(var o=n===(i?"border":"content")?4:"width"===t?1:0,a=0;4>o;o+=2)"margin"===n&&(a+=rt.css(e,n+$t[o],!0,r)),i?("content"===n&&(a-=rt.css(e,"padding"+$t[o],!0,r)),"margin"!==n&&(a-=rt.css(e,"border"+$t[o]+"Width",!0,r))):(a+=rt.css(e,"padding"+$t[o],!0,r),"padding"!==n&&(a+=rt.css(e,"border"+$t[o]+"Width",!0,r)));return a}function I(e,t,n){var i=!0,r="width"===t?e.offsetWidth:e.offsetHeight,o=en(e),a=nt.boxSizing&&"border-box"===rt.css(e,"boxSizing",!1,o);if(0>=r||null==r){if(r=tn(e,t,o),(0>r||null==r)&&(r=e.style[t]),rn.test(r))return r;i=a&&(nt.boxSizingReliable()||r===e.style[t]),r=parseFloat(r)||0}return r+L(e,t,n||(a?"border":"content"),i,o)+"px"}function A(e,t,n,i,r){return new A.prototype.init(e,t,n,i,r)}function F(){return setTimeout(function(){hn=void 0}),hn=rt.now()}function N(e,t){var n,i={height:e},r=0;for(t=t?1:0;4>r;r+=2-t)n=$t[r],i["margin"+n]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function P(e,t,n){for(var i,r=(_n[t]||[]).concat(_n["*"]),o=0,a=r.length;a>o;o++)if(i=r[o].call(n,t,e))return i}function R(e,t,n){var i,r,o,a,s,l,c,u,d=this,p={},f=e.style,h=e.nodeType&&Et(e),m=rt._data(e,"fxshow");n.queue||(s=rt._queueHooks(e,"fx"),null==s.unqueued&&(s.unqueued=0,l=s.empty.fire,s.empty.fire=function(){s.unqueued||l()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,rt.queue(e,"fx").length||s.empty.fire()})})),1===e.nodeType&&("height"in t||"width"in t)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],c=rt.css(e,"display"),u="none"===c?rt._data(e,"olddisplay")||T(e.nodeName):c,"inline"===u&&"none"===rt.css(e,"float")&&(nt.inlineBlockNeedsLayout&&"inline"!==T(e.nodeName)?f.zoom=1:f.display="inline-block")),n.overflow&&(f.overflow="hidden",nt.shrinkWrapBlocks()||d.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}));for(i in t)if(r=t[i],gn.exec(r)){if(delete t[i],o=o||"toggle"===r,r===(h?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;h=!0}p[i]=m&&m[i]||rt.style(e,i)}else c=void 0;if(rt.isEmptyObject(p))"inline"===("none"===c?T(e.nodeName):c)&&(f.display=c);else{m?"hidden"in m&&(h=m.hidden):m=rt._data(e,"fxshow",{}),o&&(m.hidden=!h),h?rt(e).show():d.done(function(){rt(e).hide()}),d.done(function(){var t;rt._removeData(e,"fxshow");for(t in p)rt.style(e,t,p[t])});for(i in p)a=P(h?m[i]:0,i,d),i in m||(m[i]=a.start,h&&(a.end=a.start,a.start="width"===i||"height"===i?1:0))}}function M(e,t){var n,i,r,o,a;for(n in e)if(i=rt.camelCase(n),r=t[i],o=e[n],rt.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),a=rt.cssHooks[i],a&&"expand"in a){o=a.expand(o),delete e[i];for(n in o)n in e||(e[n]=o[n],t[n]=r)}else t[i]=r}function q(e,t,n){var i,r,o=0,a=bn.length,s=rt.Deferred().always(function(){delete l.elem}),l=function(){if(r)return!1;for(var t=hn||F(),n=Math.max(0,c.startTime+c.duration-t),i=n/c.duration||0,o=1-i,a=0,l=c.tweens.length;l>a;a++)c.tweens[a].run(o);return s.notifyWith(e,[c,o,n]),1>o&&l?n:(s.resolveWith(e,[c]),!1)},c=s.promise({elem:e,props:rt.extend({},t),opts:rt.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:hn||F(),duration:n.duration,tweens:[],createTween:function(t,n){var i=rt.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(i),i},stop:function(t){var n=0,i=t?c.tweens.length:0;if(r)return this;for(r=!0;i>n;n++)c.tweens[n].run(1);return t?s.resolveWith(e,[c,t]):s.rejectWith(e,[c,t]),this}}),u=c.props;for(M(u,c.opts.specialEasing);a>o;o++)if(i=bn[o].call(c,e,u,c.opts))return i;return rt.map(u,P,c),rt.isFunction(c.opts.start)&&c.opts.start.call(e,c),rt.fx.timer(rt.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function j(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var i,r=0,o=t.toLowerCase().match(bt)||[];if(rt.isFunction(n))for(;i=o[r++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(e[i]=e[i]||[]).unshift(n)):(e[i]=e[i]||[]).push(n)}}function O(e,t,n,i){function r(s){var l;return o[s]=!0,rt.each(e[s]||[],function(e,s){var c=s(t,n,i);return"string"!=typeof c||a||o[c]?a?!(l=c):void 0:(t.dataTypes.unshift(c),r(c),!1)}),l}var o={},a=e===Wn;return r(t.dataTypes[0])||!o["*"]&&r("*")}function H(e,t){var n,i,r=rt.ajaxSettings.flatOptions||{};for(i in t)void 0!==t[i]&&((r[i]?e:n||(n={}))[i]=t[i]);return n&&rt.extend(!0,e,n),e}function z(e,t,n){for(var i,r,o,a,s=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(a in s)if(s[a]&&s[a].test(r)){l.unshift(a);break}if(l[0]in n)o=l[0];else{for(a in n){if(!l[0]||e.converters[a+" "+l[0]]){o=a;break}i||(i=a)}o=o||i}return o?(o!==l[0]&&l.unshift(o),n[o]):void 0}function B(e,t,n,i){var r,o,a,s,l,c={},u=e.dataTypes.slice();if(u[1])for(a in e.converters)c[a.toLowerCase()]=e.converters[a];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(a=c[l+" "+o]||c["* "+o],!a)for(r in c)if(s=r.split(" "),s[1]===o&&(a=c[l+" "+s[0]]||c["* "+s[0]])){a===!0?a=c[r]:c[r]!==!0&&(o=s[0],u.unshift(s[1]));break}if(a!==!0)if(a&&e["throws"])t=a(t);else try{t=a(t)}catch(d){return{state:"parsererror",error:a?d:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}function W(e,t,n,i){var r;if(rt.isArray(t))rt.each(t,function(t,r){n||Xn.test(e)?i(e,r):W(e+"["+("object"==typeof r?t:"")+"]",r,n,i)});else if(n||"object"!==rt.type(t))i(e,t);else for(r in t)W(e+"["+r+"]",t[r],n,i)}function V(){try{return new e.XMLHttpRequest}catch(t){}}function U(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function Z(e){return rt.isWindow(e)?e:9===e.nodeType?e.defaultView||e.parentWindow:!1}var X=[],K=X.slice,G=X.concat,Y=X.push,J=X.indexOf,Q={},et=Q.toString,tt=Q.hasOwnProperty,nt={},it="1.11.2",rt=function(e,t){return new rt.fn.init(e,t)},ot=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,at=/^-ms-/,st=/-([\da-z])/gi,lt=function(e,t){return t.toUpperCase()};rt.fn=rt.prototype={jquery:it,constructor:rt,selector:"",length:0,toArray:function(){return K.call(this)},get:function(e){return null!=e?0>e?this[e+this.length]:this[e]:K.call(this)},pushStack:function(e){var t=rt.merge(this.constructor(),e);return t.prevObject=this,t.context=this.context,t},each:function(e,t){return rt.each(this,e,t)},map:function(e){return this.pushStack(rt.map(this,function(t,n){return e.call(t,n,t)}))},slice:function(){return this.pushStack(K.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(e){var t=this.length,n=+e+(0>e?t:0);return this.pushStack(n>=0&&t>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:Y,sort:X.sort,splice:X.splice},rt.extend=rt.fn.extend=function(){var e,t,n,i,r,o,a=arguments[0]||{},s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[s]||{},s++),"object"==typeof a||rt.isFunction(a)||(a={}),s===l&&(a=this,s--);l>s;s++)if(null!=(r=arguments[s]))for(i in r)e=a[i],n=r[i],a!==n&&(c&&n&&(rt.isPlainObject(n)||(t=rt.isArray(n)))?(t?(t=!1,o=e&&rt.isArray(e)?e:[]):o=e&&rt.isPlainObject(e)?e:{},a[i]=rt.extend(c,o,n)):void 0!==n&&(a[i]=n));return a},rt.extend({expando:"jQuery"+(it+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isFunction:function(e){return"function"===rt.type(e)},isArray:Array.isArray||function(e){return"array"===rt.type(e)},isWindow:function(e){return null!=e&&e==e.window},isNumeric:function(e){return!rt.isArray(e)&&e-parseFloat(e)+1>=0},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},isPlainObject:function(e){var t;if(!e||"object"!==rt.type(e)||e.nodeType||rt.isWindow(e))return!1;try{if(e.constructor&&!tt.call(e,"constructor")&&!tt.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}if(nt.ownLast)for(t in e)return tt.call(e,t);for(t in e);return void 0===t||tt.call(e,t)},type:function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?Q[et.call(e)]||"object":typeof e},globalEval:function(t){t&&rt.trim(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(at,"ms-").replace(st,lt)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,t,i){var r,o=0,a=e.length,s=n(e);if(i){if(s)for(;a>o&&(r=t.apply(e[o],i),r!==!1);o++);else for(o in e)if(r=t.apply(e[o],i),r===!1)break}else if(s)for(;a>o&&(r=t.call(e[o],o,e[o]),r!==!1);o++);else for(o in e)if(r=t.call(e[o],o,e[o]),r===!1)break;return e},trim:function(e){return null==e?"":(e+"").replace(ot,"")},makeArray:function(e,t){var i=t||[];return null!=e&&(n(Object(e))?rt.merge(i,"string"==typeof e?[e]:e):Y.call(i,e)),i},inArray:function(e,t,n){var i;if(t){if(J)return J.call(t,e,n);for(i=t.length,n=n?0>n?Math.max(0,i+n):n:0;i>n;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;n>i;)e[r++]=t[i++];if(n!==n)for(;void 0!==t[i];)e[r++]=t[i++];return e.length=r,e},grep:function(e,t,n){for(var i,r=[],o=0,a=e.length,s=!n;a>o;o++)i=!t(e[o],o),i!==s&&r.push(e[o]);return r},map:function(e,t,i){var r,o=0,a=e.length,s=n(e),l=[];if(s)for(;a>o;o++)r=t(e[o],o,i),null!=r&&l.push(r);else for(o in e)r=t(e[o],o,i),null!=r&&l.push(r);return G.apply([],l)},guid:1,proxy:function(e,t){var n,i,r;return"string"==typeof t&&(r=e[t],t=e,e=r),rt.isFunction(e)?(n=K.call(arguments,2),i=function(){return e.apply(t||this,n.concat(K.call(arguments)))},i.guid=e.guid=e.guid||rt.guid++,i):void 0},now:function(){return+new Date},support:nt}),rt.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(e,t){Q["[object "+t+"]"]=t.toLowerCase()});var ct=/*!
 * Sizzle CSS Selector Engine v2.2.0-pre
 * http://sizzlejs.com/
 *
 * Copyright 2008, 2014 jQuery Foundation, Inc. and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2014-12-16
 */
function(e){function t(e,t,n,i){var r,o,a,s,l,c,d,f,h,m;if((t?t.ownerDocument||t:O)!==A&&I(t),t=t||A,n=n||[],s=t.nodeType,"string"!=typeof e||!e||1!==s&&9!==s&&11!==s)return n;if(!i&&N){if(11!==s&&(r=yt.exec(e)))if(a=r[1]){if(9===s){if(o=t.getElementById(a),!o||!o.parentNode)return n;if(o.id===a)return n.push(o),n}else if(t.ownerDocument&&(o=t.ownerDocument.getElementById(a))&&q(t,o)&&o.id===a)return n.push(o),n}else{if(r[2])return J.apply(n,t.getElementsByTagName(e)),n;if((a=r[3])&&w.getElementsByClassName)return J.apply(n,t.getElementsByClassName(a)),n}if(w.qsa&&(!P||!P.test(e))){if(f=d=j,h=t,m=1!==s&&e,1===s&&"object"!==t.nodeName.toLowerCase()){for(c=T(e),(d=t.getAttribute("id"))?f=d.replace(_t,"\\$&"):t.setAttribute("id",f),f="[id='"+f+"'] ",l=c.length;l--;)c[l]=f+p(c[l]);h=bt.test(e)&&u(t.parentNode)||t,m=c.join(",")}if(m)try{return J.apply(n,h.querySelectorAll(m)),n}catch(g){}finally{d||t.removeAttribute("id")}}}return $(e.replace(lt,"$1"),t,n,i)}function n(){function e(n,i){return t.push(n+" ")>x.cacheLength&&delete e[t.shift()],e[n+" "]=i}var t=[];return e}function i(e){return e[j]=!0,e}function r(e){var t=A.createElement("div");try{return!!e(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function o(e,t){for(var n=e.split("|"),i=e.length;i--;)x.attrHandle[n[i]]=t}function a(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&(~t.sourceIndex||Z)-(~e.sourceIndex||Z);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function s(e){return function(t){var n=t.nodeName.toLowerCase();return"input"===n&&t.type===e}}function l(e){return function(t){var n=t.nodeName.toLowerCase();return("input"===n||"button"===n)&&t.type===e}}function c(e){return i(function(t){return t=+t,i(function(n,i){for(var r,o=e([],n.length,t),a=o.length;a--;)n[r=o[a]]&&(n[r]=!(i[r]=n[r]))})})}function u(e){return e&&"undefined"!=typeof e.getElementsByTagName&&e}function d(){}function p(e){for(var t=0,n=e.length,i="";n>t;t++)i+=e[t].value;return i}function f(e,t,n){var i=t.dir,r=n&&"parentNode"===i,o=z++;return t.first?function(t,n,o){for(;t=t[i];)if(1===t.nodeType||r)return e(t,n,o)}:function(t,n,a){var s,l,c=[H,o];if(a){for(;t=t[i];)if((1===t.nodeType||r)&&e(t,n,a))return!0}else for(;t=t[i];)if(1===t.nodeType||r){if(l=t[j]||(t[j]={}),(s=l[i])&&s[0]===H&&s[1]===o)return c[2]=s[2];if(l[i]=c,c[2]=e(t,n,a))return!0}}}function h(e){return e.length>1?function(t,n,i){for(var r=e.length;r--;)if(!e[r](t,n,i))return!1;return!0}:e[0]}function m(e,n,i){for(var r=0,o=n.length;o>r;r++)t(e,n[r],i);return i}function g(e,t,n,i,r){for(var o,a=[],s=0,l=e.length,c=null!=t;l>s;s++)(o=e[s])&&(!n||n(o,i,r))&&(a.push(o),c&&t.push(s));return a}function v(e,t,n,r,o,a){return r&&!r[j]&&(r=v(r)),o&&!o[j]&&(o=v(o,a)),i(function(i,a,s,l){var c,u,d,p=[],f=[],h=a.length,v=i||m(t||"*",s.nodeType?[s]:s,[]),y=!e||!i&&t?v:g(v,p,e,s,l),b=n?o||(i?e:h||r)?[]:a:y;if(n&&n(y,b,s,l),r)for(c=g(b,f),r(c,[],s,l),u=c.length;u--;)(d=c[u])&&(b[f[u]]=!(y[f[u]]=d));if(i){if(o||e){if(o){for(c=[],u=b.length;u--;)(d=b[u])&&c.push(y[u]=d);o(null,b=[],c,l)}for(u=b.length;u--;)(d=b[u])&&(c=o?et(i,d):p[u])>-1&&(i[c]=!(a[c]=d))}}else b=g(b===a?b.splice(h,b.length):b),o?o(null,a,b,l):J.apply(a,b)})}function y(e){for(var t,n,i,r=e.length,o=x.relative[e[0].type],a=o||x.relative[" "],s=o?1:0,l=f(function(e){return e===t},a,!0),c=f(function(e){return et(t,e)>-1},a,!0),u=[function(e,n,i){var r=!o&&(i||n!==E)||((t=n).nodeType?l(e,n,i):c(e,n,i));return t=null,r}];r>s;s++)if(n=x.relative[e[s].type])u=[f(h(u),n)];else{if(n=x.filter[e[s].type].apply(null,e[s].matches),n[j]){for(i=++s;r>i&&!x.relative[e[i].type];i++);return v(s>1&&h(u),s>1&&p(e.slice(0,s-1).concat({value:" "===e[s-2].type?"*":""})).replace(lt,"$1"),n,i>s&&y(e.slice(s,i)),r>i&&y(e=e.slice(i)),r>i&&p(e))}u.push(n)}return h(u)}function b(e,n){var r=n.length>0,o=e.length>0,a=function(i,a,s,l,c){var u,d,p,f=0,h="0",m=i&&[],v=[],y=E,b=i||o&&x.find.TAG("*",c),_=H+=null==y?1:Math.random()||.1,w=b.length;for(c&&(E=a!==A&&a);h!==w&&null!=(u=b[h]);h++){if(o&&u){for(d=0;p=e[d++];)if(p(u,a,s)){l.push(u);break}c&&(H=_)}r&&((u=!p&&u)&&f--,i&&m.push(u))}if(f+=h,r&&h!==f){for(d=0;p=n[d++];)p(m,v,a,s);if(i){if(f>0)for(;h--;)m[h]||v[h]||(v[h]=G.call(l));v=g(v)}J.apply(l,v),c&&!i&&v.length>0&&f+n.length>1&&t.uniqueSort(l)}return c&&(H=_,E=y),m};return r?i(a):a}var _,w,x,C,k,T,S,$,E,D,L,I,A,F,N,P,R,M,q,j="sizzle"+1*new Date,O=e.document,H=0,z=0,B=n(),W=n(),V=n(),U=function(e,t){return e===t&&(L=!0),0},Z=1<<31,X={}.hasOwnProperty,K=[],G=K.pop,Y=K.push,J=K.push,Q=K.slice,et=function(e,t){for(var n=0,i=e.length;i>n;n++)if(e[n]===t)return n;return-1},tt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",nt="[\\x20\\t\\r\\n\\f]",it="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",rt=it.replace("w","w#"),ot="\\["+nt+"*("+it+")(?:"+nt+"*([*^$|!~]?=)"+nt+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+rt+"))|)"+nt+"*\\]",at=":("+it+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ot+")*)|.*)\\)|)",st=new RegExp(nt+"+","g"),lt=new RegExp("^"+nt+"+|((?:^|[^\\\\])(?:\\\\.)*)"+nt+"+$","g"),ct=new RegExp("^"+nt+"*,"+nt+"*"),ut=new RegExp("^"+nt+"*([>+~]|"+nt+")"+nt+"*"),dt=new RegExp("="+nt+"*([^\\]'\"]*?)"+nt+"*\\]","g"),pt=new RegExp(at),ft=new RegExp("^"+rt+"$"),ht={ID:new RegExp("^#("+it+")"),CLASS:new RegExp("^\\.("+it+")"),TAG:new RegExp("^("+it.replace("w","w*")+")"),ATTR:new RegExp("^"+ot),PSEUDO:new RegExp("^"+at),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+nt+"*(even|odd|(([+-]|)(\\d*)n|)"+nt+"*(?:([+-]|)"+nt+"*(\\d+)|))"+nt+"*\\)|)","i"),bool:new RegExp("^(?:"+tt+")$","i"),needsContext:new RegExp("^"+nt+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+nt+"*((?:-\\d)?\\d*)"+nt+"*\\)|)(?=[^-]|$)","i")},mt=/^(?:input|select|textarea|button)$/i,gt=/^h\d$/i,vt=/^[^{]+\{\s*\[native \w/,yt=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,bt=/[+~]/,_t=/'|\\/g,wt=new RegExp("\\\\([\\da-f]{1,6}"+nt+"?|("+nt+")|.)","ig"),xt=function(e,t,n){var i="0x"+t-65536;return i!==i||n?t:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},Ct=function(){I()};try{J.apply(K=Q.call(O.childNodes),O.childNodes),K[O.childNodes.length].nodeType}catch(kt){J={apply:K.length?function(e,t){Y.apply(e,Q.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}w=t.support={},k=t.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?"HTML"!==t.nodeName:!1},I=t.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:O;return i!==A&&9===i.nodeType&&i.documentElement?(A=i,F=i.documentElement,n=i.defaultView,n&&n!==n.top&&(n.addEventListener?n.addEventListener("unload",Ct,!1):n.attachEvent&&n.attachEvent("onunload",Ct)),N=!k(i),w.attributes=r(function(e){return e.className="i",!e.getAttribute("className")}),w.getElementsByTagName=r(function(e){return e.appendChild(i.createComment("")),!e.getElementsByTagName("*").length}),w.getElementsByClassName=vt.test(i.getElementsByClassName),w.getById=r(function(e){return F.appendChild(e).id=j,!i.getElementsByName||!i.getElementsByName(j).length}),w.getById?(x.find.ID=function(e,t){if("undefined"!=typeof t.getElementById&&N){var n=t.getElementById(e);return n&&n.parentNode?[n]:[]}},x.filter.ID=function(e){var t=e.replace(wt,xt);return function(e){return e.getAttribute("id")===t}}):(delete x.find.ID,x.filter.ID=function(e){var t=e.replace(wt,xt);return function(e){var n="undefined"!=typeof e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}}),x.find.TAG=w.getElementsByTagName?function(e,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(e):w.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"===e){for(;n=o[r++];)1===n.nodeType&&i.push(n);return i}return o},x.find.CLASS=w.getElementsByClassName&&function(e,t){return N?t.getElementsByClassName(e):void 0},R=[],P=[],(w.qsa=vt.test(i.querySelectorAll))&&(r(function(e){F.appendChild(e).innerHTML="<a id='"+j+"'></a><select id='"+j+"-\f]' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&P.push("[*^$]="+nt+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||P.push("\\["+nt+"*(?:value|"+tt+")"),e.querySelectorAll("[id~="+j+"-]").length||P.push("~="),e.querySelectorAll(":checked").length||P.push(":checked"),e.querySelectorAll("a#"+j+"+*").length||P.push(".#.+[+~]")}),r(function(e){var t=i.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&P.push("name"+nt+"*[*^$|!~]?="),e.querySelectorAll(":enabled").length||P.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),P.push(",.*:")})),(w.matchesSelector=vt.test(M=F.matches||F.webkitMatchesSelector||F.mozMatchesSelector||F.oMatchesSelector||F.msMatchesSelector))&&r(function(e){w.disconnectedMatch=M.call(e,"div"),M.call(e,"[s!='']:x"),R.push("!=",at)}),P=P.length&&new RegExp(P.join("|")),R=R.length&&new RegExp(R.join("|")),t=vt.test(F.compareDocumentPosition),q=t||vt.test(F.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},U=t?function(e,t){if(e===t)return L=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n?n:(n=(e.ownerDocument||e)===(t.ownerDocument||t)?e.compareDocumentPosition(t):1,1&n||!w.sortDetached&&t.compareDocumentPosition(e)===n?e===i||e.ownerDocument===O&&q(O,e)?-1:t===i||t.ownerDocument===O&&q(O,t)?1:D?et(D,e)-et(D,t):0:4&n?-1:1)}:function(e,t){if(e===t)return L=!0,0;var n,r=0,o=e.parentNode,s=t.parentNode,l=[e],c=[t];if(!o||!s)return e===i?-1:t===i?1:o?-1:s?1:D?et(D,e)-et(D,t):0;if(o===s)return a(e,t);for(n=e;n=n.parentNode;)l.unshift(n);for(n=t;n=n.parentNode;)c.unshift(n);for(;l[r]===c[r];)r++;return r?a(l[r],c[r]):l[r]===O?-1:c[r]===O?1:0},i):A},t.matches=function(e,n){return t(e,null,null,n)},t.matchesSelector=function(e,n){if((e.ownerDocument||e)!==A&&I(e),n=n.replace(dt,"='$1']"),!(!w.matchesSelector||!N||R&&R.test(n)||P&&P.test(n)))try{var i=M.call(e,n);if(i||w.disconnectedMatch||e.document&&11!==e.document.nodeType)return i}catch(r){}return t(n,A,null,[e]).length>0},t.contains=function(e,t){return(e.ownerDocument||e)!==A&&I(e),q(e,t)},t.attr=function(e,t){(e.ownerDocument||e)!==A&&I(e);var n=x.attrHandle[t.toLowerCase()],i=n&&X.call(x.attrHandle,t.toLowerCase())?n(e,t,!N):void 0;return void 0!==i?i:w.attributes||!N?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},t.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},t.uniqueSort=function(e){var t,n=[],i=0,r=0;if(L=!w.detectDuplicates,D=!w.sortStable&&e.slice(0),e.sort(U),L){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return D=null,e},C=t.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=C(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=C(t);return n},x=t.selectors={cacheLength:50,createPseudo:i,match:ht,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(wt,xt),e[3]=(e[3]||e[4]||e[5]||"").replace(wt,xt),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||t.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&t.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return ht.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&pt.test(n)&&(t=T(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(wt,xt).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=B[e+" "];return t||(t=new RegExp("(^|"+nt+")"+e+"("+nt+"|$)"))&&B(e,function(e){return t.test("string"==typeof e.className&&e.className||"undefined"!=typeof e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(e,n,i){return function(r){var o=t.attr(r,e);return null==o?"!="===n:n?(o+="","="===n?o===i:"!="===n?o!==i:"^="===n?i&&0===o.indexOf(i):"*="===n?i&&o.indexOf(i)>-1:"$="===n?i&&o.slice(-i.length)===i:"~="===n?(" "+o.replace(st," ")+" ").indexOf(i)>-1:"|="===n?o===i||o.slice(0,i.length+1)===i+"-":!1):!0}},CHILD:function(e,t,n,i,r){var o="nth"!==e.slice(0,3),a="last"!==e.slice(-4),s="of-type"===t;return 1===i&&0===r?function(e){return!!e.parentNode}:function(t,n,l){var c,u,d,p,f,h,m=o!==a?"nextSibling":"previousSibling",g=t.parentNode,v=s&&t.nodeName.toLowerCase(),y=!l&&!s;if(g){if(o){for(;m;){for(d=t;d=d[m];)if(s?d.nodeName.toLowerCase()===v:1===d.nodeType)return!1;h=m="only"===e&&!h&&"nextSibling"}return!0}if(h=[a?g.firstChild:g.lastChild],a&&y){for(u=g[j]||(g[j]={}),c=u[e]||[],f=c[0]===H&&c[1],p=c[0]===H&&c[2],d=f&&g.childNodes[f];d=++f&&d&&d[m]||(p=f=0)||h.pop();)if(1===d.nodeType&&++p&&d===t){u[e]=[H,f,p];break}}else if(y&&(c=(t[j]||(t[j]={}))[e])&&c[0]===H)p=c[1];else for(;(d=++f&&d&&d[m]||(p=f=0)||h.pop())&&((s?d.nodeName.toLowerCase()!==v:1!==d.nodeType)||!++p||(y&&((d[j]||(d[j]={}))[e]=[H,p]),d!==t)););return p-=r,p===i||p%i===0&&p/i>=0}}},PSEUDO:function(e,n){var r,o=x.pseudos[e]||x.setFilters[e.toLowerCase()]||t.error("unsupported pseudo: "+e);return o[j]?o(n):o.length>1?(r=[e,e,"",n],x.setFilters.hasOwnProperty(e.toLowerCase())?i(function(e,t){for(var i,r=o(e,n),a=r.length;a--;)i=et(e,r[a]),e[i]=!(t[i]=r[a])}):function(e){return o(e,0,r)}):o}},pseudos:{not:i(function(e){var t=[],n=[],r=S(e.replace(lt,"$1"));return r[j]?i(function(e,t,n,i){for(var o,a=r(e,null,i,[]),s=e.length;s--;)(o=a[s])&&(e[s]=!(t[s]=o))}):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}}),has:i(function(e){return function(n){return t(e,n).length>0}}),contains:i(function(e){return e=e.replace(wt,xt),function(t){return(t.textContent||t.innerText||C(t)).indexOf(e)>-1}}),lang:i(function(e){return ft.test(e||"")||t.error("unsupported lang: "+e),e=e.replace(wt,xt).toLowerCase(),function(t){var n;do if(n=N?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return n=n.toLowerCase(),n===e||0===n.indexOf(e+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var n=e.location&&e.location.hash;return n&&n.slice(1)===t.id},root:function(e){return e===F},focus:function(e){return e===A.activeElement&&(!A.hasFocus||A.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!x.pseudos.empty(e)},header:function(e){return gt.test(e.nodeName)},input:function(e){return mt.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:c(function(){return[0]}),last:c(function(e,t){return[t-1]}),eq:c(function(e,t,n){return[0>n?n+t:n]}),even:c(function(e,t){for(var n=0;t>n;n+=2)e.push(n);return e}),odd:c(function(e,t){for(var n=1;t>n;n+=2)e.push(n);return e}),lt:c(function(e,t,n){for(var i=0>n?n+t:n;--i>=0;)e.push(i);return e}),gt:c(function(e,t,n){for(var i=0>n?n+t:n;++i<t;)e.push(i);return e})}},x.pseudos.nth=x.pseudos.eq;for(_ in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})x.pseudos[_]=s(_);for(_ in{submit:!0,reset:!0})x.pseudos[_]=l(_);return d.prototype=x.filters=x.pseudos,x.setFilters=new d,T=t.tokenize=function(e,n){var i,r,o,a,s,l,c,u=W[e+" "];if(u)return n?0:u.slice(0);for(s=e,l=[],c=x.preFilter;s;){(!i||(r=ct.exec(s)))&&(r&&(s=s.slice(r[0].length)||s),l.push(o=[])),i=!1,(r=ut.exec(s))&&(i=r.shift(),o.push({value:i,type:r[0].replace(lt," ")}),s=s.slice(i.length));for(a in x.filter)!(r=ht[a].exec(s))||c[a]&&!(r=c[a](r))||(i=r.shift(),o.push({value:i,type:a,matches:r}),s=s.slice(i.length));if(!i)break}return n?s.length:s?t.error(e):W(e,l).slice(0)},S=t.compile=function(e,t){var n,i=[],r=[],o=V[e+" "];if(!o){for(t||(t=T(e)),n=t.length;n--;)o=y(t[n]),o[j]?i.push(o):r.push(o);o=V(e,b(r,i)),o.selector=e}return o},$=t.select=function(e,t,n,i){var r,o,a,s,l,c="function"==typeof e&&e,d=!i&&T(e=c.selector||e);if(n=n||[],1===d.length){if(o=d[0]=d[0].slice(0),o.length>2&&"ID"===(a=o[0]).type&&w.getById&&9===t.nodeType&&N&&x.relative[o[1].type]){if(t=(x.find.ID(a.matches[0].replace(wt,xt),t)||[])[0],!t)return n;c&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=ht.needsContext.test(e)?0:o.length;r--&&(a=o[r],!x.relative[s=a.type]);)if((l=x.find[s])&&(i=l(a.matches[0].replace(wt,xt),bt.test(o[0].type)&&u(t.parentNode)||t))){if(o.splice(r,1),e=i.length&&p(o),!e)return J.apply(n,i),n;break}}return(c||S(e,d))(i,t,!N,n,bt.test(e)&&u(t.parentNode)||t),n},w.sortStable=j.split("").sort(U).join("")===j,w.detectDuplicates=!!L,I(),w.sortDetached=r(function(e){return 1&e.compareDocumentPosition(A.createElement("div"))}),r(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||o("type|href|height|width",function(e,t,n){return n?void 0:e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),w.attributes&&r(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||o("value",function(e,t,n){return n||"input"!==e.nodeName.toLowerCase()?void 0:e.defaultValue}),r(function(e){return null==e.getAttribute("disabled")})||o(tt,function(e,t,n){var i;return n?void 0:e[t]===!0?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),t}(e);rt.find=ct,rt.expr=ct.selectors,rt.expr[":"]=rt.expr.pseudos,rt.unique=ct.uniqueSort,rt.text=ct.getText,rt.isXMLDoc=ct.isXML,rt.contains=ct.contains;var ut=rt.expr.match.needsContext,dt=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,pt=/^.[^:#\[\.,]*$/;rt.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?rt.find.matchesSelector(i,e)?[i]:[]:rt.find.matches(e,rt.grep(t,function(e){return 1===e.nodeType}))},rt.fn.extend({find:function(e){var t,n=[],i=this,r=i.length;if("string"!=typeof e)return this.pushStack(rt(e).filter(function(){for(t=0;r>t;t++)if(rt.contains(i[t],this))return!0}));for(t=0;r>t;t++)rt.find(e,i[t],n);return n=this.pushStack(r>1?rt.unique(n):n),n.selector=this.selector?this.selector+" "+e:e,n},filter:function(e){return this.pushStack(i(this,e||[],!1))},not:function(e){return this.pushStack(i(this,e||[],!0))},is:function(e){return!!i(this,"string"==typeof e&&ut.test(e)?rt(e):e||[],!1).length}});var ft,ht=e.document,mt=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,gt=rt.fn.init=function(e,t){var n,i;if(!e)return this;if("string"==typeof e){if(n="<"===e.charAt(0)&&">"===e.charAt(e.length-1)&&e.length>=3?[null,e,null]:mt.exec(e),!n||!n[1]&&t)return!t||t.jquery?(t||ft).find(e):this.constructor(t).find(e);if(n[1]){if(t=t instanceof rt?t[0]:t,rt.merge(this,rt.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:ht,!0)),dt.test(n[1])&&rt.isPlainObject(t))for(n in t)rt.isFunction(this[n])?this[n](t[n]):this.attr(n,t[n]);return this}if(i=ht.getElementById(n[2]),i&&i.parentNode){if(i.id!==n[2])return ft.find(e);this.length=1,this[0]=i}return this.context=ht,this.selector=e,this}return e.nodeType?(this.context=this[0]=e,this.length=1,this):rt.isFunction(e)?"undefined"!=typeof ft.ready?ft.ready(e):e(rt):(void 0!==e.selector&&(this.selector=e.selector,this.context=e.context),rt.makeArray(e,this))};gt.prototype=rt.fn,ft=rt(ht);var vt=/^(?:parents|prev(?:Until|All))/,yt={children:!0,contents:!0,next:!0,prev:!0};rt.extend({dir:function(e,t,n){for(var i=[],r=e[t];r&&9!==r.nodeType&&(void 0===n||1!==r.nodeType||!rt(r).is(n));)1===r.nodeType&&i.push(r),r=r[t];return i},sibling:function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}}),rt.fn.extend({has:function(e){var t,n=rt(e,this),i=n.length;return this.filter(function(){for(t=0;i>t;t++)if(rt.contains(this,n[t]))return!0})},closest:function(e,t){for(var n,i=0,r=this.length,o=[],a=ut.test(e)||"string"!=typeof e?rt(e,t||this.context):0;r>i;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(a?a.index(n)>-1:1===n.nodeType&&rt.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?rt.unique(o):o)},index:function(e){return e?"string"==typeof e?rt.inArray(this[0],rt(e)):rt.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(rt.unique(rt.merge(this.get(),rt(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),rt.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return rt.dir(e,"parentNode")},parentsUntil:function(e,t,n){return rt.dir(e,"parentNode",n)},next:function(e){return r(e,"nextSibling")},prev:function(e){return r(e,"previousSibling")},nextAll:function(e){return rt.dir(e,"nextSibling")},prevAll:function(e){return rt.dir(e,"previousSibling")},nextUntil:function(e,t,n){return rt.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return rt.dir(e,"previousSibling",n)},siblings:function(e){return rt.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return rt.sibling(e.firstChild)},contents:function(e){return rt.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:rt.merge([],e.childNodes)}},function(e,t){rt.fn[e]=function(n,i){var r=rt.map(this,t,n);return"Until"!==e.slice(-5)&&(i=n),i&&"string"==typeof i&&(r=rt.filter(i,r)),this.length>1&&(yt[e]||(r=rt.unique(r)),vt.test(e)&&(r=r.reverse())),this.pushStack(r)}});var bt=/\S+/g,_t={};rt.Callbacks=function(e){e="string"==typeof e?_t[e]||o(e):rt.extend({},e);var t,n,i,r,a,s,l=[],c=!e.once&&[],u=function(o){for(n=e.memory&&o,i=!0,a=s||0,s=0,r=l.length,t=!0;l&&r>a;a++)if(l[a].apply(o[0],o[1])===!1&&e.stopOnFalse){n=!1;break}t=!1,l&&(c?c.length&&u(c.shift()):n?l=[]:d.disable())},d={add:function(){if(l){var i=l.length;!function o(t){rt.each(t,function(t,n){var i=rt.type(n);"function"===i?e.unique&&d.has(n)||l.push(n):n&&n.length&&"string"!==i&&o(n)})}(arguments),t?r=l.length:n&&(s=i,u(n))}return this},remove:function(){return l&&rt.each(arguments,function(e,n){for(var i;(i=rt.inArray(n,l,i))>-1;)l.splice(i,1),t&&(r>=i&&r--,a>=i&&a--)}),this},has:function(e){return e?rt.inArray(e,l)>-1:!(!l||!l.length)},empty:function(){return l=[],r=0,this},disable:function(){return l=c=n=void 0,this},disabled:function(){return!l},lock:function(){return c=void 0,n||d.disable(),this},locked:function(){return!c},fireWith:function(e,n){return!l||i&&!c||(n=n||[],n=[e,n.slice?n.slice():n],t?c.push(n):u(n)),this},fire:function(){return d.fireWith(this,arguments),this},fired:function(){return!!i}};return d},rt.extend({Deferred:function(e){var t=[["resolve","done",rt.Callbacks("once memory"),"resolved"],["reject","fail",rt.Callbacks("once memory"),"rejected"],["notify","progress",rt.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return r.done(arguments).fail(arguments),this},then:function(){var e=arguments;return rt.Deferred(function(n){rt.each(t,function(t,o){var a=rt.isFunction(e[t])&&e[t];r[o[1]](function(){var e=a&&a.apply(this,arguments);e&&rt.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[o[0]+"With"](this===i?n.promise():this,a?[e]:arguments)})}),e=null}).promise()},promise:function(e){return null!=e?rt.extend(e,i):i}},r={};return i.pipe=i.then,rt.each(t,function(e,o){var a=o[2],s=o[3];i[o[1]]=a.add,s&&a.add(function(){n=s},t[1^e][2].disable,t[2][2].lock),r[o[0]]=function(){return r[o[0]+"With"](this===r?i:this,arguments),this},r[o[0]+"With"]=a.fireWith}),i.promise(r),e&&e.call(r,r),r},when:function(e){var t,n,i,r=0,o=K.call(arguments),a=o.length,s=1!==a||e&&rt.isFunction(e.promise)?a:0,l=1===s?e:rt.Deferred(),c=function(e,n,i){return function(r){n[e]=this,i[e]=arguments.length>1?K.call(arguments):r,i===t?l.notifyWith(n,i):--s||l.resolveWith(n,i)}};if(a>1)for(t=new Array(a),n=new Array(a),i=new Array(a);a>r;r++)o[r]&&rt.isFunction(o[r].promise)?o[r].promise().done(c(r,i,o)).fail(l.reject).progress(c(r,n,t)):--s;return s||l.resolveWith(i,o),l.promise()}});var wt;rt.fn.ready=function(e){return rt.ready.promise().done(e),this},rt.extend({isReady:!1,readyWait:1,holdReady:function(e){e?rt.readyWait++:rt.ready(!0)},ready:function(e){if(e===!0?!--rt.readyWait:!rt.isReady){if(!ht.body)return setTimeout(rt.ready);rt.isReady=!0,e!==!0&&--rt.readyWait>0||(wt.resolveWith(ht,[rt]),rt.fn.triggerHandler&&(rt(ht).triggerHandler("ready"),rt(ht).off("ready")))}}}),rt.ready.promise=function(t){if(!wt)if(wt=rt.Deferred(),"complete"===ht.readyState)setTimeout(rt.ready);else if(ht.addEventListener)ht.addEventListener("DOMContentLoaded",s,!1),e.addEventListener("load",s,!1);else{ht.attachEvent("onreadystatechange",s),e.attachEvent("onload",s);var n=!1;try{n=null==e.frameElement&&ht.documentElement}catch(i){}n&&n.doScroll&&!function r(){if(!rt.isReady){try{n.doScroll("left")}catch(e){return setTimeout(r,50)}a(),rt.ready()}}()}return wt.promise(t)};var xt,Ct="undefined";for(xt in rt(nt))break;nt.ownLast="0"!==xt,nt.inlineBlockNeedsLayout=!1,rt(function(){var e,t,n,i;n=ht.getElementsByTagName("body")[0],n&&n.style&&(t=ht.createElement("div"),i=ht.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Ct&&(t.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",nt.inlineBlockNeedsLayout=e=3===t.offsetWidth,e&&(n.style.zoom=1)),n.removeChild(i))}),function(){var e=ht.createElement("div");if(null==nt.deleteExpando){nt.deleteExpando=!0;try{delete e.test}catch(t){nt.deleteExpando=!1}}e=null}(),rt.acceptData=function(e){var t=rt.noData[(e.nodeName+" ").toLowerCase()],n=+e.nodeType||1;return 1!==n&&9!==n?!1:!t||t!==!0&&e.getAttribute("classid")===t};var kt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Tt=/([A-Z])/g;rt.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(e){return e=e.nodeType?rt.cache[e[rt.expando]]:e[rt.expando],!!e&&!c(e)},data:function(e,t,n){return u(e,t,n)},removeData:function(e,t){return d(e,t)},_data:function(e,t,n){return u(e,t,n,!0)},_removeData:function(e,t){return d(e,t,!0)}}),rt.fn.extend({data:function(e,t){var n,i,r,o=this[0],a=o&&o.attributes;if(void 0===e){if(this.length&&(r=rt.data(o),1===o.nodeType&&!rt._data(o,"parsedAttrs"))){for(n=a.length;n--;)a[n]&&(i=a[n].name,0===i.indexOf("data-")&&(i=rt.camelCase(i.slice(5)),l(o,i,r[i])));rt._data(o,"parsedAttrs",!0)}return r}return"object"==typeof e?this.each(function(){rt.data(this,e)}):arguments.length>1?this.each(function(){rt.data(this,e,t)}):o?l(o,e,rt.data(o,e)):void 0},removeData:function(e){return this.each(function(){rt.removeData(this,e)})}}),rt.extend({queue:function(e,t,n){var i;return e?(t=(t||"fx")+"queue",i=rt._data(e,t),n&&(!i||rt.isArray(n)?i=rt._data(e,t,rt.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(e,t){t=t||"fx";var n=rt.queue(e,t),i=n.length,r=n.shift(),o=rt._queueHooks(e,t),a=function(){rt.dequeue(e,t)};"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,a,o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return rt._data(e,n)||rt._data(e,n,{empty:rt.Callbacks("once memory").add(function(){rt._removeData(e,t+"queue"),rt._removeData(e,n)})})}}),rt.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?rt.queue(this[0],e):void 0===t?this:this.each(function(){var n=rt.queue(this,e,t);rt._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&rt.dequeue(this,e)})},dequeue:function(e){return this.each(function(){rt.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,i=1,r=rt.Deferred(),o=this,a=this.length,s=function(){--i||r.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)n=rt._data(o[a],e+"queueHooks"),n&&n.empty&&(i++,n.empty.add(s));return s(),r.promise(t)}});var St=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,$t=["Top","Right","Bottom","Left"],Et=function(e,t){return e=t||e,"none"===rt.css(e,"display")||!rt.contains(e.ownerDocument,e)},Dt=rt.access=function(e,t,n,i,r,o,a){var s=0,l=e.length,c=null==n;if("object"===rt.type(n)){r=!0;for(s in n)rt.access(e,t,s,n[s],!0,o,a)}else if(void 0!==i&&(r=!0,rt.isFunction(i)||(a=!0),c&&(a?(t.call(e,i),t=null):(c=t,t=function(e,t,n){return c.call(rt(e),n)})),t))for(;l>s;s++)t(e[s],n,a?i:i.call(e[s],s,t(e[s],n)));return r?e:c?t.call(e):l?t(e[0],n):o},Lt=/^(?:checkbox|radio)$/i;!function(){var e=ht.createElement("input"),t=ht.createElement("div"),n=ht.createDocumentFragment();if(t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",nt.leadingWhitespace=3===t.firstChild.nodeType,nt.tbody=!t.getElementsByTagName("tbody").length,nt.htmlSerialize=!!t.getElementsByTagName("link").length,nt.html5Clone="<:nav></:nav>"!==ht.createElement("nav").cloneNode(!0).outerHTML,e.type="checkbox",e.checked=!0,n.appendChild(e),nt.appendChecked=e.checked,t.innerHTML="<textarea>x</textarea>",nt.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,n.appendChild(t),t.innerHTML="<input type='radio' checked='checked' name='t'/>",nt.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,nt.noCloneEvent=!0,t.attachEvent&&(t.attachEvent("onclick",function(){nt.noCloneEvent=!1}),t.cloneNode(!0).click()),null==nt.deleteExpando){nt.deleteExpando=!0;try{delete t.test}catch(i){nt.deleteExpando=!1}}}(),function(){var t,n,i=ht.createElement("div");for(t in{submit:!0,change:!0,focusin:!0})n="on"+t,(nt[t+"Bubbles"]=n in e)||(i.setAttribute(n,"t"),nt[t+"Bubbles"]=i.attributes[n].expando===!1);i=null}();var It=/^(?:input|select|textarea)$/i,At=/^key/,Ft=/^(?:mouse|pointer|contextmenu)|click/,Nt=/^(?:focusinfocus|focusoutblur)$/,Pt=/^([^.]*)(?:\.(.+)|)$/;rt.event={global:{},add:function(e,t,n,i,r){var o,a,s,l,c,u,d,p,f,h,m,g=rt._data(e);if(g){for(n.handler&&(l=n,n=l.handler,r=l.selector),n.guid||(n.guid=rt.guid++),(a=g.events)||(a=g.events={}),(u=g.handle)||(u=g.handle=function(e){return typeof rt===Ct||e&&rt.event.triggered===e.type?void 0:rt.event.dispatch.apply(u.elem,arguments)},u.elem=e),t=(t||"").match(bt)||[""],s=t.length;s--;)o=Pt.exec(t[s])||[],f=m=o[1],h=(o[2]||"").split(".").sort(),f&&(c=rt.event.special[f]||{},f=(r?c.delegateType:c.bindType)||f,c=rt.event.special[f]||{},d=rt.extend({type:f,origType:m,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&rt.expr.match.needsContext.test(r),namespace:h.join(".")},l),(p=a[f])||(p=a[f]=[],p.delegateCount=0,c.setup&&c.setup.call(e,i,h,u)!==!1||(e.addEventListener?e.addEventListener(f,u,!1):e.attachEvent&&e.attachEvent("on"+f,u))),c.add&&(c.add.call(e,d),d.handler.guid||(d.handler.guid=n.guid)),r?p.splice(p.delegateCount++,0,d):p.push(d),rt.event.global[f]=!0);e=null}},remove:function(e,t,n,i,r){var o,a,s,l,c,u,d,p,f,h,m,g=rt.hasData(e)&&rt._data(e);if(g&&(u=g.events)){for(t=(t||"").match(bt)||[""],c=t.length;c--;)if(s=Pt.exec(t[c])||[],f=m=s[1],h=(s[2]||"").split(".").sort(),f){for(d=rt.event.special[f]||{},f=(i?d.delegateType:d.bindType)||f,p=u[f]||[],s=s[2]&&new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=o=p.length;o--;)a=p[o],!r&&m!==a.origType||n&&n.guid!==a.guid||s&&!s.test(a.namespace)||i&&i!==a.selector&&("**"!==i||!a.selector)||(p.splice(o,1),a.selector&&p.delegateCount--,d.remove&&d.remove.call(e,a));l&&!p.length&&(d.teardown&&d.teardown.call(e,h,g.handle)!==!1||rt.removeEvent(e,f,g.handle),delete u[f])}else for(f in u)rt.event.remove(e,f+t[c],n,i,!0);rt.isEmptyObject(u)&&(delete g.handle,rt._removeData(e,"events"))}},trigger:function(t,n,i,r){var o,a,s,l,c,u,d,p=[i||ht],f=tt.call(t,"type")?t.type:t,h=tt.call(t,"namespace")?t.namespace.split("."):[];if(s=u=i=i||ht,3!==i.nodeType&&8!==i.nodeType&&!Nt.test(f+rt.event.triggered)&&(f.indexOf(".")>=0&&(h=f.split("."),f=h.shift(),h.sort()),a=f.indexOf(":")<0&&"on"+f,t=t[rt.expando]?t:new rt.Event(f,"object"==typeof t&&t),t.isTrigger=r?2:3,t.namespace=h.join("."),t.namespace_re=t.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=i),n=null==n?[t]:rt.makeArray(n,[t]),c=rt.event.special[f]||{},r||!c.trigger||c.trigger.apply(i,n)!==!1)){if(!r&&!c.noBubble&&!rt.isWindow(i)){for(l=c.delegateType||f,Nt.test(l+f)||(s=s.parentNode);s;s=s.parentNode)p.push(s),u=s;
u===(i.ownerDocument||ht)&&p.push(u.defaultView||u.parentWindow||e)}for(d=0;(s=p[d++])&&!t.isPropagationStopped();)t.type=d>1?l:c.bindType||f,o=(rt._data(s,"events")||{})[t.type]&&rt._data(s,"handle"),o&&o.apply(s,n),o=a&&s[a],o&&o.apply&&rt.acceptData(s)&&(t.result=o.apply(s,n),t.result===!1&&t.preventDefault());if(t.type=f,!r&&!t.isDefaultPrevented()&&(!c._default||c._default.apply(p.pop(),n)===!1)&&rt.acceptData(i)&&a&&i[f]&&!rt.isWindow(i)){u=i[a],u&&(i[a]=null),rt.event.triggered=f;try{i[f]()}catch(m){}rt.event.triggered=void 0,u&&(i[a]=u)}return t.result}},dispatch:function(e){e=rt.event.fix(e);var t,n,i,r,o,a=[],s=K.call(arguments),l=(rt._data(this,"events")||{})[e.type]||[],c=rt.event.special[e.type]||{};if(s[0]=e,e.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,e)!==!1){for(a=rt.event.handlers.call(this,e,l),t=0;(r=a[t++])&&!e.isPropagationStopped();)for(e.currentTarget=r.elem,o=0;(i=r.handlers[o++])&&!e.isImmediatePropagationStopped();)(!e.namespace_re||e.namespace_re.test(i.namespace))&&(e.handleObj=i,e.data=i.data,n=((rt.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,s),void 0!==n&&(e.result=n)===!1&&(e.preventDefault(),e.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,e),e.result}},handlers:function(e,t){var n,i,r,o,a=[],s=t.delegateCount,l=e.target;if(s&&l.nodeType&&(!e.button||"click"!==e.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(l.disabled!==!0||"click"!==e.type)){for(r=[],o=0;s>o;o++)i=t[o],n=i.selector+" ",void 0===r[n]&&(r[n]=i.needsContext?rt(n,this).index(l)>=0:rt.find(n,this,null,[l]).length),r[n]&&r.push(i);r.length&&a.push({elem:l,handlers:r})}return s<t.length&&a.push({elem:this,handlers:t.slice(s)}),a},fix:function(e){if(e[rt.expando])return e;var t,n,i,r=e.type,o=e,a=this.fixHooks[r];for(a||(this.fixHooks[r]=a=Ft.test(r)?this.mouseHooks:At.test(r)?this.keyHooks:{}),i=a.props?this.props.concat(a.props):this.props,e=new rt.Event(o),t=i.length;t--;)n=i[t],e[n]=o[n];return e.target||(e.target=o.srcElement||ht),3===e.target.nodeType&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,a.filter?a.filter(e,o):e},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return null==e.which&&(e.which=null!=t.charCode?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,t){var n,i,r,o=t.button,a=t.fromElement;return null==e.pageX&&null!=t.clientX&&(i=e.target.ownerDocument||ht,r=i.documentElement,n=i.body,e.pageX=t.clientX+(r&&r.scrollLeft||n&&n.scrollLeft||0)-(r&&r.clientLeft||n&&n.clientLeft||0),e.pageY=t.clientY+(r&&r.scrollTop||n&&n.scrollTop||0)-(r&&r.clientTop||n&&n.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?t.toElement:a),e.which||void 0===o||(e.which=1&o?1:2&o?3:4&o?2:0),e}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==h()&&this.focus)try{return this.focus(),!1}catch(e){}},delegateType:"focusin"},blur:{trigger:function(){return this===h()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return rt.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(e){return rt.nodeName(e.target,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}},simulate:function(e,t,n,i){var r=rt.extend(new rt.Event,n,{type:e,isSimulated:!0,originalEvent:{}});i?rt.event.trigger(r,null,t):rt.event.dispatch.call(t,r),r.isDefaultPrevented()&&n.preventDefault()}},rt.removeEvent=ht.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var i="on"+t;e.detachEvent&&(typeof e[i]===Ct&&(e[i]=null),e.detachEvent(i,n))},rt.Event=function(e,t){return this instanceof rt.Event?(e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&e.returnValue===!1?p:f):this.type=e,t&&rt.extend(this,t),this.timeStamp=e&&e.timeStamp||rt.now(),void(this[rt.expando]=!0)):new rt.Event(e,t)},rt.Event.prototype={isDefaultPrevented:f,isPropagationStopped:f,isImmediatePropagationStopped:f,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=p,e&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=p,e&&(e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0)},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=p,e&&e.stopImmediatePropagation&&e.stopImmediatePropagation(),this.stopPropagation()}},rt.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){rt.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,i=this,r=e.relatedTarget,o=e.handleObj;return(!r||r!==i&&!rt.contains(i,r))&&(e.type=o.origType,n=o.handler.apply(this,arguments),e.type=t),n}}}),nt.submitBubbles||(rt.event.special.submit={setup:function(){return rt.nodeName(this,"form")?!1:void rt.event.add(this,"click._submit keypress._submit",function(e){var t=e.target,n=rt.nodeName(t,"input")||rt.nodeName(t,"button")?t.form:void 0;n&&!rt._data(n,"submitBubbles")&&(rt.event.add(n,"submit._submit",function(e){e._submit_bubble=!0}),rt._data(n,"submitBubbles",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&rt.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){return rt.nodeName(this,"form")?!1:void rt.event.remove(this,"._submit")}}),nt.changeBubbles||(rt.event.special.change={setup:function(){return It.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(rt.event.add(this,"propertychange._change",function(e){"checked"===e.originalEvent.propertyName&&(this._just_changed=!0)}),rt.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),rt.event.simulate("change",this,e,!0)})),!1):void rt.event.add(this,"beforeactivate._change",function(e){var t=e.target;It.test(t.nodeName)&&!rt._data(t,"changeBubbles")&&(rt.event.add(t,"change._change",function(e){!this.parentNode||e.isSimulated||e.isTrigger||rt.event.simulate("change",this.parentNode,e,!0)}),rt._data(t,"changeBubbles",!0))})},handle:function(e){var t=e.target;return this!==t||e.isSimulated||e.isTrigger||"radio"!==t.type&&"checkbox"!==t.type?e.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return rt.event.remove(this,"._change"),!It.test(this.nodeName)}}),nt.focusinBubbles||rt.each({focus:"focusin",blur:"focusout"},function(e,t){var n=function(e){rt.event.simulate(t,e.target,rt.event.fix(e),!0)};rt.event.special[t]={setup:function(){var i=this.ownerDocument||this,r=rt._data(i,t);r||i.addEventListener(e,n,!0),rt._data(i,t,(r||0)+1)},teardown:function(){var i=this.ownerDocument||this,r=rt._data(i,t)-1;r?rt._data(i,t,r):(i.removeEventListener(e,n,!0),rt._removeData(i,t))}}}),rt.fn.extend({on:function(e,t,n,i,r){var o,a;if("object"==typeof e){"string"!=typeof t&&(n=n||t,t=void 0);for(o in e)this.on(o,t,n,e[o],r);return this}if(null==n&&null==i?(i=t,n=t=void 0):null==i&&("string"==typeof t?(i=n,n=void 0):(i=n,n=t,t=void 0)),i===!1)i=f;else if(!i)return this;return 1===r&&(a=i,i=function(e){return rt().off(e),a.apply(this,arguments)},i.guid=a.guid||(a.guid=rt.guid++)),this.each(function(){rt.event.add(this,e,i,n,t)})},one:function(e,t,n,i){return this.on(e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,rt(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof e){for(r in e)this.off(r,t,e[r]);return this}return(t===!1||"function"==typeof t)&&(n=t,t=void 0),n===!1&&(n=f),this.each(function(){rt.event.remove(this,e,n,t)})},trigger:function(e,t){return this.each(function(){rt.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];return n?rt.event.trigger(e,t,n,!0):void 0}});var Rt="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",Mt=/ jQuery\d+="(?:null|\d+)"/g,qt=new RegExp("<(?:"+Rt+")[\\s/>]","i"),jt=/^\s+/,Ot=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Ht=/<([\w:]+)/,zt=/<tbody/i,Bt=/<|&#?\w+;/,Wt=/<(?:script|style|link)/i,Vt=/checked\s*(?:[^=]|=\s*.checked.)/i,Ut=/^$|\/(?:java|ecma)script/i,Zt=/^true\/(.*)/,Xt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Kt={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:nt.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Gt=m(ht),Yt=Gt.appendChild(ht.createElement("div"));Kt.optgroup=Kt.option,Kt.tbody=Kt.tfoot=Kt.colgroup=Kt.caption=Kt.thead,Kt.th=Kt.td,rt.extend({clone:function(e,t,n){var i,r,o,a,s,l=rt.contains(e.ownerDocument,e);if(nt.html5Clone||rt.isXMLDoc(e)||!qt.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(Yt.innerHTML=e.outerHTML,Yt.removeChild(o=Yt.firstChild)),!(nt.noCloneEvent&&nt.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||rt.isXMLDoc(e)))for(i=g(o),s=g(e),a=0;null!=(r=s[a]);++a)i[a]&&C(r,i[a]);if(t)if(n)for(s=s||g(e),i=i||g(o),a=0;null!=(r=s[a]);a++)x(r,i[a]);else x(e,o);return i=g(o,"script"),i.length>0&&w(i,!l&&g(e,"script")),i=s=r=null,o},buildFragment:function(e,t,n,i){for(var r,o,a,s,l,c,u,d=e.length,p=m(t),f=[],h=0;d>h;h++)if(o=e[h],o||0===o)if("object"===rt.type(o))rt.merge(f,o.nodeType?[o]:o);else if(Bt.test(o)){for(s=s||p.appendChild(t.createElement("div")),l=(Ht.exec(o)||["",""])[1].toLowerCase(),u=Kt[l]||Kt._default,s.innerHTML=u[1]+o.replace(Ot,"<$1></$2>")+u[2],r=u[0];r--;)s=s.lastChild;if(!nt.leadingWhitespace&&jt.test(o)&&f.push(t.createTextNode(jt.exec(o)[0])),!nt.tbody)for(o="table"!==l||zt.test(o)?"<table>"!==u[1]||zt.test(o)?0:s:s.firstChild,r=o&&o.childNodes.length;r--;)rt.nodeName(c=o.childNodes[r],"tbody")&&!c.childNodes.length&&o.removeChild(c);for(rt.merge(f,s.childNodes),s.textContent="";s.firstChild;)s.removeChild(s.firstChild);s=p.lastChild}else f.push(t.createTextNode(o));for(s&&p.removeChild(s),nt.appendChecked||rt.grep(g(f,"input"),v),h=0;o=f[h++];)if((!i||-1===rt.inArray(o,i))&&(a=rt.contains(o.ownerDocument,o),s=g(p.appendChild(o),"script"),a&&w(s),n))for(r=0;o=s[r++];)Ut.test(o.type||"")&&n.push(o);return s=null,p},cleanData:function(e,t){for(var n,i,r,o,a=0,s=rt.expando,l=rt.cache,c=nt.deleteExpando,u=rt.event.special;null!=(n=e[a]);a++)if((t||rt.acceptData(n))&&(r=n[s],o=r&&l[r])){if(o.events)for(i in o.events)u[i]?rt.event.remove(n,i):rt.removeEvent(n,i,o.handle);l[r]&&(delete l[r],c?delete n[s]:typeof n.removeAttribute!==Ct?n.removeAttribute(s):n[s]=null,X.push(r))}}}),rt.fn.extend({text:function(e){return Dt(this,function(e){return void 0===e?rt.text(this):this.empty().append((this[0]&&this[0].ownerDocument||ht).createTextNode(e))},null,e,arguments.length)},append:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=y(this,e);t.appendChild(e)}})},prepend:function(){return this.domManip(arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=y(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return this.domManip(arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},remove:function(e,t){for(var n,i=e?rt.filter(e,this):this,r=0;null!=(n=i[r]);r++)t||1!==n.nodeType||rt.cleanData(g(n)),n.parentNode&&(t&&rt.contains(n.ownerDocument,n)&&w(g(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var e,t=0;null!=(e=this[t]);t++){for(1===e.nodeType&&rt.cleanData(g(e,!1));e.firstChild;)e.removeChild(e.firstChild);e.options&&rt.nodeName(e,"select")&&(e.options.length=0)}return this},clone:function(e,t){return e=null==e?!1:e,t=null==t?e:t,this.map(function(){return rt.clone(this,e,t)})},html:function(e){return Dt(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e)return 1===t.nodeType?t.innerHTML.replace(Mt,""):void 0;if(!("string"!=typeof e||Wt.test(e)||!nt.htmlSerialize&&qt.test(e)||!nt.leadingWhitespace&&jt.test(e)||Kt[(Ht.exec(e)||["",""])[1].toLowerCase()])){e=e.replace(Ot,"<$1></$2>");try{for(;i>n;n++)t=this[n]||{},1===t.nodeType&&(rt.cleanData(g(t,!1)),t.innerHTML=e);t=0}catch(r){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var e=arguments[0];return this.domManip(arguments,function(t){e=this.parentNode,rt.cleanData(g(this)),e&&e.replaceChild(t,this)}),e&&(e.length||e.nodeType)?this:this.remove()},detach:function(e){return this.remove(e,!0)},domManip:function(e,t){e=G.apply([],e);var n,i,r,o,a,s,l=0,c=this.length,u=this,d=c-1,p=e[0],f=rt.isFunction(p);if(f||c>1&&"string"==typeof p&&!nt.checkClone&&Vt.test(p))return this.each(function(n){var i=u.eq(n);f&&(e[0]=p.call(this,n,i.html())),i.domManip(e,t)});if(c&&(s=rt.buildFragment(e,this[0].ownerDocument,!1,this),n=s.firstChild,1===s.childNodes.length&&(s=n),n)){for(o=rt.map(g(s,"script"),b),r=o.length;c>l;l++)i=s,l!==d&&(i=rt.clone(i,!0,!0),r&&rt.merge(o,g(i,"script"))),t.call(this[l],i,l);if(r)for(a=o[o.length-1].ownerDocument,rt.map(o,_),l=0;r>l;l++)i=o[l],Ut.test(i.type||"")&&!rt._data(i,"globalEval")&&rt.contains(a,i)&&(i.src?rt._evalUrl&&rt._evalUrl(i.src):rt.globalEval((i.text||i.textContent||i.innerHTML||"").replace(Xt,"")));s=n=null}return this}}),rt.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){rt.fn[e]=function(e){for(var n,i=0,r=[],o=rt(e),a=o.length-1;a>=i;i++)n=i===a?this:this.clone(!0),rt(o[i])[t](n),Y.apply(r,n.get());return this.pushStack(r)}});var Jt,Qt={};!function(){var e;nt.shrinkWrapBlocks=function(){if(null!=e)return e;e=!1;var t,n,i;return n=ht.getElementsByTagName("body")[0],n&&n.style?(t=ht.createElement("div"),i=ht.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),typeof t.style.zoom!==Ct&&(t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",t.appendChild(ht.createElement("div")).style.width="5px",e=3!==t.offsetWidth),n.removeChild(i),e):void 0}}();var en,tn,nn=/^margin/,rn=new RegExp("^("+St+")(?!px)[a-z%]+$","i"),on=/^(top|right|bottom|left)$/;e.getComputedStyle?(en=function(t){return t.ownerDocument.defaultView.opener?t.ownerDocument.defaultView.getComputedStyle(t,null):e.getComputedStyle(t,null)},tn=function(e,t,n){var i,r,o,a,s=e.style;return n=n||en(e),a=n?n.getPropertyValue(t)||n[t]:void 0,n&&(""!==a||rt.contains(e.ownerDocument,e)||(a=rt.style(e,t)),rn.test(a)&&nn.test(t)&&(i=s.width,r=s.minWidth,o=s.maxWidth,s.minWidth=s.maxWidth=s.width=a,a=n.width,s.width=i,s.minWidth=r,s.maxWidth=o)),void 0===a?a:a+""}):ht.documentElement.currentStyle&&(en=function(e){return e.currentStyle},tn=function(e,t,n){var i,r,o,a,s=e.style;return n=n||en(e),a=n?n[t]:void 0,null==a&&s&&s[t]&&(a=s[t]),rn.test(a)&&!on.test(t)&&(i=s.left,r=e.runtimeStyle,o=r&&r.left,o&&(r.left=e.currentStyle.left),s.left="fontSize"===t?"1em":a,a=s.pixelLeft+"px",s.left=i,o&&(r.left=o)),void 0===a?a:a+""||"auto"}),function(){function t(){var t,n,i,r;n=ht.getElementsByTagName("body")[0],n&&n.style&&(t=ht.createElement("div"),i=ht.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(t),t.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",o=a=!1,l=!0,e.getComputedStyle&&(o="1%"!==(e.getComputedStyle(t,null)||{}).top,a="4px"===(e.getComputedStyle(t,null)||{width:"4px"}).width,r=t.appendChild(ht.createElement("div")),r.style.cssText=t.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",r.style.marginRight=r.style.width="0",t.style.width="1px",l=!parseFloat((e.getComputedStyle(r,null)||{}).marginRight),t.removeChild(r)),t.innerHTML="<table><tr><td></td><td>t</td></tr></table>",r=t.getElementsByTagName("td"),r[0].style.cssText="margin:0;border:0;padding:0;display:none",s=0===r[0].offsetHeight,s&&(r[0].style.display="",r[1].style.display="none",s=0===r[0].offsetHeight),n.removeChild(i))}var n,i,r,o,a,s,l;n=ht.createElement("div"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",r=n.getElementsByTagName("a")[0],i=r&&r.style,i&&(i.cssText="float:left;opacity:.5",nt.opacity="0.5"===i.opacity,nt.cssFloat=!!i.cssFloat,n.style.backgroundClip="content-box",n.cloneNode(!0).style.backgroundClip="",nt.clearCloneStyle="content-box"===n.style.backgroundClip,nt.boxSizing=""===i.boxSizing||""===i.MozBoxSizing||""===i.WebkitBoxSizing,rt.extend(nt,{reliableHiddenOffsets:function(){return null==s&&t(),s},boxSizingReliable:function(){return null==a&&t(),a},pixelPosition:function(){return null==o&&t(),o},reliableMarginRight:function(){return null==l&&t(),l}}))}(),rt.swap=function(e,t,n,i){var r,o,a={};for(o in t)a[o]=e.style[o],e.style[o]=t[o];r=n.apply(e,i||[]);for(o in t)e.style[o]=a[o];return r};var an=/alpha\([^)]*\)/i,sn=/opacity\s*=\s*([^)]*)/,ln=/^(none|table(?!-c[ea]).+)/,cn=new RegExp("^("+St+")(.*)$","i"),un=new RegExp("^([+-])=("+St+")","i"),dn={position:"absolute",visibility:"hidden",display:"block"},pn={letterSpacing:"0",fontWeight:"400"},fn=["Webkit","O","Moz","ms"];rt.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=tn(e,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":nt.cssFloat?"cssFloat":"styleFloat"},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,a,s=rt.camelCase(t),l=e.style;if(t=rt.cssProps[s]||(rt.cssProps[s]=$(l,s)),a=rt.cssHooks[t]||rt.cssHooks[s],void 0===n)return a&&"get"in a&&void 0!==(r=a.get(e,!1,i))?r:l[t];if(o=typeof n,"string"===o&&(r=un.exec(n))&&(n=(r[1]+1)*r[2]+parseFloat(rt.css(e,t)),o="number"),null!=n&&n===n&&("number"!==o||rt.cssNumber[s]||(n+="px"),nt.clearCloneStyle||""!==n||0!==t.indexOf("background")||(l[t]="inherit"),!(a&&"set"in a&&void 0===(n=a.set(e,n,i)))))try{l[t]=n}catch(c){}}},css:function(e,t,n,i){var r,o,a,s=rt.camelCase(t);return t=rt.cssProps[s]||(rt.cssProps[s]=$(e.style,s)),a=rt.cssHooks[t]||rt.cssHooks[s],a&&"get"in a&&(o=a.get(e,!0,n)),void 0===o&&(o=tn(e,t,i)),"normal"===o&&t in pn&&(o=pn[t]),""===n||n?(r=parseFloat(o),n===!0||rt.isNumeric(r)?r||0:o):o}}),rt.each(["height","width"],function(e,t){rt.cssHooks[t]={get:function(e,n,i){return n?ln.test(rt.css(e,"display"))&&0===e.offsetWidth?rt.swap(e,dn,function(){return I(e,t,i)}):I(e,t,i):void 0},set:function(e,n,i){var r=i&&en(e);return D(e,n,i?L(e,t,i,nt.boxSizing&&"border-box"===rt.css(e,"boxSizing",!1,r),r):0)}}}),nt.opacity||(rt.cssHooks.opacity={get:function(e,t){return sn.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,i=e.currentStyle,r=rt.isNumeric(t)?"alpha(opacity="+100*t+")":"",o=i&&i.filter||n.filter||"";n.zoom=1,(t>=1||""===t)&&""===rt.trim(o.replace(an,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===t||i&&!i.filter)||(n.filter=an.test(o)?o.replace(an,r):o+" "+r)}}),rt.cssHooks.marginRight=S(nt.reliableMarginRight,function(e,t){return t?rt.swap(e,{display:"inline-block"},tn,[e,"marginRight"]):void 0}),rt.each({margin:"",padding:"",border:"Width"},function(e,t){rt.cssHooks[e+t]={expand:function(n){for(var i=0,r={},o="string"==typeof n?n.split(" "):[n];4>i;i++)r[e+$t[i]+t]=o[i]||o[i-2]||o[0];return r}},nn.test(e)||(rt.cssHooks[e+t].set=D)}),rt.fn.extend({css:function(e,t){return Dt(this,function(e,t,n){var i,r,o={},a=0;if(rt.isArray(t)){for(i=en(e),r=t.length;r>a;a++)o[t[a]]=rt.css(e,t[a],!1,i);return o}return void 0!==n?rt.style(e,t,n):rt.css(e,t)},e,t,arguments.length>1)},show:function(){return E(this,!0)},hide:function(){return E(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Et(this)?rt(this).show():rt(this).hide()})}}),rt.Tween=A,A.prototype={constructor:A,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||"swing",this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(rt.cssNumber[n]?"":"px")},cur:function(){var e=A.propHooks[this.prop];return e&&e.get?e.get(this):A.propHooks._default.get(this)},run:function(e){var t,n=A.propHooks[this.prop];return this.pos=t=this.options.duration?rt.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):A.propHooks._default.set(this),this}},A.prototype.init.prototype=A.prototype,A.propHooks={_default:{get:function(e){var t;return null==e.elem[e.prop]||e.elem.style&&null!=e.elem.style[e.prop]?(t=rt.css(e.elem,e.prop,""),t&&"auto"!==t?t:0):e.elem[e.prop]},set:function(e){rt.fx.step[e.prop]?rt.fx.step[e.prop](e):e.elem.style&&(null!=e.elem.style[rt.cssProps[e.prop]]||rt.cssHooks[e.prop])?rt.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},A.propHooks.scrollTop=A.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},rt.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},rt.fx=A.prototype.init,rt.fx.step={};var hn,mn,gn=/^(?:toggle|show|hide)$/,vn=new RegExp("^(?:([+-])=|)("+St+")([a-z%]*)$","i"),yn=/queueHooks$/,bn=[R],_n={"*":[function(e,t){var n=this.createTween(e,t),i=n.cur(),r=vn.exec(t),o=r&&r[3]||(rt.cssNumber[e]?"":"px"),a=(rt.cssNumber[e]||"px"!==o&&+i)&&vn.exec(rt.css(n.elem,e)),s=1,l=20;if(a&&a[3]!==o){o=o||a[3],r=r||[],a=+i||1;do s=s||".5",a/=s,rt.style(n.elem,e,a+o);while(s!==(s=n.cur()/i)&&1!==s&&--l)}return r&&(a=n.start=+a||+i||0,n.unit=o,n.end=r[1]?a+(r[1]+1)*r[2]:+r[2]),n}]};rt.Animation=rt.extend(q,{tweener:function(e,t){rt.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");for(var n,i=0,r=e.length;r>i;i++)n=e[i],_n[n]=_n[n]||[],_n[n].unshift(t)},prefilter:function(e,t){t?bn.unshift(e):bn.push(e)}}),rt.speed=function(e,t,n){var i=e&&"object"==typeof e?rt.extend({},e):{complete:n||!n&&t||rt.isFunction(e)&&e,duration:e,easing:n&&t||t&&!rt.isFunction(t)&&t};return i.duration=rt.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in rt.fx.speeds?rt.fx.speeds[i.duration]:rt.fx.speeds._default,(null==i.queue||i.queue===!0)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){rt.isFunction(i.old)&&i.old.call(this),i.queue&&rt.dequeue(this,i.queue)},i},rt.fn.extend({fadeTo:function(e,t,n,i){return this.filter(Et).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(e,t,n,i){var r=rt.isEmptyObject(e),o=rt.speed(t,n,i),a=function(){var t=q(this,rt.extend({},e),o);(r||rt._data(this,"finish"))&&t.stop(!0)};return a.finish=a,r||o.queue===!1?this.each(a):this.queue(o.queue,a)},stop:function(e,t,n){var i=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,r=null!=e&&e+"queueHooks",o=rt.timers,a=rt._data(this);if(r)a[r]&&a[r].stop&&i(a[r]);else for(r in a)a[r]&&a[r].stop&&yn.test(r)&&i(a[r]);for(r=o.length;r--;)o[r].elem!==this||null!=e&&o[r].queue!==e||(o[r].anim.stop(n),t=!1,o.splice(r,1));(t||!n)&&rt.dequeue(this,e)})},finish:function(e){return e!==!1&&(e=e||"fx"),this.each(function(){var t,n=rt._data(this),i=n[e+"queue"],r=n[e+"queueHooks"],o=rt.timers,a=i?i.length:0;for(n.finish=!0,rt.queue(this,e,[]),r&&r.stop&&r.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;a>t;t++)i[t]&&i[t].finish&&i[t].finish.call(this);delete n.finish})}}),rt.each(["toggle","show","hide"],function(e,t){var n=rt.fn[t];rt.fn[t]=function(e,i,r){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(N(t,!0),e,i,r)}}),rt.each({slideDown:N("show"),slideUp:N("hide"),slideToggle:N("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){rt.fn[e]=function(e,n,i){return this.animate(t,e,n,i)}}),rt.timers=[],rt.fx.tick=function(){var e,t=rt.timers,n=0;for(hn=rt.now();n<t.length;n++)e=t[n],e()||t[n]!==e||t.splice(n--,1);t.length||rt.fx.stop(),hn=void 0},rt.fx.timer=function(e){rt.timers.push(e),e()?rt.fx.start():rt.timers.pop()},rt.fx.interval=13,rt.fx.start=function(){mn||(mn=setInterval(rt.fx.tick,rt.fx.interval))},rt.fx.stop=function(){clearInterval(mn),mn=null},rt.fx.speeds={slow:600,fast:200,_default:400},rt.fn.delay=function(e,t){return e=rt.fx?rt.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var i=setTimeout(t,e);n.stop=function(){clearTimeout(i)}})},function(){var e,t,n,i,r;t=ht.createElement("div"),t.setAttribute("className","t"),t.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=t.getElementsByTagName("a")[0],n=ht.createElement("select"),r=n.appendChild(ht.createElement("option")),e=t.getElementsByTagName("input")[0],i.style.cssText="top:1px",nt.getSetAttribute="t"!==t.className,nt.style=/top/.test(i.getAttribute("style")),nt.hrefNormalized="/a"===i.getAttribute("href"),nt.checkOn=!!e.value,nt.optSelected=r.selected,nt.enctype=!!ht.createElement("form").enctype,n.disabled=!0,nt.optDisabled=!r.disabled,e=ht.createElement("input"),e.setAttribute("value",""),nt.input=""===e.getAttribute("value"),e.value="t",e.setAttribute("type","radio"),nt.radioValue="t"===e.value}();var wn=/\r/g;rt.fn.extend({val:function(e){var t,n,i,r=this[0];{if(arguments.length)return i=rt.isFunction(e),this.each(function(n){var r;1===this.nodeType&&(r=i?e.call(this,n,rt(this).val()):e,null==r?r="":"number"==typeof r?r+="":rt.isArray(r)&&(r=rt.map(r,function(e){return null==e?"":e+""})),t=rt.valHooks[this.type]||rt.valHooks[this.nodeName.toLowerCase()],t&&"set"in t&&void 0!==t.set(this,r,"value")||(this.value=r))});if(r)return t=rt.valHooks[r.type]||rt.valHooks[r.nodeName.toLowerCase()],t&&"get"in t&&void 0!==(n=t.get(r,"value"))?n:(n=r.value,"string"==typeof n?n.replace(wn,""):null==n?"":n)}}}),rt.extend({valHooks:{option:{get:function(e){var t=rt.find.attr(e,"value");return null!=t?t:rt.trim(rt.text(e))}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type||0>r,a=o?null:[],s=o?r+1:i.length,l=0>r?s:o?r:0;s>l;l++)if(n=i[l],!(!n.selected&&l!==r||(nt.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&rt.nodeName(n.parentNode,"optgroup"))){if(t=rt(n).val(),o)return t;a.push(t)}return a},set:function(e,t){for(var n,i,r=e.options,o=rt.makeArray(t),a=r.length;a--;)if(i=r[a],rt.inArray(rt.valHooks.option.get(i),o)>=0)try{i.selected=n=!0}catch(s){i.scrollHeight}else i.selected=!1;return n||(e.selectedIndex=-1),r}}}}),rt.each(["radio","checkbox"],function(){rt.valHooks[this]={set:function(e,t){return rt.isArray(t)?e.checked=rt.inArray(rt(e).val(),t)>=0:void 0}},nt.checkOn||(rt.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var xn,Cn,kn=rt.expr.attrHandle,Tn=/^(?:checked|selected)$/i,Sn=nt.getSetAttribute,$n=nt.input;rt.fn.extend({attr:function(e,t){return Dt(this,rt.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){rt.removeAttr(this,e)})}}),rt.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(e&&3!==o&&8!==o&&2!==o)return typeof e.getAttribute===Ct?rt.prop(e,t,n):(1===o&&rt.isXMLDoc(e)||(t=t.toLowerCase(),i=rt.attrHooks[t]||(rt.expr.match.bool.test(t)?Cn:xn)),void 0===n?i&&"get"in i&&null!==(r=i.get(e,t))?r:(r=rt.find.attr(e,t),null==r?void 0:r):null!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):void rt.removeAttr(e,t))},removeAttr:function(e,t){var n,i,r=0,o=t&&t.match(bt);if(o&&1===e.nodeType)for(;n=o[r++];)i=rt.propFix[n]||n,rt.expr.match.bool.test(n)?$n&&Sn||!Tn.test(n)?e[i]=!1:e[rt.camelCase("default-"+n)]=e[i]=!1:rt.attr(e,n,""),e.removeAttribute(Sn?n:i)},attrHooks:{type:{set:function(e,t){if(!nt.radioValue&&"radio"===t&&rt.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}}}),Cn={set:function(e,t,n){return t===!1?rt.removeAttr(e,n):$n&&Sn||!Tn.test(n)?e.setAttribute(!Sn&&rt.propFix[n]||n,n):e[rt.camelCase("default-"+n)]=e[n]=!0,n}},rt.each(rt.expr.match.bool.source.match(/\w+/g),function(e,t){var n=kn[t]||rt.find.attr;kn[t]=$n&&Sn||!Tn.test(t)?function(e,t,i){var r,o;return i||(o=kn[t],kn[t]=r,r=null!=n(e,t,i)?t.toLowerCase():null,kn[t]=o),r}:function(e,t,n){return n?void 0:e[rt.camelCase("default-"+t)]?t.toLowerCase():null}}),$n&&Sn||(rt.attrHooks.value={set:function(e,t,n){return rt.nodeName(e,"input")?void(e.defaultValue=t):xn&&xn.set(e,t,n)}}),Sn||(xn={set:function(e,t,n){var i=e.getAttributeNode(n);return i||e.setAttributeNode(i=e.ownerDocument.createAttribute(n)),i.value=t+="","value"===n||t===e.getAttribute(n)?t:void 0}},kn.id=kn.name=kn.coords=function(e,t,n){var i;return n?void 0:(i=e.getAttributeNode(t))&&""!==i.value?i.value:null},rt.valHooks.button={get:function(e,t){var n=e.getAttributeNode(t);return n&&n.specified?n.value:void 0},set:xn.set},rt.attrHooks.contenteditable={set:function(e,t,n){xn.set(e,""===t?!1:t,n)}},rt.each(["width","height"],function(e,t){rt.attrHooks[t]={set:function(e,n){return""===n?(e.setAttribute(t,"auto"),n):void 0}}})),nt.style||(rt.attrHooks.style={get:function(e){return e.style.cssText||void 0},set:function(e,t){return e.style.cssText=t+""}});var En=/^(?:input|select|textarea|button|object)$/i,Dn=/^(?:a|area)$/i;rt.fn.extend({prop:function(e,t){return Dt(this,rt.prop,e,t,arguments.length>1)},removeProp:function(e){return e=rt.propFix[e]||e,this.each(function(){try{this[e]=void 0,delete this[e]}catch(t){}})}}),rt.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(e,t,n){var i,r,o,a=e.nodeType;if(e&&3!==a&&8!==a&&2!==a)return o=1!==a||!rt.isXMLDoc(e),o&&(t=rt.propFix[t]||t,r=rt.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=rt.find.attr(e,"tabindex");return t?parseInt(t,10):En.test(e.nodeName)||Dn.test(e.nodeName)&&e.href?0:-1}}}}),nt.hrefNormalized||rt.each(["href","src"],function(e,t){rt.propHooks[t]={get:function(e){return e.getAttribute(t,4)}}}),nt.optSelected||(rt.propHooks.selected={get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}}),rt.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){rt.propFix[this.toLowerCase()]=this}),nt.enctype||(rt.propFix.enctype="encoding");var Ln=/[\t\r\n\f]/g;rt.fn.extend({addClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c="string"==typeof e&&e;if(rt.isFunction(e))return this.each(function(t){rt(this).addClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(bt)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Ln," "):" ")){for(o=0;r=t[o++];)i.indexOf(" "+r+" ")<0&&(i+=r+" ");a=rt.trim(i),n.className!==a&&(n.className=a)}return this},removeClass:function(e){var t,n,i,r,o,a,s=0,l=this.length,c=0===arguments.length||"string"==typeof e&&e;if(rt.isFunction(e))return this.each(function(t){rt(this).removeClass(e.call(this,t,this.className))});if(c)for(t=(e||"").match(bt)||[];l>s;s++)if(n=this[s],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Ln," "):"")){for(o=0;r=t[o++];)for(;i.indexOf(" "+r+" ")>=0;)i=i.replace(" "+r+" "," ");a=e?rt.trim(i):"",n.className!==a&&(n.className=a)}return this},toggleClass:function(e,t){var n=typeof e;return"boolean"==typeof t&&"string"===n?t?this.addClass(e):this.removeClass(e):this.each(rt.isFunction(e)?function(n){rt(this).toggleClass(e.call(this,n,this.className,t),t)}:function(){if("string"===n)for(var t,i=0,r=rt(this),o=e.match(bt)||[];t=o[i++];)r.hasClass(t)?r.removeClass(t):r.addClass(t);
else(n===Ct||"boolean"===n)&&(this.className&&rt._data(this,"__className__",this.className),this.className=this.className||e===!1?"":rt._data(this,"__className__")||"")})},hasClass:function(e){for(var t=" "+e+" ",n=0,i=this.length;i>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(Ln," ").indexOf(t)>=0)return!0;return!1}}),rt.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){rt.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}),rt.fn.extend({hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)}});var In=rt.now(),An=/\?/,Fn=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;rt.parseJSON=function(t){if(e.JSON&&e.JSON.parse)return e.JSON.parse(t+"");var n,i=null,r=rt.trim(t+"");return r&&!rt.trim(r.replace(Fn,function(e,t,r,o){return n&&t&&(i=0),0===i?e:(n=r||t,i+=!o-!r,"")}))?Function("return "+r)():rt.error("Invalid JSON: "+t)},rt.parseXML=function(t){var n,i;if(!t||"string"!=typeof t)return null;try{e.DOMParser?(i=new DOMParser,n=i.parseFromString(t,"text/xml")):(n=new ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(t))}catch(r){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||rt.error("Invalid XML: "+t),n};var Nn,Pn,Rn=/#.*$/,Mn=/([?&])_=[^&]*/,qn=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,jn=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,On=/^(?:GET|HEAD)$/,Hn=/^\/\//,zn=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Bn={},Wn={},Vn="*/".concat("*");try{Pn=location.href}catch(Un){Pn=ht.createElement("a"),Pn.href="",Pn=Pn.href}Nn=zn.exec(Pn.toLowerCase())||[],rt.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Pn,type:"GET",isLocal:jn.test(Nn[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Vn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":rt.parseJSON,"text xml":rt.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?H(H(e,rt.ajaxSettings),t):H(rt.ajaxSettings,e)},ajaxPrefilter:j(Bn),ajaxTransport:j(Wn),ajax:function(e,t){function n(e,t,n,i){var r,u,v,y,_,x=t;2!==b&&(b=2,s&&clearTimeout(s),c=void 0,a=i||"",w.readyState=e>0?4:0,r=e>=200&&300>e||304===e,n&&(y=z(d,w,n)),y=B(d,y,w,r),r?(d.ifModified&&(_=w.getResponseHeader("Last-Modified"),_&&(rt.lastModified[o]=_),_=w.getResponseHeader("etag"),_&&(rt.etag[o]=_)),204===e||"HEAD"===d.type?x="nocontent":304===e?x="notmodified":(x=y.state,u=y.data,v=y.error,r=!v)):(v=x,(e||!x)&&(x="error",0>e&&(e=0))),w.status=e,w.statusText=(t||x)+"",r?h.resolveWith(p,[u,x,w]):h.rejectWith(p,[w,x,v]),w.statusCode(g),g=void 0,l&&f.trigger(r?"ajaxSuccess":"ajaxError",[w,d,r?u:v]),m.fireWith(p,[w,x]),l&&(f.trigger("ajaxComplete",[w,d]),--rt.active||rt.event.trigger("ajaxStop")))}"object"==typeof e&&(t=e,e=void 0),t=t||{};var i,r,o,a,s,l,c,u,d=rt.ajaxSetup({},t),p=d.context||d,f=d.context&&(p.nodeType||p.jquery)?rt(p):rt.event,h=rt.Deferred(),m=rt.Callbacks("once memory"),g=d.statusCode||{},v={},y={},b=0,_="canceled",w={readyState:0,getResponseHeader:function(e){var t;if(2===b){if(!u)for(u={};t=qn.exec(a);)u[t[1].toLowerCase()]=t[2];t=u[e.toLowerCase()]}return null==t?null:t},getAllResponseHeaders:function(){return 2===b?a:null},setRequestHeader:function(e,t){var n=e.toLowerCase();return b||(e=y[n]=y[n]||e,v[e]=t),this},overrideMimeType:function(e){return b||(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(2>b)for(t in e)g[t]=[g[t],e[t]];else w.always(e[w.status]);return this},abort:function(e){var t=e||_;return c&&c.abort(t),n(0,t),this}};if(h.promise(w).complete=m.add,w.success=w.done,w.error=w.fail,d.url=((e||d.url||Pn)+"").replace(Rn,"").replace(Hn,Nn[1]+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=rt.trim(d.dataType||"*").toLowerCase().match(bt)||[""],null==d.crossDomain&&(i=zn.exec(d.url.toLowerCase()),d.crossDomain=!(!i||i[1]===Nn[1]&&i[2]===Nn[2]&&(i[3]||("http:"===i[1]?"80":"443"))===(Nn[3]||("http:"===Nn[1]?"80":"443")))),d.data&&d.processData&&"string"!=typeof d.data&&(d.data=rt.param(d.data,d.traditional)),O(Bn,d,t,w),2===b)return w;l=rt.event&&d.global,l&&0===rt.active++&&rt.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!On.test(d.type),o=d.url,d.hasContent||(d.data&&(o=d.url+=(An.test(o)?"&":"?")+d.data,delete d.data),d.cache===!1&&(d.url=Mn.test(o)?o.replace(Mn,"$1_="+In++):o+(An.test(o)?"&":"?")+"_="+In++)),d.ifModified&&(rt.lastModified[o]&&w.setRequestHeader("If-Modified-Since",rt.lastModified[o]),rt.etag[o]&&w.setRequestHeader("If-None-Match",rt.etag[o])),(d.data&&d.hasContent&&d.contentType!==!1||t.contentType)&&w.setRequestHeader("Content-Type",d.contentType),w.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Vn+"; q=0.01":""):d.accepts["*"]);for(r in d.headers)w.setRequestHeader(r,d.headers[r]);if(d.beforeSend&&(d.beforeSend.call(p,w,d)===!1||2===b))return w.abort();_="abort";for(r in{success:1,error:1,complete:1})w[r](d[r]);if(c=O(Wn,d,t,w)){w.readyState=1,l&&f.trigger("ajaxSend",[w,d]),d.async&&d.timeout>0&&(s=setTimeout(function(){w.abort("timeout")},d.timeout));try{b=1,c.send(v,n)}catch(x){if(!(2>b))throw x;n(-1,x)}}else n(-1,"No Transport");return w},getJSON:function(e,t,n){return rt.get(e,t,n,"json")},getScript:function(e,t){return rt.get(e,void 0,t,"script")}}),rt.each(["get","post"],function(e,t){rt[t]=function(e,n,i,r){return rt.isFunction(n)&&(r=r||i,i=n,n=void 0),rt.ajax({url:e,type:t,dataType:r,data:n,success:i})}}),rt._evalUrl=function(e){return rt.ajax({url:e,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},rt.fn.extend({wrapAll:function(e){if(rt.isFunction(e))return this.each(function(t){rt(this).wrapAll(e.call(this,t))});if(this[0]){var t=rt(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstChild&&1===e.firstChild.nodeType;)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return this.each(rt.isFunction(e)?function(t){rt(this).wrapInner(e.call(this,t))}:function(){var t=rt(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=rt.isFunction(e);return this.each(function(n){rt(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){rt.nodeName(this,"body")||rt(this).replaceWith(this.childNodes)}).end()}}),rt.expr.filters.hidden=function(e){return e.offsetWidth<=0&&e.offsetHeight<=0||!nt.reliableHiddenOffsets()&&"none"===(e.style&&e.style.display||rt.css(e,"display"))},rt.expr.filters.visible=function(e){return!rt.expr.filters.hidden(e)};var Zn=/%20/g,Xn=/\[\]$/,Kn=/\r?\n/g,Gn=/^(?:submit|button|image|reset|file)$/i,Yn=/^(?:input|select|textarea|keygen)/i;rt.param=function(e,t){var n,i=[],r=function(e,t){t=rt.isFunction(t)?t():null==t?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};if(void 0===t&&(t=rt.ajaxSettings&&rt.ajaxSettings.traditional),rt.isArray(e)||e.jquery&&!rt.isPlainObject(e))rt.each(e,function(){r(this.name,this.value)});else for(n in e)W(n,e[n],t,r);return i.join("&").replace(Zn,"+")},rt.fn.extend({serialize:function(){return rt.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=rt.prop(this,"elements");return e?rt.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!rt(this).is(":disabled")&&Yn.test(this.nodeName)&&!Gn.test(e)&&(this.checked||!Lt.test(e))}).map(function(e,t){var n=rt(this).val();return null==n?null:rt.isArray(n)?rt.map(n,function(e){return{name:t.name,value:e.replace(Kn,"\r\n")}}):{name:t.name,value:n.replace(Kn,"\r\n")}}).get()}}),rt.ajaxSettings.xhr=void 0!==e.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&V()||U()}:V;var Jn=0,Qn={},ei=rt.ajaxSettings.xhr();e.attachEvent&&e.attachEvent("onunload",function(){for(var e in Qn)Qn[e](void 0,!0)}),nt.cors=!!ei&&"withCredentials"in ei,ei=nt.ajax=!!ei,ei&&rt.ajaxTransport(function(e){if(!e.crossDomain||nt.cors){var t;return{send:function(n,i){var r,o=e.xhr(),a=++Jn;if(o.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(r in e.xhrFields)o[r]=e.xhrFields[r];e.mimeType&&o.overrideMimeType&&o.overrideMimeType(e.mimeType),e.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(r in n)void 0!==n[r]&&o.setRequestHeader(r,n[r]+"");o.send(e.hasContent&&e.data||null),t=function(n,r){var s,l,c;if(t&&(r||4===o.readyState))if(delete Qn[a],t=void 0,o.onreadystatechange=rt.noop,r)4!==o.readyState&&o.abort();else{c={},s=o.status,"string"==typeof o.responseText&&(c.text=o.responseText);try{l=o.statusText}catch(u){l=""}s||!e.isLocal||e.crossDomain?1223===s&&(s=204):s=c.text?200:404}c&&i(s,l,c,o.getAllResponseHeaders())},e.async?4===o.readyState?setTimeout(t):o.onreadystatechange=Qn[a]=t:t()},abort:function(){t&&t(void 0,!0)}}}}),rt.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(e){return rt.globalEval(e),e}}}),rt.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),rt.ajaxTransport("script",function(e){if(e.crossDomain){var t,n=ht.head||rt("head")[0]||ht.documentElement;return{send:function(i,r){t=ht.createElement("script"),t.async=!0,e.scriptCharset&&(t.charset=e.scriptCharset),t.src=e.url,t.onload=t.onreadystatechange=function(e,n){(n||!t.readyState||/loaded|complete/.test(t.readyState))&&(t.onload=t.onreadystatechange=null,t.parentNode&&t.parentNode.removeChild(t),t=null,n||r(200,"success"))},n.insertBefore(t,n.firstChild)},abort:function(){t&&t.onload(void 0,!0)}}}});var ti=[],ni=/(=)\?(?=&|$)|\?\?/;rt.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=ti.pop()||rt.expando+"_"+In++;return this[e]=!0,e}}),rt.ajaxPrefilter("json jsonp",function(t,n,i){var r,o,a,s=t.jsonp!==!1&&(ni.test(t.url)?"url":"string"==typeof t.data&&!(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&ni.test(t.data)&&"data");return s||"jsonp"===t.dataTypes[0]?(r=t.jsonpCallback=rt.isFunction(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,s?t[s]=t[s].replace(ni,"$1"+r):t.jsonp!==!1&&(t.url+=(An.test(t.url)?"&":"?")+t.jsonp+"="+r),t.converters["script json"]=function(){return a||rt.error(r+" was not called"),a[0]},t.dataTypes[0]="json",o=e[r],e[r]=function(){a=arguments},i.always(function(){e[r]=o,t[r]&&(t.jsonpCallback=n.jsonpCallback,ti.push(r)),a&&rt.isFunction(o)&&o(a[0]),a=o=void 0}),"script"):void 0}),rt.parseHTML=function(e,t,n){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t,t=!1),t=t||ht;var i=dt.exec(e),r=!n&&[];return i?[t.createElement(i[1])]:(i=rt.buildFragment([e],t,r),r&&r.length&&rt(r).remove(),rt.merge([],i.childNodes))};var ii=rt.fn.load;rt.fn.load=function(e,t,n){if("string"!=typeof e&&ii)return ii.apply(this,arguments);var i,r,o,a=this,s=e.indexOf(" ");return s>=0&&(i=rt.trim(e.slice(s,e.length)),e=e.slice(0,s)),rt.isFunction(t)?(n=t,t=void 0):t&&"object"==typeof t&&(o="POST"),a.length>0&&rt.ajax({url:e,type:o,dataType:"html",data:t}).done(function(e){r=arguments,a.html(i?rt("<div>").append(rt.parseHTML(e)).find(i):e)}).complete(n&&function(e,t){a.each(n,r||[e.responseText,t,e])}),this},rt.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){rt.fn[t]=function(e){return this.on(t,e)}}),rt.expr.filters.animated=function(e){return rt.grep(rt.timers,function(t){return e===t.elem}).length};var ri=e.document.documentElement;rt.offset={setOffset:function(e,t,n){var i,r,o,a,s,l,c,u=rt.css(e,"position"),d=rt(e),p={};"static"===u&&(e.style.position="relative"),s=d.offset(),o=rt.css(e,"top"),l=rt.css(e,"left"),c=("absolute"===u||"fixed"===u)&&rt.inArray("auto",[o,l])>-1,c?(i=d.position(),a=i.top,r=i.left):(a=parseFloat(o)||0,r=parseFloat(l)||0),rt.isFunction(t)&&(t=t.call(e,n,s)),null!=t.top&&(p.top=t.top-s.top+a),null!=t.left&&(p.left=t.left-s.left+r),"using"in t?t.using.call(e,p):d.css(p)}},rt.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(t){rt.offset.setOffset(this,e,t)});var t,n,i={top:0,left:0},r=this[0],o=r&&r.ownerDocument;if(o)return t=o.documentElement,rt.contains(t,r)?(typeof r.getBoundingClientRect!==Ct&&(i=r.getBoundingClientRect()),n=Z(o),{top:i.top+(n.pageYOffset||t.scrollTop)-(t.clientTop||0),left:i.left+(n.pageXOffset||t.scrollLeft)-(t.clientLeft||0)}):i},position:function(){if(this[0]){var e,t,n={top:0,left:0},i=this[0];return"fixed"===rt.css(i,"position")?t=i.getBoundingClientRect():(e=this.offsetParent(),t=this.offset(),rt.nodeName(e[0],"html")||(n=e.offset()),n.top+=rt.css(e[0],"borderTopWidth",!0),n.left+=rt.css(e[0],"borderLeftWidth",!0)),{top:t.top-n.top-rt.css(i,"marginTop",!0),left:t.left-n.left-rt.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent||ri;e&&!rt.nodeName(e,"html")&&"static"===rt.css(e,"position");)e=e.offsetParent;return e||ri})}}),rt.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var n=/Y/.test(t);rt.fn[e]=function(i){return Dt(this,function(e,i,r){var o=Z(e);return void 0===r?o?t in o?o[t]:o.document.documentElement[i]:e[i]:void(o?o.scrollTo(n?rt(o).scrollLeft():r,n?r:rt(o).scrollTop()):e[i]=r)},e,i,arguments.length,null)}}),rt.each(["top","left"],function(e,t){rt.cssHooks[t]=S(nt.pixelPosition,function(e,n){return n?(n=tn(e,t),rn.test(n)?rt(e).position()[t]+"px":n):void 0})}),rt.each({Height:"height",Width:"width"},function(e,t){rt.each({padding:"inner"+e,content:t,"":"outer"+e},function(n,i){rt.fn[i]=function(i,r){var o=arguments.length&&(n||"boolean"!=typeof i),a=n||(i===!0||r===!0?"margin":"border");return Dt(this,function(t,n,i){var r;return rt.isWindow(t)?t.document.documentElement["client"+e]:9===t.nodeType?(r=t.documentElement,Math.max(t.body["scroll"+e],r["scroll"+e],t.body["offset"+e],r["offset"+e],r["client"+e])):void 0===i?rt.css(t,n,a):rt.style(t,n,i,a)},t,o?i:void 0,o,null)}})}),rt.fn.size=function(){return this.length},rt.fn.andSelf=rt.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return rt});var oi=e.jQuery,ai=e.$;return rt.noConflict=function(t){return e.$===rt&&(e.$=ai),t&&e.jQuery===rt&&(e.jQuery=oi),rt},typeof t===Ct&&(e.jQuery=e.$=rt),rt}),function(e,t){e.rails!==t&&e.error("jquery-ujs has already been loaded!");var n,i=e(document);e.rails=n={linkClickSelector:"a[data-confirm], a[data-method], a[data-remote], a[data-disable-with], a[data-disable]",buttonClickSelector:"button[data-remote]:not(form button), button[data-confirm]:not(form button)",inputChangeSelector:"select[data-remote], input[data-remote], textarea[data-remote]",formSubmitSelector:"form",formInputClickSelector:"form input[type=submit], form input[type=image], form button[type=submit], form button:not([type]), input[type=submit][form], input[type=image][form], button[type=submit][form], button[form]:not([type])",disableSelector:"input[data-disable-with]:enabled, button[data-disable-with]:enabled, textarea[data-disable-with]:enabled, input[data-disable]:enabled, button[data-disable]:enabled, textarea[data-disable]:enabled",enableSelector:"input[data-disable-with]:disabled, button[data-disable-with]:disabled, textarea[data-disable-with]:disabled, input[data-disable]:disabled, button[data-disable]:disabled, textarea[data-disable]:disabled",requiredInputSelector:"input[name][required]:not([disabled]),textarea[name][required]:not([disabled])",fileInputSelector:"input[type=file]",linkDisableSelector:"a[data-disable-with], a[data-disable]",buttonDisableSelector:"button[data-remote][data-disable-with], button[data-remote][data-disable]",CSRFProtection:function(t){var n=e('meta[name="csrf-token"]').attr("content");n&&t.setRequestHeader("X-CSRF-Token",n)},refreshCSRFTokens:function(){var t=e("meta[name=csrf-token]").attr("content"),n=e("meta[name=csrf-param]").attr("content");e('form input[name="'+n+'"]').val(t)},fire:function(t,n,i){var r=e.Event(n);return t.trigger(r,i),r.result!==!1},confirm:function(e){return confirm(e)},ajax:function(t){return e.ajax(t)},href:function(e){return e[0].href},handleRemote:function(i){var r,o,a,s,l,c;if(n.fire(i,"ajax:before")){if(s=i.data("with-credentials")||null,l=i.data("type")||e.ajaxSettings&&e.ajaxSettings.dataType,i.is("form")){r=i.attr("method"),o=i.attr("action"),a=i.serializeArray();var u=i.data("ujs:submit-button");u&&(a.push(u),i.data("ujs:submit-button",null))}else i.is(n.inputChangeSelector)?(r=i.data("method"),o=i.data("url"),a=i.serialize(),i.data("params")&&(a=a+"&"+i.data("params"))):i.is(n.buttonClickSelector)?(r=i.data("method")||"get",o=i.data("url"),a=i.serialize(),i.data("params")&&(a=a+"&"+i.data("params"))):(r=i.data("method"),o=n.href(i),a=i.data("params")||null);return c={type:r||"GET",data:a,dataType:l,beforeSend:function(e,r){return r.dataType===t&&e.setRequestHeader("accept","*/*;q=0.5, "+r.accepts.script),n.fire(i,"ajax:beforeSend",[e,r])?void i.trigger("ajax:send",e):!1},success:function(e,t,n){i.trigger("ajax:success",[e,t,n])},complete:function(e,t){i.trigger("ajax:complete",[e,t])},error:function(e,t,n){i.trigger("ajax:error",[e,t,n])},crossDomain:n.isCrossDomain(o)},s&&(c.xhrFields={withCredentials:s}),o&&(c.url=o),n.ajax(c)}return!1},isCrossDomain:function(e){var t=document.createElement("a");t.href=location.href;var n=document.createElement("a");try{return n.href=e,n.href=n.href,!n.protocol||!n.host||t.protocol+"//"+t.host!=n.protocol+"//"+n.host}catch(i){return!0}},handleMethod:function(i){var r=n.href(i),o=i.data("method"),a=i.attr("target"),s=e("meta[name=csrf-token]").attr("content"),l=e("meta[name=csrf-param]").attr("content"),c=e('<form method="post" action="'+r+'"></form>'),u='<input name="_method" value="'+o+'" type="hidden" />';l===t||s===t||n.isCrossDomain(r)||(u+='<input name="'+l+'" value="'+s+'" type="hidden" />'),a&&c.attr("target",a),c.hide().append(u).appendTo("body"),c.submit()},formElements:function(t,n){return t.is("form")?e(t[0].elements).filter(n):t.find(n)},disableFormElements:function(t){n.formElements(t,n.disableSelector).each(function(){n.disableFormElement(e(this))})},disableFormElement:function(e){var n,i;n=e.is("button")?"html":"val",i=e.data("disable-with"),e.data("ujs:enable-with",e[n]()),i!==t&&e[n](i),e.prop("disabled",!0)},enableFormElements:function(t){n.formElements(t,n.enableSelector).each(function(){n.enableFormElement(e(this))})},enableFormElement:function(e){var t=e.is("button")?"html":"val";e.data("ujs:enable-with")&&e[t](e.data("ujs:enable-with")),e.prop("disabled",!1)},allowAction:function(e){var t,i=e.data("confirm"),r=!1;return i?(n.fire(e,"confirm")&&(r=n.confirm(i),t=n.fire(e,"confirm:complete",[r])),r&&t):!0},blankInputs:function(t,n,i){var r,o,a=e(),s=n||"input,textarea",l=t.find(s);return l.each(function(){if(r=e(this),o=r.is("input[type=checkbox],input[type=radio]")?r.is(":checked"):r.val(),!o==!i){if(r.is("input[type=radio]")&&l.filter('input[type=radio]:checked[name="'+r.attr("name")+'"]').length)return!0;a=a.add(r)}}),a.length?a:!1},nonBlankInputs:function(e,t){return n.blankInputs(e,t,!0)},stopEverything:function(t){return e(t.target).trigger("ujs:everythingStopped"),t.stopImmediatePropagation(),!1},disableElement:function(e){var i=e.data("disable-with");e.data("ujs:enable-with",e.html()),i!==t&&e.html(i),e.bind("click.railsDisable",function(e){return n.stopEverything(e)})},enableElement:function(e){e.data("ujs:enable-with")!==t&&(e.html(e.data("ujs:enable-with")),e.removeData("ujs:enable-with")),e.unbind("click.railsDisable")}},n.fire(i,"rails:attachBindings")&&(e.ajaxPrefilter(function(e,t,i){e.crossDomain||n.CSRFProtection(i)}),e(window).on("pageshow.rails",function(){e(e.rails.enableSelector).each(function(){var t=e(this);t.data("ujs:enable-with")&&e.rails.enableFormElement(t)}),e(e.rails.linkDisableSelector).each(function(){var t=e(this);t.data("ujs:enable-with")&&e.rails.enableElement(t)})}),i.delegate(n.linkDisableSelector,"ajax:complete",function(){n.enableElement(e(this))}),i.delegate(n.buttonDisableSelector,"ajax:complete",function(){n.enableFormElement(e(this))}),i.delegate(n.linkClickSelector,"click.rails",function(i){var r=e(this),o=r.data("method"),a=r.data("params"),s=i.metaKey||i.ctrlKey;if(!n.allowAction(r))return n.stopEverything(i);if(!s&&r.is(n.linkDisableSelector)&&n.disableElement(r),r.data("remote")!==t){if(s&&(!o||"GET"===o)&&!a)return!0;var l=n.handleRemote(r);return l===!1?n.enableElement(r):l.fail(function(){n.enableElement(r)}),!1}return o?(n.handleMethod(r),!1):void 0}),i.delegate(n.buttonClickSelector,"click.rails",function(t){var i=e(this);if(!n.allowAction(i))return n.stopEverything(t);i.is(n.buttonDisableSelector)&&n.disableFormElement(i);var r=n.handleRemote(i);return r===!1?n.enableFormElement(i):r.fail(function(){n.enableFormElement(i)}),!1}),i.delegate(n.inputChangeSelector,"change.rails",function(t){var i=e(this);return n.allowAction(i)?(n.handleRemote(i),!1):n.stopEverything(t)}),i.delegate(n.formSubmitSelector,"submit.rails",function(i){var r,o,a=e(this),s=a.data("remote")!==t;if(!n.allowAction(a))return n.stopEverything(i);if(a.attr("novalidate")==t&&(r=n.blankInputs(a,n.requiredInputSelector),r&&n.fire(a,"ajax:aborted:required",[r])))return n.stopEverything(i);if(s){if(o=n.nonBlankInputs(a,n.fileInputSelector)){setTimeout(function(){n.disableFormElements(a)},13);var l=n.fire(a,"ajax:aborted:file",[o]);return l||setTimeout(function(){n.enableFormElements(a)},13),l}return n.handleRemote(a),!1}setTimeout(function(){n.disableFormElements(a)},13)}),i.delegate(n.formInputClickSelector,"click.rails",function(t){var i=e(this);if(!n.allowAction(i))return n.stopEverything(t);var r=i.attr("name"),o=r?{name:r,value:i.val()}:null;i.closest("form").data("ujs:submit-button",o)}),i.delegate(n.formSubmitSelector,"ajax:send.rails",function(t){this==t.target&&n.disableFormElements(e(this))}),i.delegate(n.formSubmitSelector,"ajax:complete.rails",function(t){this==t.target&&n.enableFormElements(e(this))}),e(function(){n.refreshCSRFTokens()}))}(jQuery),/*!
 * Modernizr v2.8.3
 * www.modernizr.com
 *
 * Copyright (c) Faruk Ates, Paul Irish, Alex Sexton
 * Available under the BSD and MIT licenses: www.modernizr.com/license/
 */
window.Modernizr=function(e,t,n){function i(e){b.cssText=e}function r(e,t){return i(C.join(e+";")+(t||""))}function o(e,t){return typeof e===t}function a(e,t){return!!~(""+e).indexOf(t)}function s(e,t){for(var i in e){var r=e[i];if(!a(r,"-")&&b[r]!==n)return"pfx"==t?r:!0}return!1}function l(e,t,i){for(var r in e){var a=t[e[r]];if(a!==n)return i===!1?e[r]:o(a,"function")?a.bind(i||t):a}return!1}function c(e,t,n){var i=e.charAt(0).toUpperCase()+e.slice(1),r=(e+" "+T.join(i+" ")+i).split(" ");return o(t,"string")||o(t,"undefined")?s(r,t):(r=(e+" "+S.join(i+" ")+i).split(" "),l(r,t,n))}function u(){h.input=function(n){for(var i=0,r=n.length;r>i;i++)L[n[i]]=!!(n[i]in _);return L.list&&(L.list=!(!t.createElement("datalist")||!e.HTMLDataListElement)),L}("autocomplete autofocus list placeholder max min multiple pattern required step".split(" ")),h.inputtypes=function(e){for(var i,r,o,a=0,s=e.length;s>a;a++)_.setAttribute("type",r=e[a]),i="text"!==_.type,i&&(_.value=w,_.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(r)&&_.style.WebkitAppearance!==n?(g.appendChild(_),o=t.defaultView,i=o.getComputedStyle&&"textfield"!==o.getComputedStyle(_,null).WebkitAppearance&&0!==_.offsetHeight,g.removeChild(_)):/^(search|tel)$/.test(r)||(i=/^(url|email)$/.test(r)?_.checkValidity&&_.checkValidity()===!1:_.value!=w)),D[e[a]]=!!i;return D}("search tel url email datetime date month week time datetime-local number range color".split(" "))}var d,p,f="2.8.3",h={},m=!0,g=t.documentElement,v="modernizr",y=t.createElement(v),b=y.style,_=t.createElement("input"),w=":)",x={}.toString,C=" -webkit- -moz- -o- -ms- ".split(" "),k="Webkit Moz O ms",T=k.split(" "),S=k.toLowerCase().split(" "),$={svg:"http://www.w3.org/2000/svg"},E={},D={},L={},I=[],A=I.slice,F=function(e,n,i,r){var o,a,s,l,c=t.createElement("div"),u=t.body,d=u||t.createElement("body");if(parseInt(i,10))for(;i--;)s=t.createElement("div"),s.id=r?r[i]:v+(i+1),c.appendChild(s);return o=["&#173;",'<style id="s',v,'">',e,"</style>"].join(""),c.id=v,(u?c:d).innerHTML+=o,d.appendChild(c),u||(d.style.background="",d.style.overflow="hidden",l=g.style.overflow,g.style.overflow="hidden",g.appendChild(d)),a=n(c,e),u?c.parentNode.removeChild(c):(d.parentNode.removeChild(d),g.style.overflow=l),!!a},N=function(t){var n=e.matchMedia||e.msMatchMedia;if(n)return n(t)&&n(t).matches||!1;var i;return F("@media "+t+" { #"+v+" { position: absolute; } }",function(t){i="absolute"==(e.getComputedStyle?getComputedStyle(t,null):t.currentStyle).position}),i},P=function(){function e(e,r){r=r||t.createElement(i[e]||"div"),e="on"+e;var a=e in r;return a||(r.setAttribute||(r=t.createElement("div")),r.setAttribute&&r.removeAttribute&&(r.setAttribute(e,""),a=o(r[e],"function"),o(r[e],"undefined")||(r[e]=n),r.removeAttribute(e))),r=null,a}var i={select:"input",change:"input",submit:"form",reset:"form",error:"img",load:"img",abort:"img"};return e}(),R={}.hasOwnProperty;p=o(R,"undefined")||o(R.call,"undefined")?function(e,t){return t in e&&o(e.constructor.prototype[t],"undefined")}:function(e,t){return R.call(e,t)},Function.prototype.bind||(Function.prototype.bind=function(e){var t=this;if("function"!=typeof t)throw new TypeError;var n=A.call(arguments,1),i=function(){if(this instanceof i){var r=function(){};r.prototype=t.prototype;var o=new r,a=t.apply(o,n.concat(A.call(arguments)));return Object(a)===a?a:o}return t.apply(e,n.concat(A.call(arguments)))};return i}),E.flexbox=function(){return c("flexWrap")},E.flexboxlegacy=function(){return c("boxDirection")},E.canvas=function(){var e=t.createElement("canvas");return!(!e.getContext||!e.getContext("2d"))},E.canvastext=function(){return!(!h.canvas||!o(t.createElement("canvas").getContext("2d").fillText,"function"))},E.webgl=function(){return!!e.WebGLRenderingContext},E.touch=function(){var n;return"ontouchstart"in e||e.DocumentTouch&&t instanceof DocumentTouch?n=!0:F(["@media (",C.join("touch-enabled),("),v,")","{#modernizr{top:9px;position:absolute}}"].join(""),function(e){n=9===e.offsetTop}),n},E.geolocation=function(){return"geolocation"in navigator},E.postmessage=function(){return!!e.postMessage},E.websqldatabase=function(){return!!e.openDatabase},E.indexedDB=function(){return!!c("indexedDB",e)},E.hashchange=function(){return P("hashchange",e)&&(t.documentMode===n||t.documentMode>7)},E.history=function(){return!(!e.history||!history.pushState)},E.draganddrop=function(){var e=t.createElement("div");return"draggable"in e||"ondragstart"in e&&"ondrop"in e},E.websockets=function(){return"WebSocket"in e||"MozWebSocket"in e},E.rgba=function(){return i("background-color:rgba(150,255,150,.5)"),a(b.backgroundColor,"rgba")},E.hsla=function(){return i("background-color:hsla(120,40%,100%,.5)"),a(b.backgroundColor,"rgba")||a(b.backgroundColor,"hsla")},E.multiplebgs=function(){return i("background:url(https://),url(https://),red url(https://)"),/(url\s*\(.*?){3}/.test(b.background)},E.backgroundsize=function(){return c("backgroundSize")},E.borderimage=function(){return c("borderImage")},E.borderradius=function(){return c("borderRadius")},E.boxshadow=function(){return c("boxShadow")},E.textshadow=function(){return""===t.createElement("div").style.textShadow},E.opacity=function(){return r("opacity:.55"),/^0.55$/.test(b.opacity)},E.cssanimations=function(){return c("animationName")},E.csscolumns=function(){return c("columnCount")},E.cssgradients=function(){var e="background-image:",t="gradient(linear,left top,right bottom,from(#9f9),to(white));",n="linear-gradient(left top,#9f9, white);";return i((e+"-webkit- ".split(" ").join(t+e)+C.join(n+e)).slice(0,-e.length)),a(b.backgroundImage,"gradient")},E.cssreflections=function(){return c("boxReflect")},E.csstransforms=function(){return!!c("transform")},E.csstransforms3d=function(){var e=!!c("perspective");return e&&"webkitPerspective"in g.style&&F("@media (transform-3d),(-webkit-transform-3d){#modernizr{left:9px;position:absolute;height:3px;}}",function(t){e=9===t.offsetLeft&&3===t.offsetHeight}),e},E.csstransitions=function(){return c("transition")},E.fontface=function(){var e;return F('@font-face {font-family:"font";src:url("https://")}',function(n,i){var r=t.getElementById("smodernizr"),o=r.sheet||r.styleSheet,a=o?o.cssRules&&o.cssRules[0]?o.cssRules[0].cssText:o.cssText||"":"";e=/src/i.test(a)&&0===a.indexOf(i.split(" ")[0])}),e},E.generatedcontent=function(){var e;return F(["#",v,"{font:0/0 a}#",v,':after{content:"',w,'";visibility:hidden;font:3px/1 a}'].join(""),function(t){e=t.offsetHeight>=3}),e},E.video=function(){var e=t.createElement("video"),n=!1;try{(n=!!e.canPlayType)&&(n=new Boolean(n),n.ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),n.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),n.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,""))}catch(i){}return n},E.audio=function(){var e=t.createElement("audio"),n=!1;try{(n=!!e.canPlayType)&&(n=new Boolean(n),n.ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),n.mp3=e.canPlayType("audio/mpeg;").replace(/^no$/,""),n.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),n.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,""))}catch(i){}return n},E.localstorage=function(){try{return localStorage.setItem(v,v),localStorage.removeItem(v),!0}catch(e){return!1}},E.sessionstorage=function(){try{return sessionStorage.setItem(v,v),sessionStorage.removeItem(v),!0}catch(e){return!1}},E.webworkers=function(){return!!e.Worker},E.applicationcache=function(){return!!e.applicationCache},E.svg=function(){return!!t.createElementNS&&!!t.createElementNS($.svg,"svg").createSVGRect},E.inlinesvg=function(){var e=t.createElement("div");return e.innerHTML="<svg/>",(e.firstChild&&e.firstChild.namespaceURI)==$.svg},E.smil=function(){return!!t.createElementNS&&/SVGAnimate/.test(x.call(t.createElementNS($.svg,"animate")))},E.svgclippaths=function(){return!!t.createElementNS&&/SVGClipPath/.test(x.call(t.createElementNS($.svg,"clipPath")))};for(var M in E)p(E,M)&&(d=M.toLowerCase(),h[d]=E[M](),I.push((h[d]?"":"no-")+d));return h.input||u(),h.addTest=function(e,t){if("object"==typeof e)for(var i in e)p(e,i)&&h.addTest(i,e[i]);else{if(e=e.toLowerCase(),h[e]!==n)return h;t="function"==typeof t?t():t,"undefined"!=typeof m&&m&&(g.className+=" "+(t?"":"no-")+e),h[e]=t}return h},i(""),y=_=null,function(e,t){function n(e,t){var n=e.createElement("p"),i=e.getElementsByTagName("head")[0]||e.documentElement;return n.innerHTML="x<style>"+t+"</style>",i.insertBefore(n.lastChild,i.firstChild)}function i(){var e=y.elements;return"string"==typeof e?e.split(" "):e}function r(e){var t=v[e[m]];return t||(t={},g++,e[m]=g,v[g]=t),t}function o(e,n,i){if(n||(n=t),u)return n.createElement(e);i||(i=r(n));var o;return o=i.cache[e]?i.cache[e].cloneNode():h.test(e)?(i.cache[e]=i.createElem(e)).cloneNode():i.createElem(e),!o.canHaveChildren||f.test(e)||o.tagUrn?o:i.frag.appendChild(o)}function a(e,n){if(e||(e=t),u)return e.createDocumentFragment();n=n||r(e);for(var o=n.frag.cloneNode(),a=0,s=i(),l=s.length;l>a;a++)o.createElement(s[a]);return o}function s(e,t){t.cache||(t.cache={},t.createElem=e.createElement,t.createFrag=e.createDocumentFragment,t.frag=t.createFrag()),e.createElement=function(n){return y.shivMethods?o(n,e,t):t.createElem(n)},e.createDocumentFragment=Function("h,f","return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&("+i().join().replace(/[\w\-]+/g,function(e){return t.createElem(e),t.frag.createElement(e),'c("'+e+'")'})+");return n}")(y,t.frag)}function l(e){e||(e=t);var i=r(e);return!y.shivCSS||c||i.hasCSS||(i.hasCSS=!!n(e,"article,aside,dialog,figcaption,figure,footer,header,hgroup,main,nav,section{display:block}mark{background:#FF0;color:#000}template{display:none}")),u||s(e,i),e}var c,u,d="3.7.0",p=e.html5||{},f=/^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,h=/^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,m="_html5shiv",g=0,v={};!function(){try{var e=t.createElement("a");e.innerHTML="<xyz></xyz>",c="hidden"in e,u=1==e.childNodes.length||function(){t.createElement("a");var e=t.createDocumentFragment();return"undefined"==typeof e.cloneNode||"undefined"==typeof e.createDocumentFragment||"undefined"==typeof e.createElement}()}catch(n){c=!0,u=!0}}();var y={elements:p.elements||"abbr article aside audio bdi canvas data datalist details dialog figcaption figure footer header hgroup main mark meter nav output progress section summary template time video",version:d,shivCSS:p.shivCSS!==!1,supportsUnknownElements:u,shivMethods:p.shivMethods!==!1,type:"default",shivDocument:l,createElement:o,createDocumentFragment:a};e.html5=y,l(t)}(this,t),h._version=f,h._prefixes=C,h._domPrefixes=S,h._cssomPrefixes=T,h.mq=N,h.hasEvent=P,h.testProp=function(e){return s([e])},h.testAllProps=c,h.testStyles=F,h.prefixed=function(e,t,n){return t?c(e,t,n):c(e,"pfx")},g.className=g.className.replace(/(^|\s)no-js(\s|$)/,"$1$2")+(m?" js "+I.join(" "):""),h}(this,this.document),/*
 * Foundation Responsive Library
 * http://foundation.zurb.com
 * Copyright 2014, ZURB
 * Free to use under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
*/
function(e,t,n,i){"use strict";function r(e){return("string"==typeof e||e instanceof String)&&(e=e.replace(/^['\\/"]+|(;\s?})+|['\\/"]+$/g,"")),e}var o=function(t){for(var n=t.length,i=e("head");n--;)0===i.has("."+t[n]).length&&i.append('<meta class="'+t[n]+'" />')};o(["foundation-mq-small","foundation-mq-small-only","foundation-mq-medium","foundation-mq-medium-only","foundation-mq-large","foundation-mq-large-only","foundation-mq-xlarge","foundation-mq-xlarge-only","foundation-mq-xxlarge","foundation-data-attribute-namespace"]),e(function(){"undefined"!=typeof FastClick&&"undefined"!=typeof n.body&&FastClick.attach(n.body)});var a=function(t,i){if("string"==typeof t){if(i){var r;if(i.jquery){if(r=i[0],!r)return i}else r=i;return e(r.querySelectorAll(t))}return e(n.querySelectorAll(t))}return e(t,i)},s=function(e){var t=[];return e||t.push("data"),this.namespace.length>0&&t.push(this.namespace),t.push(this.name),t.join("-")},l=function(e){for(var t=e.split("-"),n=t.length,i=[];n--;)0!==n?i.push(t[n]):this.namespace.length>0?i.push(this.namespace,t[n]):i.push(t[n]);return i.reverse().join("-")},c=function(t,n){var i=this,r=function(){var r=a(this),o=!r.data(i.attr_name(!0)+"-init");r.data(i.attr_name(!0)+"-init",e.extend({},i.settings,n||t,i.data_options(r))),o&&i.events(this)};return a(this.scope).is("["+this.attr_name()+"]")?r.call(this.scope):a("["+this.attr_name()+"]",this.scope).each(r),"string"==typeof t?this[t].call(this,n):void 0},u=function(e,t){function n(){t(e[0])}function i(){if(this.one("load",n),/MSIE (\d+\.\d+);/.test(navigator.userAgent)){var e=this.attr("src"),t=e.match(/\?/)?"&":"?";t+="random="+(new Date).getTime(),this.attr("src",e+t)}}return e.attr("src")?void(e[0].complete||4===e[0].readyState?n():i.call(e)):void n()};/*! matchMedia() polyfill - Test a CSS media type/query in JS. Authors & copyright (c) 2012: Scott Jehl, Paul Irish, Nicholas Zakas, David Knight. Dual MIT/BSD license */
t.matchMedia||(t.matchMedia=function(){var e=t.styleMedia||t.media;if(!e){var i=n.createElement("style"),r=n.getElementsByTagName("script")[0],o=null;i.type="text/css",i.id="matchmediajs-test",r.parentNode.insertBefore(i,r),o="getComputedStyle"in t&&t.getComputedStyle(i,null)||i.currentStyle,e={matchMedium:function(e){var t="@media "+e+"{ #matchmediajs-test { width: 1px; } }";return i.styleSheet?i.styleSheet.cssText=t:i.textContent=t,"1px"===o.width}}}return function(t){return{matches:e.matchMedium(t||"all"),media:t||"all"}}}()),/*
   * jquery.requestAnimationFrame
   * https://github.com/gnarf37/jquery-requestAnimationFrame
   * Requires jQuery 1.8+
   *
   * Copyright (c) 2012 Corey Frang
   * Licensed under the MIT license.
   */
function(e){function n(){i&&(a(n),l&&e.fx.tick())}for(var i,r=0,o=["webkit","moz"],a=t.requestAnimationFrame,s=t.cancelAnimationFrame,l="undefined"!=typeof e.fx;r<o.length&&!a;r++)a=t[o[r]+"RequestAnimationFrame"],s=s||t[o[r]+"CancelAnimationFrame"]||t[o[r]+"CancelRequestAnimationFrame"];a?(t.requestAnimationFrame=a,t.cancelAnimationFrame=s,l&&(e.fx.timer=function(t){t()&&e.timers.push(t)&&!i&&(i=!0,n())},e.fx.stop=function(){i=!1})):(t.requestAnimationFrame=function(e){var n=(new Date).getTime(),i=Math.max(0,16-(n-r)),o=t.setTimeout(function(){e(n+i)},i);return r=n+i,o},t.cancelAnimationFrame=function(e){clearTimeout(e)})}(e),t.Foundation={name:"Foundation",version:"5.5.2",media_queries:{small:a(".foundation-mq-small").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"small-only":a(".foundation-mq-small-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),medium:a(".foundation-mq-medium").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"medium-only":a(".foundation-mq-medium-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),large:a(".foundation-mq-large").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"large-only":a(".foundation-mq-large-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),xlarge:a(".foundation-mq-xlarge").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"xlarge-only":a(".foundation-mq-xlarge-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),xxlarge:a(".foundation-mq-xxlarge").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,"")},stylesheet:e("<style></style>").appendTo("head")[0].sheet,global:{namespace:""},init:function(e,n,i,r,o){var s=[e,i,r,o],l=[];if(this.rtl=/rtl/i.test(a("html").attr("dir")),this.scope=e||this.scope,this.set_namespace(),n&&"string"==typeof n&&!/reflow/i.test(n))this.libs.hasOwnProperty(n)&&l.push(this.init_lib(n,s));else for(var c in this.libs)l.push(this.init_lib(c,n));return a(t).load(function(){a(t).trigger("resize.fndtn.clearing").trigger("resize.fndtn.dropdown").trigger("resize.fndtn.equalizer").trigger("resize.fndtn.interchange").trigger("resize.fndtn.joyride").trigger("resize.fndtn.magellan").trigger("resize.fndtn.topbar").trigger("resize.fndtn.slider")}),e},init_lib:function(t,n){return this.libs.hasOwnProperty(t)?(this.patch(this.libs[t]),n&&n.hasOwnProperty(t)?("undefined"!=typeof this.libs[t].settings?e.extend(!0,this.libs[t].settings,n[t]):"undefined"!=typeof this.libs[t].defaults&&e.extend(!0,this.libs[t].defaults,n[t]),this.libs[t].init.apply(this.libs[t],[this.scope,n[t]])):(n=n instanceof Array?n:new Array(n),this.libs[t].init.apply(this.libs[t],n))):function(){}},patch:function(e){e.scope=this.scope,e.namespace=this.global.namespace,e.rtl=this.rtl,e.data_options=this.utils.data_options,e.attr_name=s,e.add_namespace=l,e.bindings=c,e.S=this.utils.S},inherit:function(e,t){for(var n=t.split(" "),i=n.length;i--;)this.utils.hasOwnProperty(n[i])&&(e[n[i]]=this.utils[n[i]])},set_namespace:function(){var t=this.global.namespace===i?e(".foundation-data-attribute-namespace").css("font-family"):this.global.namespace;this.global.namespace=t===i||/false/i.test(t)?"":t},libs:{},utils:{S:a,throttle:function(e,t){var n=null;return function(){var i=this,r=arguments;null==n&&(n=setTimeout(function(){e.apply(i,r),n=null},t))}},debounce:function(e,t,n){var i,r;return function(){var o=this,a=arguments,s=function(){i=null,n||(r=e.apply(o,a))},l=n&&!i;return clearTimeout(i),i=setTimeout(s,t),l&&(r=e.apply(o,a)),r}},data_options:function(t,n){function i(e){return!isNaN(e-0)&&null!==e&&""!==e&&e!==!1&&e!==!0}function r(t){return"string"==typeof t?e.trim(t):t}n=n||"options";var o,a,s,l={},c=function(e){var t=Foundation.global.namespace;return e.data(t.length>0?t+"-"+n:n)},u=c(t);if("object"==typeof u)return u;for(s=(u||":").split(";"),o=s.length;o--;)a=s[o].split(":"),a=[a[0],a.slice(1).join(":")],/true/i.test(a[1])&&(a[1]=!0),/false/i.test(a[1])&&(a[1]=!1),i(a[1])&&(a[1]=-1===a[1].indexOf(".")?parseInt(a[1],10):parseFloat(a[1])),2===a.length&&a[0].length>0&&(l[r(a[0])]=r(a[1]));return l},register_media:function(t,n){Foundation.media_queries[t]===i&&(e("head").append('<meta class="'+n+'"/>'),Foundation.media_queries[t]=r(e("."+n).css("font-family")))},add_custom_rule:function(e,t){if(t===i&&Foundation.stylesheet)Foundation.stylesheet.insertRule(e,Foundation.stylesheet.cssRules.length);else{var n=Foundation.media_queries[t];n!==i&&Foundation.stylesheet.insertRule("@media "+Foundation.media_queries[t]+"{ "+e+" }",Foundation.stylesheet.cssRules.length)}},image_loaded:function(e,t){function n(e){for(var t=e.length,n=t-1;n>=0;n--)if(e.attr("height")===i)return!1;return!0}var r=this,o=e.length;(0===o||n(e))&&t(e),e.each(function(){u(r.S(this),function(){o-=1,0===o&&t(e)})})},random_str:function(){return this.fidx||(this.fidx=0),this.prefix=this.prefix||[this.name||"F",(+new Date).toString(36)].join("-"),this.prefix+(this.fidx++).toString(36)},match:function(e){return t.matchMedia(e).matches},is_small_up:function(){return this.match(Foundation.media_queries.small)},is_medium_up:function(){return this.match(Foundation.media_queries.medium)},is_large_up:function(){return this.match(Foundation.media_queries.large)},is_xlarge_up:function(){return this.match(Foundation.media_queries.xlarge)},is_xxlarge_up:function(){return this.match(Foundation.media_queries.xxlarge)},is_small_only:function(){return!(this.is_medium_up()||this.is_large_up()||this.is_xlarge_up()||this.is_xxlarge_up())},is_medium_only:function(){return this.is_medium_up()&&!this.is_large_up()&&!this.is_xlarge_up()&&!this.is_xxlarge_up()},is_large_only:function(){return this.is_medium_up()&&this.is_large_up()&&!this.is_xlarge_up()&&!this.is_xxlarge_up()},is_xlarge_only:function(){return this.is_medium_up()&&this.is_large_up()&&this.is_xlarge_up()&&!this.is_xxlarge_up()},is_xxlarge_only:function(){return this.is_medium_up()&&this.is_large_up()&&this.is_xlarge_up()&&this.is_xxlarge_up()}}},e.fn.foundation=function(){var e=Array.prototype.slice.call(arguments,0);return this.each(function(){return Foundation.init.apply(Foundation,[this].concat(e)),this})}}(jQuery,window,window.document),function(e,t,n,i){"use strict";var r=function(){},o=function(r,o){if(r.hasClass(o.slides_container_class))return this;var c,u,d,p,f,h,m=this,g=r,v=0,y=!1;m.slides=function(){return g.children(o.slide_selector)},m.slides().first().addClass(o.active_slide_class),m.update_slide_number=function(t){o.slide_number&&(u.find("span:first").text(parseInt(t)+1),u.find("span:last").text(m.slides().length)),o.bullets&&(d.children().removeClass(o.bullets_active_class),e(d.children().get(t)).addClass(o.bullets_active_class))},m.update_active_link=function(t){var n=e('[data-orbit-link="'+m.slides().eq(t).attr("data-orbit-slide")+'"]');n.siblings().removeClass(o.bullets_active_class),n.addClass(o.bullets_active_class)},m.build_markup=function(){g.wrap('<div class="'+o.container_class+'"></div>'),c=g.parent(),g.addClass(o.slides_container_class),o.stack_on_small&&c.addClass(o.stack_on_small_class),o.navigation_arrows&&(c.append(e('<a href="#"><span></span></a>').addClass(o.prev_class)),c.append(e('<a href="#"><span></span></a>').addClass(o.next_class))),o.timer&&(p=e("<div>").addClass(o.timer_container_class),p.append("<span>"),p.append(e("<div>").addClass(o.timer_progress_class)),p.addClass(o.timer_paused_class),c.append(p)),o.slide_number&&(u=e("<div>").addClass(o.slide_number_class),u.append("<span></span> "+o.slide_number_text+" <span></span>"),c.append(u)),o.bullets&&(d=e("<ol>").addClass(o.bullets_container_class),c.append(d),d.wrap('<div class="orbit-bullets-container"></div>'),m.slides().each(function(t){var n=e("<li>").attr("data-orbit-slide",t).on("click",m.link_bullet);d.append(n)}))},m._goto=function(t,n){if(t===v)return!1;"object"==typeof h&&h.restart();var i=m.slides(),r="next";if(y=!0,v>t&&(r="prev"),t>=i.length){if(!o.circular)return!1;t=0}else if(0>t){if(!o.circular)return!1;t=i.length-1}var a=e(i.get(v)),s=e(i.get(t));a.css("zIndex",2),a.removeClass(o.active_slide_class),s.css("zIndex",4).addClass(o.active_slide_class),g.trigger("before-slide-change.fndtn.orbit"),o.before_slide_change(),m.update_active_link(t);var l=function(){var e=function(){v=t,y=!1,n===!0&&(h=m.create_timer(),h.start()),m.update_slide_number(v),g.trigger("after-slide-change.fndtn.orbit",[{slide_number:v,total_slides:i.length}]),o.after_slide_change(v,i.length)};g.outerHeight()!=s.outerHeight()&&o.variable_height?g.animate({height:s.outerHeight()},250,"linear",e):e()};if(1===i.length)return l(),!1;var c=function(){"next"===r&&f.next(a,s,l),"prev"===r&&f.prev(a,s,l)};s.outerHeight()>g.outerHeight()&&o.variable_height?g.animate({height:s.outerHeight()},250,"linear",c):c()},m.next=function(e){e.stopImmediatePropagation(),e.preventDefault(),m._goto(v+1)},m.prev=function(e){e.stopImmediatePropagation(),e.preventDefault(),m._goto(v-1)},m.link_custom=function(t){t.preventDefault();var n=e(this).attr("data-orbit-link");if("string"==typeof n&&""!=(n=e.trim(n))){var i=c.find("[data-orbit-slide="+n+"]");-1!=i.index()&&m._goto(i.index())}},m.link_bullet=function(){var t=e(this).attr("data-orbit-slide");if("string"==typeof t&&""!=(t=e.trim(t)))if(isNaN(parseInt(t))){var n=c.find("[data-orbit-slide="+t+"]");-1!=n.index()&&m._goto(n.index()+1)}else m._goto(parseInt(t))},m.timer_callback=function(){m._goto(v+1,!0)},m.compute_dimensions=function(){var t=e(m.slides().get(v)),n=t.outerHeight();o.variable_height||m.slides().each(function(){e(this).outerHeight()>n&&(n=e(this).outerHeight())}),g.height(n)},m.create_timer=function(){var e=new a(c.find("."+o.timer_container_class),o,m.timer_callback);return e},m.stop_timer=function(){"object"==typeof h&&h.stop()},m.toggle_timer=function(){var e=c.find("."+o.timer_container_class);e.hasClass(o.timer_paused_class)?("undefined"==typeof h&&(h=m.create_timer()),h.start()):"object"==typeof h&&h.stop()},m.init=function(){m.build_markup(),o.timer&&(h=m.create_timer(),Foundation.utils.image_loaded(this.slides().children("img"),h.start)),f=new l(o,g),"slide"===o.animation&&(f=new s(o,g)),c.on("click","."+o.next_class,m.next),c.on("click","."+o.prev_class,m.prev),o.next_on_click&&c.on("click","."+o.slides_container_class+" [data-orbit-slide]",m.link_bullet),c.on("click",m.toggle_timer),o.swipe&&c.on("touchstart.fndtn.orbit",function(e){e.touches||(e=e.originalEvent);var t={start_page_x:e.touches[0].pageX,start_page_y:e.touches[0].pageY,start_time:(new Date).getTime(),delta_x:0,is_scrolling:i};c.data("swipe-transition",t),e.stopPropagation()}).on("touchmove.fndtn.orbit",function(e){if(e.touches||(e=e.originalEvent),!(e.touches.length>1||e.scale&&1!==e.scale)){var t=c.data("swipe-transition");if("undefined"==typeof t&&(t={}),t.delta_x=e.touches[0].pageX-t.start_page_x,"undefined"==typeof t.is_scrolling&&(t.is_scrolling=!!(t.is_scrolling||Math.abs(t.delta_x)<Math.abs(e.touches[0].pageY-t.start_page_y))),!t.is_scrolling&&!t.active){e.preventDefault();var n=t.delta_x<0?v+1:v-1;t.active=!0,m._goto(n)}}}).on("touchend.fndtn.orbit",function(e){c.data("swipe-transition",{}),e.stopPropagation()}),c.on("mouseenter.fndtn.orbit",function(){o.timer&&o.pause_on_hover&&m.stop_timer()}).on("mouseleave.fndtn.orbit",function(){o.timer&&o.resume_on_mouseout&&h.start()}),e(n).on("click","[data-orbit-link]",m.link_custom),e(t).on("load resize",m.compute_dimensions),Foundation.utils.image_loaded(this.slides().children("img"),m.compute_dimensions),Foundation.utils.image_loaded(this.slides().children("img"),function(){c.prev("."+o.preloader_class).css("display","none"),m.update_slide_number(0),m.update_active_link(0),g.trigger("ready.fndtn.orbit")})},m.init()},a=function(e,t,n){var i,r,o=this,a=t.timer_speed,s=e.find("."+t.timer_progress_class),l=-1;this.update_progress=function(e){var t=s.clone();t.attr("style",""),t.css("width",e+"%"),s.replaceWith(t),s=t},this.restart=function(){clearTimeout(r),e.addClass(t.timer_paused_class),l=-1,o.update_progress(0)},this.start=function(){return e.hasClass(t.timer_paused_class)?(l=-1===l?a:l,e.removeClass(t.timer_paused_class),i=(new Date).getTime(),s.animate({width:"100%"},l,"linear"),r=setTimeout(function(){o.restart(),n()},l),void e.trigger("timer-started.fndtn.orbit")):!0},this.stop=function(){if(e.hasClass(t.timer_paused_class))return!0;clearTimeout(r),e.addClass(t.timer_paused_class);var n=(new Date).getTime();l-=n-i;var s=100-l/a*100;o.update_progress(s),e.trigger("timer-stopped.fndtn.orbit")}},s=function(t){var n=t.animation_speed,i=1===e("html[dir=rtl]").length,r=i?"marginRight":"marginLeft",o={};o[r]="0%",this.next=function(e,t,i){e.animate({marginLeft:"-100%"},n),t.animate(o,n,function(){e.css(r,"100%"),i()})},this.prev=function(e,t,i){e.animate({marginLeft:"100%"},n),t.css(r,"-100%"),t.animate(o,n,function(){e.css(r,"100%"),i()})}},l=function(t){{var n=t.animation_speed;1===e("html[dir=rtl]").length}this.next=function(e,t,i){t.css({margin:"0%",opacity:"0.01"}),t.animate({opacity:"1"},n,"linear",function(){e.css("margin","100%"),i()})},this.prev=function(e,t,i){t.css({margin:"0%",opacity:"0.01"}),t.animate({opacity:"1"},n,"linear",function(){e.css("margin","100%"),i()})}};Foundation.libs=Foundation.libs||{},Foundation.libs.orbit={name:"orbit",version:"5.5.2",settings:{animation:"slide",timer_speed:1e4,pause_on_hover:!0,resume_on_mouseout:!1,next_on_click:!0,animation_speed:500,stack_on_small:!1,navigation_arrows:!0,slide_number:!0,slide_number_text:"of",container_class:"orbit-container",stack_on_small_class:"orbit-stack-on-small",timer_container_class:"orbit-timer",timer_paused_class:"paused",timer_progress_class:"orbit-progress",slides_container_class:"orbit-slides-container",preloader_class:"preloader",slide_selector:"*",bullets_container_class:"orbit-bullets",bullets_active_class:"active",slide_number_class:"orbit-slide-number",caption_class:"orbit-caption",active_slide_class:"active",orbit_transition_class:"orbit-transitioning",bullets:!0,circular:!0,timer:!0,variable_height:!1,swipe:!0,before_slide_change:r,after_slide_change:r},init:function(e,t,n){this.bindings(t,n)},events:function(e){var t=new o(this.S(e),this.S(e).data("orbit-init"));this.S(e).data(this.name+"-instance",t)},reflow:function(){var e=this;if(e.S(e.scope).is("[data-orbit]")){var t=e.S(e.scope),n=t.data(e.name+"-instance");n.compute_dimensions()}else e.S("[data-orbit]",e.scope).each(function(t,n){var i=e.S(n),r=(e.data_options(i),i.data(e.name+"-instance"));r.compute_dimensions()})}}}(jQuery,window,window.document),function(e){"use strict";Foundation.libs.accordion={name:"accordion",version:"5.5.2",settings:{content_class:"content",active_class:"active",multi_expand:!1,toggleable:!0,callback:function(){}},init:function(e,t,n){this.bindings(t,n)},events:function(t){var n=this,i=this.S;n.create(this.S(t)),i(this.scope).off(".fndtn.accordion").on("click.fndtn.accordion","["+this.attr_name()+"] > dd > a, ["+this.attr_name()+"] > li > a",function(t){var r=i(this).closest("["+n.attr_name()+"]"),o=n.attr_name()+"="+r.attr(n.attr_name()),a=r.data(n.attr_name(!0)+"-init")||n.settings,s=i("#"+this.href.split("#")[1]),l=e("> dd, > li",r),c=l.children("."+a.content_class),u=c.filter("."+a.active_class);return t.preventDefault(),r.attr(n.attr_name())&&(c=c.add("["+o+"] dd > ."+a.content_class+", ["+o+"] li > ."+a.content_class),l=l.add("["+o+"] dd, ["+o+"] li")),a.toggleable&&s.is(u)?(s.parent("dd, li").toggleClass(a.active_class,!1),s.toggleClass(a.active_class,!1),i(this).attr("aria-expanded",function(e,t){return"true"===t?"false":"true"}),a.callback(s),s.triggerHandler("toggled",[r]),void r.triggerHandler("toggled",[s])):(a.multi_expand||(c.removeClass(a.active_class),l.removeClass(a.active_class),l.children("a").attr("aria-expanded","false")),s.addClass(a.active_class).parent().addClass(a.active_class),a.callback(s),s.triggerHandler("toggled",[r]),r.triggerHandler("toggled",[s]),void i(this).attr("aria-expanded","true"))})},create:function(t){var n=this,i=t,r=e("> .accordion-navigation",i),o=i.data(n.attr_name(!0)+"-init")||n.settings;r.children("a").attr("aria-expanded","false"),r.has("."+o.content_class+"."+o.active_class).children("a").attr("aria-expanded","true"),o.multi_expand&&t.attr("aria-multiselectable","true")},off:function(){},reflow:function(){}}}(jQuery,window,window.document),function(e){"use strict";Foundation.libs.alert={name:"alert",version:"5.5.2",settings:{callback:function(){}},init:function(e,t,n){this.bindings(t,n)},events:function(){var t=this,n=this.S;e(this.scope).off(".alert").on("click.fndtn.alert","["+this.attr_name()+"] .close",function(e){var i=n(this).closest("["+t.attr_name()+"]"),r=i.data(t.attr_name(!0)+"-init")||t.settings;e.preventDefault(),Modernizr.csstransitions?(i.addClass("alert-close"),i.on("transitionend webkitTransitionEnd oTransitionEnd",function(){n(this).trigger("close.fndtn.alert").remove(),r.callback()})):i.fadeOut(300,function(){n(this).trigger("close.fndtn.alert").remove(),r.callback()})})},reflow:function(){}}}(jQuery,window,window.document),function(e){"use strict";Foundation.libs.offcanvas={name:"offcanvas",version:"5.5.2",settings:{open_method:"move",close_on_click:!1},init:function(e,t,n){this.bindings(t,n)},events:function(){var t=this,n=t.S,i="",r="",o="";"move"===this.settings.open_method?(i="move-",r="right",o="left"):"overlap_single"===this.settings.open_method?(i="offcanvas-overlap-",r="right",o="left"):"overlap"===this.settings.open_method&&(i="offcanvas-overlap"),n(this.scope).off(".offcanvas").on("click.fndtn.offcanvas",".left-off-canvas-toggle",function(o){t.click_toggle_class(o,i+r),"overlap"!==t.settings.open_method&&n(".left-submenu").removeClass(i+r),e(".left-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".left-off-canvas-menu a",function(o){var a=t.get_settings(o),s=n(this).parent();!a.close_on_click||s.hasClass("has-submenu")||s.hasClass("back")?n(this).parent().hasClass("has-submenu")?(o.preventDefault(),n(this).siblings(".left-submenu").toggleClass(i+r)):s.hasClass("back")&&(o.preventDefault(),s.parent().removeClass(i+r)):(t.hide.call(t,i+r,t.get_wrapper(o)),s.parent().removeClass(i+r)),e(".left-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".right-off-canvas-toggle",function(r){t.click_toggle_class(r,i+o),"overlap"!==t.settings.open_method&&n(".right-submenu").removeClass(i+o),e(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".right-off-canvas-menu a",function(r){var a=t.get_settings(r),s=n(this).parent();!a.close_on_click||s.hasClass("has-submenu")||s.hasClass("back")?n(this).parent().hasClass("has-submenu")?(r.preventDefault(),n(this).siblings(".right-submenu").toggleClass(i+o)):s.hasClass("back")&&(r.preventDefault(),s.parent().removeClass(i+o)):(t.hide.call(t,i+o,t.get_wrapper(r)),s.parent().removeClass(i+o)),e(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".exit-off-canvas",function(a){t.click_remove_class(a,i+o),n(".right-submenu").removeClass(i+o),r&&(t.click_remove_class(a,i+r),n(".left-submenu").removeClass(i+o)),e(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".exit-off-canvas",function(n){t.click_remove_class(n,i+o),e(".left-off-canvas-toggle").attr("aria-expanded","false"),r&&(t.click_remove_class(n,i+r),e(".right-off-canvas-toggle").attr("aria-expanded","false"))})},toggle:function(e,t){t=t||this.get_wrapper(),t.is("."+e)?this.hide(e,t):this.show(e,t)},show:function(e,t){t=t||this.get_wrapper(),t.trigger("open.fndtn.offcanvas"),t.addClass(e)},hide:function(e,t){t=t||this.get_wrapper(),t.trigger("close.fndtn.offcanvas"),t.removeClass(e)},click_toggle_class:function(e,t){e.preventDefault();var n=this.get_wrapper(e);this.toggle(t,n)},click_remove_class:function(e,t){e.preventDefault();var n=this.get_wrapper(e);this.hide(t,n)},get_settings:function(e){var t=this.S(e.target).closest("["+this.attr_name()+"]");return t.data(this.attr_name(!0)+"-init")||this.settings},get_wrapper:function(e){var t=this.S(e?e.target:this.scope).closest(".off-canvas-wrap");return 0===t.length&&(t=this.S(".off-canvas-wrap")),t},reflow:function(){}}}(jQuery,window,window.document),function(e,t,n,i){"use strict";function r(e){var t=/fade/i.test(e),n=/pop/i.test(e);return{animate:t||n,pop:n,fade:t}}Foundation.libs.reveal={name:"reveal",version:"5.5.2",locked:!1,settings:{animation:"fadeAndPop",animation_speed:250,close_on_background_click:!0,close_on_esc:!0,dismiss_modal_class:"close-reveal-modal",multiple_opened:!1,bg_class:"reveal-modal-bg",root_element:"body",open:function(){},opened:function(){},close:function(){},closed:function(){},on_ajax_error:e.noop,bg:e(".reveal-modal-bg"),css:{open:{opacity:0,visibility:"visible",display:"block"},close:{opacity:1,visibility:"hidden",display:"none"}}},init:function(t,n,i){e.extend(!0,this.settings,n,i),this.bindings(n,i)},events:function(){var e=this,t=e.S;return t(this.scope).off(".reveal").on("click.fndtn.reveal","["+this.add_namespace("data-reveal-id")+"]:not([disabled])",function(n){if(n.preventDefault(),!e.locked){var i=t(this),r=i.data(e.data_attr("reveal-ajax")),o=i.data(e.data_attr("reveal-replace-content"));if(e.locked=!0,"undefined"==typeof r)e.open.call(e,i);else{var a=r===!0?i.attr("href"):r;e.open.call(e,i,{url:a},{replaceContentSel:o})}}}),t(n).on("click.fndtn.reveal",this.close_targets(),function(n){if(n.preventDefault(),!e.locked){var i=t("["+e.attr_name()+"].open").data(e.attr_name(!0)+"-init")||e.settings,r=t(n.target)[0]===t("."+i.bg_class)[0];if(r){if(!i.close_on_background_click)return;n.stopPropagation()}e.locked=!0,e.close.call(e,r?t("["+e.attr_name()+"].open:not(.toback)"):t(this).closest("["+e.attr_name()+"]"))}}),t("["+e.attr_name()+"]",this.scope).length>0?t(this.scope).on("open.fndtn.reveal",this.settings.open).on("opened.fndtn.reveal",this.settings.opened).on("opened.fndtn.reveal",this.open_video).on("close.fndtn.reveal",this.settings.close).on("closed.fndtn.reveal",this.settings.closed).on("closed.fndtn.reveal",this.close_video):t(this.scope).on("open.fndtn.reveal","["+e.attr_name()+"]",this.settings.open).on("opened.fndtn.reveal","["+e.attr_name()+"]",this.settings.opened).on("opened.fndtn.reveal","["+e.attr_name()+"]",this.open_video).on("close.fndtn.reveal","["+e.attr_name()+"]",this.settings.close).on("closed.fndtn.reveal","["+e.attr_name()+"]",this.settings.closed).on("closed.fndtn.reveal","["+e.attr_name()+"]",this.close_video),!0},key_up_on:function(){var e=this;return e.S("body").off("keyup.fndtn.reveal").on("keyup.fndtn.reveal",function(t){var n=e.S("["+e.attr_name()+"].open"),i=n.data(e.attr_name(!0)+"-init")||e.settings;i&&27===t.which&&i.close_on_esc&&!e.locked&&e.close.call(e,n)}),!0},key_up_off:function(){return this.S("body").off("keyup.fndtn.reveal"),!0},open:function(n,r){var o,a=this;n?"undefined"!=typeof n.selector?o=a.S("#"+n.data(a.data_attr("reveal-id"))).first():(o=a.S(this.scope),r=n):o=a.S(this.scope);var s=o.data(a.attr_name(!0)+"-init");if(s=s||this.settings,o.hasClass("open")&&n!==i&&n.attr("data-reveal-id")==o.attr("id"))return a.close(o);if(!o.hasClass("open")){var l=a.S("["+a.attr_name()+"].open");if("undefined"==typeof o.data("css-top")&&o.data("css-top",parseInt(o.css("top"),10)).data("offset",this.cache_offset(o)),o.attr("tabindex","0").attr("aria-hidden","false"),this.key_up_on(o),o.on("open.fndtn.reveal",function(e){"fndtn.reveal"!==e.namespace}),o.on("open.fndtn.reveal").trigger("open.fndtn.reveal"),l.length<1&&this.toggle_bg(o,!0),"string"==typeof r&&(r={url:r}),"undefined"!=typeof r&&r.url){var c="undefined"!=typeof r.success?r.success:null;e.extend(r,{success:function(t,n,i){if(e.isFunction(c)){var r=c(t,n,i);"string"==typeof r&&(t=r)}"undefined"!=typeof options&&"undefined"!=typeof options.replaceContentSel?o.find(options.replaceContentSel).html(t):o.html(t),a.S(o).foundation("section","reflow"),a.S(o).children().foundation(),l.length>0&&(s.multiple_opened?a.to_back(l):a.hide(l,s.css.close)),a.show(o,s.css.open)}}),s.on_ajax_error!==e.noop&&e.extend(r,{error:s.on_ajax_error}),e.ajax(r)}else l.length>0&&(s.multiple_opened?a.to_back(l):a.hide(l,s.css.close)),this.show(o,s.css.open)}a.S(t).trigger("resize")},close:function(t){var t=t&&t.length?t:this.S(this.scope),n=this.S("["+this.attr_name()+"].open"),i=t.data(this.attr_name(!0)+"-init")||this.settings,r=this;n.length>0&&(t.removeAttr("tabindex","0").attr("aria-hidden","true"),this.locked=!0,this.key_up_off(t),t.trigger("close.fndtn.reveal"),(i.multiple_opened&&1===n.length||!i.multiple_opened||t.length>1)&&(r.toggle_bg(t,!1),r.to_front(t)),i.multiple_opened?(r.hide(t,i.css.close,i),r.to_front(e(e.makeArray(n).reverse()[1]))):r.hide(n,i.css.close,i))},close_targets:function(){var e="."+this.settings.dismiss_modal_class;return this.settings.close_on_background_click?e+", ."+this.settings.bg_class:e},toggle_bg:function(t,n){0===this.S("."+this.settings.bg_class).length&&(this.settings.bg=e("<div />",{"class":this.settings.bg_class}).appendTo("body").hide());var r=this.settings.bg.filter(":visible").length>0;n!=r&&((n==i?r:!n)?this.hide(this.settings.bg):this.show(this.settings.bg))},show:function(n,i){if(i){var o=n.data(this.attr_name(!0)+"-init")||this.settings,a=o.root_element,s=this;if(0===n.parent(a).length){var l=n.wrap('<div style="display: none;" />').parent();n.on("closed.fndtn.reveal.wrapped",function(){n.detach().appendTo(l),n.unwrap().unbind("closed.fndtn.reveal.wrapped")}),n.detach().appendTo(a)}var c=r(o.animation);if(c.animate||(this.locked=!1),c.pop){i.top=e(t).scrollTop()-n.data("offset")+"px";var u={top:e(t).scrollTop()+n.data("css-top")+"px",opacity:1};return setTimeout(function(){return n.css(i).animate(u,o.animation_speed,"linear",function(){s.locked=!1,n.trigger("opened.fndtn.reveal")}).addClass("open")},o.animation_speed/2)}if(c.fade){i.top=e(t).scrollTop()+n.data("css-top")+"px";var u={opacity:1};return setTimeout(function(){return n.css(i).animate(u,o.animation_speed,"linear",function(){s.locked=!1,n.trigger("opened.fndtn.reveal")}).addClass("open")},o.animation_speed/2)}return n.css(i).show().css({opacity:1}).addClass("open").trigger("opened.fndtn.reveal")}var o=this.settings;return r(o.animation).fade?n.fadeIn(o.animation_speed/2):(this.locked=!1,n.show())},to_back:function(e){e.addClass("toback")},to_front:function(e){e.removeClass("toback")},hide:function(n,i){if(i){var o=n.data(this.attr_name(!0)+"-init"),a=this;o=o||this.settings;var s=r(o.animation);if(s.animate||(this.locked=!1),s.pop){var l={top:-e(t).scrollTop()-n.data("offset")+"px",opacity:0};return setTimeout(function(){return n.animate(l,o.animation_speed,"linear",function(){a.locked=!1,n.css(i).trigger("closed.fndtn.reveal")}).removeClass("open")},o.animation_speed/2)}if(s.fade){var l={opacity:0};return setTimeout(function(){return n.animate(l,o.animation_speed,"linear",function(){a.locked=!1,n.css(i).trigger("closed.fndtn.reveal")}).removeClass("open")},o.animation_speed/2)}return n.hide().css(i).removeClass("open").trigger("closed.fndtn.reveal")}var o=this.settings;return r(o.animation).fade?n.fadeOut(o.animation_speed/2):n.hide()},close_video:function(t){var n=e(".flex-video",t.target),i=e("iframe",n);i.length>0&&(i.attr("data-src",i[0].src),i.attr("src",i.attr("src")),n.hide())},open_video:function(t){var n=e(".flex-video",t.target),r=n.find("iframe");if(r.length>0){var o=r.attr("data-src");if("string"==typeof o)r[0].src=r.attr("data-src");else{var a=r[0].src;r[0].src=i,r[0].src=a}n.show()}},data_attr:function(e){return this.namespace.length>0?this.namespace+"-"+e:e},cache_offset:function(e){var t=e.show().height()+parseInt(e.css("top"),10)+e.scrollY;return e.hide(),t},off:function(){e(this.scope).off(".fndtn.reveal")},reflow:function(){}}}(jQuery,window,window.document),function(e,t,n,i){"use strict";Foundation.libs.tab={name:"tab",version:"5.5.2",settings:{active_class:"active",callback:function(){},deep_linking:!1,scroll_to_content:!0,is_hover:!1},default_tab_hashes:[],init:function(e,n,i){var r=this,o=this.S;o("["+this.attr_name()+"] > .active > a",this.scope).each(function(){r.default_tab_hashes.push(this.hash)}),r.entry_location=t.location.href,this.bindings(n,i),this.handle_location_hash_change()},events:function(){var e=this,n=this.S,i=function(t,i){var r=n(i).closest("["+e.attr_name()+"]").data(e.attr_name(!0)+"-init");(!r.is_hover||Modernizr.touch)&&(t.preventDefault(),t.stopPropagation(),e.toggle_active_tab(n(i).parent()))};n(this.scope).off(".tab").on("keydown.fndtn.tab","["+this.attr_name()+"] > * > a",function(e){var t=this,n=e.keyCode||e.which;9==n&&(e.preventDefault(),i(e,t))}).on("click.fndtn.tab","["+this.attr_name()+"] > * > a",function(e){var t=this;i(e,t)}).on("mouseenter.fndtn.tab","["+this.attr_name()+"] > * > a",function(){var t=n(this).closest("["+e.attr_name()+"]").data(e.attr_name(!0)+"-init");t.is_hover&&e.toggle_active_tab(n(this).parent())}),n(t).on("hashchange.fndtn.tab",function(t){t.preventDefault(),e.handle_location_hash_change()})},handle_location_hash_change:function(){var t=this,n=this.S;n("["+this.attr_name()+"]",this.scope).each(function(){var r=n(this).data(t.attr_name(!0)+"-init");if(r.deep_linking){var o;if(o=r.scroll_to_content?t.scope.location.hash:t.scope.location.hash.replace("fndtn-",""),""!=o){var a=n(o);if(a.hasClass("content")&&a.parent().hasClass("tabs-content"))t.toggle_active_tab(e("["+t.attr_name()+"] > * > a[href="+o+"]").parent());else{var s=a.closest(".content").attr("id");s!=i&&t.toggle_active_tab(e("["+t.attr_name()+"] > * > a[href=#"+s+"]").parent(),o)}}else for(var l=0;l<t.default_tab_hashes.length;l++)t.toggle_active_tab(e("["+t.attr_name()+"] > * > a[href="+t.default_tab_hashes[l]+"]").parent())}})},toggle_active_tab:function(r,o){var a=this,s=a.S,l=r.closest("["+this.attr_name()+"]"),c=r.find("a"),u=r.children("a").first(),d="#"+u.attr("href").split("#")[1],p=s(d),f=r.siblings(),h=l.data(this.attr_name(!0)+"-init"),m=function(t){var i,r=e(this),o=e(this).parents("li").prev().children('[role="tab"]'),a=e(this).parents("li").next().children('[role="tab"]');switch(t.keyCode){case 37:i=o;break;case 39:i=a;break;default:i=!1}i.length&&(r.attr({tabindex:"-1","aria-selected":null}),i.attr({tabindex:"0","aria-selected":!0}).focus()),e('[role="tabpanel"]').attr("aria-hidden","true"),e("#"+e(n.activeElement).attr("href").substring(1)).attr("aria-hidden",null)},g=function(e){var n=t.location.href===a.entry_location,i=h.scroll_to_content?a.default_tab_hashes[0]:n?t.location.hash:"fndtn-"+a.default_tab_hashes[0].replace("#","");n&&e===i||(t.location.hash=e)};u.data("tab-content")&&(d="#"+u.data("tab-content").split("#")[1],p=s(d)),h.deep_linking&&(h.scroll_to_content?(g(o||d),o==i||o==d?r.parent()[0].scrollIntoView():s(d)[0].scrollIntoView()):g(o!=i?"fndtn-"+o.replace("#",""):"fndtn-"+d.replace("#",""))),r.addClass(h.active_class).triggerHandler("opened"),c.attr({"aria-selected":"true",tabindex:0}),f.removeClass(h.active_class),f.find("a").attr({"aria-selected":"false",tabindex:-1}),p.siblings().removeClass(h.active_class).attr({"aria-hidden":"true",tabindex:-1}),p.addClass(h.active_class).attr("aria-hidden","false").removeAttr("tabindex"),h.callback(r),p.triggerHandler("toggled",[p]),l.triggerHandler("toggled",[r]),c.off("keydown").on("keydown",m)},data_attr:function(e){return this.namespace.length>0?this.namespace+"-"+e:e},off:function(){},reflow:function(){}}}(jQuery,window,window.document),function(e,t){"use strict";Foundation.libs.tooltip={name:"tooltip",version:"5.5.2",settings:{additional_inheritable_classes:[],tooltip_class:".tooltip",append_to:"body",touch_close_text:"Tap To Close",disable_for_touch:!1,hover_delay:200,show_on:"all",tip_template:function(e,t){return'<span data-selector="'+e+'" id="'+e+'" class="'+Foundation.libs.tooltip.settings.tooltip_class.substring(1)+'" role="tooltip">'+t+'<span class="nub"></span></span>'}},cache:{},init:function(e,t,n){Foundation.inherit(this,"random_str"),this.bindings(t,n)},should_show:function(t){var n=e.extend({},this.settings,this.data_options(t));return"all"===n.show_on?!0:this.small()&&"small"===n.show_on?!0:this.medium()&&"medium"===n.show_on?!0:this.large()&&"large"===n.show_on?!0:!1},medium:function(){return matchMedia(Foundation.media_queries.medium).matches},large:function(){return matchMedia(Foundation.media_queries.large).matches},events:function(t){function n(e,t,n){e.timer||(n?(e.timer=null,r.showTip(t)):e.timer=setTimeout(function(){e.timer=null,r.showTip(t)}.bind(e),r.settings.hover_delay))}function i(e,t){e.timer&&(clearTimeout(e.timer),e.timer=null),r.hide(t)}var r=this,o=r.S;r.create(this.S(t)),e(this.scope).off(".tooltip").on("mouseenter.fndtn.tooltip mouseleave.fndtn.tooltip touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip","["+this.attr_name()+"]",function(t){var a=o(this),s=e.extend({},r.settings,r.data_options(a)),l=!1;
if(Modernizr.touch&&/touchstart|MSPointerDown/i.test(t.type)&&o(t.target).is("a"))return!1;if(/mouse/i.test(t.type)&&r.ie_touch(t))return!1;if(a.hasClass("open"))Modernizr.touch&&/touchstart|MSPointerDown/i.test(t.type)&&t.preventDefault(),r.hide(a);else{if(s.disable_for_touch&&Modernizr.touch&&/touchstart|MSPointerDown/i.test(t.type))return;if(!s.disable_for_touch&&Modernizr.touch&&/touchstart|MSPointerDown/i.test(t.type)&&(t.preventDefault(),o(s.tooltip_class+".open").hide(),l=!0,e(".open["+r.attr_name()+"]").length>0)){var c=o(e(".open["+r.attr_name()+"]")[0]);r.hide(c)}/enter|over/i.test(t.type)?n(this,a):"mouseout"===t.type||"mouseleave"===t.type?i(this,a):n(this,a,!0)}}).on("mouseleave.fndtn.tooltip touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip","["+this.attr_name()+"].open",function(t){return/mouse/i.test(t.type)&&r.ie_touch(t)?!1:void(("touch"!=e(this).data("tooltip-open-event-type")||"mouseleave"!=t.type)&&("mouse"==e(this).data("tooltip-open-event-type")&&/MSPointerDown|touchstart/i.test(t.type)?r.convert_to_touch(e(this)):i(this,e(this))))}).on("DOMNodeRemoved DOMAttrModified","["+this.attr_name()+"]:not(a)",function(){i(this,o(this))})},ie_touch:function(){return!1},showTip:function(e){var t=this.getTip(e);return this.should_show(e,t)?this.show(e):void 0},getTip:function(t){var n=this.selector(t),i=e.extend({},this.settings,this.data_options(t)),r=null;return n&&(r=this.S('span[data-selector="'+n+'"]'+i.tooltip_class)),"object"==typeof r?r:!1},selector:function(e){var t=e.attr(this.attr_name())||e.attr("data-selector");return"string"!=typeof t&&(t=this.random_str(6),e.attr("data-selector",t).attr("aria-describedby",t)),t},create:function(n){var i=this,r=e.extend({},this.settings,this.data_options(n)),o=this.settings.tip_template;"string"==typeof r.tip_template&&t.hasOwnProperty(r.tip_template)&&(o=t[r.tip_template]);var a=e(o(this.selector(n),e("<div></div>").html(n.attr("title")).html())),s=this.inheritable_classes(n);a.addClass(s).appendTo(r.append_to),Modernizr.touch&&(a.append('<span class="tap-to-close">'+r.touch_close_text+"</span>"),a.on("touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip",function(){i.hide(n)})),n.removeAttr("title").attr("title","")},reposition:function(t,n,i){var r,o,a,s,l;if(n.css("visibility","hidden").show(),r=t.data("width"),o=n.children(".nub"),a=o.outerHeight(),s=o.outerHeight(),n.css(this.small()?{width:"100%"}:{width:r?r:"auto"}),l=function(e,t,n,i,r){return e.css({top:t?t:"auto",bottom:i?i:"auto",left:r?r:"auto",right:n?n:"auto"}).end()},l(n,t.offset().top+t.outerHeight()+10,"auto","auto",t.offset().left),this.small())l(n,t.offset().top+t.outerHeight()+10,"auto","auto",12.5,e(this.scope).width()),n.addClass("tip-override"),l(o,-a,"auto","auto",t.offset().left);else{var c=t.offset().left;Foundation.rtl&&(o.addClass("rtl"),c=t.offset().left+t.outerWidth()-n.outerWidth()),l(n,t.offset().top+t.outerHeight()+10,"auto","auto",c),o.attr("style")&&o.removeAttr("style"),n.removeClass("tip-override"),i&&i.indexOf("tip-top")>-1?(Foundation.rtl&&o.addClass("rtl"),l(n,t.offset().top-n.outerHeight(),"auto","auto",c).removeClass("tip-override")):i&&i.indexOf("tip-left")>-1?(l(n,t.offset().top+t.outerHeight()/2-n.outerHeight()/2,"auto","auto",t.offset().left-n.outerWidth()-a).removeClass("tip-override"),o.removeClass("rtl")):i&&i.indexOf("tip-right")>-1&&(l(n,t.offset().top+t.outerHeight()/2-n.outerHeight()/2,"auto","auto",t.offset().left+t.outerWidth()+a).removeClass("tip-override"),o.removeClass("rtl"))}n.css("visibility","visible").hide()},small:function(){return matchMedia(Foundation.media_queries.small).matches&&!matchMedia(Foundation.media_queries.medium).matches},inheritable_classes:function(t){var n=e.extend({},this.settings,this.data_options(t)),i=["tip-top","tip-left","tip-bottom","tip-right","radius","round"].concat(n.additional_inheritable_classes),r=t.attr("class"),o=r?e.map(r.split(" "),function(t){return-1!==e.inArray(t,i)?t:void 0}).join(" "):"";return e.trim(o)},convert_to_touch:function(t){var n=this,i=n.getTip(t),r=e.extend({},n.settings,n.data_options(t));0===i.find(".tap-to-close").length&&(i.append('<span class="tap-to-close">'+r.touch_close_text+"</span>"),i.on("click.fndtn.tooltip.tapclose touchstart.fndtn.tooltip.tapclose MSPointerDown.fndtn.tooltip.tapclose",function(){n.hide(t)})),t.data("tooltip-open-event-type","touch")},show:function(e){var t=this.getTip(e);"touch"==e.data("tooltip-open-event-type")&&this.convert_to_touch(e),this.reposition(e,t,e.attr("class")),e.addClass("open"),t.fadeIn(150)},hide:function(e){var t=this.getTip(e);t.fadeOut(150,function(){t.find(".tap-to-close").remove(),t.off("click.fndtn.tooltip.tapclose MSPointerDown.fndtn.tapclose"),e.removeClass("open")})},off:function(){var t=this;this.S(this.scope).off(".fndtn.tooltip"),this.S(this.settings.tooltip_class).each(function(n){e("["+t.attr_name()+"]").eq(n).attr("title",e(this).text())}).remove()},reflow:function(){}}}(jQuery,window,window.document),/*
Turbolinks 5.0.3
Copyright © 2017 Basecamp, LLC
 */
function(){(function(){(function(){this.Turbolinks={supported:function(){return null!=window.history.pushState&&null!=window.requestAnimationFrame&&null!=window.addEventListener}(),visit:function(t,n){return e.controller.visit(t,n)},clearCache:function(){return e.controller.clearCache()}}}).call(this)}).call(this);var e=this.Turbolinks;(function(){(function(){var t,n,i=[].slice;e.copyObject=function(e){var t,n,i;n={};for(t in e)i=e[t],n[t]=i;return n},e.closest=function(e,n){return t.call(e,n)},t=function(){var e,t;return e=document.documentElement,null!=(t=e.closest)?t:function(e){var t;for(t=this;t;){if(t.nodeType===Node.ELEMENT_NODE&&n.call(t,e))return t;t=t.parentNode}}}(),e.defer=function(e){return setTimeout(e,1)},e.throttle=function(e){var t;return t=null,function(){var n;return n=1<=arguments.length?i.call(arguments,0):[],null!=t?t:t=requestAnimationFrame(function(i){return function(){return t=null,e.apply(i,n)}}(this))}},e.dispatch=function(e,t){var n,i,r,o,a;return o=null!=t?t:{},a=o.target,n=o.cancelable,i=o.data,r=document.createEvent("Events"),r.initEvent(e,!0,n===!0),r.data=null!=i?i:{},(null!=a?a:document).dispatchEvent(r),r},e.match=function(e,t){return n.call(e,t)},n=function(){var e,t,n,i;return e=document.documentElement,null!=(t=null!=(n=null!=(i=e.matchesSelector)?i:e.webkitMatchesSelector)?n:e.msMatchesSelector)?t:e.mozMatchesSelector}(),e.uuid=function(){var e,t,n;for(n="",e=t=1;36>=t;e=++t)n+=9===e||14===e||19===e||24===e?"-":15===e?"4":20===e?(Math.floor(4*Math.random())+8).toString(16):Math.floor(15*Math.random()).toString(16);return n}}).call(this),function(){e.Location=function(){function e(e){var t,n;null==e&&(e=""),n=document.createElement("a"),n.href=e.toString(),this.absoluteURL=n.href,t=n.hash.length,2>t?this.requestURL=this.absoluteURL:(this.requestURL=this.absoluteURL.slice(0,-t),this.anchor=n.hash.slice(1))}var t,n,i,r;return e.wrap=function(e){return e instanceof this?e:new this(e)},e.prototype.getOrigin=function(){return this.absoluteURL.split("/",3).join("/")},e.prototype.getPath=function(){var e,t;return null!=(e=null!=(t=this.absoluteURL.match(/\/\/[^\/]*(\/[^?;]*)/))?t[1]:void 0)?e:"/"},e.prototype.getPathComponents=function(){return this.getPath().split("/").slice(1)},e.prototype.getLastPathComponent=function(){return this.getPathComponents().slice(-1)[0]},e.prototype.getExtension=function(){var e,t;return null!=(e=null!=(t=this.getLastPathComponent().match(/\.[^.]*$/))?t[0]:void 0)?e:""},e.prototype.isHTML=function(){return this.getExtension().match(/^(?:|\.(?:htm|html|xhtml))$/)},e.prototype.isPrefixedBy=function(e){var t;return t=n(e),this.isEqualTo(e)||r(this.absoluteURL,t)},e.prototype.isEqualTo=function(e){return this.absoluteURL===(null!=e?e.absoluteURL:void 0)},e.prototype.toCacheKey=function(){return this.requestURL},e.prototype.toJSON=function(){return this.absoluteURL},e.prototype.toString=function(){return this.absoluteURL},e.prototype.valueOf=function(){return this.absoluteURL},n=function(e){return t(e.getOrigin()+e.getPath())},t=function(e){return i(e,"/")?e:e+"/"},r=function(e,t){return e.slice(0,t.length)===t},i=function(e,t){return e.slice(-t.length)===t},e}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.HttpRequest=function(){function n(n,i,r){this.delegate=n,this.requestCanceled=t(this.requestCanceled,this),this.requestTimedOut=t(this.requestTimedOut,this),this.requestFailed=t(this.requestFailed,this),this.requestLoaded=t(this.requestLoaded,this),this.requestProgressed=t(this.requestProgressed,this),this.url=e.Location.wrap(i).requestURL,this.referrer=e.Location.wrap(r).absoluteURL,this.createXHR()}return n.NETWORK_FAILURE=0,n.TIMEOUT_FAILURE=-1,n.timeout=60,n.prototype.send=function(){var e;return this.xhr&&!this.sent?(this.notifyApplicationBeforeRequestStart(),this.setProgress(0),this.xhr.send(),this.sent=!0,"function"==typeof(e=this.delegate).requestStarted?e.requestStarted():void 0):void 0},n.prototype.cancel=function(){return this.xhr&&this.sent?this.xhr.abort():void 0},n.prototype.requestProgressed=function(e){return e.lengthComputable?this.setProgress(e.loaded/e.total):void 0},n.prototype.requestLoaded=function(){return this.endRequest(function(e){return function(){var t;return 200<=(t=e.xhr.status)&&300>t?e.delegate.requestCompletedWithResponse(e.xhr.responseText,e.xhr.getResponseHeader("Turbolinks-Location")):(e.failed=!0,e.delegate.requestFailedWithStatusCode(e.xhr.status,e.xhr.responseText))}}(this))},n.prototype.requestFailed=function(){return this.endRequest(function(e){return function(){return e.failed=!0,e.delegate.requestFailedWithStatusCode(e.constructor.NETWORK_FAILURE)}}(this))},n.prototype.requestTimedOut=function(){return this.endRequest(function(e){return function(){return e.failed=!0,e.delegate.requestFailedWithStatusCode(e.constructor.TIMEOUT_FAILURE)}}(this))},n.prototype.requestCanceled=function(){return this.endRequest()},n.prototype.notifyApplicationBeforeRequestStart=function(){return e.dispatch("turbolinks:request-start",{data:{url:this.url,xhr:this.xhr}})},n.prototype.notifyApplicationAfterRequestEnd=function(){return e.dispatch("turbolinks:request-end",{data:{url:this.url,xhr:this.xhr}})},n.prototype.createXHR=function(){return this.xhr=new XMLHttpRequest,this.xhr.open("GET",this.url,!0),this.xhr.timeout=1e3*this.constructor.timeout,this.xhr.setRequestHeader("Accept","text/html, application/xhtml+xml"),this.xhr.setRequestHeader("Turbolinks-Referrer",this.referrer),this.xhr.onprogress=this.requestProgressed,this.xhr.onload=this.requestLoaded,this.xhr.onerror=this.requestFailed,this.xhr.ontimeout=this.requestTimedOut,this.xhr.onabort=this.requestCanceled},n.prototype.endRequest=function(e){return this.xhr?(this.notifyApplicationAfterRequestEnd(),null!=e&&e.call(this),this.destroy()):void 0},n.prototype.setProgress=function(e){var t;return this.progress=e,"function"==typeof(t=this.delegate).requestProgressed?t.requestProgressed(this.progress):void 0},n.prototype.destroy=function(){var e;return this.setProgress(1),"function"==typeof(e=this.delegate).requestFinished&&e.requestFinished(),this.delegate=null,this.xhr=null},n}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.ProgressBar=function(){function e(){this.trickle=t(this.trickle,this),this.stylesheetElement=this.createStylesheetElement(),this.progressElement=this.createProgressElement()}var n;return n=300,e.defaultCSS=".turbolinks-progress-bar {\n  position: fixed;\n  display: block;\n  top: 0;\n  left: 0;\n  height: 3px;\n  background: #0076ff;\n  z-index: 9999;\n  transition: width "+n+"ms ease-out, opacity "+n/2+"ms "+n/2+"ms ease-in;\n  transform: translate3d(0, 0, 0);\n}",e.prototype.show=function(){return this.visible?void 0:(this.visible=!0,this.installStylesheetElement(),this.installProgressElement(),this.startTrickling())},e.prototype.hide=function(){return this.visible&&!this.hiding?(this.hiding=!0,this.fadeProgressElement(function(e){return function(){return e.uninstallProgressElement(),e.stopTrickling(),e.visible=!1,e.hiding=!1}}(this))):void 0},e.prototype.setValue=function(e){return this.value=e,this.refresh()},e.prototype.installStylesheetElement=function(){return document.head.insertBefore(this.stylesheetElement,document.head.firstChild)},e.prototype.installProgressElement=function(){return this.progressElement.style.width=0,this.progressElement.style.opacity=1,document.documentElement.insertBefore(this.progressElement,document.body),this.refresh()},e.prototype.fadeProgressElement=function(e){return this.progressElement.style.opacity=0,setTimeout(e,1.5*n)},e.prototype.uninstallProgressElement=function(){return this.progressElement.parentNode?document.documentElement.removeChild(this.progressElement):void 0},e.prototype.startTrickling=function(){return null!=this.trickleInterval?this.trickleInterval:this.trickleInterval=setInterval(this.trickle,n)},e.prototype.stopTrickling=function(){return clearInterval(this.trickleInterval),this.trickleInterval=null},e.prototype.trickle=function(){return this.setValue(this.value+Math.random()/100)},e.prototype.refresh=function(){return requestAnimationFrame(function(e){return function(){return e.progressElement.style.width=10+90*e.value+"%"}}(this))},e.prototype.createStylesheetElement=function(){var e;return e=document.createElement("style"),e.type="text/css",e.textContent=this.constructor.defaultCSS,e},e.prototype.createProgressElement=function(){var e;return e=document.createElement("div"),e.className="turbolinks-progress-bar",e},e}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.BrowserAdapter=function(){function n(n){this.controller=n,this.showProgressBar=t(this.showProgressBar,this),this.progressBar=new e.ProgressBar}var i,r,o,a;return a=e.HttpRequest,i=a.NETWORK_FAILURE,o=a.TIMEOUT_FAILURE,r=500,n.prototype.visitProposedToLocationWithAction=function(e,t){return this.controller.startVisitToLocationWithAction(e,t)},n.prototype.visitStarted=function(e){return e.issueRequest(),e.changeHistory(),e.loadCachedSnapshot()},n.prototype.visitRequestStarted=function(e){return this.progressBar.setValue(0),e.hasCachedSnapshot()||"restore"!==e.action?this.showProgressBarAfterDelay():this.showProgressBar()},n.prototype.visitRequestProgressed=function(e){return this.progressBar.setValue(e.progress)},n.prototype.visitRequestCompleted=function(e){return e.loadResponse()},n.prototype.visitRequestFailedWithStatusCode=function(e,t){switch(t){case i:case o:return this.reload();default:return e.loadResponse()}},n.prototype.visitRequestFinished=function(){return this.hideProgressBar()},n.prototype.visitCompleted=function(e){return e.followRedirect()},n.prototype.pageInvalidated=function(){return this.reload()},n.prototype.showProgressBarAfterDelay=function(){return this.progressBarTimeout=setTimeout(this.showProgressBar,r)},n.prototype.showProgressBar=function(){return this.progressBar.show()},n.prototype.hideProgressBar=function(){return this.progressBar.hide(),clearTimeout(this.progressBarTimeout)},n.prototype.reload=function(){return window.location.reload()},n}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.History=function(){function n(e){this.delegate=e,this.onPageLoad=t(this.onPageLoad,this),this.onPopState=t(this.onPopState,this)}return n.prototype.start=function(){return this.started?void 0:(addEventListener("popstate",this.onPopState,!1),addEventListener("load",this.onPageLoad,!1),this.started=!0)},n.prototype.stop=function(){return this.started?(removeEventListener("popstate",this.onPopState,!1),removeEventListener("load",this.onPageLoad,!1),this.started=!1):void 0},n.prototype.push=function(t,n){return t=e.Location.wrap(t),this.update("push",t,n)},n.prototype.replace=function(t,n){return t=e.Location.wrap(t),this.update("replace",t,n)},n.prototype.onPopState=function(t){var n,i,r,o;return this.shouldHandlePopState()&&(o=null!=(i=t.state)?i.turbolinks:void 0)?(n=e.Location.wrap(window.location),r=o.restorationIdentifier,this.delegate.historyPoppedToLocationWithRestorationIdentifier(n,r)):void 0},n.prototype.onPageLoad=function(){return e.defer(function(e){return function(){return e.pageLoaded=!0}}(this))},n.prototype.shouldHandlePopState=function(){return this.pageIsLoaded()},n.prototype.pageIsLoaded=function(){return this.pageLoaded||"complete"===document.readyState},n.prototype.update=function(e,t,n){var i;return i={turbolinks:{restorationIdentifier:n}},history[e+"State"](i,null,t)},n}()}.call(this),function(){e.Snapshot=function(){function t(e){var t,n;n=e.head,t=e.body,this.head=null!=n?n:document.createElement("head"),this.body=null!=t?t:document.createElement("body")}return t.wrap=function(e){return e instanceof this?e:this.fromHTML(e)},t.fromHTML=function(e){var t;return t=document.createElement("html"),t.innerHTML=e,this.fromElement(t)},t.fromElement=function(e){return new this({head:e.querySelector("head"),body:e.querySelector("body")})},t.prototype.clone=function(){return new t({head:this.head.cloneNode(!0),body:this.body.cloneNode(!0)})},t.prototype.getRootLocation=function(){var t,n;return n=null!=(t=this.getSetting("root"))?t:"/",new e.Location(n)},t.prototype.getCacheControlValue=function(){return this.getSetting("cache-control")},t.prototype.hasAnchor=function(e){try{return null!=this.body.querySelector("[id='"+e+"']")}catch(t){}},t.prototype.isPreviewable=function(){return"no-preview"!==this.getCacheControlValue()},t.prototype.isCacheable=function(){return"no-cache"!==this.getCacheControlValue()},t.prototype.getSetting=function(e){var t,n;return n=this.head.querySelectorAll("meta[name='turbolinks-"+e+"']"),t=n[n.length-1],null!=t?t.getAttribute("content"):void 0},t}()}.call(this),function(){var t=[].slice;e.Renderer=function(){function e(){}var n;return e.render=function(){var e,n,i,r;return i=arguments[0],n=arguments[1],e=3<=arguments.length?t.call(arguments,2):[],r=function(e,t,n){n.prototype=e.prototype;var i=new n,r=e.apply(i,t);return Object(r)===r?r:i}(this,e,function(){}),r.delegate=i,r.render(n),r},e.prototype.renderView=function(e){return this.delegate.viewWillRender(this.newBody),e(),this.delegate.viewRendered(this.newBody)},e.prototype.invalidateView=function(){return this.delegate.viewInvalidated()},e.prototype.createScriptElement=function(e){var t;return"false"===e.getAttribute("data-turbolinks-eval")?e:(t=document.createElement("script"),t.textContent=e.textContent,n(t,e),t)},n=function(e,t){var n,i,r,o,a,s,l;for(o=t.attributes,s=[],n=0,i=o.length;i>n;n++)a=o[n],r=a.name,l=a.value,s.push(e.setAttribute(r,l));return s},e}()}.call(this),function(){e.HeadDetails=function(){function e(e){var t,n,o,a,s,l,c;for(this.element=e,this.elements={},c=this.element.childNodes,a=0,l=c.length;l>a;a++)o=c[a],o.nodeType===Node.ELEMENT_NODE&&(s=o.outerHTML,n=null!=(t=this.elements)[s]?t[s]:t[s]={type:r(o),tracked:i(o),elements:[]},n.elements.push(o))}var t,n,i,r;return e.prototype.hasElementWithKey=function(e){return e in this.elements},e.prototype.getTrackedElementSignature=function(){var e,t;return function(){var n,i;n=this.elements,i=[];for(e in n)t=n[e].tracked,t&&i.push(e);return i}.call(this).join("")},e.prototype.getScriptElementsNotInDetails=function(e){return this.getElementsMatchingTypeNotInDetails("script",e)},e.prototype.getStylesheetElementsNotInDetails=function(e){return this.getElementsMatchingTypeNotInDetails("stylesheet",e)},e.prototype.getElementsMatchingTypeNotInDetails=function(e,t){var n,i,r,o,a,s;r=this.elements,a=[];for(i in r)o=r[i],s=o.type,n=o.elements,s!==e||t.hasElementWithKey(i)||a.push(n[0]);return a},e.prototype.getProvisionalElements=function(){var e,t,n,i,r,o,a;n=[],i=this.elements;for(t in i)r=i[t],a=r.type,o=r.tracked,e=r.elements,null!=a||o?e.length>1&&n.push.apply(n,e.slice(1)):n.push.apply(n,e);return n},r=function(e){return t(e)?"script":n(e)?"stylesheet":void 0},i=function(e){return"reload"===e.getAttribute("data-turbolinks-track")},t=function(e){var t;return t=e.tagName.toLowerCase(),"script"===t},n=function(e){var t;return t=e.tagName.toLowerCase(),"style"===t||"link"===t&&"stylesheet"===e.getAttribute("rel")},e}()}.call(this),function(){var t=function(e,t){function i(){this.constructor=e}for(var r in t)n.call(t,r)&&(e[r]=t[r]);return i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype,e},n={}.hasOwnProperty;e.SnapshotRenderer=function(n){function i(t,n){this.currentSnapshot=t,this.newSnapshot=n,this.currentHeadDetails=new e.HeadDetails(this.currentSnapshot.head),this.newHeadDetails=new e.HeadDetails(this.newSnapshot.head),this.newBody=this.newSnapshot.body}return t(i,n),i.prototype.render=function(e){return this.trackedElementsAreIdentical()?(this.mergeHead(),this.renderView(function(t){return function(){return t.replaceBody(),t.focusFirstAutofocusableElement(),e()}}(this))):this.invalidateView()},i.prototype.mergeHead=function(){return this.copyNewHeadStylesheetElements(),this.copyNewHeadScriptElements(),this.removeCurrentHeadProvisionalElements(),this.copyNewHeadProvisionalElements()},i.prototype.replaceBody=function(){return this.activateBodyScriptElements(),this.importBodyPermanentElements(),this.assignNewBody()},i.prototype.trackedElementsAreIdentical=function(){return this.currentHeadDetails.getTrackedElementSignature()===this.newHeadDetails.getTrackedElementSignature()},i.prototype.copyNewHeadStylesheetElements=function(){var e,t,n,i,r;for(i=this.getNewHeadStylesheetElements(),r=[],t=0,n=i.length;n>t;t++)e=i[t],r.push(document.head.appendChild(e));return r},i.prototype.copyNewHeadScriptElements=function(){var e,t,n,i,r;for(i=this.getNewHeadScriptElements(),r=[],t=0,n=i.length;n>t;t++)e=i[t],r.push(document.head.appendChild(this.createScriptElement(e)));return r},i.prototype.removeCurrentHeadProvisionalElements=function(){var e,t,n,i,r;for(i=this.getCurrentHeadProvisionalElements(),r=[],t=0,n=i.length;n>t;t++)e=i[t],r.push(document.head.removeChild(e));return r},i.prototype.copyNewHeadProvisionalElements=function(){var e,t,n,i,r;for(i=this.getNewHeadProvisionalElements(),r=[],t=0,n=i.length;n>t;t++)e=i[t],r.push(document.head.appendChild(e));return r},i.prototype.importBodyPermanentElements=function(){var e,t,n,i,r,o;for(i=this.getNewBodyPermanentElements(),o=[],t=0,n=i.length;n>t;t++)r=i[t],o.push((e=this.findCurrentBodyPermanentElement(r))?r.parentNode.replaceChild(e,r):void 0);return o},i.prototype.activateBodyScriptElements=function(){var e,t,n,i,r,o;for(i=this.getNewBodyScriptElements(),o=[],t=0,n=i.length;n>t;t++)r=i[t],e=this.createScriptElement(r),o.push(r.parentNode.replaceChild(e,r));return o},i.prototype.assignNewBody=function(){return document.body=this.newBody},i.prototype.focusFirstAutofocusableElement=function(){var e;return null!=(e=this.findFirstAutofocusableElement())?e.focus():void 0},i.prototype.getNewHeadStylesheetElements=function(){return this.newHeadDetails.getStylesheetElementsNotInDetails(this.currentHeadDetails)},i.prototype.getNewHeadScriptElements=function(){return this.newHeadDetails.getScriptElementsNotInDetails(this.currentHeadDetails)},i.prototype.getCurrentHeadProvisionalElements=function(){return this.currentHeadDetails.getProvisionalElements()},i.prototype.getNewHeadProvisionalElements=function(){return this.newHeadDetails.getProvisionalElements()},i.prototype.getNewBodyPermanentElements=function(){return this.newBody.querySelectorAll("[id][data-turbolinks-permanent]")},i.prototype.findCurrentBodyPermanentElement=function(e){return document.body.querySelector("#"+e.id+"[data-turbolinks-permanent]")},i.prototype.getNewBodyScriptElements=function(){return this.newBody.querySelectorAll("script")},i.prototype.findFirstAutofocusableElement=function(){return document.body.querySelector("[autofocus]")},i}(e.Renderer)}.call(this),function(){var t=function(e,t){function i(){this.constructor=e}for(var r in t)n.call(t,r)&&(e[r]=t[r]);return i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype,e},n={}.hasOwnProperty;e.ErrorRenderer=function(e){function n(e){this.html=e}return t(n,e),n.prototype.render=function(e){return this.renderView(function(t){return function(){return t.replaceDocumentHTML(),t.activateBodyScriptElements(),e()}}(this))},n.prototype.replaceDocumentHTML=function(){return document.documentElement.innerHTML=this.html},n.prototype.activateBodyScriptElements=function(){var e,t,n,i,r,o;for(i=this.getScriptElements(),o=[],t=0,n=i.length;n>t;t++)r=i[t],e=this.createScriptElement(r),o.push(r.parentNode.replaceChild(e,r));return o},n.prototype.getScriptElements=function(){return document.documentElement.querySelectorAll("script")},n}(e.Renderer)}.call(this),function(){e.View=function(){function t(e){this.delegate=e,this.element=document.documentElement}return t.prototype.getRootLocation=function(){return this.getSnapshot().getRootLocation()},t.prototype.getSnapshot=function(){return e.Snapshot.fromElement(this.element)},t.prototype.render=function(e,t){var n,i,r;return r=e.snapshot,n=e.error,i=e.isPreview,this.markAsPreview(i),null!=r?this.renderSnapshot(r,t):this.renderError(n,t)},t.prototype.markAsPreview=function(e){return e?this.element.setAttribute("data-turbolinks-preview",""):this.element.removeAttribute("data-turbolinks-preview")},t.prototype.renderSnapshot=function(t,n){return e.SnapshotRenderer.render(this.delegate,n,this.getSnapshot(),e.Snapshot.wrap(t))},t.prototype.renderError=function(t,n){return e.ErrorRenderer.render(this.delegate,n,t)},t}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.ScrollManager=function(){function n(n){this.delegate=n,this.onScroll=t(this.onScroll,this),this.onScroll=e.throttle(this.onScroll)}return n.prototype.start=function(){return this.started?void 0:(addEventListener("scroll",this.onScroll,!1),this.onScroll(),this.started=!0)},n.prototype.stop=function(){return this.started?(removeEventListener("scroll",this.onScroll,!1),this.started=!1):void 0},n.prototype.scrollToElement=function(e){return e.scrollIntoView()},n.prototype.scrollToPosition=function(e){var t,n;return t=e.x,n=e.y,window.scrollTo(t,n)},n.prototype.onScroll=function(){return this.updatePosition({x:window.pageXOffset,y:window.pageYOffset})},n.prototype.updatePosition=function(e){var t;return this.position=e,null!=(t=this.delegate)?t.scrollPositionChanged(this.position):void 0},n}()}.call(this),function(){e.SnapshotCache=function(){function t(e){this.size=e,this.keys=[],this.snapshots={}}var n;return t.prototype.has=function(e){var t;return t=n(e),t in this.snapshots},t.prototype.get=function(e){var t;return this.has(e)?(t=this.read(e),this.touch(e),t):void 0},t.prototype.put=function(e,t){return this.write(e,t),this.touch(e),t},t.prototype.read=function(e){var t;return t=n(e),this.snapshots[t]},t.prototype.write=function(e,t){var i;return i=n(e),this.snapshots[i]=t},t.prototype.touch=function(e){var t,i;return i=n(e),t=this.keys.indexOf(i),t>-1&&this.keys.splice(t,1),this.keys.unshift(i),this.trim()},t.prototype.trim=function(){var e,t,n,i,r;for(i=this.keys.splice(this.size),r=[],e=0,n=i.length;n>e;e++)t=i[e],r.push(delete this.snapshots[t]);return r},n=function(t){return e.Location.wrap(t).toCacheKey()},t}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.Visit=function(){function n(n,i,r){this.controller=n,this.action=r,this.performScroll=t(this.performScroll,this),this.identifier=e.uuid(),this.location=e.Location.wrap(i),this.adapter=this.controller.adapter,this.state="initialized",this.timingMetrics={}}var i;return n.prototype.start=function(){return"initialized"===this.state?(this.recordTimingMetric("visitStart"),this.state="started",this.adapter.visitStarted(this)):void 0},n.prototype.cancel=function(){var e;return"started"===this.state?(null!=(e=this.request)&&e.cancel(),this.cancelRender(),this.state="canceled"):void 0},n.prototype.complete=function(){var e;return"started"===this.state?(this.recordTimingMetric("visitEnd"),this.state="completed","function"==typeof(e=this.adapter).visitCompleted&&e.visitCompleted(this),this.controller.visitCompleted(this)):void 0},n.prototype.fail=function(){var e;return"started"===this.state?(this.state="failed","function"==typeof(e=this.adapter).visitFailed?e.visitFailed(this):void 0):void 0},n.prototype.changeHistory=function(){var e,t;return this.historyChanged?void 0:(e=this.location.isEqualTo(this.referrer)?"replace":this.action,t=i(e),this.controller[t](this.location,this.restorationIdentifier),this.historyChanged=!0)},n.prototype.issueRequest=function(){return this.shouldIssueRequest()&&null==this.request?(this.progress=0,this.request=new e.HttpRequest(this,this.location,this.referrer),this.request.send()):void 0},n.prototype.getCachedSnapshot=function(){var e;return!(e=this.controller.getCachedSnapshotForLocation(this.location))||null!=this.location.anchor&&!e.hasAnchor(this.location.anchor)||"restore"!==this.action&&!e.isPreviewable()?void 0:e},n.prototype.hasCachedSnapshot=function(){return null!=this.getCachedSnapshot()},n.prototype.loadCachedSnapshot=function(){var e,t;return(t=this.getCachedSnapshot())?(e=this.shouldIssueRequest(),this.render(function(){var n;return this.cacheSnapshot(),this.controller.render({snapshot:t,isPreview:e},this.performScroll),"function"==typeof(n=this.adapter).visitRendered&&n.visitRendered(this),e?void 0:this.complete()})):void 0},n.prototype.loadResponse=function(){return null!=this.response?this.render(function(){var e,t;return this.cacheSnapshot(),this.request.failed?(this.controller.render({error:this.response},this.performScroll),"function"==typeof(e=this.adapter).visitRendered&&e.visitRendered(this),this.fail()):(this.controller.render({snapshot:this.response},this.performScroll),"function"==typeof(t=this.adapter).visitRendered&&t.visitRendered(this),this.complete())}):void 0},n.prototype.followRedirect=function(){return this.redirectedToLocation&&!this.followedRedirect?(this.location=this.redirectedToLocation,this.controller.replaceHistoryWithLocationAndRestorationIdentifier(this.redirectedToLocation,this.restorationIdentifier),this.followedRedirect=!0):void 0},n.prototype.requestStarted=function(){var e;return this.recordTimingMetric("requestStart"),"function"==typeof(e=this.adapter).visitRequestStarted?e.visitRequestStarted(this):void 0},n.prototype.requestProgressed=function(e){var t;return this.progress=e,"function"==typeof(t=this.adapter).visitRequestProgressed?t.visitRequestProgressed(this):void 0},n.prototype.requestCompletedWithResponse=function(t,n){return this.response=t,null!=n&&(this.redirectedToLocation=e.Location.wrap(n)),this.adapter.visitRequestCompleted(this)},n.prototype.requestFailedWithStatusCode=function(e,t){return this.response=t,this.adapter.visitRequestFailedWithStatusCode(this,e)},n.prototype.requestFinished=function(){var e;return this.recordTimingMetric("requestEnd"),"function"==typeof(e=this.adapter).visitRequestFinished?e.visitRequestFinished(this):void 0},n.prototype.performScroll=function(){return this.scrolled?void 0:("restore"===this.action?this.scrollToRestoredPosition()||this.scrollToTop():this.scrollToAnchor()||this.scrollToTop(),this.scrolled=!0)},n.prototype.scrollToRestoredPosition=function(){var e,t;return e=null!=(t=this.restorationData)?t.scrollPosition:void 0,null!=e?(this.controller.scrollToPosition(e),!0):void 0},n.prototype.scrollToAnchor=function(){return null!=this.location.anchor?(this.controller.scrollToAnchor(this.location.anchor),!0):void 0},n.prototype.scrollToTop=function(){return this.controller.scrollToPosition({x:0,y:0})},n.prototype.recordTimingMetric=function(e){var t;return null!=(t=this.timingMetrics)[e]?t[e]:t[e]=(new Date).getTime()},n.prototype.getTimingMetrics=function(){return e.copyObject(this.timingMetrics)},i=function(e){switch(e){case"replace":return"replaceHistoryWithLocationAndRestorationIdentifier";case"advance":case"restore":return"pushHistoryWithLocationAndRestorationIdentifier"}},n.prototype.shouldIssueRequest=function(){return"restore"===this.action?!this.hasCachedSnapshot():!0},n.prototype.cacheSnapshot=function(){return this.snapshotCached?void 0:(this.controller.cacheSnapshot(),this.snapshotCached=!0)},n.prototype.render=function(e){return this.cancelRender(),this.frame=requestAnimationFrame(function(t){return function(){return t.frame=null,e.call(t)}}(this))},n.prototype.cancelRender=function(){return this.frame?cancelAnimationFrame(this.frame):void 0},n}()}.call(this),function(){var t=function(e,t){return function(){return e.apply(t,arguments)}};e.Controller=function(){function n(){this.clickBubbled=t(this.clickBubbled,this),this.clickCaptured=t(this.clickCaptured,this),this.pageLoaded=t(this.pageLoaded,this),this.history=new e.History(this),this.view=new e.View(this),this.scrollManager=new e.ScrollManager(this),this.restorationData={},this.clearCache()}return n.prototype.start=function(){return e.supported&&!this.started?(addEventListener("click",this.clickCaptured,!0),addEventListener("DOMContentLoaded",this.pageLoaded,!1),this.scrollManager.start(),this.startHistory(),this.started=!0,this.enabled=!0):void 0},n.prototype.disable=function(){return this.enabled=!1},n.prototype.stop=function(){return this.started?(removeEventListener("click",this.clickCaptured,!0),removeEventListener("DOMContentLoaded",this.pageLoaded,!1),this.scrollManager.stop(),this.stopHistory(),this.started=!1):void 0},n.prototype.clearCache=function(){return this.cache=new e.SnapshotCache(10)},n.prototype.visit=function(t,n){var i,r;return null==n&&(n={}),t=e.Location.wrap(t),this.applicationAllowsVisitingLocation(t)?this.locationIsVisitable(t)?(i=null!=(r=n.action)?r:"advance",this.adapter.visitProposedToLocationWithAction(t,i)):window.location=t:void 0},n.prototype.startVisitToLocationWithAction=function(t,n,i){var r;return e.supported?(r=this.getRestorationDataForIdentifier(i),this.startVisit(t,n,{restorationData:r})):window.location=t},n.prototype.startHistory=function(){return this.location=e.Location.wrap(window.location),this.restorationIdentifier=e.uuid(),this.history.start(),this.history.replace(this.location,this.restorationIdentifier)},n.prototype.stopHistory=function(){return this.history.stop()},n.prototype.pushHistoryWithLocationAndRestorationIdentifier=function(t,n){return this.restorationIdentifier=n,this.location=e.Location.wrap(t),this.history.push(this.location,this.restorationIdentifier)},n.prototype.replaceHistoryWithLocationAndRestorationIdentifier=function(t,n){return this.restorationIdentifier=n,this.location=e.Location.wrap(t),this.history.replace(this.location,this.restorationIdentifier)},n.prototype.historyPoppedToLocationWithRestorationIdentifier=function(t,n){var i;return this.restorationIdentifier=n,this.enabled?(i=this.getRestorationDataForIdentifier(this.restorationIdentifier),this.startVisit(t,"restore",{restorationIdentifier:this.restorationIdentifier,restorationData:i,historyChanged:!0}),this.location=e.Location.wrap(t)):this.adapter.pageInvalidated()},n.prototype.getCachedSnapshotForLocation=function(e){var t;return t=this.cache.get(e),t?t.clone():void 0},n.prototype.shouldCacheSnapshot=function(){return this.view.getSnapshot().isCacheable()},n.prototype.cacheSnapshot=function(){var e;return this.shouldCacheSnapshot()?(this.notifyApplicationBeforeCachingSnapshot(),e=this.view.getSnapshot(),this.cache.put(this.lastRenderedLocation,e.clone())):void 0},n.prototype.scrollToAnchor=function(e){var t;return(t=document.getElementById(e))?this.scrollToElement(t):this.scrollToPosition({x:0,y:0})},n.prototype.scrollToElement=function(e){return this.scrollManager.scrollToElement(e)},n.prototype.scrollToPosition=function(e){return this.scrollManager.scrollToPosition(e)},n.prototype.scrollPositionChanged=function(e){var t;return t=this.getCurrentRestorationData(),t.scrollPosition=e},n.prototype.render=function(e,t){return this.view.render(e,t)},n.prototype.viewInvalidated=function(){return this.adapter.pageInvalidated()},n.prototype.viewWillRender=function(e){return this.notifyApplicationBeforeRender(e)},n.prototype.viewRendered=function(){return this.lastRenderedLocation=this.currentVisit.location,this.notifyApplicationAfterRender()},n.prototype.pageLoaded=function(){return this.lastRenderedLocation=this.location,this.notifyApplicationAfterPageLoad()},n.prototype.clickCaptured=function(){return removeEventListener("click",this.clickBubbled,!1),addEventListener("click",this.clickBubbled,!1)},n.prototype.clickBubbled=function(e){var t,n,i;return this.enabled&&this.clickEventIsSignificant(e)&&(n=this.getVisitableLinkForNode(e.target))&&(i=this.getVisitableLocationForLink(n))&&this.applicationAllowsFollowingLinkToLocation(n,i)?(e.preventDefault(),t=this.getActionForLink(n),this.visit(i,{action:t})):void 0},n.prototype.applicationAllowsFollowingLinkToLocation=function(e,t){var n;return n=this.notifyApplicationAfterClickingLinkToLocation(e,t),!n.defaultPrevented},n.prototype.applicationAllowsVisitingLocation=function(e){var t;return t=this.notifyApplicationBeforeVisitingLocation(e),!t.defaultPrevented},n.prototype.notifyApplicationAfterClickingLinkToLocation=function(t,n){return e.dispatch("turbolinks:click",{target:t,data:{url:n.absoluteURL},cancelable:!0})},n.prototype.notifyApplicationBeforeVisitingLocation=function(t){return e.dispatch("turbolinks:before-visit",{data:{url:t.absoluteURL},cancelable:!0})},n.prototype.notifyApplicationAfterVisitingLocation=function(t){return e.dispatch("turbolinks:visit",{data:{url:t.absoluteURL}})},n.prototype.notifyApplicationBeforeCachingSnapshot=function(){return e.dispatch("turbolinks:before-cache")},n.prototype.notifyApplicationBeforeRender=function(t){return e.dispatch("turbolinks:before-render",{data:{newBody:t}})
},n.prototype.notifyApplicationAfterRender=function(){return e.dispatch("turbolinks:render")},n.prototype.notifyApplicationAfterPageLoad=function(t){return null==t&&(t={}),e.dispatch("turbolinks:load",{data:{url:this.location.absoluteURL,timing:t}})},n.prototype.startVisit=function(e,t,n){var i;return null!=(i=this.currentVisit)&&i.cancel(),this.currentVisit=this.createVisit(e,t,n),this.currentVisit.start(),this.notifyApplicationAfterVisitingLocation(e)},n.prototype.createVisit=function(t,n,i){var r,o,a,s,l;return o=null!=i?i:{},s=o.restorationIdentifier,a=o.restorationData,r=o.historyChanged,l=new e.Visit(this,t,n),l.restorationIdentifier=null!=s?s:e.uuid(),l.restorationData=e.copyObject(a),l.historyChanged=r,l.referrer=this.location,l},n.prototype.visitCompleted=function(e){return this.notifyApplicationAfterPageLoad(e.getTimingMetrics())},n.prototype.clickEventIsSignificant=function(e){return!(e.defaultPrevented||e.target.isContentEditable||e.which>1||e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)},n.prototype.getVisitableLinkForNode=function(t){return this.nodeIsVisitable(t)?e.closest(t,"a[href]:not([target]):not([download])"):void 0},n.prototype.getVisitableLocationForLink=function(t){var n;return n=new e.Location(t.getAttribute("href")),this.locationIsVisitable(n)?n:void 0},n.prototype.getActionForLink=function(e){var t;return null!=(t=e.getAttribute("data-turbolinks-action"))?t:"advance"},n.prototype.nodeIsVisitable=function(t){var n;return(n=e.closest(t,"[data-turbolinks]"))?"false"!==n.getAttribute("data-turbolinks"):!0},n.prototype.locationIsVisitable=function(e){return e.isPrefixedBy(this.view.getRootLocation())&&e.isHTML()},n.prototype.getCurrentRestorationData=function(){return this.getRestorationDataForIdentifier(this.restorationIdentifier)},n.prototype.getRestorationDataForIdentifier=function(e){var t;return null!=(t=this.restorationData)[e]?t[e]:t[e]={}},n}()}.call(this),function(){var t,n,i;e.start=function(){return n()?(null==e.controller&&(e.controller=t()),e.controller.start()):void 0},n=function(){return null==window.Turbolinks&&(window.Turbolinks=e),i()},t=function(){var t;return t=new e.Controller,t.adapter=new e.BrowserAdapter(t),t},i=function(){return window.Turbolinks===e},i()&&e.start()}.call(this)}).call(this),"object"==typeof module&&module.exports?module.exports=e:"function"==typeof define&&define.amd&&define(e)}.call(this),function(){var e,t,n,i,r,o,a,s,l;$(function(){var e;return Foundation.global.namespace="",e="4.0","scrollRestoration"in history&&(history.scrollRestoration="manual"),$(document).foundation(),$("a.left-off-canvas-toggle,li.has-submenu, .next, .previous, .add_to_cart_link").on("click",function(){return{}}),$("img").error(function(){return $(this).attr("src","/assets/default_image-07632f869356a0d0671025fb72e369cf9af7252977c7c41a06fea7241af49af9.jpg")}),$(document).on("close.fndtn.reveal","[data-reveal]",function(){$("body").removeClass("modal-open"),"mobile-subscribe-window"!==$(this).attr("id")||getCookie("subscribe").length||($("body").removeClass("modal-top"),$(".reveal-modal-bg").removeClass("modal-tr-bg")),$(".reveal-modal-bg").removeClass("transparent_bg")}),$(document).on("open.fndtn.reveal","[data-reveal]",function(){$("body").addClass("modal-open"),"mobile-subscribe-window"===$(this).attr("id")&&$("body").addClass("modal-top")}),$(document).on("opened.fndtn.reveal","[data-reveal]",function(){return"mobile-subscribe-window"===$(this).attr("id")?$(".reveal-modal-bg").addClass("modal-tr-bg"):void 0}),window.getCookie=function(e){var t,n,i,r;for(r=e+"=",n=document.cookie.split(";"),i=0;i<n.length;){for(t=n[i];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(r))return t.substring(r.length,t.length);i++}return""}}),window.setCookie=function(e,t,n){var i,r;i=new Date,i.setTime(i.getTime()+24*n*60*60*1e3),r="expires="+i.toUTCString(),document.cookie=e+"="+t+";"+r+";path=/"},$(document).on("click","a#view-more-top-content",function(){$("#top_content").hasClass("read-more")?($(this).text("Read Less"),$("#top_content").removeClass("read-more")):($(this).text("Read More"),$("#top_content").addClass("read-more"))}),$(document).on("click","a#view-more-seo-post",function(){$("#seo_post").hasClass("read-more")?($(this).text("Read Less"),$("#seo_post").removeClass("read-more")):($(this).text("Read More"),$("#seo_post").addClass("read-more"))}),l=function(){var e;return getCookieValue("sticky_coupon_banner").length?void 0:(e=$(".sticky-coupon-banner .wrapper"),setTimeout(function(){return $(".sticky-coupon-banner").fadeIn(),$(".wrapper").css("transform","translate3d(0, 0, 0)")},500))},$(document).on("click",".close-sticky-coupon-banner, .sticky-coupon-image",function(){return setCookie("sticky_coupon_banner","closed",7),$(".sticky-coupon-banner").fadeOut(500)}),window.static_header_scroll=function(){return $(window).scrollTop()>75?($(".fixed-header").css({transform:"translate3d(0, 0, 0)",display:"block"}),$(".unbxd-as-wrapper").addClass("unbxd_as_wrapper_scroll")):($(".fixed-header").css({transform:"translate3d(0, -50px, 0)",display:"none"}),$(".unbxd-as-wrapper").removeClass("unbxd_as_wrapper_scroll"))},o=function(){loadScript("/assets/countdown.min-ba9b85223a6c43d080dc356111b73baae7cce0719cd5f5d2486d28f4c7537596.js",function(){e()})},e=function(){var e,t;return t=$("#offer_message_timer").val(),e=new Date(t+" UTC"),$("#offer_message_clock").countdown(e).on("update.countdown",function(e){var t,n;return n=24*e.offset.totalDays+e.offset.hours,t="<span class='deal_timer'>%M</span>:<span class='deal_timer'>%S</span>",n>99?t="<span class='deal_timer' style='width:60px'>"+n+"</span>:"+t:n>0&&10>n?t="<span class='deal_timer'>0"+n+"</span>:"+t:n>0&&(t="<span class='deal_timer'>"+n+"</span>:"+t),$(this).html(e.strftime(t))}).on("finish.countdown",function(){return $("#offer_message_clock").html("Offer on product has expired!").addClass("disabled"),setTimeout(function(){return $("#offer_message_countdown").remove()},5e3)})},a=function(){loadScript("/assets/slick-6cd2f97fa8d7182ba8419fc46849bfcf2df340b9a540b08b82eb3f52f1344871.js",function(){t()})},t=function(){return $("#homepage_cart").length>0&&$(".cart-slider").not(".slick-initialized").slick({slidesToShow:1,slidesToShow:1,dots:!1,arrows:!1,autoplay:!0,autoplaySpeed:2e3,touchThreshold:500}),$(".banner-slider-box").length>0?$(".auto_scrollable_banner").not(".slick-initialized").slick({slidesToShow:1,slidesToScroll:1,dots:!0,arrows:!1,autoplay:!0,autoplaySpeed:2e3,touchThreshold:500}):void 0},document.addEventListener("turbolinks:before-cache",function(){return"function"==typeof $(window).slick?$(".auto_scrollable_banner, .cart-slider").slick("unslick"):void 0}),i=function(){return $(".lazy").each(function(){var e;return e=$(this),e.is("picture")?e.children().each(function(){var e;return e=$(this),e.is("source")&&e.data("srcset")?e.attr("srcset",e.data("srcset")):e.data("src")?(e.attr("src",e.data("src")),e.show()):void 0}):e.data("src")?(e.attr("src",e.data("src")),e.show()):void 0})},r=function(){var n,r,s;return i(),lazyLoad(),l(),($("#homepage_cart").length>0||$(".banner-slider-box").length>0)&&("function"==typeof $(window).slick?t():a()),$("#offer_message_timer").length>0&&("function"==typeof $(window).countdown?e():o()),$("#branch-banner-iframe").contents().find("body").find("#branch-banner").length<=0&&($("#container").css("margin-top","0"),$("#branch-banner-iframe").hide()),$(".menu-icon").length>0&&($(".menu-icon").on("click",function(){return $("body").addClass("no-scroll-background"),$("#menu-side-nav").css("background-image","url(/assets/theme_bg-bef8374e828b174007399bb07fef10c44c4fa38084118d941cd2f1a156dd1489.png)"),$("#menu-side-nav").css({overflowX:"visible",width:"250px",transition:"0.3s"}),$(".menu-accordion").show(),$(".off-canvas-wrap").addClass("move-right")}),$(".page, .close-menu").on("click",function(){return"0px"!==$("#menu-side-nav").css("width")?($("body").removeClass("no-scroll-background"),$("#menu-side-nav").css({overflowX:"hidden",width:"0px",transition:"0.3s"}),$(".off-canvas-wrap").removeClass("move-right"),$(".menu-content").removeClass("active"),$(".accordion-navigation").removeClass("active"),$(".menu-accordion").hide()):void 0}),$(".border-custom").foundation({accordion:{multi_expand:!0,toggleable:!0}}),$(".has-submenu").on("click",function(){return $(this).children().html($(this).children().text().includes("+")?"-":"+")}),$(".orbit-timer").hide(),$(document).foundation({orbit:{slide_number:!1,navigation_arrows:!1,bullets:!1,pause_on_hover:!0,timer_speed:4e3,resume_on_mouseout:!0,variable_height:!0}})),$("input.search-trending").on("click",function(){var e,t;return e=$(this).parents(".search_margin").find(".trending-results-box"),t=!1,t&&!e.data("visited")&&0===$("#trending-results").length?$.ajax({url:"/trending_searches",type:"GET",datatype:JSON,caches:!0,success:function(t){var n,i,r,o;if(t.empty_response)return e.remove();for(o='<ul id="trending-results">',n=Math.min(7,t.length),r=1;n>r;)i=t[r],o+='<li><a href="/search?utf8=\u2713&q='+i.query+'&clk_src=trends">'+i.query+"</a></li>",r++;return o+="</ul>",$(".trending-results-text").after(o),e.data("visited","true")},error:function(){return e.remove()}}):void 0}),$(".designs_show.page").length>0?(n=$("#line_items_count").attr("pid"),r=parseInt($("#line_items_count").data("quantity")),s=window.location.pathname+"/get_line_items_count?id="+n,void $.ajax({url:s,type:"GET",datatype:"script",success:function(e){var t;t=parseInt(e.count),t>0&&($(".line_items_count_text").html(2>=r&&t>2?"This product will most likely be <span>Sold Out</span> in a few hours":t>5?"Fast Mover: <span>"+t+" people </span>added this to cart today! ":1===e.count?"<span>"+t+" person </span>added this to cart today!":"<span>"+t+" people </span>added this to cart today!"),$("#line_items_count").slideDown(500,function(){setTimeout(function(){$("#line_items_count").slideUp(500)},5e3)}))},error:function(){}})):($("input.search-trending").on({focus:function(){var e;return e=$(this).parents(".search_margin").find(".trending-results-box"),e.data("visited")?void 0:e.slideDown()},blur:function(){var e;return e=$(this).parents(".search_margin").find(".trending-results-box"),e.slideUp()},keyup:function(){var e;return e=$(this).parents(".search_margin").find(".trending-results-box"),e.slideUp()}}),$(document).on("click","span#scroll-btn-left",function(){return $("header.scroll").animate({scrollLeft:"+=175"}),$("span#scroll-btn-right").fadeIn(300)}),$(document).on("click","#branch-banner-close",function(){return branch.closeBanner()}),void $(document).on("click","span#scroll-btn-right",function(){return $("header.scroll").animate({scrollLeft:"-=175"},{complete:function(){return 0===$(this).scrollLeft()?$("span#scroll-btn-right").fadeOut(300):void 0}})}))},s=function(){var e,t;return e=$(".next-page").attr("id").split("_")[1],t=-1===window.location.href.indexOf("?")?window.location.href+"?more_items=true&page="+e:window.location.href.replace(/&?((pid=)|(page=))[^\&]*/,"")+"&more_items=true&page="+e},window.getMoreItems=function(){$(window).on("scroll",function(){var e,t;return static_header_scroll(),$(".navigate_home_page").length>0&&(e=$(".navigate_home_page").position().top),$(".pages_home").length&&$(".next-page").length>0&&e>$(window).scrollTop()&&(t=s(),t&&e-$(window).scrollTop()<1100&&n(t)),getFooterPng()})},n=function(e){$.ajax({type:"GET",url:e,dataType:"script",beforeSend:function(){$(".next-page, .previous-page").hide(),$("#more-items-loader").show(),static_header_scroll(),$(window).off("scroll")},complete:function(){$(".next-page, .previous-page").show(),$("#more-items-loader").hide(),getMoreItems(),lazyLoad(),i(),t(),applyCountdownTimer()},success:function(e){},error:function(){}})},window.getFooterPng=function(){return!$("#mobile_footer").data("bg_loaded")&&$(document).height()-$(window).height()-$(window).scrollTop()<1e3?($("#mobile_footer").attr("data-bg_loaded",!0),$("#mobile_footer").css("background-image","url(/assets/theme_bg-bef8374e828b174007399bb07fef10c44c4fa38084118d941cd2f1a156dd1489.png)")):void 0},$(function(){var e,t,n,i;$(".pages_home .previous-page").length&&(n=$(".previous-page").attr("id").split("_")[1],i=window.location.href.replace(/&?page=[^\&]*/,"")+"&page="+n,$(".previous-page").attr("href",i)),$(".pages_home .next-page").length&&(e=$(".next-page").attr("id").split("_")[1],t=-1===window.location.href.indexOf("?")?window.location.href+"?page="+e:window.location.href.replace(/&?page=[^\&]*/,"")+"&page="+e,$(".next-page").attr("href",t)),getMoreItems()}),afterWindowOrTrubolinksLoad(function(){return r()}),window.signedIn=function(){return"1"===getCookieValue("signed_in")},$(function(){var e,t,n;return t=function(e){return"undefined"!=typeof fbq?fbq("track","AddToCart",{value:e.price,currency:"INR",content_ids:e.id,content_type:"product",content_category:e.category}):void 0},n=function(e){return ga("ec:addProduct",e),ga("ec:setAction","add"),ga("send","event","UX","click","add to cart catalog")},$(document).on("click",".add_to_cart_link",function(){var t;return t=$(this).data(),e(t.id)}),e=function(e){$.ajax({type:"POST",dataType:"json",url:"/line_items",data:{line_items:[{design_id:e,quantity:1}]},success:function(e){return e.ga_hash&&$.each(e.ga_hash,function(e,i){return t(i),n(i)}),void 0===e.url?($(".cart_count").html(e.cart_count),window.location.assign("/cart")):void 0===e.cart_count?Turbolinks.supported?Turbolinks.visit(e.url):window.location.assign(e.url):void 0}})},$(document).height()<=$(window).height()&&getFooterPng(),window.stickyButton=function(e,t,n){var i,r;return i=$(this).scrollTop(),r=$(t).offset().top-window.innerHeight+n*$(t).height(),i>r&&$(e).removeClass("sticky-button"),r>i?$(e).addClass("sticky-button"):void 0}})}.call(this),applyRating=function(){$("#form-rating-star").raty({score:function(){return $(this).data("star")},starOff:"/assets/star-off-6aaeebdaab93d594c005d366ce0d94fba02e7a07fd03557dbee8482f04a91c22.jpg",starOn:"/assets/star-on-fd26bf0ea0990cfd808f7540f958eed324b86fc609bf56ec2b3a5612cdfde5f5.jpg",starHalf:"/assets/star-half-db15fb9b3561d5c741d8aea9ef4f0957bd9bc51aa1caa6d7a5c316e083c1abd5.jpg",click:function(){return $("#save-alert-message").data("rating-given",0)},path:"",showHalf:!0}),$(".star").raty({readOnly:!0,score:function(){return $(this).attr("dscore")},starOff:"/assets/star-off-6aaeebdaab93d594c005d366ce0d94fba02e7a07fd03557dbee8482f04a91c22.jpg",starOn:"/assets/star-on-fd26bf0ea0990cfd808f7540f958eed324b86fc609bf56ec2b3a5612cdfde5f5.jpg",starHalf:"/assets/star-half-db15fb9b3561d5c741d8aea9ef4f0957bd9bc51aa1caa6d7a5c316e083c1abd5.jpg",path:""})},loadJqueryRaty=function(){loadScript("/assets/jquery.raty-b76ff8ca706482b88a9a01523965f54cec6431ed4c871bb6de8b398cd5b288f7.js",function(){applyRating()})},applyCTimer=function(){$("[data-countdown]").each(function(){var e;return e=new Date($(this).data("countdown")+" UTC"),$(this).slideDown().addClass("label-ribbon"),$(this).countdown(e).on("update.countdown",function(e){var t;return t=24*e.offset.totalDays+e.offset.hours,totalDays=parseInt(e.offset.totalDays),$(this).html(t>48?e.strftime(totalDays+1+" Days Left"):48>=t&&t>=1?e.strftime(t+1+" Hours Left"):e.strftime("%M:%S"))}).on("finish.countdown",function(){return $(this).html("Deal Expired!").fadeOut(3e3)})})},loadSTimer=function(){loadScript("/assets/countdown.min-ba9b85223a6c43d080dc356111b73baae7cce0719cd5f5d2486d28f4c7537596.js",function(){applyCTimer()})},applyCountdownTimer=function(){$(".countdown-timer").each(function(){var e=$(this),t=parseInt(e.find(".active_clock").val()),n=new Date(t),i=applyColourFromLink(e),r=setInterval(function(){e.find(".clock").countdown(n).on("update.countdown",function(e){var t,n;return n=24*e.offset.totalDays+e.offset.hours,t="<span class='deal_timer' style='background:"+i+"'>%M</span>:<span class='deal_timer' style='background:"+i+"'>%S</span>",n>99?t="<span class='deal_timer' style='width:60px;color:"+i+"'>"+n+"</span>:"+t:n>0&&10>n?t="<span class='deal_timer' style='background:"+i+"'>0"+n+"</span>:"+t:n>0&&(t="<span class='deal_timer'style='background:"+i+"'>"+n+"</span>:"+t),$(this).html(e.strftime(t))}).on("finish.countdown",function(){return clearInterval(r),e.fadeOut(500)})},50)})},loadCTimer=function(){loadScript("/assets/countdown.min-ba9b85223a6c43d080dc356111b73baae7cce0719cd5f5d2486d28f4c7537596.js",function(){applyCountdownTimer()})},afterWindowOrTrubolinksLoad(function(){var e,t;$(".page.designs_show").length>0&&$("#product_id").length>0&&(t=$("#product_id").html().split(" ").pop(),e=$(".add_to_buy_bow").length>0?"in_stock_product_view":"out_of_stock_product_view",ga("send","event","Product View",e,t,{nonInteraction:1}))}),afterWindowOrTrubolinksLoad(function(){if($("div.designs_show.page, div.pages_home.page, div.store_flash_deals.page").length>0){if("function"==typeof $(window).countdown?applyCountdownTimer():loadCTimer(),"function"==typeof $(window).raty)return applyRating();if($(".reviews_ratings,.review-container").length>0)return loadJqueryRaty()}else if($("div.store_catalog_page.page").length>0)return"function"==typeof $(window).countdown?applyCTimer():loadSTimer()}),$(document).ready(function(){function e(e){return date=t(e.getDate()),month=t(e.getMonth()+1),hours=t(e.getHours()),minutes=t(e.getMinutes()),date+"/"+month+"-"+hours+":"+minutes}function t(e){return 10>e?"0"+e:e}function n(e,t){for(var n=window.location.search.substring(1),r=n.split("&"),o=0;o<r.length;o++){var l=r[o].split("=");l[0]==e&&(l[1]==t?i(a,1,s):i(a,0,s))}}function i(e,t,n){var i=new Date;i.setTime(i.getTime()+24*n*60*60*1e3);var r="expires="+i.toUTCString();document.cookie=e+"="+t+"; "+r+"; path=/"}var r="utm_source",o="criteo",a="crtg_dd",s=60;applyFlashDealsTime=function(){$(".fd-header").each(function(){var t=$(this),n=t.attr("value"),i=new Date(n),r=e(i);t.text(r)})},applyFlashDealsTime(),n(r,o)}),gaImpressions=function(){var e=$(".ga_design_container[data-ga-data][data-ga-pending]");e.each(function(){var e=$(this),t=e.data().gaData;ga("ec:addImpression",t),e.removeAttr("data-ga-pending")})},$(document).on("click",".ga_design_container[data-ga-data] a",function(){var e=$(this),t=$(e.parents(".ga_design_container[data-ga-data]")),n=t.data().gaData;ga("ec:addProduct",n),ga("ec:setAction","click",{list:n.list}),ga("send","event","UX","click","Results"),"undefined"!=typeof Unbxd&&unbxdTrack("click",{pid:n.id,prank:n.position})}),function(){"[object OperaMini]"===Object.prototype.toString.call(window.operamini)&&$(document).ready(function(){var e,t,n;return $("li.tabs > .tab-title").click(function(){var e,t;return t=$(".tabs-content > .content.active").attr("id"),$("#"+t).removeClass("active"),e=$($(this)[0].children[0]).attr("href"),$(e).addClass("active"),$("li.tabs > .tab-title.active").removeClass("active"),$(this).addClass("active")}),$(".addon_types").on("change",function(){var e;return e=$(this).attr("value"),$(this).find("option").each(function(){var t;return t=$(this).val(),t!==e&&$("#atv_"+t).hide(),$("#atv_"+t).show()})}),/cart/i.test(window.location.href)&&$(".item_block").length>0&&(n=$(".cart_checkout"),t=$(".add_place_order").attr("href"),n.hide(),e=$(".add_cont_shop"),e.css({background:"none","margin-top":"-7px","font-weight":"bold"}),e.before('<a class="opera_checkout_position_fix button small success" href="'+t+'">CHECKOUT</a>')),$(".sort_filter").length>0?($(".sort_filter").removeClass("fixed"),$(".sort_filter").addClass("opera_footer_fix")):void 0})}.call(this);var root,observer;root="undefined"!=typeof exports&&null!==exports?exports:this,root.lazyLoad=function(){if("IntersectionObserver"in window){var e=document.querySelectorAll(".js-lazy"),t={rootMargin:"-100px 0px 0px 0px",threshold:.1};observer=new IntersectionObserver(onIntersection,t),e.forEach(function(e){observer.observe(e)})}else if(-1==navigator.userAgent.indexOf("Opera"))"function"==typeof $(window).inView?loadInViewImage():loadScript("<%= asset_url('inview.min.js') %>",function(){loadInViewImage()});else{e=document.querySelectorAll(".js-lazy");for(var n=0,i=e.length;i>n;n++)preloadImage(e[n],"src")}},function(){var e,t,n,i;e=void 0,e=function(e){this.$form=$(e),this.$submitButton=$("button[type=submit]",this.$form),this.$errorField=this.$form.siblings(".wishlist-error"),this.$wishlistLoader=this.$form.siblings(".wishlist-loader"),this.action=this.$form.prop("action"),this.isAddToWishlist=this.$form.hasClass("new_wishlist"),this.$sibling=this.$form.siblings(this.isAddToWishlist?".delete_wishlist":".new_wishlist"),this.$siblingSubmitButton=$("button[type=submit]",this.$sibling),this.switchingForms=!1,this.submitingForm=!1,this.error=void 0,this.showSibling=function(){return e=this,this.startTransition("switchingForms"),this.$form.fadeOut(200,function(){return e.$sibling.fadeIn(200,function(){return e.completeTransition("switchingForms")})})},this.showSelf=function(){return this.$sibling.hide(),this.$form.show()},this.askSignIn=function(){return window.location="/accounts/sign_in"},this.disableButtons=function(){return this.$submitButton.prop("disabled",!0),this.$siblingSubmitButton.prop("disabled",!0)},this.enableButtons=function(){return this.inTransition()?void 0:(this.$submitButton.prop("disabled",!1),this.$siblingSubmitButton.prop("disabled",!1))},this.serialize=function(){return this.$form.serialize()},this.beforSubmitForm=function(){return this.startTransition("submitingForm"),this.showSibling()},this.completeSubmitForm=function(e){return this.error=e,this.completeTransition("submitingForm")},this.submitForm=function(){return e=this,signedIn()?$.ajax({type:"POST",url:this.action,dataType:"JSON",data:this.serialize(),beforeSend:function(){return e.beforSubmitForm()},success:function(t){var n;return n=e.isAddToWishlist?t.error:null,e.completeSubmitForm(n)},error:function(){return e.completeSubmitForm("something went wrong")},statusCode:{401:function(){return e.askSignIn()}}}):e.askSignIn()},this.startTransition=function(e){return"switchingForms"===e?this.switchingForms=!0:"submitingForm"===e&&(this.submitingForm=!0),this.disableButtons()},this.completeTransition=function(e){return"switchingForms"===e?this.switchingForms=!1:"submitingForm"===e&&(this.submitingForm=!1),this.error&&this.showSelf(),this.enableButtons()},this.inTransition=function(){return this.switchingForms||this.submitingForm}},$(document).on("click",".ga-track-plp-heart-click",function(){return ga("send","event","UX","click","wishlist click on plp")}),$(document).on("submit",".new_wishlist",function(t){var n;return t.preventDefault(),n=new e(this),n.submitForm()}),$(document).on("submit",".delete_wishlist",function(t){var n;return t.preventDefault(),n=new e(this),n.submitForm()}),$(function(){}),window.enableWishList=function(e){var t;return t={design_ids:e||$.map($(".wishlist-forms.hide"),function(e){return e.dataset.designId}).join(",")},signedIn()&&t.design_ids.length>0?void $.get("/user/wishlists/filter",t,function(e){i(e.wishlist_design_ids)}):i([])},i=function(e){e&&e.length>0&&(t(e),n(e)),$(".wishlist-forms").removeClass("hide")},t=function(e){$.each(e,function(e,t){$("#design_"+t+"_new_wishlist").hide()})},n=function(e){$.each(e,function(e,t){$("#design_"+t+"_delete_wishlist").show()})},document.addEventListener("turbolinks:load",function(){return enableWishList()})}.call(this),function(){var e,t,n,i;i=document.getElementById("product_video"),n=function(){i.play()},t=function(){i.pause()},e=function(){i.muted=!0},$("#design_images").on("after-slide-change.fndtn.orbit",function(){var r;return r=document.getElementsByClassName("active")[0],r.contains(i)?n():i?(t(),e()):void 0})}.call(this),function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.Cleave=t():e.Cleave=t()}(this,function(){return function(e){function t(i){if(n[i])return n[i].exports;var r=n[i]={exports:{},id:i,loaded:!1};return e[i].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){(function(t){"use strict";var i=function(e,t){var n=this;if(n.element="string"==typeof e?document.querySelector(e):"undefined"!=typeof e.length&&e.length>0?e[0]:e,!n.element)throw new Error("[cleave.js] Please check the element");t.initValue=n.element.value,n.properties=i.DefaultProperties.assign({},t),n.init()};i.prototype={init:function(){var e=this,t=e.properties;return t.numeral||t.phone||t.creditCard||t.date||0!==t.blocksLength||t.prefix?(t.maxLength=i.Util.getMaxLength(t.blocks),e.isAndroid=i.Util.isAndroid(),e.lastInputValue="",e.onChangeListener=e.onChange.bind(e),e.onKeyDownListener=e.onKeyDown.bind(e),e.onFocusListener=e.onFocus.bind(e),e.onCutListener=e.onCut.bind(e),e.onCopyListener=e.onCopy.bind(e),e.element.addEventListener("input",e.onChangeListener),e.element.addEventListener("keydown",e.onKeyDownListener),e.element.addEventListener("focus",e.onFocusListener),e.element.addEventListener("cut",e.onCutListener),e.element.addEventListener("copy",e.onCopyListener),e.initPhoneFormatter(),e.initDateFormatter(),e.initNumeralFormatter(),void((t.initValue||t.prefix&&!t.noImmediatePrefix)&&e.onInput(t.initValue))):void e.onInput(t.initValue)},initNumeralFormatter:function(){var e=this,t=e.properties;t.numeral&&(t.numeralFormatter=new i.NumeralFormatter(t.numeralDecimalMark,t.numeralIntegerScale,t.numeralDecimalScale,t.numeralThousandsGroupStyle,t.numeralPositiveOnly,t.stripLeadingZeroes,t.delimiter))},initDateFormatter:function(){var e=this,t=e.properties;t.date&&(t.dateFormatter=new i.DateFormatter(t.datePattern),t.blocks=t.dateFormatter.getBlocks(),t.blocksLength=t.blocks.length,t.maxLength=i.Util.getMaxLength(t.blocks))},initPhoneFormatter:function(){var e=this,t=e.properties;if(t.phone)try{t.phoneFormatter=new i.PhoneFormatter(new t.root.Cleave.AsYouTypeFormatter(t.phoneRegionCode),t.delimiter)}catch(n){throw new Error("[cleave.js] Please include phone-type-formatter.{country}.js lib")}},onKeyDown:function(e){var t=this,n=t.properties,r=e.which||e.keyCode,o=i.Util,a=t.element.value;return o.isAndroidBackspaceKeydown(t.lastInputValue,a)&&(r=8),t.lastInputValue=a,8===r&&o.isDelimiter(a.slice(-n.delimiterLength),n.delimiter,n.delimiters)?void(n.backspace=!0):void(n.backspace=!1)},onChange:function(){this.onInput(this.element.value)},onFocus:function(){var e=this,t=e.properties;i.Util.fixPrefixCursor(e.element,t.prefix,t.delimiter,t.delimiters)},onCut:function(e){this.copyClipboardData(e),this.onInput("")},onCopy:function(e){this.copyClipboardData(e)},copyClipboardData:function(e){var t=this,n=t.properties,r=i.Util,o=t.element.value,a="";a=n.copyDelimiter?o:r.stripDelimiters(o,n.delimiter,n.delimiters);try{e.clipboardData?e.clipboardData.setData("Text",a):window.clipboardData.setData("Text",a),e.preventDefault()}catch(s){}},onInput:function(e){var t=this,n=t.properties,r=i.Util;return n.numeral||!n.backspace||r.isDelimiter(e.slice(-n.delimiterLength),n.delimiter,n.delimiters)||(e=r.headStr(e,e.length-n.delimiterLength)),n.phone?(n.result=!n.prefix||n.noImmediatePrefix&&!e.length?n.phoneFormatter.format(e):n.prefix+n.phoneFormatter.format(e).slice(n.prefix.length),void t.updateValueState()):n.numeral?(n.result=!n.prefix||n.noImmediatePrefix&&!e.length?n.numeralFormatter.format(e):n.prefix+n.numeralFormatter.format(e),void t.updateValueState()):(n.date&&(e=n.dateFormatter.getValidatedDate(e)),e=r.stripDelimiters(e,n.delimiter,n.delimiters),e=r.getPrefixStrippedValue(e,n.prefix,n.prefixLength,n.result),e=n.numericOnly?r.strip(e,/[^\d]/g):e,e=n.uppercase?e.toUpperCase():e,e=n.lowercase?e.toLowerCase():e,!n.prefix||n.noImmediatePrefix&&!e.length||(e=n.prefix+e,0!==n.blocksLength)?(n.creditCard&&t.updateCreditCardPropsByValue(e),e=r.headStr(e,n.maxLength),n.result=r.getFormattedValue(e,n.blocks,n.blocksLength,n.delimiter,n.delimiters,n.delimiterLazyShow),void t.updateValueState()):(n.result=e,void t.updateValueState()))},updateCreditCardPropsByValue:function(e){var t,n=this,r=n.properties,o=i.Util;o.headStr(r.result,4)!==o.headStr(e,4)&&(t=i.CreditCardDetector.getInfo(e,r.creditCardStrictMode),r.blocks=t.blocks,r.blocksLength=r.blocks.length,r.maxLength=o.getMaxLength(r.blocks),r.creditCardType!==t.type&&(r.creditCardType=t.type,r.onCreditCardTypeChanged.call(n,r.creditCardType)))},updateValueState:function(){var e=this,t=i.Util,n=e.properties;if(e.element){var r=e.element.selectionEnd,o=e.element.value,a=n.result;if(r=t.getNextCursorPosition(r,o,a,n.delimiter,n.delimiters),e.isAndroid)return void window.setTimeout(function(){e.element.value=a,t.setSelection(e.element,r,n.document,!1),e.callOnValueChanged()},1);e.element.value=a,t.setSelection(e.element,r,n.document,!1),e.callOnValueChanged()}},callOnValueChanged:function(){var e=this,t=e.properties;t.onValueChanged.call(e,{target:{value:t.result,rawValue:e.getRawValue()}})},setPhoneRegionCode:function(e){var t=this,n=t.properties;n.phoneRegionCode=e,t.initPhoneFormatter(),t.onChange()},setRawValue:function(e){var t=this,n=t.properties;e=void 0!==e&&null!==e?e.toString():"",n.numeral&&(e=e.replace(".",n.numeralDecimalMark)),n.backspace=!1,t.element.value=e,t.onInput(e)},getRawValue:function(){var e=this,t=e.properties,n=i.Util,r=e.element.value;return t.rawValueTrimPrefix&&(r=n.getPrefixStrippedValue(r,t.prefix,t.prefixLength,t.result)),r=t.numeral?t.numeralFormatter.getRawValue(r):n.stripDelimiters(r,t.delimiter,t.delimiters)},getISOFormatDate:function(){var e=this,t=e.properties;return t.date?t.dateFormatter.getISOFormatDate():""},getFormattedValue:function(){return this.element.value},destroy:function(){var e=this;e.element.removeEventListener("input",e.onChangeListener),e.element.removeEventListener("keydown",e.onKeyDownListener),e.element.removeEventListener("focus",e.onFocusListener),e.element.removeEventListener("cut",e.onCutListener),e.element.removeEventListener("copy",e.onCopyListener)},toString:function(){return"[Cleave Object]"}},i.NumeralFormatter=n(1),i.DateFormatter=n(2),i.PhoneFormatter=n(3),i.CreditCardDetector=n(4),i.Util=n(5),i.DefaultProperties=n(6),("object"==typeof t&&t?t:window).Cleave=i,e.exports=i}).call(t,function(){return this}())},function(e){"use strict";var t=function(e,n,i,r,o,a,s){var l=this;l.numeralDecimalMark=e||".",l.numeralIntegerScale=n>0?n:0,l.numeralDecimalScale=i>=0?i:2,l.numeralThousandsGroupStyle=r||t.groupStyle.thousand,l.numeralPositiveOnly=!!o,l.stripLeadingZeroes=a!==!1,l.delimiter=s||""===s?s:",",l.delimiterRE=s?new RegExp("\\"+s,"g"):""};t.groupStyle={thousand:"thousand",lakh:"lakh",wan:"wan",none:"none"},t.prototype={getRawValue:function(e){return e.replace(this.delimiterRE,"").replace(this.numeralDecimalMark,".")},format:function(e){var n,i,r=this,o="";switch(e=e.replace(/[A-Za-z]/g,"").replace(r.numeralDecimalMark,"M").replace(/[^\dM-]/g,"").replace(/^\-/,"N").replace(/\-/g,"").replace("N",r.numeralPositiveOnly?"":"-").replace("M",r.numeralDecimalMark),r.stripLeadingZeroes&&(e=e.replace(/^(-)?0+(?=\d)/,"$1")),i=e,e.indexOf(r.numeralDecimalMark)>=0&&(n=e.split(r.numeralDecimalMark),i=n[0],o=r.numeralDecimalMark+n[1].slice(0,r.numeralDecimalScale)),r.numeralIntegerScale>0&&(i=i.slice(0,r.numeralIntegerScale+("-"===e.slice(0,1)?1:0))),r.numeralThousandsGroupStyle){case t.groupStyle.lakh:i=i.replace(/(\d)(?=(\d\d)+\d$)/g,"$1"+r.delimiter);break;case t.groupStyle.wan:i=i.replace(/(\d)(?=(\d{4})+$)/g,"$1"+r.delimiter);break;case t.groupStyle.thousand:i=i.replace(/(\d)(?=(\d{3})+$)/g,"$1"+r.delimiter)}return i.toString()+(r.numeralDecimalScale>0?o.toString():"")}},e.exports=t},function(e){"use strict";var t=function(e){var t=this;t.date=[],t.blocks=[],t.datePattern=e,t.initBlocks()};t.prototype={initBlocks:function(){var e=this;e.datePattern.forEach(function(t){e.blocks.push("Y"===t?4:2)})},getISOFormatDate:function(){var e=this,t=e.date;return t[2]?t[2]+"-"+e.addLeadingZero(t[1])+"-"+e.addLeadingZero(t[0]):""},getBlocks:function(){return this.blocks},getValidatedDate:function(e){var t=this,n="";return e=e.replace(/[^\d]/g,""),t.blocks.forEach(function(i,r){if(e.length>0){var o=e.slice(0,i),a=o.slice(0,1),s=e.slice(i);switch(t.datePattern[r]){case"d":"00"===o?o="01":parseInt(a,10)>3?o="0"+a:parseInt(o,10)>31&&(o="31");break;case"m":"00"===o?o="01":parseInt(a,10)>1?o="0"+a:parseInt(o,10)>12&&(o="12")}n+=o,e=s}}),this.getFixedDateString(n)},getFixedDateString:function(e){var t,n,i,r=this,o=r.datePattern,a=[],s=0,l=0,c=0,u=0,d=0,p=0,f=!1;return 4===e.length&&"y"!==o[0].toLowerCase()&&"y"!==o[1].toLowerCase()&&(u="d"===o[0]?0:2,d=2-u,t=parseInt(e.slice(u,u+2),10),n=parseInt(e.slice(d,d+2),10),a=this.getFixedDate(t,n,0)),8===e.length&&(o.forEach(function(e,t){switch(e){case"d":s=t;break;case"m":l=t;break;default:c=t}}),p=2*c,u=c>=s?2*s:2*s+2,d=c>=l?2*l:2*l+2,t=parseInt(e.slice(u,u+2),10),n=parseInt(e.slice(d,d+2),10),i=parseInt(e.slice(p,p+4),10),f=4===e.slice(p,p+4).length,a=this.getFixedDate(t,n,i)),r.date=a,0===a.length?e:o.reduce(function(e,t){switch(t){case"d":return e+r.addLeadingZero(a[0]);
case"m":return e+r.addLeadingZero(a[1]);default:return e+(f?r.addLeadingZeroForYear(a[2]):"")}},"")},getFixedDate:function(e,t,n){return e=Math.min(e,31),t=Math.min(t,12),n=parseInt(n||0,10),(7>t&&t%2===0||t>8&&t%2===1)&&(e=Math.min(e,2===t?this.isLeapYear(n)?29:28:30)),[e,t,n]},isLeapYear:function(e){return e%4===0&&e%100!==0||e%400===0},addLeadingZero:function(e){return(10>e?"0":"")+e},addLeadingZeroForYear:function(e){return(10>e?"000":100>e?"00":1e3>e?"0":"")+e}},e.exports=t},function(e){"use strict";var t=function(e,t){var n=this;n.delimiter=t||""===t?t:" ",n.delimiterRE=t?new RegExp("\\"+t,"g"):"",n.formatter=e};t.prototype={setFormatter:function(e){this.formatter=e},format:function(e){var t=this;t.formatter.clear(),e=e.replace(/[^\d+]/g,""),e=e.replace(t.delimiterRE,"");for(var n,i="",r=!1,o=0,a=e.length;a>o;o++)n=t.formatter.inputDigit(e.charAt(o)),/[\s()-]/g.test(n)?(i=n,r=!0):r||(i=n);return i=i.replace(/[()]/g,""),i=i.replace(/[\s-]/g,t.delimiter)}},e.exports=t},function(e){"use strict";var t={blocks:{uatp:[4,5,6],amex:[4,6,5],diners:[4,6,4],discover:[4,4,4,4],mastercard:[4,4,4,4],dankort:[4,4,4,4],instapayment:[4,4,4,4],jcb15:[4,6,5],jcb:[4,4,4,4],maestro:[4,4,4,4],visa:[4,4,4,4],mir:[4,4,4,4],unionPay:[4,4,4,4],general:[4,4,4,4],generalStrict:[4,4,4,7]},re:{uatp:/^(?!1800)1\d{0,14}/,amex:/^3[47]\d{0,13}/,discover:/^(?:6011|65\d{0,2}|64[4-9]\d?)\d{0,12}/,diners:/^3(?:0([0-5]|9)|[689]\d?)\d{0,11}/,mastercard:/^(5[1-5]\d{0,2}|22[2-9]\d{0,1}|2[3-7]\d{0,2})\d{0,12}/,dankort:/^(5019|4175|4571)\d{0,12}/,instapayment:/^63[7-9]\d{0,13}/,jcb15:/^(?:2131|1800)\d{0,11}/,jcb:/^(?:35\d{0,2})\d{0,12}/,maestro:/^(?:5[0678]\d{0,2}|6304|67\d{0,2})\d{0,12}/,mir:/^220[0-4]\d{0,12}/,visa:/^4\d{0,15}/,unionPay:/^62\d{0,14}/},getInfo:function(e,n){var i=t.blocks,r=t.re;n=!!n;for(var o in r)if(r[o].test(e)){var a;return a=n?i.generalStrict:i[o],{type:o,blocks:a}}return{type:"unknown",blocks:n?i.generalStrict:i.general}}};e.exports=t},function(e){"use strict";var t={noop:function(){},strip:function(e,t){return e.replace(t,"")},isDelimiter:function(e,t,n){return 0===n.length?e===t:n.some(function(t){return e===t?!0:void 0})},getDelimiterREByDelimiter:function(e){return new RegExp(e.replace(/([.?*+^$[\]\\(){}|-])/g,"\\$1"),"g")},getNextCursorPosition:function(e,t,n,i,r){return t.length===e?n.length:e+this.getPositionOffset(e,t,n,i,r)},getPositionOffset:function(e,t,n,i,r){var o,a,s;return o=this.stripDelimiters(t.slice(0,e),i,r),a=this.stripDelimiters(n.slice(0,e),i,r),s=o.length-a.length,0!==s?s/Math.abs(s):0},stripDelimiters:function(e,t,n){var i=this;if(0===n.length){var r=t?i.getDelimiterREByDelimiter(t):"";return e.replace(r,"")}return n.forEach(function(t){e=e.replace(i.getDelimiterREByDelimiter(t),"")}),e},headStr:function(e,t){return e.slice(0,t)},getMaxLength:function(e){return e.reduce(function(e,t){return e+t},0)},getPrefixStrippedValue:function(e,t,n,i){if(e.slice(0,n)!==t)if(e.length<i.length)e=e.length>n?i:t;else{var r=this.getFirstDiffIndex(t,e.slice(0,n));e=t+e.slice(r,r+1)+e.slice(n+1)}return e.slice(n)},getFirstDiffIndex:function(e,t){for(var n=0;e.charAt(n)===t.charAt(n);)if(""===e.charAt(n++))return-1;return n},getFormattedValue:function(e,t,n,i,r,o){var a,s="",l=r.length>0;return 0===n?e:(t.forEach(function(t,c){if(e.length>0){var u=e.slice(0,t),d=e.slice(t);a=l?r[o?c-1:c]||a:i,o?(c>0&&(s+=a),s+=u):(s+=u,u.length===t&&n-1>c&&(s+=a)),e=d}}),s)},fixPrefixCursor:function(e,t,n,i){if(e){var r=e.value,o=n||i[0]||" ";if(e.setSelectionRange&&t&&!(t.length+o.length<r.length)){var a=2*r.length;setTimeout(function(){e.setSelectionRange(a,a)},1)}}},setSelection:function(e,t,n){if(e===n.activeElement&&!(e&&e.value.length<=t))if(e.createTextRange){var i=e.createTextRange();i.move("character",t),i.select()}else try{e.setSelectionRange(t,t)}catch(r){console.warn("The input element type does not support selection")}},isAndroid:function(){return navigator&&/android/i.test(navigator.userAgent)},isAndroidBackspaceKeydown:function(e,t){return this.isAndroid()&&e&&t?t===e.slice(0,-1):!1}};e.exports=t},function(e,t){(function(t){"use strict";var n={assign:function(e,n){return e=e||{},n=n||{},e.creditCard=!!n.creditCard,e.creditCardStrictMode=!!n.creditCardStrictMode,e.creditCardType="",e.onCreditCardTypeChanged=n.onCreditCardTypeChanged||function(){},e.phone=!!n.phone,e.phoneRegionCode=n.phoneRegionCode||"AU",e.phoneFormatter={},e.date=!!n.date,e.datePattern=n.datePattern||["d","m","Y"],e.dateFormatter={},e.numeral=!!n.numeral,e.numeralIntegerScale=n.numeralIntegerScale>0?n.numeralIntegerScale:0,e.numeralDecimalScale=n.numeralDecimalScale>=0?n.numeralDecimalScale:2,e.numeralDecimalMark=n.numeralDecimalMark||".",e.numeralThousandsGroupStyle=n.numeralThousandsGroupStyle||"thousand",e.numeralPositiveOnly=!!n.numeralPositiveOnly,e.stripLeadingZeroes=n.stripLeadingZeroes!==!1,e.numericOnly=e.creditCard||e.date||!!n.numericOnly,e.uppercase=!!n.uppercase,e.lowercase=!!n.lowercase,e.prefix=e.creditCard||e.date?"":n.prefix||"",e.noImmediatePrefix=!!n.noImmediatePrefix,e.prefixLength=e.prefix.length,e.rawValueTrimPrefix=!!n.rawValueTrimPrefix,e.copyDelimiter=!!n.copyDelimiter,e.initValue=void 0!==n.initValue&&null!==n.initValue?n.initValue.toString():"",e.delimiter=n.delimiter||""===n.delimiter?n.delimiter:n.date?"/":n.numeral?",":(n.phone," "),e.delimiterLength=e.delimiter.length,e.delimiterLazyShow=!!n.delimiterLazyShow,e.delimiters=n.delimiters||[],e.blocks=n.blocks||[],e.blocksLength=e.blocks.length,e.document=n.document||document,e.root="object"==typeof t&&t?t:window,e.maxLength=0,e.backspace=!1,e.result="",e.onValueChanged=n.onValueChanged||function(){},e}};e.exports=n}).call(t,function(){return this}())}])}),/*! PhotoSwipe - v4.1.2 - 2017-04-05
* http://photoswipe.com
* Copyright (c) 2017 Dmitry Semenov; */
function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";var e=function(e,t,n,i){var r={features:null,bind:function(e,t,n,i){var r=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var o=0;o<t.length;o++)t[o]&&e[r](t[o],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){r.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){r.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(r.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)if(t.hasOwnProperty(i)){if(n&&e.hasOwnProperty(i))continue;e[i]=t[i]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(r.features)return r.features;var e=r.createEl(),t=e.style,n="",i={};if(i.oldIE=document.all&&!document.addEventListener,i.touch="ontouchstart"in window,window.requestAnimationFrame&&(i.raf=window.requestAnimationFrame,i.caf=window.cancelAnimationFrame),i.pointerEvent=navigator.pointerEnabled||navigator.msPointerEnabled,!i.pointerEvent){var o=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&(a=parseInt(a[1],10),a>=1&&8>a&&(i.isOldIOSPhone=!0))}var s=o.match(/Android\s([0-9\.]*)/),l=s?s[1]:0;l=parseFloat(l),l>=1&&(4.4>l&&(i.isOldAndroid=!0),i.androidVersion=l),i.isMobileOpera=/opera mini|opera mobi/i.test(o)}for(var c,u,d=["transform","perspective","animationName"],p=["","webkit","Moz","ms","O"],f=0;4>f;f++){n=p[f];for(var h=0;3>h;h++)c=d[h],u=n+(n?c.charAt(0).toUpperCase()+c.slice(1):c),!i[c]&&u in t&&(i[c]=u);n&&!i.raf&&(n=n.toLowerCase(),i.raf=window[n+"RequestAnimationFrame"],i.raf&&(i.caf=window[n+"CancelAnimationFrame"]||window[n+"CancelRequestAnimationFrame"]))}if(!i.raf){var m=0;i.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-m)),i=window.setTimeout(function(){e(t+n)},n);return m=t+n,i},i.caf=function(e){clearTimeout(e)}}return i.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,r.features=i,i}};r.detectFeatures(),r.features.oldIE&&(r.bind=function(e,t,n,i){t=t.split(" ");for(var r,o=(i?"detach":"attach")+"Event",a=function(){n.handleEvent.call(n)},s=0;s<t.length;s++)if(r=t[s])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+r])return!1}else n["oldIE"+r]=a;e[o]("on"+r,n["oldIE"+r])}else e[o]("on"+r,n)});var o=this,a=25,s=3,l={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e?1:t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};r.extend(l,i);var c,u,d,p,f,h,m,g,v,y,b,_,w,x,C,k,T,S,$,E,D,L,I,A,F,N,P,R,M,q,j,O,H,z,B,W,V,U,Z,X,K,G,Y,J,Q,et,tt,nt,it,rt,ot,at,st,lt,ct,ut,dt,pt=function(){return{x:0,y:0}},ft=pt(),ht=pt(),mt=pt(),gt={},vt=0,yt={},bt=pt(),_t=0,wt=!0,xt=[],Ct={},kt=!1,Tt=function(e,t){r.extend(o,t.publicMethods),xt.push(e)},St=function(e){var t=ti();return e>t-1?e-t:0>e?t+e:e},$t={},Et=function(e,t){return $t[e]||($t[e]=[]),$t[e].push(t)},Dt=function(e){var t=$t[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(o,n)}},Lt=function(){return(new Date).getTime()},It=function(e){ct=e,o.bg.style.opacity=e*l.bgOpacity},At=function(e,t,n,i,r){(!kt||r&&r!==o.currItem)&&(i/=r?r.fitRatio:o.currItem.fitRatio),e[L]=_+t+"px, "+n+"px"+w+" scale("+i+")"},Ft=function(e){rt&&(e&&(y>o.currItem.fitRatio?kt||(fi(o.currItem,!1,!0),kt=!0):kt&&(fi(o.currItem),kt=!1)),At(rt,mt.x,mt.y,y))},Nt=function(e){e.container&&At(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},Pt=function(e,t){t[L]=_+e+"px, 0px"+w},Rt=function(e,t){if(!l.loop&&t){var n=p+(bt.x*vt-e)/bt.x,i=Math.round(e-_n.x);(0>n&&i>0||n>=ti()-1&&0>i)&&(e=_n.x+i*l.mainScrollEndFriction)}_n.x=e,Pt(e,f)},Mt=function(e,t){var n=wn[e]-yt[e];return ht[e]+ft[e]+n-n*(t/b)},qt=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},jt=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},Ot=null,Ht=function(){Ot&&(r.unbind(document,"mousemove",Ht),r.addClass(e,"pswp--has_mouse"),l.mouseUsed=!0,Dt("mouseUsed")),Ot=setTimeout(function(){Ot=null},100)},zt=function(){r.bind(document,"keydown",o),j.transform&&r.bind(o.scrollWrap,"click",o),l.mouseUsed||r.bind(document,"mousemove",Ht),r.bind(window,"resize scroll orientationchange",o),Dt("bindEvents")},Bt=function(){r.unbind(window,"resize scroll orientationchange",o),r.unbind(window,"scroll",v.scroll),r.unbind(document,"keydown",o),r.unbind(document,"mousemove",Ht),j.transform&&r.unbind(o.scrollWrap,"click",o),Z&&r.unbind(window,m,o),clearTimeout(O),Dt("unbindEvents")},Wt=function(e,t){var n=ci(o.currItem,gt,e);return t&&(it=n),n},Vt=function(e){return e||(e=o.currItem),e.initialZoomLevel},Ut=function(e){return e||(e=o.currItem),e.w>0?l.maxSpreadZoom:1},Zt=function(e,t,n,i){return i===o.currItem.initialZoomLevel?(n[e]=o.currItem.initialPosition[e],!0):(n[e]=Mt(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]?(n[e]=t.max[e],!0):!1)},Xt=function(){if(L){var t=j.perspective&&!A;return _="translate"+(t?"3d(":"("),void(w=j.perspective?", 0px)":")")}L="left",r.addClass(e,"pswp--ie"),Pt=function(e,t){t.left=e+"px"},Nt=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,i=t*e.w,r=t*e.h;n.width=i+"px",n.height=r+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},Ft=function(){if(rt){var e=rt,t=o.currItem,n=t.fitRatio>1?1:t.fitRatio,i=n*t.w,r=n*t.h;e.width=i+"px",e.height=r+"px",e.left=mt.x+"px",e.top=mt.y+"px"}}},Kt=function(e){var t="";l.escKey&&27===e.keyCode?t="close":l.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,o[t]()))},Gt=function(e){e&&(G||K||ot||V)&&(e.preventDefault(),e.stopPropagation())},Yt=function(){o.setScrollOffset(0,r.getScrollY())},Jt={},Qt=0,en=function(e){Jt[e]&&(Jt[e].raf&&N(Jt[e].raf),Qt--,delete Jt[e])},tn=function(e){Jt[e]&&en(e),Jt[e]||(Qt++,Jt[e]={})},nn=function(){for(var e in Jt)Jt.hasOwnProperty(e)&&en(e)},rn=function(e,t,n,i,r,o,a){var s,l=Lt();tn(e);var c=function(){if(Jt[e]){if(s=Lt()-l,s>=i)return en(e),o(n),void(a&&a());o((n-t)*r(s/i)+t),Jt[e].raf=F(c)}};c()},on={shout:Dt,listen:Et,viewportSize:gt,options:l,isMainScrollAnimating:function(){return ot},getZoomLevel:function(){return y},getCurrentIndex:function(){return p},isDragging:function(){return Z},isZooming:function(){return et},setScrollOffset:function(e,t){yt.x=e,q=yt.y=t,Dt("updateScrollOffset",yt)},applyZoomPan:function(e,t,n,i){mt.x=t,mt.y=n,y=e,Ft(i)},init:function(){if(!c&&!u){var n;o.framework=r,o.template=e,o.bg=r.getChildByClass(e,"pswp__bg"),P=e.className,c=!0,j=r.detectFeatures(),F=j.raf,N=j.caf,L=j.transform,M=j.oldIE,o.scrollWrap=r.getChildByClass(e,"pswp__scroll-wrap"),o.container=r.getChildByClass(o.scrollWrap,"pswp__container"),f=o.container.style,o.itemHolders=k=[{el:o.container.children[0],wrap:0,index:-1},{el:o.container.children[1],wrap:0,index:-1},{el:o.container.children[2],wrap:0,index:-1}],k[0].el.style.display=k[2].el.style.display="none",Xt(),v={resize:o.updateSize,orientationchange:function(){clearTimeout(O),O=setTimeout(function(){gt.x!==o.scrollWrap.clientWidth&&o.updateSize()},500)},scroll:Yt,keydown:Kt,click:Gt};var i=j.isOldIOSPhone||j.isOldAndroid||j.isMobileOpera;for(j.animationName&&j.transform&&!i||(l.showAnimationDuration=l.hideAnimationDuration=0),n=0;n<xt.length;n++)o["init"+xt[n]]();if(t){var a=o.ui=new t(o,r);a.init()}Dt("firstUpdate"),p=p||l.index||0,(isNaN(p)||0>p||p>=ti())&&(p=0),o.currItem=ei(p),(j.isOldIOSPhone||j.isOldAndroid)&&(wt=!1),e.setAttribute("aria-hidden","false"),l.modal&&(wt?e.style.position="fixed":(e.style.position="absolute",e.style.top=r.getScrollY()+"px")),void 0===q&&(Dt("initialLayout"),q=R=r.getScrollY());var d="pswp--open ";for(l.mainClass&&(d+=l.mainClass+" "),l.showHideOpacity&&(d+="pswp--animate_opacity "),d+=A?"pswp--touch":"pswp--notouch",d+=j.animationName?" pswp--css_animation":"",d+=j.svg?" pswp--svg":"",r.addClass(e,d),o.updateSize(),h=-1,_t=null,n=0;s>n;n++)Pt((n+h)*bt.x,k[n].el.style);M||r.bind(o.scrollWrap,g,o),Et("initialZoomInEnd",function(){o.setContent(k[0],p-1),o.setContent(k[2],p+1),k[0].el.style.display=k[2].el.style.display="block",l.focus&&e.focus(),zt()}),o.setContent(k[1],p),o.updateCurrItem(),Dt("afterInit"),wt||(x=setInterval(function(){Qt||Z||et||y!==o.currItem.initialZoomLevel||o.updateSize()},1e3)),r.addClass(e,"pswp--visible")}},close:function(){c&&(c=!1,u=!0,Dt("close"),Bt(),ii(o.currItem,null,!0,o.destroy))},destroy:function(){Dt("destroy"),Gn&&clearTimeout(Gn),e.setAttribute("aria-hidden","true"),e.className=P,x&&clearInterval(x),r.unbind(o.scrollWrap,g,o),r.unbind(window,"scroll",o),Sn(),nn(),$t=null},panTo:function(e,t,n){n||(e>it.min.x?e=it.min.x:e<it.max.x&&(e=it.max.x),t>it.min.y?t=it.min.y:t<it.max.y&&(t=it.max.y)),mt.x=e,mt.y=t,Ft()},handleEvent:function(e){e=e||window.event,v[e.type]&&v[e.type](e)},goTo:function(e){e=St(e);var t=e-p;_t=t,p=e,o.currItem=ei(p),vt-=t,Rt(bt.x*vt),nn(),ot=!1,o.updateCurrItem()},next:function(){o.goTo(p+1)},prev:function(){o.goTo(p-1)},updateCurrZoomItem:function(e){if(e&&Dt("beforeChange",0),k[1].el.children.length){var t=k[1].el.children[0];rt=r.hasClass(t,"pswp__zoom-wrap")?t.style:null}else rt=null;it=o.currItem.bounds,b=y=o.currItem.initialZoomLevel,mt.x=it.center.x,mt.y=it.center.y,e&&Dt("afterChange")},invalidateCurrItems:function(){C=!0;for(var e=0;s>e;e++)k[e].item&&(k[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==_t){var t,n=Math.abs(_t);if(!(e&&2>n)){o.currItem=ei(p),kt=!1,Dt("beforeChange",_t),n>=s&&(h+=_t+(_t>0?-s:s),n=s);for(var i=0;n>i;i++)_t>0?(t=k.shift(),k[s-1]=t,h++,Pt((h+2)*bt.x,t.el.style),o.setContent(t,p-n+i+1+1)):(t=k.pop(),k.unshift(t),h--,Pt(h*bt.x,t.el.style),o.setContent(t,p+n-i-1-1));if(rt&&1===Math.abs(_t)){var r=ei(T);r.initialZoomLevel!==y&&(ci(r,gt),fi(r),Nt(r))}_t=0,o.updateCurrZoomItem(),T=p,Dt("afterChange")}}},updateSize:function(t){if(!wt&&l.modal){var n=r.getScrollY();if(q!==n&&(e.style.top=n+"px",q=n),!t&&Ct.x===window.innerWidth&&Ct.y===window.innerHeight)return;Ct.x=window.innerWidth,Ct.y=window.innerHeight,e.style.height=Ct.y+"px"}if(gt.x=o.scrollWrap.clientWidth,gt.y=o.scrollWrap.clientHeight,Yt(),bt.x=gt.x+Math.round(gt.x*l.spacing),bt.y=gt.y,Rt(bt.x*vt),Dt("beforeResize"),void 0!==h){for(var i,a,c,u=0;s>u;u++)i=k[u],Pt((u+h)*bt.x,i.el.style),c=p+u-1,l.loop&&ti()>2&&(c=St(c)),a=ei(c),a&&(C||a.needsUpdate||!a.bounds)?(o.cleanSlide(a),o.setContent(i,c),1===u&&(o.currItem=a,o.updateCurrZoomItem(!0)),a.needsUpdate=!1):-1===i.index&&c>=0&&o.setContent(i,c),a&&a.container&&(ci(a,gt),fi(a),Nt(a));C=!1}b=y=o.currItem.initialZoomLevel,it=o.currItem.bounds,it&&(mt.x=it.center.x,mt.y=it.center.y,Ft(!0)),Dt("resize")},zoomTo:function(e,t,n,i,o){t&&(b=y,wn.x=Math.abs(t.x)-mt.x,wn.y=Math.abs(t.y)-mt.y,qt(ht,mt));var a=Wt(e,!1),s={};Zt("x",a,s,e),Zt("y",a,s,e);var l=y,c={x:mt.x,y:mt.y};jt(s);var u=function(t){1===t?(y=e,mt.x=s.x,mt.y=s.y):(y=(e-l)*t+l,mt.x=(s.x-c.x)*t+c.x,mt.y=(s.y-c.y)*t+c.y),o&&o(t),Ft(1===t)};n?rn("customZoomTo",0,1,n,i||r.easing.sine.inOut,u):u(1)}},an=30,sn=10,ln={},cn={},un={},dn={},pn={},fn=[],hn={},mn=[],gn={},vn=0,yn=pt(),bn=0,_n=pt(),wn=pt(),xn=pt(),Cn=function(e,t){return e.x===t.x&&e.y===t.y},kn=function(e,t){return Math.abs(e.x-t.x)<a&&Math.abs(e.y-t.y)<a},Tn=function(e,t){return gn.x=Math.abs(e.x-t.x),gn.y=Math.abs(e.y-t.y),Math.sqrt(gn.x*gn.x+gn.y*gn.y)},Sn=function(){Y&&(N(Y),Y=null)},$n=function(){Z&&(Y=F($n),Bn())},En=function(){return!("fit"===l.scaleMode&&y===o.currItem.initialZoomLevel)},Dn=function(e,t){return e&&e!==document?e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1?!1:t(e)?e:Dn(e.parentNode,t):!1},Ln={},In=function(e,t){return Ln.prevent=!Dn(e.target,l.isClickableElement),Dt("preventDragEvent",e,t,Ln),Ln.prevent},An=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},Fn=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},Nn=function(e,t,n){if(e-z>50){var i=mn.length>2?mn.shift():{};i.x=t,i.y=n,mn.push(i),z=e}},Pn=function(){var e=mt.y-o.currItem.initialPosition.y;return 1-Math.abs(e/(gt.y/2))},Rn={},Mn={},qn=[],jn=function(e){for(;qn.length>0;)qn.pop();return I?(dt=0,fn.forEach(function(e){0===dt?qn[0]=e:1===dt&&(qn[1]=e),dt++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(qn[0]=An(e.touches[0],Rn),e.touches.length>1&&(qn[1]=An(e.touches[1],Mn))):(Rn.x=e.pageX,Rn.y=e.pageY,Rn.id="",qn[0]=Rn),qn},On=function(e,t){var n,i,r,a,s=0,c=mt[e]+t[e],u=t[e]>0,d=_n.x+t.x,p=_n.x-hn.x;return n=c>it.min[e]||c<it.max[e]?l.panEndFriction:1,c=mt[e]+t[e]*n,!l.allowPanToNext&&y!==o.currItem.initialZoomLevel||(rt?"h"!==at||"x"!==e||K||(u?(c>it.min[e]&&(n=l.panEndFriction,s=it.min[e]-c,i=it.min[e]-ht[e]),(0>=i||0>p)&&ti()>1?(a=d,0>p&&d>hn.x&&(a=hn.x)):it.min.x!==it.max.x&&(r=c)):(c<it.max[e]&&(n=l.panEndFriction,s=c-it.max[e],i=ht[e]-it.max[e]),(0>=i||p>0)&&ti()>1?(a=d,p>0&&d<hn.x&&(a=hn.x)):it.min.x!==it.max.x&&(r=c))):a=d,"x"!==e)?void(ot||J||y>o.currItem.fitRatio&&(mt[e]+=t[e]*n)):(void 0!==a&&(Rt(a,!0),J=a===hn.x?!1:!0),it.min.x!==it.max.x&&(void 0!==r?mt.x=r:J||(mt.x+=t.x*n)),void 0!==a)},Hn=function(e){if(!("mousedown"===e.type&&e.button>0)){if(Qn)return void e.preventDefault();if(!U||"mousedown"!==e.type){if(In(e,!0)&&e.preventDefault(),Dt("pointerDown"),I){var t=r.arraySearch(fn,e.pointerId,"id");0>t&&(t=fn.length),fn[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=jn(e),i=n.length;Q=null,nn(),Z&&1!==i||(Z=st=!0,r.bind(window,m,o),W=ut=lt=V=J=G=X=K=!1,at=null,Dt("firstTouchStart",n),qt(ht,mt),ft.x=ft.y=0,qt(dn,n[0]),qt(pn,dn),hn.x=bt.x*vt,mn=[{x:dn.x,y:dn.y}],z=H=Lt(),Wt(y,!0),Sn(),$n()),!et&&i>1&&!ot&&!J&&(b=y,K=!1,et=X=!0,ft.y=ft.x=0,qt(ht,mt),qt(ln,n[0]),qt(cn,n[1]),Fn(ln,cn,xn),wn.x=Math.abs(xn.x)-mt.x,wn.y=Math.abs(xn.y)-mt.y,tt=nt=Tn(ln,cn))}}},zn=function(e){if(e.preventDefault(),I){var t=r.arraySearch(fn,e.pointerId,"id");if(t>-1){var n=fn[t];n.x=e.pageX,n.y=e.pageY}}if(Z){var i=jn(e);if(at||G||et)Q=i;else if(_n.x!==bt.x*vt)at="h";else{var o=Math.abs(i[0].x-dn.x)-Math.abs(i[0].y-dn.y);Math.abs(o)>=sn&&(at=o>0?"h":"v",Q=i)}}},Bn=function(){if(Q){var e=Q.length;if(0!==e)if(qt(ln,Q[0]),un.x=ln.x-dn.x,un.y=ln.y-dn.y,et&&e>1){if(dn.x=ln.x,dn.y=ln.y,!un.x&&!un.y&&Cn(Q[1],cn))return;qt(cn,Q[1]),K||(K=!0,Dt("zoomGestureStarted"));var t=Tn(ln,cn),n=Xn(t);n>o.currItem.initialZoomLevel+o.currItem.initialZoomLevel/15&&(ut=!0);var i=1,r=Vt(),a=Ut();if(r>n)if(l.pinchToClose&&!ut&&b<=o.currItem.initialZoomLevel){var s=r-n,c=1-s/(r/1.2);It(c),Dt("onPinchClose",c),lt=!0}else i=(r-n)/r,i>1&&(i=1),n=r-i*(r/3);else n>a&&(i=(n-a)/(6*r),i>1&&(i=1),n=a+i*r);0>i&&(i=0),tt=t,Fn(ln,cn,yn),ft.x+=yn.x-xn.x,ft.y+=yn.y-xn.y,qt(xn,yn),mt.x=Mt("x",n),mt.y=Mt("y",n),W=n>y,y=n,Ft()}else{if(!at)return;if(st&&(st=!1,Math.abs(un.x)>=sn&&(un.x-=Q[0].x-pn.x),Math.abs(un.y)>=sn&&(un.y-=Q[0].y-pn.y)),dn.x=ln.x,dn.y=ln.y,0===un.x&&0===un.y)return;if("v"===at&&l.closeOnVerticalDrag&&!En()){ft.y+=un.y,mt.y+=un.y;var u=Pn();return V=!0,Dt("onVerticalDrag",u),It(u),void Ft()}Nn(Lt(),ln.x,ln.y),G=!0,it=o.currItem.bounds;var d=On("x",un);d||(On("y",un),jt(mt),Ft())}}},Wn=function(e){if(j.isOldAndroid){if(U&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(U),U=setTimeout(function(){U=0},600))}Dt("pointerUp"),In(e,!1)&&e.preventDefault();var t;if(I){var n=r.arraySearch(fn,e.pointerId,"id");if(n>-1)if(t=fn.splice(n,1)[0],navigator.pointerEnabled)t.type=e.pointerType||"mouse";else{var i={4:"mouse",2:"touch",3:"pen"};t.type=i[e.pointerType],t.type||(t.type=e.pointerType||"mouse")}}var a,s=jn(e),c=s.length;if("mouseup"===e.type&&(c=0),2===c)return Q=null,!0;1===c&&qt(pn,s[0]),0!==c||at||ot||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Dt("touchRelease",e,t));var u=-1;if(0===c&&(Z=!1,r.unbind(window,m,o),Sn(),et?u=0:-1!==bn&&(u=Lt()-bn)),bn=1===c?Lt():-1,a=-1!==u&&150>u?"zoom":"swipe",et&&2>c&&(et=!1,1===c&&(a="zoomPointerUp"),Dt("zoomGestureEnded")),Q=null,G||K||ot||V)if(nn(),B||(B=Vn()),B.calculateSwipeSpeed("x"),V){var d=Pn();if(d<l.verticalDragRange)o.close();else{var p=mt.y,f=ct;rn("verticalDrag",0,1,300,r.easing.cubic.out,function(e){mt.y=(o.currItem.initialPosition.y-p)*e+p,It((1-f)*e+f),Ft()}),Dt("onVerticalDrag",1)}}else{if((J||ot)&&0===c){var h=Zn(a,B);if(h)return;a="zoomPointerUp"}if(!ot)return"swipe"!==a?void Kn():void(!J&&y>o.currItem.fitRatio&&Un(B))}},Vn=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(i){mn.length>1?(e=Lt()-z+50,t=mn[mn.length-2][i]):(e=Lt()-H,t=pn[i]),n.lastFlickOffset[i]=dn[i]-t,n.lastFlickDist[i]=Math.abs(n.lastFlickOffset[i]),n.lastFlickSpeed[i]=n.lastFlickDist[i]>20?n.lastFlickOffset[i]/e:0,Math.abs(n.lastFlickSpeed[i])<.1&&(n.lastFlickSpeed[i]=0),n.slowDownRatio[i]=.95,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatio[i]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(mt[e]>it.min[e]?n.backAnimDestination[e]=it.min[e]:mt[e]<it.max[e]&&(n.backAnimDestination[e]=it.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,rn("bounceZoomPan"+e,mt[e],n.backAnimDestination[e],t||300,r.easing.sine.out,function(t){mt[e]=t,Ft()}))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,mt[e]+=n.distanceOffset[e])},panAnimLoop:function(){return Jt.zoomPan&&(Jt.zoomPan.raf=F(n.panAnimLoop),n.now=Lt(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),Ft(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05)?(mt.x=Math.round(mt.x),mt.y=Math.round(mt.y),Ft(),void en("zoomPan")):void 0}};return n},Un=function(e){return e.calculateSwipeSpeed("y"),it=o.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(tn("zoomPan"),e.lastNow=Lt(),void e.panAnimLoop())},Zn=function(e,t){var n;ot||(vn=p);var i;if("swipe"===e){var a=dn.x-pn.x,s=t.lastFlickDist.x<10;a>an&&(s||t.lastFlickOffset.x>20)?i=-1:-an>a&&(s||t.lastFlickOffset.x<-20)&&(i=1)}var c;i&&(p+=i,0>p?(p=l.loop?ti()-1:0,c=!0):p>=ti()&&(p=l.loop?0:ti()-1,c=!0),(!c||l.loop)&&(_t+=i,vt-=i,n=!0));var u,d=bt.x*vt,f=Math.abs(d-_n.x);return n||d>_n.x==t.lastFlickSpeed.x>0?(u=Math.abs(t.lastFlickSpeed.x)>0?f/Math.abs(t.lastFlickSpeed.x):333,u=Math.min(u,400),u=Math.max(u,250)):u=333,vn===p&&(n=!1),ot=!0,Dt("mainScrollAnimStart"),rn("mainScroll",_n.x,d,u,r.easing.cubic.out,Rt,function(){nn(),ot=!1,vn=-1,(n||vn!==p)&&o.updateCurrItem(),Dt("mainScrollAnimComplete")}),n&&o.updateCurrItem(!0),n},Xn=function(e){return 1/nt*e*b},Kn=function(){var e=y,t=Vt(),n=Ut();t>y?e=t:y>n&&(e=n);var i,a=1,s=ct;return lt&&!W&&!ut&&t>y?(o.close(),!0):(lt&&(i=function(e){It((a-s)*e+s)}),o.zoomTo(e,0,200,r.easing.cubic.out,i),!0)};Tt("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,r){S=e+t,$=e+n,E=e+i,D=r?e+r:""};I=j.pointerEvent,I&&j.touch&&(j.touch=!1),I?navigator.pointerEnabled?e("pointer","down","move","up","cancel"):e("MSPointer","Down","Move","Up","Cancel"):j.touch?(e("touch","start","move","end","cancel"),A=!0):e("mouse","down","move","up"),m=$+" "+E+" "+D,g=S,I&&!A&&(A=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),o.likelyTouchDevice=A,v[S]=Hn,v[$]=zn,v[E]=Wn,D&&(v[D]=v[E]),j.touch&&(g+=" mousedown",m+=" mousemove mouseup",v.mousedown=v[S],v.mousemove=v[$],v.mouseup=v[E]),A||(l.allowPanToNext=!1)}}});var Gn,Yn,Jn,Qn,ei,ti,ni,ii=function(t,n,i,a){Gn&&clearTimeout(Gn),Qn=!0,Jn=!0;var s;t.initialLayout?(s=t.initialLayout,t.initialLayout=null):s=l.getThumbBoundsFn&&l.getThumbBoundsFn(p);var c=i?l.hideAnimationDuration:l.showAnimationDuration,u=function(){en("initialZoom"),i?(o.template.removeAttribute("style"),o.bg.removeAttribute("style")):(It(1),n&&(n.style.display="block"),r.addClass(e,"pswp--animated-in"),Dt("initialZoom"+(i?"OutEnd":"InEnd"))),a&&a(),Qn=!1};if(!c||!s||void 0===s.x)return Dt("initialZoom"+(i?"Out":"In")),y=t.initialZoomLevel,qt(mt,t.initialPosition),Ft(),e.style.opacity=i?0:1,It(1),void(c?setTimeout(function(){u()},c):u());var f=function(){var n=d,a=!o.currItem.src||o.currItem.loadError||l.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),i||(y=s.w/t.w,mt.x=s.x,mt.y=s.y-R,o[a?"template":"bg"].style.opacity=.001,Ft()),tn("initialZoom"),i&&!n&&r.removeClass(e,"pswp--animated-in"),a&&(i?r[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout(function(){r.addClass(e,"pswp--animate_opacity")},30)),Gn=setTimeout(function(){if(Dt("initialZoom"+(i?"Out":"In")),i){var o=s.w/t.w,l={x:mt.x,y:mt.y},d=y,p=ct,f=function(t){1===t?(y=o,mt.x=s.x,mt.y=s.y-q):(y=(o-d)*t+d,mt.x=(s.x-l.x)*t+l.x,mt.y=(s.y-q-l.y)*t+l.y),Ft(),a?e.style.opacity=1-t:It(p-t*p)};n?rn("initialZoom",0,1,c,r.easing.cubic.out,f,u):(f(1),Gn=setTimeout(u,c+20))}else y=t.initialZoomLevel,qt(mt,t.initialPosition),Ft(),It(1),a?e.style.opacity=1:It(1),Gn=setTimeout(u,c+20)},i?25:90)};f()},ri={},oi=[],ai={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Yn.length}},si=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},li=function(e,t,n){var i=e.bounds;i.center.x=Math.round((ri.x-t)/2),i.center.y=Math.round((ri.y-n)/2)+e.vGap.top,i.max.x=t>ri.x?Math.round(ri.x-t):i.center.x,i.max.y=n>ri.y?Math.round(ri.y-n)+e.vGap.top:i.center.y,i.min.x=t>ri.x?0:i.center.x,i.min.y=n>ri.y?e.vGap.top:i.center.y},ci=function(e,t,n){if(e.src&&!e.loadError){var i=!n;if(i&&(e.vGap||(e.vGap={top:0,bottom:0}),Dt("parseVerticalMargin",e)),ri.x=t.x,ri.y=t.y-e.vGap.top-e.vGap.bottom,i){var r=ri.x/e.w,o=ri.y/e.h;e.fitRatio=o>r?r:o;var a=l.scaleMode;"orig"===a?n=1:"fit"===a&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds=si())}if(!n)return;return li(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=si(),e.initialPosition=e.bounds.center,e.bounds},ui=function(e,t,n,i,r,a){t.loadError||i&&(t.imageAppended=!0,fi(t,i,t===o.currItem&&kt),n.appendChild(i),a&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},di=function(e){e.loading=!0,e.loaded=!1;var t=e.img=r.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},pi=function(e,t){return e.src&&e.loadError&&e.container?(t&&(e.container.innerHTML=""),e.container.innerHTML=l.errorMsg.replace("%url%",e.src),!0):void 0},fi=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var i=n?e.w:Math.round(e.w*e.fitRatio),r=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=r+"px"),t.style.width=i+"px",t.style.height=r+"px"}},hi=function(){if(oi.length){for(var e,t=0;t<oi.length;t++)e=oi[t],e.holder.index===e.index&&ui(e.index,e.item,e.baseDiv,e.img,!1,e.clearPlaceholder);oi=[]}};Tt("Controller",{publicMethods:{lazyLoadItem:function(e){e=St(e);var t=ei(e);t&&(!t.loaded&&!t.loading||C)&&(Dt("gettingData",e,t),t.src&&di(t))},initController:function(){r.extend(l,ai,!0),o.items=Yn=n,ei=o.getItemAt,ti=l.getNumItemsFn,ni=l.loop,ti()<3&&(l.loop=!1),Et("beforeChange",function(e){var t,n=l.preload,i=null===e?!0:e>=0,r=Math.min(n[0],ti()),a=Math.min(n[1],ti());for(t=1;(i?a:r)>=t;t++)o.lazyLoadItem(p+t);for(t=1;(i?r:a)>=t;t++)o.lazyLoadItem(p-t)}),Et("initialLayout",function(){o.currItem.initialLayout=l.getThumbBoundsFn&&l.getThumbBoundsFn(p)}),Et("mainScrollAnimComplete",hi),Et("initialZoomInEnd",hi),Et("destroy",function(){for(var e,t=0;t<Yn.length;t++)e=Yn[t],e.container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);oi=null})},getItemAt:function(e){return e>=0&&void 0!==Yn[e]?Yn[e]:!1},allowProgressiveImg:function(){return l.forceProgressiveLoading||!A||l.mouseUsed||screen.width>1200},setContent:function(e,t){l.loop&&(t=St(t));var n=o.getItemAt(e.index);n&&(n.container=null);var i,a=o.getItemAt(t);if(!a)return void(e.el.innerHTML="");Dt("gettingData",t,a),e.index=t,e.item=a;var s=a.container=r.createEl("pswp__zoom-wrap");if(!a.src&&a.html&&(a.html.tagName?s.appendChild(a.html):s.innerHTML=a.html),pi(a),ci(a,gt),!a.src||a.loadError||a.loaded)a.src&&!a.loadError&&(i=r.createEl("pswp__img","img"),i.style.opacity=1,i.src=a.src,fi(a,i),ui(t,a,s,i,!0));else{if(a.loadComplete=function(n){if(c){if(e&&e.index===t){if(pi(n,!0))return n.loadComplete=n.img=null,ci(n,gt),Nt(n),void(e.index===p&&o.updateCurrZoomItem());n.imageAppended?!Qn&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):j.transform&&(ot||Qn)?oi.push({item:n,baseDiv:s,img:n.img,index:t,holder:e,clearPlaceholder:!0}):ui(t,n,s,n.img,ot||Qn,!0)}n.loadComplete=null,n.img=null,Dt("imageLoadComplete",t,n)}},r.features.transform){var u="pswp__img pswp__img--placeholder";u+=a.msrc?"":" pswp__img--placeholder--blank";var d=r.createEl(u,a.msrc?"img":"");a.msrc&&(d.src=a.msrc),fi(a,d),s.appendChild(d),a.placeholder=d}a.loading||di(a),o.allowProgressiveImg()&&(!Jn&&j.transform?oi.push({item:a,baseDiv:s,img:a.img,index:t,holder:e}):ui(t,a,s,a.img,!0,!0))}Jn||t!==p?Nt(a):(rt=s.style,ii(a,i||a.img)),e.el.innerHTML="",e.el.appendChild(s)},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var mi,gi={},vi=function(e,t,n){var i=document.createEvent("CustomEvent"),r={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,r),e.target.dispatchEvent(i)};Tt("Tap",{publicMethods:{initTap:function(){Et("firstTouchStart",o.onTapStart),Et("touchRelease",o.onTapRelease),Et("destroy",function(){gi={},mi=null})},onTapStart:function(e){e.length>1&&(clearTimeout(mi),mi=null)},onTapRelease:function(e,t){if(t&&!G&&!X&&!Qt){var n=t;if(mi&&(clearTimeout(mi),mi=null,kn(n,gi)))return void Dt("doubleTap",n);if("mouse"===t.type)return void vi(e,t,"mouse");var i=e.target.tagName.toUpperCase();if("BUTTON"===i||r.hasClass(e.target,"pswp__single-tap"))return void vi(e,t);qt(gi,n),mi=setTimeout(function(){vi(e,t),mi=null},300)}}}});var yi;Tt("DesktopZoom",{publicMethods:{initDesktopZoom:function(){M||(A?Et("mouseUsed",function(){o.setupDesktopZoom()}):o.setupDesktopZoom(!0))},setupDesktopZoom:function(t){yi={};var n="wheel mousewheel DOMMouseScroll";Et("bindEvents",function(){r.bind(e,n,o.handleMouseWheel)}),Et("unbindEvents",function(){yi&&r.unbind(e,n,o.handleMouseWheel)}),o.mouseZoomedIn=!1;var i,a=function(){o.mouseZoomedIn&&(r.removeClass(e,"pswp--zoomed-in"),o.mouseZoomedIn=!1),1>y?r.addClass(e,"pswp--zoom-allowed"):r.removeClass(e,"pswp--zoom-allowed"),s()},s=function(){i&&(r.removeClass(e,"pswp--dragging"),i=!1)};Et("resize",a),Et("afterChange",a),Et("pointerDown",function(){o.mouseZoomedIn&&(i=!0,r.addClass(e,"pswp--dragging"))}),Et("pointerUp",s),t||a()},handleMouseWheel:function(e){if(y<=o.currItem.fitRatio)return l.modal&&(!l.closeOnScroll||Qt||Z?e.preventDefault():L&&Math.abs(e.deltaY)>2&&(d=!0,o.close())),!0;if(e.stopPropagation(),yi.x=0,"deltaX"in e)1===e.deltaMode?(yi.x=18*e.deltaX,yi.y=18*e.deltaY):(yi.x=e.deltaX,yi.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(yi.x=-.16*e.wheelDeltaX),yi.y=e.wheelDeltaY?-.16*e.wheelDeltaY:-.16*e.wheelDelta;else{if(!("detail"in e))return;yi.y=e.detail}Wt(y,!0);var t=mt.x-yi.x,n=mt.y-yi.y;(l.modal||t<=it.min.x&&t>=it.max.x&&n<=it.min.y&&n>=it.max.y)&&e.preventDefault(),o.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:gt.x/2+yt.x,y:gt.y/2+yt.y};var n=l.getDoubleTapZoom(!0,o.currItem),i=y===n;o.mouseZoomedIn=!i,o.zoomTo(i?o.currItem.initialZoomLevel:n,t,333),r[(i?"remove":"add")+"Class"](e,"pswp--zoomed-in")}}});var bi,_i,wi,xi,Ci,ki,Ti,Si,$i,Ei,Di,Li,Ii={history:!0,galleryUID:1},Ai=function(){return Di.hash.substring(1)},Fi=function(){bi&&clearTimeout(bi),wi&&clearTimeout(wi)},Ni=function(){var e=Ai(),t={};if(e.length<5)return t;var n,i=e.split("&");for(n=0;n<i.length;n++)if(i[n]){var r=i[n].split("=");r.length<2||(t[r[0]]=r[1])}if(l.galleryPIDs){var o=t.pid;for(t.pid=0,n=0;n<Yn.length;n++)if(Yn[n].pid===o){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},Pi=function(){if(wi&&clearTimeout(wi),Qt||Z)return void(wi=setTimeout(Pi,500));xi?clearTimeout(_i):xi=!0;var e=p+1,t=ei(p);t.hasOwnProperty("pid")&&(e=t.pid);var n=Ti+"&gid="+l.galleryUID+"&pid="+e;Si||-1===Di.hash.indexOf(n)&&(Ei=!0);var i=Di.href.split("#")[0]+"#"+n;Li?"#"+n!==window.location.hash&&history[Si?"replaceState":"pushState"]("",document.title,i):Si?Di.replace(i):Di.hash=n,Si=!0,_i=setTimeout(function(){xi=!1},60)};Tt("History",{publicMethods:{initHistory:function(){if(r.extend(l,Ii,!0),l.history){Di=window.location,Ei=!1,$i=!1,Si=!1,Ti=Ai(),Li="pushState"in history,Ti.indexOf("gid=")>-1&&(Ti=Ti.split("&gid=")[0],Ti=Ti.split("?gid=")[0]),Et("afterChange",o.updateURL),Et("unbindEvents",function(){r.unbind(window,"hashchange",o.onHashChange)});var e=function(){ki=!0,$i||(Ei?history.back():Ti?Di.hash=Ti:Li?history.pushState("",document.title,Di.pathname+Di.search):Di.hash=""),Fi()};Et("unbindEvents",function(){d&&e()}),Et("destroy",function(){ki||e()}),Et("firstUpdate",function(){p=Ni().pid});var t=Ti.indexOf("pid=");t>-1&&(Ti=Ti.substring(0,t),"&"===Ti.slice(-1)&&(Ti=Ti.slice(0,-1))),setTimeout(function(){c&&r.bind(window,"hashchange",o.onHashChange)},40)}},onHashChange:function(){return Ai()===Ti?($i=!0,void o.close()):void(xi||(Ci=!0,o.goTo(Ni().pid),Ci=!1))},updateURL:function(){Fi(),Ci||(Si?bi=setTimeout(Pi,800):Pi())}}}),r.extend(o,on)};return e}),/*! PhotoSwipe Default UI - 4.1.2 - 2017-04-05
* http://photoswipe.com
* Copyright (c) 2017 Dmitry Semenov; */
function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";var e=function(e,t){var n,i,r,o,a,s,l,c,u,d,p,f,h,m,g,v,y,b,_,w=this,x=!1,C=!0,k=!0,T={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"Whatsapp",label:"Share via Whatsapp",url:"whatsapp://send?text=Check this out! {{url}}"},{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href.split("?")[0]},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},S=function(e){if(v)return!0;e=e||window.event,g.timeToIdle&&g.mouseUsed&&!u&&R();for(var n,i,r=e.target||e.srcElement,o=r.getAttribute("class")||"",a=0;a<W.length;a++)n=W[a],n.onTap&&o.indexOf("pswp__"+n.name)>-1&&(n.onTap(),i=!0);if(i){e.stopPropagation&&e.stopPropagation(),v=!0;var s=t.features.isOldAndroid?600:30;y=setTimeout(function(){v=!1},s)}},$=function(){return!e.likelyTouchDevice||g.mouseUsed||screen.width>g.fitControlsWidth},E=function(e,n,i){t[(i?"add":"remove")+"Class"](e,"pswp__"+n)},D=function(){var e=1===g.getNumItemsFn();e!==m&&(E(i,"ui--one-slide",e),m=e)},L=function(){E(l,"share-modal--hidden",k)},I=function(){return k=!k,k?(t.removeClass(l,"pswp__share-modal--fade-in"),setTimeout(function(){k&&L()},300)):(L(),setTimeout(function(){k||t.addClass(l,"pswp__share-modal--fade-in")},30)),k||F(),!1},A=function(t){t=t||window.event;var n=t.target||t.srcElement;return e.shout("shareLinkClick",t,n),n.href?n.hasAttribute("download")?!0:(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),k||I(),!1):!1},F=function(){for(var e,t,n,i,r,o="",a=0;a<g.shareButtons.length;a++)e=g.shareButtons[a],n=g.getImageURLForShare(e),i=g.getPageURLForShare(e),r=g.getTextForShare(e),t=e.url.replace("{{url}}",encodeURIComponent(i)).replace("{{image_url}}",encodeURIComponent(n)).replace("{{raw_image_url}}",n).replace("{{text}}",encodeURIComponent(r)),o+='<a href="'+t+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",g.parseShareButtonOut&&(o=g.parseShareButtonOut(e,o));l.children[0].innerHTML=o,l.children[0].onclick=A},N=function(e){for(var n=0;n<g.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+g.closeElClasses[n]))return!0},P=0,R=function(){clearTimeout(_),P=0,u&&w.setIdle(!1)},M=function(e){e=e?e:window.event;var t=e.relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(_),_=setTimeout(function(){w.setIdle(!0)},g.timeToIdleOutside))},q=function(){g.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=w.getFullscreenAPI()),n?(t.bind(document,n.eventK,w.updateFullscreen),w.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs"))},j=function(){g.preloaderEl&&(O(!0),d("beforeChange",function(){clearTimeout(h),h=setTimeout(function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&O(!1):O(!0)},g.loadingIndicatorDelay)}),d("imageLoadComplete",function(t,n){e.currItem===n&&O(!0)}))},O=function(e){f!==e&&(E(p,"preloader--active",!e),f=e)},H=function(e){var n=e.vGap;if($()){var a=g.barsSize;if(g.captionEl&&"auto"===a.bottom)if(o||(o=t.createEl("pswp__caption pswp__caption--fake"),o.appendChild(t.createEl("pswp__caption__center")),i.insertBefore(o,r),t.addClass(i,"pswp__ui--fit")),g.addCaptionHTMLFn(e,o,!0)){var s=o.clientHeight;n.bottom=parseInt(s,10)||44}else n.bottom=a.top;else n.bottom="auto"===a.bottom?0:a.bottom;n.top=a.top}else n.top=n.bottom=0},z=function(){g.timeToIdle&&d("mouseUsed",function(){t.bind(document,"mousemove",R),t.bind(document,"mouseout",M),b=setInterval(function(){P++,2===P&&w.setIdle(!0)},g.timeToIdle/2)})},B=function(){d("onVerticalDrag",function(e){C&&.95>e?w.hideControls():!C&&e>=.95&&w.showControls()});var e;d("onPinchClose",function(t){C&&.9>t?(w.hideControls(),e=!0):e&&!C&&t>.9&&w.showControls()}),d("zoomGestureEnded",function(){e=!1,e&&!C&&w.showControls()})},W=[{name:"caption",option:"captionEl",onInit:function(e){r=e}},{name:"share-modal",option:"shareEl",onInit:function(e){l=e},onTap:function(){I()}},{name:"button--share",option:"shareEl",onInit:function(e){s=e},onTap:function(){I()}},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){a=e}},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e}}],V=function(){var e,n,r,o=function(i){if(i)for(var o=i.length,a=0;o>a;a++){e=i[a],n=e.className;for(var s=0;s<W.length;s++)r=W[s],n.indexOf("pswp__"+r.name)>-1&&(g[r.option]?(t.removeClass(e,"pswp__element--disabled"),r.onInit&&r.onInit(e)):t.addClass(e,"pswp__element--disabled"))}};o(i.children);var a=t.getChildByClass(i,"pswp__top-bar");a&&o(a.children)};w.init=function(){t.extend(e.options,T,!0),g=e.options,i=t.getChildByClass(e.scrollWrap,"pswp__ui"),d=e.listen,B(),d("beforeChange",w.update),d("doubleTap",function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(g.getDoubleTapZoom(!1,e.currItem),t,333)}),d("preventDragEvent",function(e,t,n){var i=e.target||e.srcElement;i&&i.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(i.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(i.tagName))&&(n.prevent=!1)}),d("bindEvents",function(){t.bind(i,"pswpTap click",S),t.bind(e.scrollWrap,"pswpTap",w.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",w.onMouseOver)}),d("unbindEvents",function(){k||I(),b&&clearInterval(b),t.unbind(document,"mouseout",M),t.unbind(document,"mousemove",R),t.unbind(i,"pswpTap click",S),t.unbind(e.scrollWrap,"pswpTap",w.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",w.onMouseOver),n&&(t.unbind(document,n.eventK,w.updateFullscreen),n.isFullscreen()&&(g.hideAnimationDuration=0,n.exit()),n=null)}),d("destroy",function(){g.captionEl&&(o&&i.removeChild(o),t.removeClass(r,"pswp__caption--empty")),l&&(l.children[0].onclick=null),t.removeClass(i,"pswp__ui--over-close"),t.addClass(i,"pswp__ui--hidden"),w.setIdle(!1)}),g.showAnimationDuration||t.removeClass(i,"pswp__ui--hidden"),d("initialZoomIn",function(){g.showAnimationDuration&&t.removeClass(i,"pswp__ui--hidden")}),d("initialZoomOut",function(){t.addClass(i,"pswp__ui--hidden")}),d("parseVerticalMargin",H),V(),g.shareEl&&s&&l&&(k=!0),D(),z(),q(),j()},w.setIdle=function(e){u=e,E(i,"ui--idle",e)},w.update=function(){C&&e.currItem?(w.updateIndexIndicator(),g.captionEl&&(g.addCaptionHTMLFn(e.currItem,r),E(r,"caption--empty",!e.currItem.title)),x=!0):x=!1,k||I(),D()},w.updateFullscreen=function(i){i&&setTimeout(function(){e.setScrollOffset(0,t.getScrollY())},50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},w.updateIndexIndicator=function(){g.counterEl&&(a.innerHTML=e.getCurrentIndex()+1+g.indexIndicatorSep+g.getNumItemsFn())},w.onGlobalTap=function(n){n=n||window.event;var i=n.target||n.srcElement;if(!v)if(n.detail&&"mouse"===n.detail.pointerType){if(N(i))return void e.close();t.hasClass(i,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?g.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(g.tapToToggleControls&&(C?w.hideControls():w.showControls()),g.tapToClose&&(t.hasClass(i,"pswp__img")||N(i)))return void e.close()},w.onMouseOver=function(e){e=e||window.event;var t=e.target||e.srcElement;E(i,"ui--over-close",N(t))},w.hideControls=function(){t.addClass(i,"pswp__ui--hidden"),C=!1},w.showControls=function(){C=!0,x||w.update(),t.removeClass(i,"pswp__ui--hidden")},w.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},w.getFullscreenAPI=function(){var t,n=document.documentElement,i="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:i}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+i}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+i}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return c=g.closeOnScroll,g.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?e.template[this.enterK]():void e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return g.closeOnScroll=c,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}};return e}),function(){var e,t,n,i,r,o,a,s,l,c,u,d,p,f,h,m;$(document).on("click",".design-info-blocks a.main",function(e){var t;e.preventDefault(),t=$(this).attr("href"),$(this).find("span").toggle(),$(t).slideToggle("fast")}),$(document).on("click",".design-info-sub-blocks a",function(e){var t;e.preventDefault(),t=$($(this).attr("href")).is(":visible"),$(".sub-specs-table").hide(),$(".sub-grp span.button-icon").removeClass("button-toggle"),t||($(this).find("span.button-icon").addClass("button-toggle"),$($(this).attr("href")).show())}),c=function(e,t,n){$.ajax({url:"/save_review",type:"Post",data:{design_id:e,rating:t,review:n},datatype:"JSON",success:function(e){200===e.status?"false"===n?($("#save-alert-message").show().css("color","#21a95a").text("Rating Saved Successfully"),$("#save-alert-message").data("rating-given",t)):($("#save-alert-message").show().css("color","#21a95a").text(e.message),window.location.reload()):$("#save-alert-message").show().text(e.message)},error:function(){}})},$(document).on("click","#review-submit-btn",function(){var e,t,n;t=$("#form-rating-star input[name='score']").val(),n=$("textarea#review-text").val(),e=$(this).data("design-id"),$("#save-alert-message").hide(),n.trim().length>30?($("textarea#review-text").removeClass("alert-border"),$("#rating-alert-message, #review-alert-message").hide(),c(e,t,n)):0===t.length?$("#rating-alert-message").show():0!==$("#save-alert-message").data("rating-given")?($("#rating-alert-message").hide(),$("textarea#review-text").addClass("alert-border"),$("#review-alert-message").show()):($("#rating-alert-message").hide(),$("textarea#review-text").addClass("alert-border"),$("#review-alert-message").show(),c(e,t,"false"))}),$(document).on("change","#pre-order-check",function(){var e;return e=this.checked?$(this).val():$(".delivery_day").data("date"),$(".delivery_day").html(""+e)}),$(document).on("change",".addon_types",function(){var e,t,n,r,o,a,s,l;o=[],l=$(this).find(":selected"),s=l.val(),r="#"+l.text().split("-")[0].trim().toLowerCase().replace(/ /g,"_"),$(".info_message").hide(),$(r).css("display","block"),$("#unstitch").val()===s&&$("#stitchingModal").length>0&&h(),$(".custom").val()===s||$(".petticoat_stitching").val()===s||$("#unstitch").val()||$(".pre_stitch").val()===s?$(this).next().addClass("hide"):$(this).next().removeClass("hide"),$(".custom").val()===s&&$(".plus_size_blouse_custom").length>0?$(".plus_size_blouse_custom").show():$(".plus_size_blouse_custom").hide(),l.hasClass("standard-stitch-variant")?i($(".standard-stitch-variant").attr("selected-variant-oldprice"),$(".standard-stitch-variant").attr("selected-variant-price")):l.hasClass("custom")&&void 0!==l.attr("data-variant-id")&&i(l.attr("data-old-price"),l.attr("data-price")),n="delivery-date",$(".addon_types").each(function(){var e,t,i;return t=$(this).find(":selected"),t.hasClass("standard")&&$("a.button.size.selected").length>0?(i=$("a.button.size.selected"),0===parseInt(i.data("prodtime"))&&(n="rts-date")):i=t,e=void 0!==i.data(n)&&i.data(n).length>0?i.data(n):$("#pre-order-check").length>0&&$("#pre-order-check").is(":checked")?$("#pre-order-check").val():i.attr("data-delivery-date"),o.push([parseInt(i.data("prodtime")),e,i.data("ready-to-ship")])}),a=o.sort().pop(),$(".delivery_day").html(a[1]),"true"===a[2]?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),t="#atv_"+s,e=$(t).find(".columns").children().length,(e>0||$(t).find(".size-chart-div").length>0)&&(l.hasClass("standard")||l.hasClass("custom")?$(".addon_option_types").hide():(l.hasClass("petticoat_stitching")||l.hasClass("pre_stitch"))&&$(".addon_option_type_without_size").hide(),$(t).show(),$(this).next().removeClass("hide"))}),$(document).on("change",".addon_types",function(){var e;return(e=$(".addon_types").find(":selected").hasClass("custom"))&&!$(".stitching_note").is(":visible")?$(".stitching_note").removeClass("hide"):e?void 0:$(".stitching_note").addClass("hide")}),i=function(e,t,n,i,r){var o,a;return null==n&&(n=void 0),null==i&&(i=!1),null==r&&(r=void 0),void 0!==r?(o=$(".product_discount_price_"+r),a=$(".product_price_wo_discount_"+r)):(o=$(".product_discount_price"),a=$(".product_price_wo_discount")),void 0!==t&&o[0].innerText!==t&&(o.text(t),o.addClass("shake-effect"),$(".changeInPriceNote").fadeIn().css("display","inline-flex"),setTimeout(function(){return o.removeClass("shake-effect"),$(".changeInPriceNote").fadeOut()},750),a.text(e)),void 0!==n?("true"===i?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),$(".delivery_day").html(n)):void 0},h=function(){return"true"===$("#unstitch").attr("popup")&&$(".addon_types").first().next().addClass("hide"),$("#unstitch").val()===$(".addon_types").find(":selected").val()&&"false"===$("#unstitch").attr("popup")?($(".addon_types").first().next().addClass("hide"),$("#main-section").css({position:"fixed",overflow:"hidden"}),$(".fixed").css("z-index","0"),$("#stitchingModal").show(),$("#unstitch").attr("popup","true")):void 0},a=function(){var e;return e=!1,$("#pre-order-check").length>0&&(e=$("#pre-order-check").is(":checked")?!0:!1),e},$(document).on("click",".plus_size_fabric_option",function(){return $(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),$(this).addClass("plus_size_fabric_selected")}),r=function(){var e,t,n;return n=!0,t=!0,$(".regular_plus_options").length>0?(e=$("select",".regular_plus_options"),e.each(function(){return"0"===this.value?t=!1:void 0})):t=!1,$(".addon_types").each(function(){var e,i;i=void 0,$(this).val().trim()||(n=!1,$(this).css({"border-color":"#d50909","border-width":"2px"})),e="#atv_"+this.value,$(e).length>0&&(i=$("select",e),i.css({"border-color":"","border-width":""}),i.each(function(){$(this).hasClass("plus_size_regular_select")||"0"!==this.value||$(this).hasClass("plus_size_custom_select")&&$(".plus_size_custom_regular").hasClass("selected_custom_plus_size")||(n=!1)}),n===!1?($(e).show(),i.each(function(){"0"===this.value&&$(this).css({"border-color":"#d50909","border-width":"2px"})})):i.css({"border-color":"","border-width":""}),$(".size").length>0&&$(".size-chart").is(":visible")&&($(".regular_plus_options").length>0?1===$(".size.selected").length||t||(alert("Please select a Size"),n=!1):1!==$(".size.selected").length&&(alert("Please select a Size"),n=!1))),($(".plus_size_regular_select",e).length>0&&1!==$(".size.selected").length&&!$(".plus_size_fabric_option",".regular_plus_options").hasClass("plus_size_fabric_selected")||$(".plus_size_custom_select",e).length>0&&$(".plus_size_custom").hasClass("selected_custom_plus_size")&&!$(".plus_size_fabric_option",".custom_hide_option_type").hasClass("plus_size_fabric_selected"))&&(alert("Please select fabric color"),n=!1)}),n===!1&&alert("Please select highlighted options"),n},s=function(){var e,t,n;return n=!0,$(".select-size").removeClass("shake-effect"),$(".variant, .variant_stitch").is(":visible")&&(n=!1,(1===$(".variant.selected, .variant_stitch.selected").length||$(".variant.selected").length>1)&&(n=!0)),$(".variant").length<1&&$(".variant_modal").is(":visible")&&$(".variant_modal.selected").length>1&&(n=!0),n===!1&&(e=$(".add_to_buy_bow").attr("design_id"),t=$("#variant_select_"+e),t.length>0&&u(t),$(".select-size").addClass("shake-effect")),n},d=function(e,t){var n,i,r;n=[];for(r in e)i=e[r],n.push(i.id),ga("ec:addProduct",i),ga("ec:setAction","add"),ga("send","event","UX","click","add to cart"),t&&ga("send","event","UX","click","add to cart with ajax"),window._pq=window._pq||[],_pq.push(["track","add_to_cart"]),"undefined"!=typeof fbq&&fbq("track","AddToCart",{value:i.price,currency:"INR",content_ids:i.id,content_type:"product",content_category:i.category});n.length>1&&ga("send","event","complete_the_look","add_to_cart",n.join("-"))},e=function(e,t,n){var i,r,o,s,l;r=[],i={quantity:1},i.design_id=$("#product_id").html().split(" ").pop(),i.rakhi_note=a(),o=[],l=$(".addon_types option.custom").is(":selected")&&$(".variant_stitch").length>0?$(".addon_types option.custom").attr("data-variant-id"):$(".variant_stitch").length>0?$(".variant_stitch.selected").attr("id"):$(".variant.selected").attr("id"),i.variant_id=l,$(".variant_stitch").is(":visible")||$(".addon_types").each(function(){var e,t,n,r,a,s,l,c;s="",c="",r=$(this).val(),n=$("#atv_"+r),e={},t=[],c=$(".size.selected").attr("id"),n.length>0&&(l=$("select",n),l.length>0&&l.each(function(){$("option:first",this).text()!==$("option:selected",this).text()&&(s=s+$("option:first",this).text()+" : "+$("option:selected",this).text()+", ")}),$("input:checked",n).each(function(){s=s+this.name+", ",t.push(this.value)}),$(".plus_size_fabric_option",n).hasClass("plus_size_fabric_selected")&&(a=$(".plus_size_fabric_selected"),s=s+a.data("optionType")+" : "+a.data("color")+", "),$(".common_addons",n).size()>0&&$(".size.selected").length>0&&$(".size-chart").is(":visible")&&(s=s+" Standard Stitching Size : size-"+c),e.addon_option_type_id=t),e.addon_type_value_id=r,e.notes=s.replace(RegExp("  ","g"),""),o.push(e),i.line_item_addons_attributes=o}),r.push(i),$(".addon_product:checked").each(function(){return i={quantity:1},i.design_id=this.value,i.pair_product=!0,$("#variant_select_"+this.value).length>0&&(i.variant_id=$("#variant_select_"+this.value+" .variant_modal.selected").attr("id")),r.push(i)}),s={line_items:r,design_page:!0},$.ajax({type:"POST",data:s,url:e,dataType:"JSON",success:function(e){d(e.ga_hash,t),t?($(".cart_count").html(e.cart_count),$(".buy_now").hide(),$(".go_to_cart").show(),$("#add_to_cart_message").fadeIn("slow"),setTimeout(function(){$("#add_to_cart_message").fadeOut("slow")},2e3)):window.location.assign(e.redirect_url)},beforeSend:function(){n||($(".progress_img").show(),$("#design_image_block").css("opacity","0.4"))},complete:function(){n||($(".progress_img").hide(),$("#design_image_block").css("opacity","1"))}})},f=function(){return $("#main-section").css({position:"inherit",overflow:"auto"}),$(".fixed").css("z-index","99")},$(document).on("click",".variant, .variant_stitch",function(){var e,t;e=$(this),$(".variant-price-text").html("Price shown is for the size selected"),e.hasClass("disabled")===!1&&($(".variant, .variant_stitch").removeClass("selected alert"),e.addClass("selected alert"),e[0].className.indexOf("variant")>0&&(t=e.attr("id"),$("#"+t).addClass("selected alert")),i(e.attr("data-old-price"),e.attr("data-price"),e.attr("data-delivery-date"),e.attr("data-ready-to-ship")),$(".variant_stitch").length>0&&($(".standard-stitch-variant").attr("selected-variant-price",$(".product_discount_price").text()),$(".standard-stitch-variant").attr("selected-variant-oldprice",$(".product_price_wo_discount").text())))}),u=function(e){var t,n;return e.addClass("animate"),n=window.innerHeight-e.children(".content").height(),t="translate3d(0px,"+(n-20)+"px, 0px)",e.css("transform",t),e.siblings(".variant-bg").css({"pointer-events":"auto",opacity:"1"})},l=function(){return $(".addon_product:checked").each(function(){var e;return e=$("#variant_select_"+this.value),e.length>0&&!e.find(".variant_modal").hasClass("selected")||e.find(".variant_modal").hasClass("variant-selected")?($(this).siblings("label").show(),$(this).prop("checked",!1)):void 0}),$(".variant_selection").css("transform","translate3d(0px, 110%, 0px)"),$(".variant-bg").css({"pointer-events":"none",opacity:"0"})},$(document).on("click",".plus_size_blouse_regular",function(){return $(".standard_option_values, .regular_plus_options").show(),$(".plus_size_blouse_regular").addClass("plus_size_regular_btn"),$(".size").removeClass("selected")}),$(document).on("click",".plus_size_custom",function(){return $(".custom_hide_option_type").show(),$(".plus_size_custom").addClass("selected_custom_plus_size"),$(".plus_size_custom_regular").removeClass("selected_custom_plus_size"),$(".plus_size_custom_regular").addClass("other_custom_plus_size"),$(".plus_size_custom").removeClass("other_custom_plus_size")}),$(document).on("click",".plus_size_custom_regular",function(){var e;return $(".custom_hide_option_type").hide(),e=$("select",".custom_hide_option_type"),$(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),e.length>0&&e.each(function(){return $(this).find("option:eq(0)").prop("selected",!0)}),$(".plus_size_custom_regular").addClass("selected_custom_plus_size"),$(".plus_size_custom").addClass("other_custom_plus_size"),$(".plus_size_custom").removeClass("selected_custom_plus_size"),$(".plus_size_custom_regular").removeClass("other_custom_plus_size")}),$(document).on("click",".addon_product",function(){var e,t;return e=$(this),e.siblings("label").toggle(),t=$("#variant_select_"+this.value),t.length>0?e.is(":checked")?u(t):void 0:ga("send","event","complete_the_look","addon_product_view",this.value)}),$(document).on("click",".variant_modal",function(){var e,t,n;return n=$(this),e=n.attr("value"),t=n.parents("#variant_select_"+e),t.find(this).hasClass("disabled")===!1&&(t.find(".variant_modal").removeClass("variant-selected alert"),t.find(this).addClass("variant-selected alert")),i(n.attr("data-old-price"),n.attr("data-price"),n.attr("data-delivery-date"),n.attr("data-ready-to-ship"),e)}),$(document).on("click",".select-addon-variant",function(){var e,t,n;return t=$(this),n=t.attr("value"),e=t.parents("#variant_select_"+n),e.find(".variant_modal").each(function(){return $(this).hasClass("variant-selected")?(e.find(".variant_modal").removeClass("selected alert variant-selected"),$(this).addClass("selected alert"),ga("send","event","complete_the_look","addon_product_view",n)):void 0}),l()}),$(document).on("click",".variant-bg, .close-variant-select",function(){return l()}),$(document).on("click",".size",function(){var e,t,n,i,r,o,a;$(".regular_plus_options").hide(),n=$("select",".regular_plus_options"),$(".plus_size_blouse_regular").removeClass("plus_size_regular_btn"),$(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),n.length>0&&n.each(function(){return $(this).find("option:eq(0)").prop("selected",!0)}),(r=$("#salwar_kameez_specific"))&&(a=parseInt($(this).text(),10),r.find("var:first").text(a+2),r.find("var:last").text(a+3),r.show(),$("#salwar_kameez_default").hide()),$(this).data("delivery-date").length>0&&(t=[],o=parseInt($(this).data("prodtime")),e=0===o?"rts-date":"delivery-date",t.push([o,$(this).data("delivery-date"),$(this).data("ready-to-ship")]),$(".addon_types").each(function(){return $(this).is(":checked")&&!$(this).hasClass("standard")?t.push([parseInt($(this).data("prodtime")),$(this).data(e),$(this).data("ready-to-ship")]):void 0}),i=t.sort().pop(),i[2]===!0?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),$(".delivery_day").html(i[1])),$(this).hasClass("disabled")===!1&&($(".size").removeClass("selected alert"),$(this).addClass("selected alert"),$(".standard_option_values").show(),$(".std_size_chart tr").removeClass("highlight"),$(".std_size_chart td:first-child:contains('"+$(this).text()+"')").parent("tr").addClass("highlight"))}),$(document).on("change","#show_rts_products",function(){return $(this).is(":checked")?$.each($(this).data("rts-sizes"),function(e,t){return $("#"+t).css("border","2px solid #7b0e1d")}):$("a.button.size").css("border","1px solid #aba9a4")}),$(document).on("click",".add_to_buy_bow, #buy_now, #add_to_cart",function(){f(),$("#stitchingModal").hide(),s()&&r()&&("undefined"!=typeof Unbxd&&unbxdTrack("addToCart",{pid:$("#product_id").html().split(" ").pop()}),e($(this).attr("data-targeturl"),!1,$(this).hasClass("add_to_buy_bow")))}),$(document).on("click",".stitch",function(){var e;return f(),$("#stitchingModal").hide(),$(".standard").prop("selected",!0),e=$(".addon_types").find(":selected").val(),$(".standard").val()===e?$(".addon_types").first().next().removeClass("hide"):void 0}),$(document).on("click",".closeStitchingModal",function(){return f(),$("#stitchingModal").hide()}),$(document).on("click","#size-chart-btn, #dynamic-size-chart-btn, #add_review, .view-more-btn",function(){return $("body").addClass("modal-open").css("margin-top","0px")}),$(document).on("click",".close-review-more",function(){return $("body").removeClass("modal-open")}),$(document).foundation("reveal",{animation:!1}),m=function(){return $("#variants_block").length>0?$(".variant").each(function(){return $(".product_discount_price").text().trim()===$(this).attr("data-price")?($(".variant-price-text").html("The price shown is for size : "+$(this).text()),!1):void 0}):void 0},m(),$(document).on("turbolinks:render",function(){return t(),m()}),$(function(){return $(".aov_select").on("change",function(){var e,t,n,i,r,o;return $("#standard_height_notice").length>0?null!==(i=$("#"+this.id+" option:selected").text().match(/\d+/g))?(e=i.map(Number).slice(0,2),t=12*e[0],e.length>1&&(t+=e[1]),n=$("#standard_height_notice").data("default"),$.each($("#standard_height_notice").data(),function(e,i){return t>=e?n=i:void 0}),$(".std_height_img").hide(),$("#std-"+n).show(),o=$($("#standard_info_icon span").length>0?"#standard_info_icon span":"<span style= 'font-size:11px;'></span>"),r=o.text("For the selected size, kameez will fall "+n.replace(/_/g," ")+" area"),$("#standard_info_icon").prepend(r),$("#standard_height_notice").show(),setTimeout(function(){return $("#standard_height_notice").fadeOut()},3e3),$("#standard_info_icon").show()):($("#standard_height_notice").hide(),$("#standard_info_icon").hide()):void 0})}),$(document).on("click","#standard_info_icon a",function(){return $("#standard_height_notice").toggle(),setTimeout(function(){return $("#standard_height_notice").fadeOut()},3e3)}),$(document).on("tap",function(){return $("#standard_height_notice").is(":visible")?$("#standard_height_notice").hide():void 0}),t=function(){($(".design_image").length>1||1===$(".design_image").length&&1===$(".design_video").length)&&($("#design_images").attr({"data-orbit":"","data-timer":"false"}),$("#design_images").css("text-align","initial"),$(".orbit-timer").hide(),$(document).foundation({orbit:{animation_speed:100,autoplay:!1,navigation_arrows:!0,bullets:!0,variable_height:!0,timer:!1,circular:!1}}),$(".label_for_image_box").length>0&&$(".label_for_image_box, .label_text").css("display","inline-block"))},t(),n=function(e){var t,n,i,r;$(window).scrollTop(100),$(".fixed").css("z-index","0"),$("#branch-banner-iframe").css("display","none"),r=document.querySelectorAll(".pswp")[0],n=[],$(".design_image").each(function(){var e,t,i,r,o,a;return e=$(this),i=e[0].id,r=e[0].width,t=e[0].height,a=r>2?1.5*r:400*r,o=t>2?1.5*t:400*t,n.push("product_video_src"===i?{html:'<video class="design_video" id="product_video_zoom" controls muted playsinline loop user-scalable="no"><source src="'+e.data("src")+'" type="video/mp4"></video>'}:{src:e.data("src"),w:a,h:o,title:"Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution."})}),i={index:e.data("index")},t=new PhotoSwipe(r,PhotoSwipeUI_Default,n,i),t.listen("gettingData",function(e,t){var n;n=new Image,n.src=t.src,0!==n.width&&(t.w=n.width,t.h=n.height)}),t.listen("afterChange",function(){var e;e=t.currItem.html,void 0!==e?$("#product_video_zoom")[0].play():$("#product_video_zoom")[0]&&($("#product_video_zoom")[0].pause(),$("#product_video_zoom")[0].muted=!0)}),t.listen("destroy",function(){return $(".fixed").css("z-index","99"),$("#branch-banner-iframe").fadeIn()}),t.init()},$(document).on("click",".design_image",function(){return n($(this))}),$(document).on("click","#share-now-button",function(){return p($(this)),ga("send","event","UX","click","share and earn")}),p=function(e){var t,n,i,r,o;return t=e,signedIn()?(i="check this out",r=t.attr("data-d-link")+"?utm_medium=mirraw_mobile_web&referral_campaign=share_and_earn_user_"+t.attr("data-u-id"),n=encodeURIComponent(i)+" - "+encodeURIComponent(r),o="whatsapp://send?text="+n,window.location.href=o):window.location="/accounts/sign_in"},$(function(){return $(document).scroll(function(){var e,t,n;return $(".buy-now").length>0?(e=$("#action_buttons"),t=$(this).scrollTop(),n=$(".buy-now").offset().top-window.innerHeight+15,""===$(".line_items_count_text").text()?$("#line_items_count").hide():$("#line_items_count").show(),t>n?e.removeClass("fixed_button"):e.addClass("fixed_button")):void 0})}),$(function(){return $(document).on("click","#check_for_pdd",function(){var e,t;return t=$("#pdd_product_id").val(),e=$("#pin_code").val(),""!==e?o(t,e):void 0})}),o=function(e,t){var n;return n=/(^\d{6}$)/,null!=e&&null!=t?$.ajax({type:"GET",data:{pincode:t,id:e},url:"/designs/pdd_design",success:function(e){var i;return e.error===!1?(i=e.eta,$(".delivery_block").show(),$(".delivery_day").html(i).css("color","#303030"),$(".pdd_error_message").hide()):n.test(t)?($(".delivery_block").hide(),$(".pdd_error_message").show(),$(".pdd_error_message").html("Unable to calculate, Please try another pincode").css("color","red")):($(".delivery_block").hide(),$(".pdd_error_message").show(),$(".pdd_error_message").html("<strong>Invalid pincode.</strong> Please enter a valid pincode").css("color","red"))}}):void 0}}.call(this),$(document).on("click",".ga-sign-in-tracking",function(){ga("send","event","footer-component","user-signed-in","user-signed-in")}),$(document).on("click",".ga-sign-out-tracking",function(){ga("send","event","footer-component","user-signed-out","user-signed-out")});