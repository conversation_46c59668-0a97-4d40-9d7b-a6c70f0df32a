/*Catalog Specific CSS */
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_1 {
  background-color: rgba(170, 222, 233, 0.62);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_2 {
  background-color: rgba(194, 150, 220, 0.6);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_3 {
  background-color: rgba(243, 150, 189, 0.52);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_4 {
  background-color: rgba(224, 164, 137, 0.66);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_5 {
  background-color: rgba(171, 188, 241, 0.49);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_6 {
  background-color: rgba(201, 241, 210, 0.58);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_7 {
  background-color: rgba(204, 169, 218, 0.51);
}

/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.design_8 {
  background-color: rgba(206, 182, 172, 0.63);
}

/* line 13, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.desifn_0 {
  background-color: rgba(157, 137, 220, 0.27843);
}

/* line 16, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 21, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body {
  overflow: scroll !important;
}
/* line 23, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .heading_underline {
  text-decoration: underline;
}
/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .line_through_text {
  text-decoration: line-through;
}
/* line 29, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .truncate {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 35, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb {
  display: flex;
}
@media only screen and (min-width: 500px) and (max-width: 1040px) {
  /* line 35, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  body .store-breadcrumb {
    margin-top: 15px;
  }
}
/* line 42, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb ul {
  margin-bottom: 0px;
  margin-left: 0px;
}
/* line 46, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb li:not(:first-child):before {
  content: '/';
  margin-left: 0px;
  margin-right: 0px;
}
/* line 51, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb a {
  font-size: 12px !important;
  color: #303030;
}
/* line 55, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb li {
  font-size: 12px !important;
  display: inline-block;
}
/* line 59, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store-breadcrumb .final {
  font-weight: bold;
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design {
  margin: 0;
}
/* line 65, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
/* line 70, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li img {
  width: 100%;
}
/* line 73, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .label-ribbon:before {
  font-family: "foundation-icons";
  content: "\f12b";
  color: #ffffff !important;
  font-size: 14px;
  padding: 0px 3px;
}
/* line 80, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .label-ribbon {
  border-color: #670b19 !important;
  position: absolute;
  font-weight: 500;
  font-size: 12px;
  padding: 0px 0.2em 3px 0em;
  background-color: #b11f2d !important;
  color: #ffffff !important;
  display: inline-block;
  line-height: 1;
  margin: -6px 0px 0px -7px;
  border: 0 solid transparent;
  z-index: 1;
}
/* line 94, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .label-ribbon:after {
  position: absolute;
  content: '';
  top: 100%;
  left: 0;
  background-color: transparent !important;
  border-style: solid;
  border-width: 0px 0.78em 1em 0px;
  border-color: transparent;
  border-right-color: inherit;
  width: 0;
  height: 0;
}
/* line 108, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .b1g1.label-ribbon:before {
  content: '';
}
/* line 112, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page {
  border: 0.75px solid #d8d8d8;
}
/* line 114, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel {
  position: relative;
  padding: 0em 0.3em 0.3em 0.3em;
}
/* line 117, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc {
  margin-bottom: 0em;
}
/* line 119, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc a {
  color: #8f1b1d;
  font-size: 0.75rem;
  font-weight: bold;
}
/* line 124, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc .title-column, body .store_page_design > li .fr_page .panel.design_desc .wishlist-column {
  padding: 0;
}
/* line 129, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button {
  font-size: 1.6rem;
  margin: 0;
  padding: 0;
  float: right;
  background: transparent;
  color: #8f1b1d;
  position: absolute;
  right: 0;
  top: 1px;
}
/* line 139, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button:focus {
  outline: none;
}
/* line 143, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button.empty-heart {
  color: gray;
}
/* line 149, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel .add_to_cart_link {
  bottom: 0em;
  color: #ffffff;
  background: #8f1b1d;
  position: absolute;
  padding: 10px 2px;
  font-weight: 700;
}
/* line 157, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design > li .fr_page .panel .sold_out_link {
  margin-bottom: 0.7em;
  width: 89%;
  bottom: 0em;
  color: #ffffff;
  font-weight: 700;
}
/* line 168, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design li.original_price {
  font-size: 0.8em;
  color: white;
}
/* line 172, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design li.discount_price {
  font-weight: bold;
  color: white;
}
/* line 176, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design li.percent_off {
  color: red;
  margin-top: -1.7em;
  font-size: 0.9em;
}
/* line 182, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design .design_desc {
  padding: 0.5em;
}
/* line 184, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_design .design_desc li {
  padding-bottom: 0em;
}
/* line 189, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .postfix {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem;
  overflow: visible;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 2;
}
/* line 205, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .postfix.button {
  border-color: true;
}
/* line 209, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .postfix.button.radius {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 217, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .postfix.button.round {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}
/* line 225, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body span.postfix, body label.postfix {
  background: #f2f2f2;
  color: #333333;
  border-color: #cccccc;
}
/* line 231, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block, body .wishlist-block {
  margin-bottom: 2em;
}
/* line 234, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #top_content, body .wishlist-block #top_content {
  font-size: 0.9rem;
}
/* line 235, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #top_content.read-more, body .wishlist-block #top_content.read-more {
  height: 25px;
  overflow: hidden;
}
/* line 241, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #top_content #hidden_content > *, body .wishlist-block #top_content #hidden_content > * {
  font-size: 0.9rem;
}
/* line 245, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #top_content > *, body .wishlist-block #top_content > * {
  font-size: 0.9rem;
}
/* line 250, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block a#view-more-top-content, body .store_page_block a#view-more-seo-post, body .wishlist-block a#view-more-top-content, body .wishlist-block a#view-more-seo-post {
  color: #008CBA;
  font-size: 0.9rem;
  text-align: center;
}
/* line 255, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title, body .wishlist-block .heading_title {
  padding: 0.2rem 0rem;
  max-width: 100%;
}
/* line 258, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title .columns, body .wishlist-block .heading_title .columns {
  padding: 0px;
}
/* line 261, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title .product-title, body .wishlist-block .heading_title .product-title {
  font-size: 1.2rem;
}
/* line 263, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title .product-title h1, body .wishlist-block .heading_title .product-title h1 {
  margin: 0px;
}
/* line 266, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title .product-title .product_count, body .wishlist-block .heading_title .product-title .product_count {
  font-size: 0.8rem;
}
/* line 270, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title #toggle-design-box, body .wishlist-block .heading_title #toggle-design-box {
  float: right;
  text-align: right;
}
/* line 273, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title #toggle-design-box span, body .wishlist-block .heading_title #toggle-design-box span {
  font-size: 0.9rem;
  vertical-align: super;
}
/* line 277, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .heading_title #toggle-design-box i, body .wishlist-block .heading_title #toggle-design-box i {
  font-size: 2rem;
  vertical-align: top;
}
/* line 283, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #category_links, body .wishlist-block #category_links {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding: 0.3rem 0rem;
  max-width: 100%;
}
/* line 289, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #category_links .custom, body .wishlist-block #category_links .custom {
  background-color: white;
  border: 1px solid #303030;
  color: #303030;
  font-size: 0.725rem;
  padding: 0.7em;
  margin-bottom: 0.3rem;
}
/* line 298, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons, body .wishlist-block #action_buttons {
  /*padding: 0 0.625rem;*/
}
/* line 300, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons #short_btn, body .wishlist-block #action_buttons #short_btn {
  padding: 2px;
}
/* line 303, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons #filter_btn, body .wishlist-block #action_buttons #filter_btn {
  padding: 2px;
}
/* line 306, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons .select_box, body .wishlist-block #action_buttons .select_box {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: 45px;
}
/* line 311, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons .select_box #custom_sort, body .wishlist-block #action_buttons .select_box #custom_sort {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 10px;
  top: 0px;
  text-transform: uppercase;
  font-size: 12px;
  color: #303030;
  padding-top: 18px;
  border-right-color: white;
  border-right-width: 2px;
}
/* line 324, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons .select_box .form_input_select, body .wishlist-block #action_buttons .select_box .form_input_select {
  width: 100%;
  font-size: 14px;
  border: 0;
  overflow: hidden;
  text-transform: none;
  background: 0 0;
  -webkit-appearance: none;
  opacity: 0;
  filter: alpha(opacity=0);
  margin-bottom: 12px;
}
/* line 337, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #action_buttons #filter-button, body .wishlist-block #action_buttons #filter-button {
  width: 100%;
  height: 45px;
  background: #e7e7e7;
  color: #303030;
  font-size: 12px;
}
/* line 346, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #more-designs-loader, body .wishlist-block #more-designs-loader {
  display: none;
  border: 4px dotted #670b19;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}
/* line 355, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #load-more-designs-btn, body .wishlist-block #load-more-designs-btn {
  display: none;
  font-size: 14px;
  border-radius: 2px;
  cursor: pointer;
  background-color: #8f1b1d;
}
/* line 363, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .float-Btn, body .wishlist-block .float-Btn {
  position: fixed;
  bottom: 50px;
  right: 12px;
  z-index: 19;
}
/* line 368, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .float-Btn i:before, body .wishlist-block .float-Btn i:before {
  background-color: #8f1b1d;
  width: 2.3rem;
  height: 2.3rem;
  line-height: 2.3rem;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  font-size: 1.5rem;
  opacity: 0.95;
}
/* line 380, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #back-top, body .wishlist-block #back-top {
  display: none;
}
/* line 384, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block #toggle-design-box #toggle-design-icon, body .wishlist-block #toggle-design-box #toggle-design-icon {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
/* line 391, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .navigate, body .wishlist-block .navigate {
  text-align: center;
  margin-top: 5px;
  border-top: 1px solid #efefef;
  padding-top: 5px;
}
/* line 396, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .store_page_block .navigate .pagination, body .wishlist-block .navigate .pagination {
  padding: 0 60px;
  display: flex;
  justify-content: space-between;
}
/* line 404, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .reveal-modal {
  overflow: scroll;
  height: 100%;
  top: 0px;
  bottom: 0px;
  margin-top: 0px;
  position: fixed;
  -webkit-overflow-scrolling: touch;
}
/* line 412, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .reveal-modal #loader {
  display: none;
  border: 10px dotted #670b19;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  position: absolute;
  left: 43%;
  z-index: 9999;
  top: 44%;
  animation: spin 2s ease-in-out infinite;
}
/* line 426, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details {
  border-radius: 0px;
  height: 100%;
}
/* line 429, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .chips_maker {
  background: #000000;
  padding: 0px 4px;
  display: none;
  border-radius: 2px;
}
/* line 434, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .chips_maker .chip {
  display: inline-block;
  padding: 2px 10px;
  height: 25px;
  margin: 3px 2px;
  font-size: 15px;
  line-height: 20px;
  border-radius: 16px;
  background-color: #607D8B;
}
/* line 443, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .chips_maker .chip .closebtn {
  padding-left: 10px;
  color: #FF9800;
  font-weight: bold;
  float: right;
  font-size: 20px;
  cursor: pointer;
}
/* line 453, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .panel_heading {
  padding: 0px;
}
/* line 456, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .panel_content.small-4 {
  padding: 0px 0px 59px 0px;
  height: 100%;
  overflow-y: scroll;
  border-radius: 0px;
  background-color: #670b19;
}
/* line 463, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content {
  padding-bottom: 59px;
  width: 66%;
  background-color: #fff;
  height: 100%;
  margin-bottom: 0px;
  overflow-y: scroll;
  border-radius: 0px;
}
/* line 471, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .active {
  padding-bottom: 0px;
}
/* line 474, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-radiobox {
  display: inline-block;
  margin-bottom: 1rem;
}
/* line 477, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-radiobox label {
  color: white;
}
/* line 482, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox {
  display: inline-block;
  margin-bottom: 1rem;
}
/* line 485, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox label {
  color: white;
}
/* line 490, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch {
  margin-bottom: 0.5rem !important;
  width: 25%;
}
/* line 493, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .color-input {
  visibility: hidden;
  width: 0;
}
/* line 497, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label {
  position: relative;
  left: 20px;
  bottom: 20px;
}
/* line 502, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label-custom-color {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  margin: 0px;
  position: relative;
  border: 1px solid #d8d8d8;
}
/* line 510, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label-custom-color:after {
  content: '\2713';
  display: block;
  position: absolute;
  bottom: -3px;
  left: 2px;
  opacity: 0;
  color: whitesmoke;
  font-size: 16px;
}
/* line 520, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch input:checked + .label-custom-color:after {
  opacity: 1;
}
/* line 523, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .multicolor-value {
  background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
  background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
}
/* line 529, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .switch.tiny label {
  height: 1rem;
  width: 2rem;
  background: #9e9696;
}
/* line 534, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .switch.tiny label:after {
  height: 0.5rem;
  width: 0.7rem;
  background: #f4f4f4;
}
/* line 539, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .switch.tiny input {
  left: 8px;
  top: 5px;
}
/* line 543, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .switch.tiny input:checked + label {
  background: #670b19;
}
/* line 546, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .switch.tiny input:checked + label:after {
  left: 0.9rem;
}
/* line 551, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .label-custom {
  vertical-align: top;
  display: inline-block;
  width: 75%;
  float: right;
  font-size: 0.8rem;
}
/* line 557, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .label-custom .label {
  float: right;
}
/* line 561, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .tabs-content .label-desktop-fix {
  width: 90%;
}
/* line 566, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details header .tab-filter-fix {
  font-size: 12px;
  width: 100%;
  padding: 10px 0px 10px 5px;
  text-align: left;
  margin: 0px;
  line-height: 16px;
  text-transform: capitalize;
  background: #670b19;
}
/* line 575, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details header .tab-filter-fix .tiny-green {
  border-radius: 50%;
  background: white;
  color: black;
  padding: 2px 5px;
  margin-left: 3px;
  font-size: 10px;
  font-weight: 700;
}
/* line 585, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details header .active-tab {
  background-color: #841523;
}
/* line 590, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .short_filter_btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-shadow: 0 2px 34px -4px rgba(0, 0, 0, 0.7);
  background: #c1bbae;
}
/* line 597, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .short_filter_btn div {
  background-color: #ffffff !important;
  color: #670b19;
  margin: 0px;
  font-size: 0.875rem;
  border-right: 1px solid grey;
  padding: 18px 0px;
  font-weight: bold;
}
/* line 608, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #designable_details .filter-desktop-fix {
  bottom: 10px;
  left: 172px;
  width: 74%;
}
/* line 616, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .navigate_page {
  vertical-align: middle;
  margin-bottom: 1em;
}
/* line 621, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .nav-button {
  margin: 1em 1em 0 0;
}
/* line 625, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #design_details {
  width: 100%;
}
@media only screen and (min-width: 320px) and (max-width: 1040px) {
  /* line 631, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  body .details_block {
    font-size: 0.875rem !important;
  }
  /* line 633, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  body .details_block .actual_price {
    font-size: 0.8rem;
  }
}
@media only screen and (max-width: 320px) {
  /* line 640, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  body .details_block {
    font-size: 0.675rem !important;
  }
  /* line 642, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  body .details_block .actual_price {
    font-size: 0.65rem;
  }
}
/* line 647, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .details_block {
  color: #303030;
  float: left;
  font-weight: bold;
  text-align: left;
}
/* line 652, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .details_block .actual_price {
  display: inline-block;
  color: #595959;
  text-decoration: line-through;
  margin-left: 3px;
  font-weight: 100 !important;
}
/* line 661, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .rts_plp_logo {
  background: url(/assets/RTS_PLP_logo-012fa859f92f66b05993583d8e2b53904e96d5981b10ebfa1cc7b7184e07711a.png) no-repeat;
  width: 38px;
  height: 38px;
  background-size: 40px;
  -webkit-border-radius: 99em;
  -moz-border-radius: 99em;
  border-radius: 99em;
  border: 2px solid #eee;
  box-shadow: 2px 2px 2px 2px rgba(0, 0, 0, 0.2);
  position: relative;
  float: right;
  background-position: -3px -3px;
  margin-top: -38px;
  top: 40px;
  padding: 9px 9px;
}
/* line 679, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .discount_new_block {
  font-size: 12px !important;
  background: url(/assets/sprite-f7e7721dd2d9b1d316fd41711a5c2fd08099db15a0dd34b4ac115bc94c6e17b8.png) no-repeat;
  padding: 9px 9px;
  line-height: 12px;
  position: relative;
  color: #ffffff;
  width: 54px;
  height: 40px;
  float: right;
  top: 40px;
  margin-top: -40px;
  background-position: 78% 45%;
}
/* line 694, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .discount_new_wrap {
  padding: 1px;
}
/* line 698, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .add_new_pos {
  position: relative !important;
  width: 100% !important;
}
/* line 703, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .discount_font {
  font-size: 0.7em;
  font-weight: normal;
  float: left;
  color: #ffffff;
}
/* line 709, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body .margin-down-5 {
  margin-bottom: 5px;
}
/* line 712, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
body #search_desktop {
  display: none;
}

/* line 720, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs {
  margin: 0;
}
/* line 723, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li img {
  width: 100%;
}
/* line 726, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page {
  border: 0.75px solid #d8d8d8;
  position: relative;
}
/* line 728, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .wishlist-remove {
  position: absolute;
  right: 4px;
  top: 4px;
  border-radius: 50px;
  height: 24px;
  box-shadow: aliceblue;
  width: 24px;
  border: solid 1.2px #94969F;
  background-color: rgba(255, 255, 255, 0.6);
  padding-left: 6px;
  padding-bottom: 2px;
  padding-top: 0x;
  font-size: 14px;
}
/* line 744, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel {
  position: relative;
  padding: 0em 0.3em 0.3em 0.3em;
}
/* line 748, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc {
  margin-bottom: 0em;
}
/* line 750, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc a {
  color: #8f1b1d;
  font-size: 0.725rem;
  font-weight: bold;
}
/* line 755, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc .title-column, .home_page_designs > li .fr_page .panel.design_desc .wishlist-column {
  padding: 0;
}
/* line 760, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button {
  font-size: 1.6rem;
  margin: 0;
  padding: 0;
  float: right;
  background: transparent;
  color: #8f1b1d;
  position: absolute;
  right: 0;
  top: 1px;
}
/* line 770, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button:focus {
  outline: none;
}
/* line 774, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs > li .fr_page .panel.design_desc .wishlist-column .wishlist-forms .wishlist-heart-button.empty-heart {
  color: gray;
}
/* line 784, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs li.original_price {
  font-size: 0.8rem;
  color: #303030;
}
/* line 788, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs li.discount_price {
  font-weight: bold;
  color: #303030;
}
/* line 792, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs li.percent_off {
  color: red;
  margin-top: -1.7em;
  font-size: 0.9em;
}
/* line 798, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .design_desc {
  padding: 0.5em;
}
/* line 800, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .design_desc li {
  padding-bottom: 0em;
}
/* line 805, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs #design_details {
  width: 100%;
}
/* line 809, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .details_block {
  color: #303030;
}
/* line 813, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .design-col1 {
  float: left;
  width: 100%;
  font-size: 0.75rem !important;
  font-weight: bold;
}
/* line 820, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .design-col2 {
  font-size: 12px !important;
  background: url(/assets/sprite-f7e7721dd2d9b1d316fd41711a5c2fd08099db15a0dd34b4ac115bc94c6e17b8.png) no-repeat;
  padding: 9px 9px;
  line-height: 12px;
  position: relative;
  color: #ffffff;
  background-position: 78% 45%;
  width: 54px;
  height: 40px;
  float: right;
  top: 40px;
  margin-top: -40px;
}
/* line 835, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .add-to-cart-bst {
  padding: 12px 2px;
  width: 100%;
  color: #ffffff;
  background: #8f1b1d;
  font-weight: 700;
}
/* line 843, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .discount_new_wrap {
  text-align: center;
  word-wrap: break-word;
  padding-left: 0.5px;
}
/* line 849, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .add_new_pos {
  position: relative !important;
  width: 100% !important;
}
/* line 854, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .discount_font {
  font-size: 0.8em;
}
/* line 858, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.home_page_designs .design_price {
  font-weight: bold;
}

/* line 863, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info {
  /*border:1px dotted grey;*/
  padding: 8px;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
}
/* line 870, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post .seo-list-anchor {
  font-size: 14px;
  padding: 5px;
  line-height: 17px;
}
/* line 875, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post .seo-list-table {
  width: 70%;
  border: 1px solid white;
}
/* line 879, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post .seo-list-line-height {
  line-height: 30px;
}
/* line 882, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post .seo-list-font {
  font-size: 14px;
}
/* line 885, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h1, .footer-info #seo_post h2, .footer-info #seo_post h3, .footer-info #seo_post h4, .footer-info #seo_post h5, .footer-info #seo_post h6 {
  color: #303030;
  font-weight: bold;
}
/* line 889, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h1 {
  font-size: 1.1875rem;
}
/* line 892, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h2 {
  font-size: 1.125rem;
}
/* line 895, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h3 {
  font-size: 1rem;
}
/* line 898, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h4 {
  font-size: 0.9375rem;
}
/* line 901, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h5 {
  font-size: 0.8125rem;
}
/* line 904, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post h6 {
  font-size: 0.6875rem;
}
/* line 907, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post p {
  font-size: 0.875rem;
  color: #303030;
  text-align: justify;
  margin-bottom: 0.9rem;
}
/* line 912, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post p a {
  font-weight: bold;
}
/* line 916, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post ul, .footer-info #seo_post ol {
  font-size: 0.875rem;
  color: #303030;
  text-align: justify;
  margin-bottom: 0.6rem;
  margin-left: 1.1rem;
}
/* line 923, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.footer-info #seo_post.read-more {
  height: 7.8em;
  overflow: hidden;
}

/* line 929, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.catalog-cert {
  position: absolute;
  width: 40px !important;
  height: 40px;
}

/* line 934, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.catalog-rating {
  position: relative;
  float: left;
  width: 40px !important;
  height: 30px;
  margin-top: -25px;
}

@media only screen and (min-width: 64.063em) {
  /* line 943, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  .search_margin {
    display: none;
  }

  /* line 946, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  #search_desktop {
    display: block;
  }
}
@media screen and (min-width: 40em) {
  /* line 952, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
  #toggle-design-box {
    display: none !important;
  }
}
/* line 957, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.rating_div .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 5%;
  font-weight: bold;
}
/* line 965, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.rating_div .green-rating {
  background-color: #16be48;
}
/* line 968, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.rating_div .red-rating {
  background-color: #FF5722;
}
/* line 971, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.rating_div .orange-rating {
  background-color: #FFA000;
}

/* line 976, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals #container {
  padding: 0;
  width: 100%;
  margin: 0;
}
/* line 981, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header {
  margin: 0 0 0.5rem 0;
  border-bottom: 1px solid #eee;
}
/* line 984, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .fd_timer, .store_flash_deals .flash-deal-header .fd-header {
  font-size: 0.875rem;
}
/* line 987, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .fd_tab {
  font-size: 0.75rem;
  font-family: 'Lato Black';
}
/* line 991, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .tab-fix {
  background-color: #ffffff;
  position: relative;
  text-align: center;
  display: inline-block;
  padding: 0.5rem 0.1rem;
  color: #8f1b1d;
  margin: 0 -2px;
  border: 1px solid #eee;
}
/* line 1001, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .fd-active-tab {
  background-color: #8f1b1d;
  color: #ffffff;
}
/* line 1005, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer {
  margin: auto;
  display: flex;
  text-align: center;
  padding: 5px 0px;
  background: #e0cfcf;
}
/* line 1011, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .ends-text {
  width: 80%;
  text-align: right;
}
/* line 1015, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .deal_ends {
  padding: 0;
}
/* line 1017, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .deal_ends .countdown {
  width: 0;
}
/* line 1019, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .deal_ends .countdown .deal_text {
  display: none;
}
/* line 1022, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .deal_ends .countdown .clock {
  padding: 0;
}
/* line 1024, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .flash-deal-header .ongoing-fd-timer .deal_ends .countdown .clock .deal_timer {
  margin: 4px;
  padding: 4px;
  background: #670b19;
  color: #fff;
  border-radius: 2px;
  font-size: 0.875rem;
  font-family: 'Lato Black';
}
/* line 1039, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .truncate a {
  color: #303030;
}
/* line 1043, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd-details-block {
  font-family: 'Lato Black';
  color: #8f1b1d;
  text-align: left;
}
/* line 1047, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd-details-block .actual_price {
  font-family: 'Lato';
  display: inline-block;
  color: #595959;
  text-decoration: line-through;
  margin-left: 3px;
  font-weight: 100 !important;
}
/* line 1057, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .percent-discount .details_block {
  color: #303030;
  float: left;
  font-weight: bold;
  text-align: left;
  padding: 0.1em 0.3em 0em 0.3em;
  font-size: 0.85em !important;
  border: 1px dashed #303030;
}
/* line 1067, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .add_to_cart_link, .store_flash_deals .sold_out_link, .store_flash_deals .wishlist-heart-button {
  position: absolute;
  bottom: 0;
  padding: 0.6em 2em 0.5em 2em;
  margin: 0 .5em;
  border: 1px solid #8f1b1d;
  color: #8f1b1d;
  font-family: 'Lato Black';
  left: 0;
  background: white;
}
/* line 1077, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .add_to_cart_link:hover, .store_flash_deals .add_to_cart_link:focus, .store_flash_deals .sold_out_link:hover, .store_flash_deals .sold_out_link:focus, .store_flash_deals .wishlist-heart-button:hover, .store_flash_deals .wishlist-heart-button:focus {
  background: none;
}
/* line 1081, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page {
  display: flex;
  border-bottom: 2px solid #eee;
  padding-bottom: 5px;
}
/* line 1085, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page img {
  border: 1px solid #eee;
}
/* line 1091, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page .wishlist-column .new_wishlist .wishlist-heart-button:after {
  content: 'ADD TO WISHLIST';
  font-size: 0.875rem;
}
/* line 1095, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page .wishlist-column .new_wishlist .wishlist-heart-button .fi-heart {
  display: none;
}
/* line 1102, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page .wishlist-column .delete_wishlist .wishlist-heart-button:after {
  content: 'REMOVE FROM WISHLIST';
  font-size: 0.875rem;
}
/* line 1106, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page .wishlist-column .delete_wishlist .wishlist-heart-button .fi-heart {
  display: none;
}
/* line 1112, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.store_flash_deals .fd_page .only-few-left-label {
  position: absolute;
  margin-top: 1.8em;
  color: #670b19;
  font-family: "Lato Black";
}

/*.star-yellow:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #FFC315 100%, #e2e0e0 0%);
      background: -o-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: -moz-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      font-size: 115%;
    }
.star-gray:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #e2e0e0 100%, #FFC315 0%);
      background: -o-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: -moz-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      
      font-size: 115%;
    }
.star-half:before{
    content: "\2605";
    display: inline-block;
    background: -webkit-linear-gradient(left, #FFC315 49%, #e2e0e0 50%);
    background: -o-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: -moz-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    -webkit-text-fill-color: transparent;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-size: 115%;
}

.star_align{
  padding: 0px;
  text-align: center;
}

*/
/* line 1169, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs {
  padding: 0.75em;
  margin-bottom: 1em;
  margin-top: 1em;
  box-sizing: border-box;
  box-shadow: 0 0 0.5em #2d2d2d;
  font-size: 14px;
  color: #303030;
  line-height: 25px;
}
/* line 1178, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs ol, #FAQs ul {
  font-size: 14px;
  margin: 5px 0px 5px 0px;
  padding-left: 20px;
}
/* line 1183, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs dt {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 1em;
}
/* line 1189, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs .accordion-navigation a {
  font-size: 1em;
  color: #670b19;
}
/* line 1192, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs .accordion-navigation a h4 {
  font-size: 15px;
  font-weight: bold;
}
/* line 1196, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs .accordion-navigation a p {
  font-size: 14px;
  font-weight: bold;
}
/* line 1202, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs dd:not(:last-child) {
  border-bottom: 1px solid #eee;
}
/* line 1205, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs dd > a:hover {
  background: none;
}
/* line 1208, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs dd.active > a {
  background: none;
}
/* line 1211, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
#FAQs .accordion-navigation > .content, #FAQs dd > .content {
  padding: 0px;
}

/* line 1217, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.sort_filter_options .fixed_button {
  position: fixed;
  background: white;
  top: auto;
  bottom: -10px;
  left: 0;
  right: 0;
  z-index: 10;
}
/* line 1225, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog_red.scss */
.sort_filter_options .fixed_button .select_box {
  background: #e7e7e7;
}
