(function(){var e,t;$(function(){var e,t;return $("#login_modal").length>0&&$("#login_modal").foundation("reveal","open"),$("#close_modal").click(function(){return $("#login_modal").foundation("reveal","close")}),$("#inches,#feet").change(function(){var e,t,n,i;return i=parseInt($("#inches").val()),e=parseInt($("#feet").val()),isNaN(i)?$("#conversion_height").html(""):(n=12*e+i,t=Math.round(2.54*n*100)/100,$("#height").val(e+"."+i),$("#conversion_height").html("In Inches: "+n+" inches  In Cms: "+t+" cms"))}),$("#weight").keyup(function(){var e,t;return t=parseInt($(this).val()),0>t&&$(this).val(0),t>200&&$(this).val(200),t=parseInt($(this).val()),e=Math.round(2.2*t*100)/100,isNaN(t)?void 0:$("#conversion_weight").html("In Pounds: "+e)}),$("#measurement_data_1").on("submit",function(e){var n;return e.preventDefault(),""===$("#weight").val()?alert("Weight is required"):$("#weight").val()<20?alert("Weight Should be more than 20"):(n=$(this).serialize(),$.ajax(t(n)))}),$("#measurement_select").change(function(){var e,n,i;return i=parseInt($("#inches").val()),e=parseInt($("#feet").val()),$("#height").val(parseFloat(e+"."+i)),n=$("#measurement_data_1").serialize(),$.ajax(t(n))}),t=function(e){return{url:"stitching_measurement/measurement_data",type:"GET",data:e,datatype:"script",beforeSend:function(){return $("#loadingImage").show()},complete:function(){return $("#loadingImage").hide()},success:function(){return $(document).foundation(),$(".example-orbit").css("height","200px")}}},$(document).on("click",".similar-img-check",function(){var t;return t=$(this).data("id"),e(t)}),e=function(e){var t;return t=$("#item_"+e),t.is(":checked")?(t[0].checked=!1,$("#check_"+e).removeClass("selected_style"),$(".item_checkbox:checked").each(function(){var e;return e=$(this).val(),$("#check_"+e).addClass("selected_style")})):(t[0].checked=!0,$(".item_checkbox:checked").each(function(){return e=$(this).val(),$("#check_"+e).addClass("selected_style")}))}}),e=function(){function e(e){this.$form=e,t.call(this)}var t,n,i,r;return t=function(){return this.$form.on("submit",function(e){return e.preventDefault(),n.call(this),this.callback(r.call(this))}.bind(this)),this.$form.find(".measurement-experience-options").on("change","input[type=radio]",function(){return i.call(this)}.bind(this))},r=function(){return this.$form.find(".measurement-experience-options input[type=radio]:checked").val()},n=function(){return this.$form.hide()},i=function(){return this.$form.find("input[type=submit]").removeAttr("disabled")},e.prototype.afterSubmit=function(e){return this.callback=e},e}(),t=function(){function e(e,t){this.$container=e,this.$form=this.$container.find("#measurement_data_1"),this.measurementExperienceForm=t,r.call(this)}var t,n,i,r,a,c;return t=function(){return this.measurementExperienceForm.afterSubmit(function(e){return c.call(this,e),i.call(this)}.bind(this))},i=function(){return this.$container.show(),a.call(this)},n=function(){return this.$container.hide()},a=function(){return $("html, body").animate({scrollTop:0},300)},c=function(e){return this.$form.find("input[type=hidden][name=measurement_experience]").val(e)},r=function(){return n.call(this),t.call(this)},e}(),$(function(){return new t($("ul.accordion"),new e($(".measurement-experience")))})}).call(this);