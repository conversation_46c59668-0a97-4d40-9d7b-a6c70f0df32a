(function(){$(function(){var e,t;return $(".mark_as_default").on("change",function(){return $(this).parent().submit()}),$("#address_country").length>0&&(e=function(e){var t;return t="",$.each(e,function(e,r){return t+="<option value = '"+r+"'>"+r+"</option>"}),t},t=function(t,r,s){return{type:"GET",url:"/country/"+t+"/get_states",success:function(t){var r,a;return a=e(t),""===a?r='<input id="address_state" name="address[state]" size="30" required=true placeholder="Enter Your State" type="text">':(a="<option value = ''>Please Select</option>"+a,r="<select id=address_state name =address[state] required=true>"+a+"</select>"),$("#address_state").replaceWith(r),"undefined"!=typeof s?$("#address_state").val(s):void 0}}},$("#address_country").on("change",function(){return""!==$(this).val()?$.ajax(t($(this).val(),$(this)[0].id)):void 0}),""!==$("#address_country").val()&&$.ajax(t($("#address_country").val(),$("#address_country")[0].id))),$("#shipping_address").on("click",function(){var e;return e=0,$("#shipping_address:checked").length>0&&(e=1),$("#ship_to_same_address").attr("value",e)})})}).call(this);