/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* Webfont: Lato-Regular */
@font-face {
  font-family: 'Lato';
  font-display: swap;
  src: url(/assets/Lato-Regular-e735410675eacc363b257112f39eb819a854b03077d7b1f0caa6e7660ffbd8b3.eot);
  /* IE9 Compat Modes */
  src: url(/assets/Lato-Regular-e735410675eacc363b257112f39eb819a854b03077d7b1f0caa6e7660ffbd8b3.eot?#iefix) format("embedded-opentype"), url(/assets/Lato-Regular-983b0caf336e8542214fc17019a4fc5e0360864b92806ca14d55c1fc1c2c5a0f.woff2) format("woff2"), url(/assets/Lato-Regular-5b9025dda4d7688e3311b0c17eddc501133b807def33effaef6593843cf5416e.woff) format("woff"), url(/assets/Lato-Regular-089ab6d4a57e0e6c4dd3b681b6fd50a5184f1b902429d35e1227e52d6ccad1bd.ttf) format("truetype");
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}
/* Webfont: Lato-Bold */
@font-face {
  font-family: 'Lato Bold';
  font-display: swap;
  src: url(/assets/Lato-Bold-b91fc1ca55df440dc11354b32a39b45db663d24bb17b16dcb2e706330955b0d1.eot);
  /* IE9 Compat Modes */
  src: url(/assets/Lato-Bold-b91fc1ca55df440dc11354b32a39b45db663d24bb17b16dcb2e706330955b0d1.eot?#iefix) format("embedded-opentype"), url(/assets/Lato-Bold-ae88fc0d7a961832f809527d30bd3983a6866d42f66a56ade23f543681594db6.woff2) format("woff2"), url(/assets/Lato-Bold-0e56b17d142eb366c8007031d14e34da48c70b4a9d9a0ca492e696a7bae45e1e.woff) format("woff"), url(/assets/Lato-Bold-f71f833c099f450606f8107b83ef208ae918c0ea00779466d45e9be96b0bc7cc.ttf) format("truetype");
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}
/* Webfont: Lato-Bold */
@font-face {
  font-family: 'Lato Black';
  font-display: swap;
  src: url(/assets/Lato-Black-369fb1e11906d5cfffcff0e5a65e708cd430d91412fdce1549de684d050fd93d.eot);
  /* IE9 Compat Modes */
  src: url(/assets/Lato-Black-369fb1e11906d5cfffcff0e5a65e708cd430d91412fdce1549de684d050fd93d.eot?#iefix) format("embedded-opentype"), url(/assets/Lato-Black-34bb46634d07ac579411823eb39fac1376b012257460066a98b95075d086ccdd.woff2) format("woff2"), url(/assets/Lato-Black-659bd5ede5d3ef9c2373704ddaab613313a6c323e631bb9c536f592f4a5092ce.woff) format("woff"), url(/assets/Lato-Black-c1eab3331b0aa1b83c04d52d37a847aef9dc924278982cfaebd0f983ecb4d17b.ttf) format("truetype");
  font-style: normal;
  font-weight: normal;
  text-rendering: optimizeLegibility;
}
@font-face {
  font-family: "foundation-icons";
  font-display: swap;
  src: url(/assets/foundation-icons-9189cd8788a2d42f89ecb72f08d55cc366a3abc441c3413d9ceca66ec3144e46.eot);
  src: url(/assets/foundation-icons-9189cd8788a2d42f89ecb72f08d55cc366a3abc441c3413d9ceca66ec3144e46.eot?#iefix) format("embedded-opentype"), url(/assets/foundation-icons-8c44c3feedae5331a281278ea3ba91d2255928a2f3010d316d6fbb9052e0c2ec.woff) format("woff"), url(/assets/foundation-icons-7e1dd03dd4ce90b658052554cd7459df16716717389a552fa4c6d56a5f8933e6.ttf) format("truetype"), url(/assets/foundation-icons-ba2d122321cfede034deb120f35abc790a023d29065c36bd53e57039897ad052.svg#fontcustom) format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 55, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/fonts.scss */
[class^="fi-"]:before, [class*=" fi-"]:before,
[class^="fi-"]:after, [class*=" fi-"]:after {
  font-family: "foundation-icons";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  text-decoration: inherit;
}

/* line 6, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-version {
  font-family: "/5.5.2/";
}
/* line 9, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-small {
  font-family: "/only screen/";
  width: 0;
}
/* line 13, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-small-only {
  font-family: "/only screen and (max-width: 29.9375em)/";
  width: 0;
}
/* line 17, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-medium {
  font-family: "/only screen and (min-width:30em)/";
  width: 30em;
}
/* line 21, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-medium-only {
  font-family: "/only screen and (min-width:30em) and (max-width:64em)/";
  width: 30em;
}
/* line 25, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-large {
  font-family: "/only screen and (min-width:64.0625em)/";
  width: 64.0625em;
}
/* line 29, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-large-only {
  font-family: "/only screen and (min-width:64.0625em) and (max-width:90em)/";
  width: 64.0625em;
}
/* line 33, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-xlarge {
  font-family: "/only screen and (min-width:90.0625em)/";
  width: 90.0625em;
}
/* line 37, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-xlarge-only {
  font-family: "/only screen and (min-width:90.0625em) and (max-width:120em)/";
  width: 90.0625em;
}
/* line 41, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-xxlarge {
  font-family: "/only screen and (min-width:120.0625em)/";
  width: 120.0625em;
}
/* line 45, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-data-attribute-namespace {
  font-family: false;
}

/* line 49, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
html, body {
  height: 100%;
  font-family: 'Lato', sans-serif;
}

/* line 53, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body {
  background: #ffffff;
  color: #303030;
}

/* line 58, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
a:hover {
  cursor: pointer;
}

/* line 62, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
img {
  max-width: 100%;
  height: auto;
  -ms-interpolation-mode: bicubic;
}

/* line 69, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#map_canvas img, #map_canvas embed, #map_canvas object {
  max-width: none !important;
}

/* line 75, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.map_canvas img, .map_canvas embed, .map_canvas object {
  max-width: none !important;
}

/* line 81, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.mqa-display img, .mqa-display embed, .mqa-display object {
  max-width: none !important;
}

/* line 87, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.clearfix:before {
  content: " ";
  display: table;
}
/* line 91, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.clearfix:after {
  content: " ";
  display: table;
  clear: both;
}

/* line 98, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.hide {
  display: none;
}

/* line 102, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.invisible {
  visibility: hidden;
}

/* line 106, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 111, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
img {
  display: inline-block;
  vertical-align: middle;
}

/* line 116, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea {
  height: auto;
  min-height: 50px;
}

/* line 121, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select {
  width: 100%;
}

/* line 125, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;
}
/* line 129, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row:before {
  content: " ";
  display: table;
}
/* line 133, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 140, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row.collapse > .column, .row.collapse > .columns {
  padding-left: 0;
  padding-right: 0;
}
/* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row.collapse .row {
  margin-left: 0;
  margin-right: 0;
}
/* line 150, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row {
  max-width: none;
  width: auto;
}
/* line 153, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row:before {
  content: " ";
  display: table;
}
/* line 157, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 162, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row.collapse {
  margin: 0;
  max-width: none;
  width: auto;
}
/* line 166, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row.collapse:before {
  content: " ";
  display: table;
}
/* line 170, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.row .row.collapse:after {
  content: " ";
  display: table;
  clear: both;
}

@media only screen {
  /* line 180, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-1 {
    width: 8.33333%;
  }

  /* line 183, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-5 {
    width: 41.66667%;
  }

  /* line 186, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-6 {
    width: 50%;
  }

  /* line 189, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-7 {
    width: 58.33333%;
  }

  /* line 192, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-8 {
    width: 66.66667%;
  }

  /* line 195, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-9 {
    width: 75%;
  }

  /* line 198, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-10 {
    width: 83.33333%;
  }

  /* line 201, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-11 {
    width: 91.66667%;
  }

  /* line 204, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-12 {
    width: 100%;
  }

  /* line 207, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.small-centered, .columns.small-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  /* line 212, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.small-uncentered, .columns.small-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  /* line 217, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.small-centered:last-child, .columns.small-centered:last-child {
    float: none;
  }

  /* line 220, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.small-uncentered:last-child, .columns.small-uncentered:last-child {
    float: left;
  }

  /* line 223, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.small-uncentered.opposite, .columns.small-uncentered.opposite {
    float: right;
  }

  /* line 229, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.small-collapse > .column, .row.small-collapse > .columns {
    padding-left: 0;
    padding-right: 0;
  }
  /* line 234, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.small-collapse .row {
    margin-left: 0;
    margin-right: 0;
  }
  /* line 240, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.small-uncollapse > .column, .row.small-uncollapse > .columns {
    float: left;
  }
}
@media only screen and (min-width: 30em) {
  /* line 248, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column, .columns {
    position: relative;
    float: left;
  }

  /* line 252, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-4 {
    width: 33.33333%;
  }

  /* line 255, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-5 {
    width: 41.66667%;
  }

  /* line 258, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-6 {
    width: 50%;
  }

  /* line 261, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-7 {
    width: 58.33333%;
  }

  /* line 264, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-8 {
    width: 66.66667%;
  }

  /* line 267, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-9 {
    width: 75%;
  }

  /* line 270, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-10 {
    width: 83.33333%;
  }

  /* line 273, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-11 {
    width: 91.66667%;
  }

  /* line 276, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-12 {
    width: 100%;
  }

  /* line 279, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.medium-centered, .columns.medium-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  /* line 284, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.medium-uncentered, .columns.medium-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  /* line 289, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.medium-centered:last-child, .columns.medium-centered:last-child {
    float: none;
  }

  /* line 292, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.medium-uncentered:last-child, .columns.medium-uncentered:last-child {
    float: left;
  }

  /* line 295, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column.medium-uncentered.opposite, .columns.medium-uncentered.opposite {
    float: right;
  }

  /* line 301, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.medium-collapse > .column, .row.medium-collapse > .columns {
    padding-left: 0;
    padding-right: 0;
  }
  /* line 306, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.medium-collapse .row {
    margin-left: 0;
    margin-right: 0;
  }
  /* line 312, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .row.medium-uncollapse > .column, .row.medium-uncollapse > .columns {
    float: left;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 320, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .column, .columns {
    position: relative;
    float: left;
  }

  /* line 324, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-1 {
    width: 8.33333%;
  }

  /* line 327, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-2 {
    width: 16.66667%;
  }

  /* line 330, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-3 {
    width: 25%;
  }

  /* line 333, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-4 {
    width: 33.33333%;
  }

  /* line 336, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-5 {
    width: 41.66667%;
  }

  /* line 339, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-6 {
    width: 50%;
  }

  /* line 342, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-7 {
    width: 58.33333%;
  }

  /* line 345, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-8 {
    width: 66.66667%;
  }

  /* line 348, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-9 {
    width: 75%;
  }

  /* line 351, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-10 {
    width: 83.33333%;
  }

  /* line 354, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-11 {
    width: 91.66667%;
  }

  /* line 357, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-12 {
    width: 100%;
  }
}
/* line 362, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion {
  margin-bottom: 0;
}
/* line 364, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion:before {
  content: " ";
  display: table;
}
/* line 368, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 373, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation, .accordion dd {
  display: block;
  margin-bottom: 0 !important;
}
/* line 377, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation.active > a, .accordion dd.active > a {
  background: #b11f2d;
}
/* line 380, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation > a, .accordion dd > a {
  display: block;
  font-size: 0.8rem;
}
/* line 384, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation > a:hover, .accordion dd > a:hover {
  background: #b11f2d;
}
/* line 387, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation > .content, .accordion dd > .content {
  display: none;
  padding: 0.9375rem;
}
/* line 391, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.accordion .accordion-navigation > .content.active, .accordion dd > .content.active {
  display: block;
}

/* line 396, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.8125rem;
  font-weight: normal;
  margin-bottom: 1.25rem;
  padding: 0.5rem 2rem 0.5rem 0.5rem;
  position: relative;
  transition: opacity 300ms ease-out;
  background-color: #008CBA;
  border-color: #0078a0;
  color: #FFFFFF;
}
/* line 409, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box .close {
  right: 0.25rem;
  background: inherit;
  color: rgba(0, 0, 0, 0.7);
  font-size: 1.375rem;
  line-height: .9;
  margin-top: -0.6875rem;
  padding: 0 6px 4px;
  position: absolute;
  top: 50%;
}
/* line 419, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box .close:hover, .alert-box .close:focus {
  opacity: 0.5;
}
/* line 423, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.radius {
  border-radius: 3px;
}
/* line 426, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.round {
  border-radius: 1000px;
}
/* line 429, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.success {
  background-color: #43AC6A;
  border-color: #3a945b;
  color: #FFFFFF;
}
/* line 434, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.alert {
  background-color: #f04124;
  border-color: #de2d0f;
  color: #FFFFFF;
}
/* line 439, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.secondary {
  background-color: #e7e7e7;
  border-color: #c7c7c7;
  color: #4f4f4f;
}
/* line 444, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.warning {
  background-color: #f08a24;
  border-color: #de770f;
  color: #FFFFFF;
}
/* line 449, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.info {
  background-color: white;
  border-color: #a5a5a5;
  border-width: 1px;
  border-left: 8px solid #f5a623;
  color: rgba(0, 0, 0, 0.6);
  font-weight: bold;
  margin-bottom: 0.8em;
}
/* line 458, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.alert-box.alert-close {
  opacity: 0;
}

/* line 462, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.flash-message {
  padding: 0px 10px;
  text-align: center;
}
/* line 470, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.flash-message .global-timer #offer_message_clock .deal_timer {
  padding: 2px;
  margin: 0px 2px;
  border-radius: 2px;
}

/* line 479, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
[class*="block-grid-"] {
  display: block;
  padding: 0;
  margin: 0 -0.625rem;
}
/* line 483, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
[class*="block-grid-"]:before {
  content: " ";
  display: table;
}
/* line 487, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
[class*="block-grid-"]:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 492, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
[class*="block-grid-"] > li {
  display: block;
  float: left;
  height: auto;
  padding: 0 0.625rem 1.25rem;
}

@media only screen {
  /* line 501, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-1 > li {
    list-style: none;
    width: 100%;
  }
  /* line 504, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-1 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 507, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-1 > li:nth-of-type(1n+1) {
    clear: both;
  }

  /* line 511, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-2 > li {
    list-style: none;
    width: 50%;
  }
  /* line 514, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-2 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 517, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
  }

  /* line 521, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 524, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 527, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 531, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 534, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 537, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .small-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }
}
@media only screen and (min-width: 30em) {
  /* line 544, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-2 > li {
    list-style: none;
    width: 50%;
  }
  /* line 547, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-2 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 550, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
  }

  /* line 554, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 557, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 560, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 564, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 567, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 570, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }

  /* line 574, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-5 > li {
    list-style: none;
    width: 20%;
  }
  /* line 577, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-5 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 580, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
  }

  /* line 584, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-6 > li {
    list-style: none;
    width: 16.66667%;
  }
  /* line 587, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-6 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 590, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .medium-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 597, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 600, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 603, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 607, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 610, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 613, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }

  /* line 617, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-5 > li {
    list-style: none;
    width: 20%;
  }
  /* line 620, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-5 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 623, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
  }

  /* line 627, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-6 > li {
    list-style: none;
    width: 16.66667%;
  }
  /* line 630, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-6 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 633, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .large-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
  }
}
/* line 640, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button:hover, button:focus {
  background-color: #8f1b1d;
  color: #FFFFFF;
}

/* line 648, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.secondary:hover, button.secondary:focus {
  background-color: #b9b9b9;
  color: #333333;
}

/* line 655, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.success, .button.success {
  background-color: white;
  border-color: #670b19;
  color: #FFFFFF;
}

/* line 662, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.success:hover, button.success:focus {
  background-color: #670b19;
}

/* line 668, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.button.success:hover, .button.success:focus {
  background-color: #670b19;
}

/* line 674, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.success:hover, button.success:focus {
  color: #FFFFFF;
}

/* line 680, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.button.success:hover, .button.success:focus {
  color: #FFFFFF;
}

/* line 685, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.alert, .button.alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
}

/* line 692, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.alert:hover, button.alert:focus {
  background-color: #cf2a0e;
}

/* line 698, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.button.alert:hover, .button.alert:focus {
  background-color: #cf2a0e;
}

/* line 704, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.alert:hover, button.alert:focus {
  color: #FFFFFF;
}

/* line 710, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.button.alert:hover, .button.alert:focus {
  color: #FFFFFF;
}

/* line 715, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.small, .button.small {
  padding: 0.875rem 1.75rem 0.9375rem 1.75rem;
  font-size: 0.8125rem;
}

/* line 720, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.radius, .button.radius {
  border-radius: 3px;
}

/* line 726, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
button.disabled, button[disabled] {
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}

/* line 737, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.button.disabled, .button[disabled] {
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}

@media only screen and (min-width: 30em) {
  /* line 748, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  button, .button {
    display: inline-block;
  }
}
/* line 753, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
form {
  margin: 0 0 1rem;
}
/* line 755, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
form .row .row {
  margin: 0 -0.5rem;
}
/* line 757, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
form .row .row .column, form .row .row .columns {
  padding: 0 0.5rem;
}

/* line 763, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
label {
  color: #4d4d4d;
  cursor: pointer;
  display: block;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.5;
  margin-bottom: 0;
}

/* line 773, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.prefix, .postfix {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem;
  overflow: visible;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 2;
}

/* line 790, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"], input[type="password"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="week"], input[type="email"], input[type="number"], input[type="search"], input[type="tel"], input[type="time"], input[type="url"], input[type="color"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  background-color: #FFFFFF;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.75);
  display: block;
  font-family: inherit;
  font-size: 0.875rem;
  height: 2.3125rem;
  margin: 0 0 1rem 0;
  padding: 0.5rem;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: border-color 0.15s linear, background 0.15s linear;
  -moz-transition: border-color 0.15s linear, background 0.15s linear;
  -ms-transition: border-color 0.15s linear, background 0.15s linear;
  -o-transition: border-color 0.15s linear, background 0.15s linear;
  transition: border-color 0.15s linear, background 0.15s linear;
}

/* line 818, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  background-color: #FFFFFF;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.75);
  display: block;
  font-family: inherit;
  font-size: 0.875rem;
  height: 2.3125rem;
  margin: 0 0 1rem 0;
  padding: 0.5rem;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: border-color 0.15s linear, background 0.15s linear;
  -moz-transition: border-color 0.15s linear, background 0.15s linear;
  -ms-transition: border-color 0.15s linear, background 0.15s linear;
  -o-transition: border-color 0.15s linear, background 0.15s linear;
  transition: border-color 0.15s linear, background 0.15s linear;
}

/* line 846, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, input[type="color"]:focus {
  background: #fafafa;
  border-color: #999999;
  outline: none;
}

/* line 853, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea:focus {
  background: #fafafa;
  border-color: #999999;
  outline: none;
}

/* line 859, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
::-webkit-input-placeholder, :-moz-placeholder, ::-moz-placeholder, :-ms-input-placeholder {
  color: #cccccc;
}

/* line 863, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: #FAFAFA;
  border-radius: 0;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMTJweCIgeT0iMHB4IiB3aWR0aD0iMjRweCIgaGVpZ2h0PSIzcHgiIHZpZXdCb3g9IjAgMCA2IDMiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDYgMyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBvbHlnb24gcG9pbnRzPSI1Ljk5MiwwIDIuOTkyLDMgLTAuMDA4LDAgIi8+PC9zdmc+);
  background-position: 100% center;
  background-repeat: no-repeat;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  color: rgba(0, 0, 0, 0.75);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: normal;
  padding: 0.5rem;
  border-radius: 0;
  height: 2.3125rem;
}
/* line 881, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select::-ms-expand {
  display: none;
}
/* line 884, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select:hover {
  background-color: #f3f3f3;
  border-color: #999999;
}
/* line 888, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select:disabled {
  background-color: #DDDDDD;
  cursor: default;
}
/* line 892, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select[multiple] {
  height: auto;
}

/* line 898, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="file"], input[type="checkbox"], input[type="radio"] {
  margin: 0 0 1rem 0;
}

/* line 903, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select {
  margin: 0 0 1rem 0;
}

/* line 908, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="checkbox"] + label, input[type="radio"] + label {
  display: inline-block;
  margin-left: 0.5rem;
  margin-right: 1rem;
  margin-bottom: 0;
  vertical-align: baseline;
}

/* line 917, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label {
  display: inline-block;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.6875rem;
  background-color: #008CBA;
  color: #FFFFFF;
}
/* line 930, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.radius {
  border-radius: 3px;
}
/* line 933, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.round {
  border-radius: 0px;
}
/* line 936, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.alert {
  background-color: #f04124;
  color: #FFFFFF;
}
/* line 940, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.warning {
  background-color: #9d9596;
  color: #FFFFFF;
}
/* line 944, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.success {
  background-color: #670e19;
  color: #FFFFFF;
}
/* line 948, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.secondary {
  background-color: #e7e7e7;
  color: #333333;
}
/* line 952, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.label.info {
  background-color: #a0d3e8;
  color: #333333;
}

/* line 958, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container {
  background: none;
  overflow: hidden;
  position: relative;
  width: 100%;
}
/* line 963, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-slides-container {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
/* line 973, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-slides-container img {
  display: block;
  max-width: 100%;
}
/* line 977, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-slides-container > * {
  position: absolute;
  top: 0;
  width: 100%;
  margin-left: 100%;
}
/* line 982, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-slides-container > *:first-child {
  margin-left: 0;
}
/* line 987, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-timer {
  position: absolute;
  top: 12px;
  right: 10px;
  height: 6px;
  width: 100px;
  z-index: 10;
}
/* line 994, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-timer .orbit-progress {
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  display: block;
  width: 0;
  position: relative;
  right: 20px;
  top: 5px;
}
/* line 1003, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-container .orbit-timer > span {
  border: solid 4px #FFFFFF;
  border-bottom: none;
  border-top: none;
  display: none;
  height: 14px;
  position: absolute;
  top: 0;
  width: 11px;
  right: 0;
}

@media only screen and (max-width: 29.9375em) {
  /* line 1018, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .orbit-timer, .orbit-next, .orbit-prev, .orbit-bullets {
    display: none;
  }
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 1028, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.panel {
  background: #ffffff;
}
/* line 1036, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.panel > :first-child {
  margin-top: 0;
}
/* line 1039, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.panel > :last-child {
  margin-bottom: 0;
}

/* line 1045, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal-bg {
  background: #000000;
  background: rgba(0, 0, 0, 0.45);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 99;
  left: 0;
}

/* line 1058, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal {
  border-radius: 3px;
  display: none;
  position: absolute;
  top: 0;
  visibility: hidden;
  width: 100%;
  z-index: 1005;
  left: 0;
  background-color: #FFFFFF;
  padding: 1.875rem;
  border: solid 1px #666666;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
}
/* line 1071, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal .column, .reveal-modal .columns {
  min-width: 0;
}
/* line 1075, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal > :first-child {
  margin-top: 0;
}
/* line 1078, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal > :last-child {
  margin-bottom: 0;
}
/* line 1082, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.reveal-modal .close-reveal-modal {
  color: #AAAAAA;
  cursor: pointer;
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  position: absolute;
  top: 0.625rem;
  right: 1.375rem;
}

@media only screen and (max-width: 29.9375em) {
  /* line 1095, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .reveal-modal {
    min-height: 100vh;
  }
}
@media only screen and (min-width: 30em) {
  /* line 1101, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .reveal-modal {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 80%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 1111, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .reveal-modal {
    top: 6.25rem;
  }
}
/* line 1116, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch {
  border: none;
  margin-bottom: 1.5rem;
  outline: 0;
  padding: 0;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/* line 1126, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label {
  background: #DDDDDD;
  color: transparent;
  cursor: pointer;
  display: block;
  margin-bottom: 1rem;
  position: relative;
  text-indent: 100%;
  width: 4rem;
  height: 2rem;
  transition: left 0.15s ease-out;
}
/* line 1138, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input {
  left: 10px;
  opacity: 0;
  padding: 0;
  position: absolute;
  top: 9px;
}
/* line 1144, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input + label {
  margin-left: 0;
  margin-right: 0;
}
/* line 1149, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label:after {
  background: #FFFFFF;
  content: "";
  display: block;
  height: 1.5rem;
  left: .25rem;
  position: absolute;
  top: .25rem;
  width: 1.5rem;
  -webkit-transition: left 0.15s ease-out;
  -moz-transition: left 0.15s ease-out;
  -o-transition: translate3d(0, 0, 0);
  transition: left 0.15s ease-out;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
/* line 1168, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input:checked + label {
  background: #008CBA;
}
/* line 1170, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input:checked + label:after {
  left: 2.25rem;
}
/* line 1174, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label {
  height: 2rem;
  width: 4rem;
}
/* line 1177, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label:after {
  height: 1.5rem;
  width: 1.5rem;
}
/* line 1182, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input:checked + label:after {
  left: 2.25rem;
}
/* line 1185, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label {
  color: transparent;
  background: #DDDDDD;
}
/* line 1188, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch label:after {
  background: #FFFFFF;
}
/* line 1192, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch input:checked + label {
  background: #008CBA;
}
/* line 1196, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch.tiny label {
  height: 1.5rem;
  width: 3rem;
}
/* line 1199, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch.tiny label:after {
  height: 1rem;
  width: 1rem;
}
/* line 1204, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.switch.tiny input:checked + label:after {
  left: 1.75rem;
}

/* line 1210, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table {
  background: #FFFFFF;
  border: solid 1px #DDDDDD;
  margin-bottom: 1.25rem;
  table-layout: auto;
}
/* line 1215, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table thead {
  background: #F5F5F5;
}
/* line 1218, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table thead tr th, table thead tr td {
  font-size: 0.8125rem;
  font-weight: bold;
  padding: 0.5rem 0.625rem 0.625rem;
}
/* line 1226, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table tr th, table tr td {
  font-size: 0.8125rem;
  padding: 0.5625rem 0.625rem;
  text-align: left;
}
/* line 1231, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table tr.even, table tr.alt, table tr:nth-of-type(even) {
  background: #F9F9F9;
}
/* line 1235, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table thead tr th {
  display: table-cell;
  line-height: 1.125rem;
}
/* line 1240, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table tfoot tr th, table tfoot tr td {
  display: table-cell;
  line-height: 1.125rem;
}
/* line 1246, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table tbody tr th, table tbody tr td {
  display: table-cell;
  line-height: 1.125rem;
}
/* line 1251, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
table tr td {
  display: table-cell;
  line-height: 1.125rem;
}

/* line 1257, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tabs-content {
  margin-bottom: 1.5rem;
  width: 100%;
}
/* line 1260, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tabs-content:before {
  content: " ";
  display: table;
}
/* line 1264, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tabs-content:after {
  content: " ";
  display: table;
  clear: both;
}
/* line 1269, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tabs-content > .content {
  display: none;
  float: left;
  padding: 0.9375rem 0;
  width: 100%;
}
/* line 1274, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tabs-content > .content.active {
  display: block;
  float: none;
}

/* line 1281, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-mq-topbar {
  font-family: "/only screen and (min-width:30em)/";
  width: 30em;
}

/* line 1286, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip {
  background: #333333;
  color: #FFFFFF;
  display: none;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.3;
  max-width: 300px;
  padding: 0.75rem;
  position: absolute;
  width: 100%;
  z-index: 1006;
  left: 50%;
}
/* line 1299, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip > .nub {
  border-color: transparent transparent #333333 transparent;
  border: solid 5px;
  display: block;
  height: 0;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 0;
  left: 5px;
}
/* line 1309, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip > .nub.rtl {
  left: auto;
  right: 5px;
}
/* line 1314, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip.radius {
  border-radius: 3px;
}
/* line 1317, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip.round {
  border-radius: 1000px;
}
/* line 1319, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip.round > .nub {
  left: 2rem;
}
/* line 1323, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tooltip.opened {
  border-bottom: dotted 1px #003f54 !important;
  color: #008CBA !important;
}

/* line 1329, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.tap-to-close {
  color: #777777;
  display: block;
  font-size: 0.625rem;
  font-weight: normal;
}

@media only screen and (min-width: 30em) {
  /* line 1338, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .tooltip > .nub {
    border-color: transparent transparent #333333 transparent;
    top: -10px;
  }
  /* line 1342, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .tooltip.tip-top > .nub {
    border-color: #333333 transparent transparent transparent;
    bottom: -10px;
    top: auto;
  }
  /* line 1347, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .tooltip.tip-left, .tooltip.tip-right {
    float: none !important;
  }
  /* line 1350, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .tooltip.tip-left > .nub {
    border-color: transparent transparent transparent #333333;
    left: auto;
    margin-top: -5px;
    right: -10px;
    top: 50%;
  }
  /* line 1357, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .tooltip.tip-right > .nub {
    border-color: transparent #333333 transparent transparent;
    left: -10px;
    margin-top: -5px;
    right: auto;
    top: 50%;
  }
}
/* line 1367, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.text-left {
  text-align: left !important;
}

/* line 1371, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.text-right {
  text-align: right !important;
}

/* line 1375, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.text-center {
  text-align: center !important;
}

/* line 1379, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.text-justify {
  text-align: justify !important;
}

/* line 1383, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, p, blockquote, th, td {
  margin: 0;
  padding: 0;
}

/* line 1388, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
p {
  font-family: inherit;
  font-weight: normal;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  text-rendering: none;
}

/* line 1396, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
h1, h2, h3, h4, h5, h6 {
  color: white;
  font-style: normal;
  font-weight: normal;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  margin-top: 0.2rem;
  text-rendering: none;
}

/* line 1406, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
h1 {
  font-size: 1.1875rem;
}

/* line 1410, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
h2 {
  font-size: 1.6875rem;
}

/* line 1414, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
h3 {
  font-size: 1.375rem;
}

/* line 1418, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
h4 {
  font-size: 1.125rem;
}

/* line 1422, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
hr {
  border: solid #DDDDDD;
  border-width: 1px 0 0;
  clear: both;
  height: 0;
  margin: 1.25rem 0 1.1875rem;
}

/* line 1430, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
em, i {
  font-style: italic;
  line-height: inherit;
}

/* line 1435, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
strong, b {
  font-weight: bold;
  line-height: inherit;
}

/* line 1440, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
small {
  font-size: 60%;
  line-height: inherit;
}

/* line 1445, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
ul, ol, dl {
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  list-style-position: outside;
  margin-bottom: 1.25rem;
}

/* line 1453, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
ul {
  margin-left: 1.1rem;
}
/* line 1455, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
ul.no-bullet {
  margin-left: 0;
}
/* line 1459, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
ul li ul, ul li ol {
  margin-left: 1.25rem;
  margin-bottom: 0;
}
/* line 1464, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
ul.no-bullet {
  list-style: none;
}

@media only screen and (min-width: 30em) {
  /* line 1470, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.4;
  }

  /* line 1473, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  h1 {
    font-size: 1.5rem;
  }

  /* line 1476, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  h2 {
    font-size: 2.3125rem;
  }

  /* line 1479, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  h3 {
    font-size: 1.6875rem;
  }

  /* line 1482, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  h4 {
    font-size: 1.4375rem;
  }
}
/* line 1496, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.move-right .exit-off-canvas, .offcanvas-overlap .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}

@media only screen {
  /* line 1514, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .show-for-small-only, .show-for-small-up, .show-for-small, .show-for-small-down, .hide-for-medium-only, .hide-for-medium-up, .hide-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 1517, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .hide-for-small-only, .hide-for-small-up, .hide-for-small, .hide-for-small-down, .show-for-medium-only, .show-for-medium-up, .show-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }
}
@media only screen and (min-width: 30em) {
  /* line 1523, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .show-for-medium-only, .show-for-medium-up, .show-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 1526, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .hide-for-medium-only, .hide-for-medium-up, .hide-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 1532, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .hide-for-medium-only, .show-for-medium-up, .hide-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 1535, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .show-for-medium-only, .hide-for-medium-up, .show-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }
}
/* line 1540, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
section.main-section {
  display: block;
}

/* line 1544, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
sup.menu-tag {
  color: #ffffff;
  padding: 3px 4px;
  font-size: 10px;
  background: #bf445e;
  background-size: 100% 100%;
  border-radius: 2px;
}

/* line 1553, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav {
  height: 100%;
  width: 0px;
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  background-color: #8f1b1d !important;
  overflow-x: hidden;
  overflow-y: scroll;
  display: block;
  color: white;
}
/* line 1566, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menu-text {
  padding: 0.4rem 1rem;
  font-size: 0.8rem;
  font-weight: bold;
}
/* line 1571, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .close-menu {
  float: right;
  width: 30px;
  height: 30px;
}
/* line 1575, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .close-menu:before, #menu-side-nav .close-menu:after {
  position: absolute;
  content: ' ';
  height: 20px;
  width: 1px;
  background-color: white;
  right: 20px;
  top: 5px;
}
/* line 1584, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .close-menu:before {
  transform: rotate(45deg);
}
/* line 1587, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .close-menu:after {
  transform: rotate(-45deg);
}
/* line 1591, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menu-accordion {
  margin-left: 0px;
  display: none;
}
/* line 1595, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .has-submenu {
  padding: 0.3rem 1rem;
}
/* line 1598, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav a {
  color: white !important;
  letter-spacing: 1px;
}
/* line 1601, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav a:hover {
  color: white !important;
}
/* line 1605, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menu-content {
  padding: 0px;
}
/* line 1607, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menu-content .border-custom {
  border-left: 1px dashed #b11f2d;
}
/* line 1611, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .all-menu {
  display: block;
  padding: 0.3rem 1rem;
  font-size: 0.8rem;
  font-weight: bold;
}
/* line 1617, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menus_static {
  list-style-type: none;
  font-size: 15px;
  border-left: 1px dashed #b11f2d;
  padding-left: 8px;
}
/* line 1622, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .menus_static li {
  border-bottom: 1px solid #b11f2d;
  padding: 4px 0px;
  width: 85%;
  font-size: 0.8rem;
}
/* line 1629, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#menu-side-nav .submenu {
  font-size: 0.8rem;
  margin-left: 5px;
}

/* line 1635, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#branch-banner-iframe {
  top: 145px !important;
  z-index: 1 !important;
}
/* line 1638, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#branch-banner-iframe .reviews {
  background-color: red !important;
}

/* line 1643, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body.branch-banner-is-active {
  margin-top: 0 !important;
}

/* line 1645, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.turbolinks-progress-bar {
  background-color: #ccc5c5;
  z-index: 99999;
}

/* line 1650, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
[class*="block-grid-"] li {
  padding: 0 0.15rem 0.35rem;
}

/* line 1654, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body {
  overflow: scroll;
}
/* line 1656, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body a {
  color: #670b19;
}
/* line 1659, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body h1, body h2, body h3, body h4, body h5, body h6 {
  color: #303030;
}
/* line 1663, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body #action_buttons .action_button_btn .add_to_buy_bow {
  background-color: #8f1b1d;
  font-weight: 700;
  font-size: 0.875rem;
  width: 100%;
  margin-bottom: 0;
  box-shadow: 0 -1px 15px 0 rgba(0, 0, 0, 0.2);
}
/* line 1676, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .panel_block .panel_content {
  background-color: #4c4b4b;
}
/* line 1679, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .panel_block .panel_heading {
  padding: 0em 0.2em;
}
/* line 1683, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .truncate {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 1689, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body a.b1g1, body .b1g1_tnc, body .design_offer_message, body .qpm_tnc, body .fd_message, body .fd_tnc {
  color: #f4846e !important;
  font-size: 14px;
  font-family: 'Lato Black';
}
/* line 1694, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .loyalty_cashback, body .loyalty_tnc {
  color: #d44f67 !important;
  font-size: 14px;
  font-family: 'Lato Black';
}
/* line 1699, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body #pushengage_confirm {
  left: 0px !important;
  width: auto !important;
}
/* line 1702, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body #pushengage_confirm #pushengage_close_btn {
  color: #333232;
}
/* line 1706, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .b1g1_text, body .bag-info {
  border-radius: 50%;
  background-color: #d44f67;
  padding: 0px 6px;
  font-size: 14px;
  font-style: italic;
  font-weight: 700;
  font-family: 'Times New Roman';
  cursor: pointer;
  color: #fff;
}
/* line 1717, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .b1g1_info {
  border-radius: 2px;
  background-color: #d44f67;
  padding: 0px 6px;
  margin: 0 5px;
  width: auto;
  font-size: 14px;
  font-style: italic;
  font-weight: 700;
  font-family: 'Times New Roman';
  cursor: pointer;
}
/* line 1728, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .b1g1_info a {
  color: #fff;
}
/* line 1732, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .b1g1_colored {
  background-color: #d44f67;
  color: #fff;
  float: left;
  font-size: 14px;
  padding: 5px;
}
/* line 1739, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .discounts-text {
  border: 1px solid #c73567 !important;
  color: #f1f1f1 !important;
}
/* line 1743, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .discount-percent {
  padding: 3px;
}
/* line 1746, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
body .sold-out-btn {
  width: 100% !important;
  font-weight: 600;
  display: block;
  text-transform: uppercase;
  padding: 0.6rem 2rem 0.6rem 2rem;
}

/* line 1755, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
:-webkit-autofill {
  transition: background-color 0s ease-in-out 5000s;
  -webkit-box-shadow: 0 0 0px 1000px #4c4b4b inset !important;
  -webkit-text-fill-color: #f1f1f1 !important;
}
/* line 1759, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
:-webkit-autofill:hover, :-webkit-autofill:focus, :-webkit-autofill:active {
  transition: background-color 0s ease-in-out 5000s;
  -webkit-box-shadow: 0 0 0px 1000px #4c4b4b inset !important;
  -webkit-text-fill-color: #f1f1f1 !important;
}

/* line 1767, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], input[type="time"] {
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  color: #f1f1f1;
  box-shadow: none;
  outline: none;
}

/* line 1777, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea, select {
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  color: #f1f1f1;
  box-shadow: none;
  outline: none;
}

/* line 1796, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="password"]:focus, input[type="password"]:hover {
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: #4c4b4b;
}
/* line 1803, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="email"]:focus, input[type="email"]:hover {
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: #4c4b4b;
}
/* line 1810, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="number"]:focus, input[type="number"]:hover {
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: #4c4b4b;
}
/* line 1817, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="tel"]:focus, input[type="tel"]:hover {
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: #4c4b4b;
}
/* line 1824, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="time"]:focus, input[type="time"]:hover {
  box-shadow: 0 1px 0 0 #5d8da5;
  border-bottom: 1px solid #5d8da5;
  background-color: #4c4b4b;
}

/* line 1833, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea:focus, textarea:hover {
  border-bottom: 1px solid #670b19;
}

/* line 1841, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
select:focus, select:hover {
  border-bottom: 1px solid #670b19;
}

/* line 1849, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"]::-webkit-input-placeholder, input[type="password"]::-webkit-input-placeholder, input[type="email"]::-webkit-input-placeholder, input[type="number"]::-webkit-input-placeholder, input[type="tel"]::-webkit-input-placeholder, input[type="time"]::-webkit-input-placeholder {
  color: grey;
}

/* line 1854, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea::-webkit-input-placeholder, select::-webkit-input-placeholder {
  color: grey;
}

/* line 1859, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"]::-moz-placeholder, input[type="password"]::-moz-placeholder, input[type="email"]::-moz-placeholder, input[type="number"]::-moz-placeholder, input[type="tel"]::-moz-placeholder, input[type="time"]::-moz-placeholder {
  color: grey;
}

/* line 1864, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea::-moz-placeholder, select::-moz-placeholder {
  color: grey;
}

/* line 1869, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"]:-ms-input-placeholder, input[type="password"]:-ms-input-placeholder, input[type="email"]:-ms-input-placeholder, input[type="number"]:-ms-input-placeholder, input[type="tel"]:-ms-input-placeholder, input[type="time"]:-ms-input-placeholder {
  color: grey;
}

/* line 1874, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea:-ms-input-placeholder, select:-ms-input-placeholder {
  color: grey;
}

/* line 1879, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
input[type="text"]:-moz-placeholder, input[type="password"]:-moz-placeholder, input[type="email"]:-moz-placeholder, input[type="number"]:-moz-placeholder, input[type="tel"]:-moz-placeholder, input[type="time"]:-moz-placeholder {
  color: grey;
}

/* line 1884, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
textarea:-moz-placeholder, select:-moz-placeholder {
  color: grey;
}

/* line 1889, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
img[data-original] {
  opacity: 1;
  transition: opacity 0.5s;
}

/* line 1896, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-arrow-down:before {
  content: "\f109";
}

/* line 1900, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-telephone:before {
  content: "\f109";
}

/* line 1904, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-arrow-up:before {
  content: "\f10c";
}

/* line 1908, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-filter:before {
  content: "\f14b";
}

/* line 1912, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-list:before {
  content: "\f169";
}

/* line 1916, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-magnifying-glass:before {
  content: "\f16c";
}

/* line 1920, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-thumbnails:before {
  content: "\f1fa";
}

/* line 1923, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-download:before {
  content: "\f143";
}

/* line 1926, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.fi-heart:before {
  content: "\f159";
}

/* line 1929, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.opera_top_margin {
  margin-top: 22px;
}

/* line 1933, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
meta.foundation-data-attribute-namespace {
  font-family: false;
}

/* line 1936, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#design_title {
  margin-top: 13px;
  display: inline;
  font-size: 1.1em;
}

/* line 1942, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#design_images_opera div {
  width: 100%;
  display: inline-block;
  display: none;
  text-align: center;
}
/* line 1949, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
#design_images_opera span {
  position: absolute;
}

/* line 1954, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.opera_next {
  right: 5px;
  margin-top: 100px;
}

/* line 1959, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.opera_prev {
  left: 5px;
  margin-top: 100px;
}

/* line 1964, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.opera_arrows {
  position: absolute;
}

/* line 1968, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.cc-window, .cc-banner {
  padding: 0 !important;
}

/* line 1972, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.cc-revoke, .cc-window {
  line-height: 1.3em !important;
}

/* line 1976, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-bullets-container {
  text-align: center;
}
/* line 1978, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-bullets-container .orbit-bullets {
  float: none;
  margin: 0 auto 6px auto;
  overflow: hidden;
  position: relative;
  text-align: center;
  top: 5px;
}
/* line 1985, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-bullets-container .orbit-bullets li {
  background: #CCCCCC;
  cursor: pointer;
  display: inline-block;
  float: none;
  height: 0.4rem;
  margin-right: 6px;
  width: 0.4rem;
  border-radius: 1000px;
}
/* line 1995, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/application_black_critical_red.css.scss */
.orbit-bullets-container .orbit-bullets .active {
  background: #670b19;
}

/* line 1, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 6, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer {
  padding-top: 10px;
  padding-bottom: 1px;
  background-color: #670b19;
  z-index: 1;
  /* buttons */
  /* icons */
  /* colors */
}
/* line 13, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #share {
  width: 100%;
  text-align: center;
  padding-bottom: 10px;
}
/* line 21, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #share a {
  width: 50px;
  height: 50px;
  display: inline-block;
  margin: 8px;
  border-radius: 50%;
  font-size: 24px;
  color: #fff;
}
/* line 31, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #share a:hover {
  opacity: 1;
}
/* line 37, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #share i {
  position: relative;
  top: 40%;
  transform: translateY(-50%);
}
/* line 45, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .facebook {
  background: #3b5998;
}
/* line 49, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .instagram {
  background: #f09433;
  background: -moz-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: -webkit-linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f09433', endColorstr='#bc1888',GradientType=1 );
}
/* line 58, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .youtube {
  background: #cb2027;
}
/* line 62, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .sign_in_button {
  background: #b11f2d;
  margin-left: 34%;
  padding: 3px;
}
/* line 67, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .row div {
  text-align: center;
  margin: 0 auto;
}
/* line 72, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer img {
  display: inline-block !important;
}
/* line 76, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer span {
  color: white;
  font-size: 0.75rem;
}
/* line 81, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav {
  margin-top: 12px;
  margin-bottom: 12px;
  margin-left: 0px;
}
/* line 85, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav .phoneNumber {
  padding: 0px;
  font-size: 0.75rem;
}
/* line 88, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav .phoneNumber a {
  color: #ffffff;
}
/* line 94, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav li {
  float: left;
  text-align: center;
  background-color: #b11f2d;
  width: 100%;
  list-style: none;
  font-size: 0.75rem;
  color: #ffffff;
}
/* line 104, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav a {
  color: text_white !important;
  text-decoration: none;
  font-size: 0.75rem;
}
/* line 110, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer #footer_nav li:first-child {
  border-right: 0px;
  padding-bottom: 0px;
}
/* line 115, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .expand ul {
  display: block !important;
}
/* line 119, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .f_free,
#mobile_footer .f_world_wide,
#mobile_footer .f_contact,
#mobile_footer .f_money_back {
  display: inline-block;
  width: 36px;
  height: 35px;
  background: url(/assets/sprite-f7e7721dd2d9b1d316fd41711a5c2fd08099db15a0dd34b4ac115bc94c6e17b8.png) no-repeat;
}
/* line 130, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .f_free {
  background-position: 4% 58%;
  width: 45px;
}
/* line 135, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .f_world_wide {
  background-position: 50% 45%;
}
/* line 139, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .f_contact {
  background-position: -3px -359px;
}
/* line 143, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .f_money_back {
  background-position: 98% 54%;
}
/* line 147, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .visible-xs {
  display: block !important;
}
/* line 152, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .nopadding {
  padding-left: 0px;
  padding-right: 0px;
}
/* line 157, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .col-xs-3 {
  width: 25%;
  float: left;
}
/* line 162, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .row {
  margin-left: 0px;
  margin-right: 0px;
  max-width: 100%;
}
/* line 168, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .col-xs-4 {
  width: 33.3333%;
  float: left;
}
/* line 173, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .copyright {
  text-align: center;
  padding-right: 0px;
  text-align: center;
  margin-bottom: 15px;
  padding-left: 0px;
}
/* line 181, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .copyright p {
  color: #ffffff;
  padding-bottom: 1px;
  line-height: 17px;
  font-size: 0.75rem;
}
/* line 187, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .clr {
  clear: both;
}
/* line 191, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile_footer .sign_in_button a {
  color: white;
  font-size: 0.825rem;
  text-transform: uppercase;
}

/* line 198, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
body .opera_footer_fix {
  position: relative;
  bottom: 0;
  width: 100%;
  z-index: 9999;
  margin-top: 12px;
}

/* line 206, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
body .opera_footer_fix a {
  margin-bottom: 0em;
  width: 100%;
  left: 0px;
  font-weight: bold;
  line-height: inherit;
}

/* line 214, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.offer-tnc-header {
  text-align: justify;
  padding: 0 5px;
}

/***Mobile subscribe window***/
/* line 220, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.modal-top {
  margin-top: 6px !important;
}

/* line 223, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.modal-tr-bg {
  background: rgba(0, 0, 0, 0.45) !important;
}

/* line 226, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window {
  overflow-y: scroll;
  position: fixed;
  top: 15% !important;
  width: 95%;
  margin: 0% 0% 0% 2.5%;
  border: none;
  box-shadow: none;
  border-radius: 0px;
  background: transparent;
  z-index: 9999999999 !important;
}
/* line 237, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window #modal-subscribe-box {
  height: 85%;
  overflow-y: scroll;
}
/* line 241, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer {
  padding: 7px;
}
/* line 244, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #subscribe-input {
  font-size: 18px;
  margin: 5px 0px;
  border-bottom: 1px solid #dd2d6a;
  height: 45px;
  background: white;
  border-radius: 0;
}
/* line 251, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #subscribe-input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: grey;
}
/* line 254, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #subscribe-input::-moz-placeholder {
  /* Firefox 19+ */
  color: grey;
}
/* line 257, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #subscribe-input:-ms-input-placeholder {
  /* IE 10+ */
  color: grey;
}
/* line 260, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #subscribe-input:-moz-placeholder {
  /* Firefox 18- */
  color: grey;
}
/* line 264, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #email-subscribe-button {
  margin: 5px 0px;
  box-shadow: 2px 2px 3px 0px grey;
  width: 100%;
  height: 40px;
  font-size: 18px;
}
/* line 271, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window .modal-footer #email-cancel-button {
  color: black;
  font-size: 14px;
  text-decoration: underline;
}
/* line 281, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window input {
  height: 55px;
  background: #424040;
  outline: medium none;
  border: medium none;
  font-size: 0.75rem;
  color: white;
}
/* line 290, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#mobile-subscribe-window label {
  font-size: 19px;
  text-align: center;
}

/* line 296, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner {
  display: none;
}
/* line 298, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner .wrapper {
  display: inline-flex;
  background: #670b19;
  position: fixed;
  bottom: 0;
  width: 100%;
  z-index: 99;
  padding: 2px;
  -webkit-transform: translateX(-152px);
  transform: translateX(-152px);
  will-change: transform;
  -webkit-transition: -webkit-transform .75s ease-out;
  transition: -webkit-transform .75s ease-out;
  transition: transform .75s ease-out;
  transition: transform .75s ease-out,-webkit-transform .75s ease-out;
}
/* line 313, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner .wrapper .sticky-coupon-image {
  max-height: 100%;
  max-width: 100%;
  display: inline-block;
}
/* line 318, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner .wrapper .close-sticky-coupon-banner {
  display: inline-block;
}
/* line 321, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner .wrapper .close-button {
  color: white;
  width: 12rem;
  font-size: 16px;
  font-weight: bold;
}
/* line 326, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.sticky-coupon-banner .wrapper .close-button .close-label {
  top: 35%;
  position: absolute;
  right: 7%;
}

/* line 334, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#newsletter-sub-image {
  width: 100%;
  margin: 0 auto;
}

@media only screen and (min-width: 30em) {
  /* line 340, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #mobile-subscribe-window {
    margin: 20px auto;
    width: 45%;
  }

  /* line 344, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #newsletter-sub-image {
    height: 180px !important;
  }
}
@media only screen and (min-width: 30em) and (min-height: 20em) {
  /* line 348, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #newsletter-sub-image {
    height: 240px !important;
  }
}
@media only screen and (min-width: 60em) {
  /* line 355, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #mobile-subscribe-window {
    margin: 20px auto;
    width: 45%;
  }

  /* line 359, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #newsletter-sub-image {
    height: 300px !important;
  }
}
/* line 363, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.subscribe_text_message {
  text-align: center;
  margin-top: -3px;
  color: #444444;
}

/* line 369, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.close_subscribe {
  width: 24px;
  font-size: 28px;
  text-align: center;
  color: #BA4800 !important;
  z-index: 100000;
}

/* line 377, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#email_subscribe_button {
  width: 78px !important;
  float: right;
  margin-top: -55px !important;
  border: medium none;
  height: 55px !important;
  border-radius: 0px 2px 2px 0px !important;
  color: white !important;
  position: absolute;
  right: 4px;
  background: #de6418 none repeat scroll 0% 0% !important;
  font-size: 18px !important;
}

/* line 391, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#subscribe_input {
  font-size: 16px !important;
  margin-bottom: 0px;
}
/* line 394, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#subscribe_input:focus {
  box-shadow: none;
  border-bottom: none;
}

/* line 400, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.transparent_bg {
  background: rgba(0, 0, 0, 0.45) !important;
}

/* line 404, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.subscribe_success {
  margin-top: 12px !important;
}

/* line 407, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.feedbackDiv {
  background-color: white;
}

@media only screen and (min-width: 520px) {
  /* line 412, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
  #notice_banner {
    margin: 0 auto;
    width: 360px;
  }
}
/* line 417, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner {
  display: none;
  background-color: #d84646;
  padding: 6px;
  margin-bottom: 10px;
  color: black;
  font-size: medium;
  text-align: center;
}
/* line 426, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .rounded_block {
  border-radius: 5px;
  background-color: #fdc5c6;
  padding: 1px 5px 6px;
}
/* line 431, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_header {
  padding-bottom: 1px;
}
/* line 433, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_header .header_text {
  font-size: 1rem;
}
/* line 436, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_header .header_sub_text {
  font-size: 0.8rem;
}
/* line 439, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_header #notice_close_btn {
  color: #9b0000;
  float: right;
}
/* line 444, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body {
  text-align: center;
}
/* line 447, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_cutout {
  border-style: dashed;
  display: inline-block;
  border-radius: 5px;
  border-width: 1.6px;
  padding: 3px 15px;
}
/* line 454, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_cutout .coupon_sub_text {
  font-size: 0.675rem;
  font-weight: 600;
}
/* line 458, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_cutout .coupon_code {
  color: #9b0000;
  font-size: 1.4rem;
  font-weight: 600;
  line-height: 1;
}
/* line 466, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_message {
  display: inline-block;
  padding: 2px;
  text-align: left;
}
/* line 471, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_message .offer_sub_msg {
  font-size: 10px;
}
/* line 474, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#notice_banner .notice_body .coupon_message .offer_msg {
  font-size: 0.75rem;
  font-weight: 600;
}

/* line 484, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info {
  /*border:1px dotted grey;*/
  padding: 8px;
  background-color: #ffffff;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  box-shadow: 0 0 0.5em #2d2d2d;
  font-size: 0.75rem;
}
/* line 494, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post .seo-list-anchor {
  font-size: 14px;
  padding: 5px;
  line-height: 17px;
}
/* line 499, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post .seo-list-table {
  width: 70%;
  border: 1px solid white;
}
/* line 503, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post .seo-list-line-height {
  line-height: 30px;
}
/* line 506, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post .seo-list-font {
  font-size: 14px;
}
/* line 509, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h1, .footer-info #seo_post h2, .footer-info #seo_post h3, .footer-info #seo_post h4, .footer-info #seo_post h5, .footer-info #seo_post h6 {
  color: #303030;
  font-weight: bold;
}
/* line 513, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h1 {
  font-size: 1.1875rem;
}
/* line 516, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h2 {
  font-size: 1.125rem;
}
/* line 519, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h3 {
  font-size: 1rem;
}
/* line 522, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h4 {
  font-size: 0.9375rem;
}
/* line 525, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h5 {
  font-size: 0.8125rem;
}
/* line 528, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post h6 {
  font-size: 0.6875rem;
}
/* line 531, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post p {
  font-size: 0.875rem;
  color: #303030;
  text-align: justify;
  margin-bottom: 0.9rem;
}
/* line 536, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post p a {
  font-weight: bold;
}
/* line 540, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post ul, .footer-info #seo_post ol {
  font-size: 0.875rem;
  color: #303030;
  text-align: justify;
  margin-bottom: 0.6rem;
  margin-left: 1.1rem;
}
/* line 547, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #seo_post.read-more {
  height: 7.8em;
  overflow: hidden;
}
/* line 552, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search {
  width: 100%;
  height: 170px;
  color: #303030;
  overflow-y: scroll;
  overflow-x: hidden;
  font-size: 12px !important;
  font-weight: bold;
  position: relative;
  scroll-behavior: smooth;
}
/* line 562, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search table {
  width: 100% !important;
  height: auto !important;
  table-layout: fixed;
}
/* line 568, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search tr:nth-child(even) {
  background-color: #decace;
}
/* line 571, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search tr td {
  font-size: 10px !important;
  font-weight: bold;
  padding: 5px 5px;
  text-align: center;
}
/* line 578, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search h2 {
  font-size: 18px !important;
  font-weight: bold;
}
/* line 583, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info ::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 3px;
}
/* line 587, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}
/* line 593, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
.footer-info #popular_search_title h3 {
  font-size: 16px !important;
  font-weight: bold;
}

/* line 599, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details {
  position: relative;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
  border-bottom: 0.1px solid #dddddd;
}
/* line 604, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details > :last-child {
  margin-bottom: 1rem;
}
/* line 607, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details::before {
  width: 100%;
  height: 100%;
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  opacity: .15;
  pointer-events: none;
  transition: opacity .2s;
  z-index: -1;
}
/* line 620, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details[open] {
  background-color: #FFF;
}
/* line 622, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details[open]::before {
  opacity: .6;
}
/* line 626, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
details *:focus {
  outline: 0;
}

/* line 630, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
summary {
  padding: 1rem 2em 1rem 0;
  display: block;
  position: relative;
  font-size: 1.33em;
  font-weight: bold;
  cursor: pointer;
}
/* line 637, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
summary::before, summary::after {
  width: .75em;
  height: 2px;
  position: absolute;
  top: 50%;
  right: 0;
  content: '';
  background-color: currentColor;
  text-align: right;
  transform: translateY(-50%);
  transition: transform .2s ease-in-out;
}
/* line 650, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
summary::after {
  transform: translateY(-50%) rotate(90deg);
}
/* line 652, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
[open] summary::after {
  transform: translateY(-50%) rotate(180deg);
}
/* line 656, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
summary::-webkit-details-marker {
  display: none;
}
/* line 659, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
summary *:focus {
  outline: 0;
}

/* line 663, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#amzn_wdgt_t_8001_0 {
  width: 100% !important;
  height: 100% !important;
}

/* line 667, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#wdgt_ft {
  display: none !important;
}

/* line 670, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/footer_red.scss */
#wdgt_brdr {
  height: 100% !important;
}
