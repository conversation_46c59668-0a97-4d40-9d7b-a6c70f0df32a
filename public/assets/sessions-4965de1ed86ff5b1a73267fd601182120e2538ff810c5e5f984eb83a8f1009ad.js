(function(){var e,o,n,t,r;$(function(){return $("#send_otp_form").on("submit",function(e){return e.preventDefault(),$(".otp-sent-phone").html($("#phone").val()),$.ajax(o($("#phone").val(),$(this).attr("action"),!1))}),$(".closebtn").on("click",function(){return $("#error-message").hide(),$("#otp-error-message").hide()}),$("#verify_otp_form").on("submit",function(e){return ga("set","dimension12","logged in with otp"),$(".overlay").show(),$(".progress_img1").show(),e.preventDefault(),$("#user_phone").val($("#phone").val()),$("#one_time_password").val(n()),$.ajax(r($("#phone").val(),n(),$(this).attr("action")))}),$("#new_account").on("submit",function(){return $(".overlay").show(),$(".progress_img1").show(),ga("set","dimension12","logged in with email")}),$("#resend_otp").on("click",function(){return $.ajax(o($("#phone").val(),"/accounts/send_otp",!0)),t(),$(".resend-tag").hide(),$(".timer").show()}),$("#email-login-link").on("click",function(){return $(".mobile-number-login").hide(),$(".email-login").show(),ga("set","dimension12","email login form view")}),$("#number-login-link").on("click",function(){return $(".email-login").hide(),$(".mobile-number-login").show(),ga("set","dimension12","otp login form view")})}),r=function(o,n,t){return{type:"POST",data:{user_phone:o,one_time_password:n},url:t,dataType:"JSON",success:function(o){return o.error?($(".overlay").hide(),$(".progress_img1").hide(),$("#otp-error-message").show(),e()):window.location.href=o.location}}},o=function(e,o,n){return{type:"POST",data:{phone:e},url:o,dataType:"JSON",success:function(o){return o.error===!0?$("#error-message").show():o.sms_sent===!0&&n===!1?($(".new-login-form").hide(),$(".verify-otp-form").show(),$(".otp-sent-phone").text(e),t()):void 0}}},n=function(){var e;return e=$("#otpBox1").val()+$("#otpBox2").val()+$("#otpBox3").val()+$("#otpBox4").val()},t=function(){var e,o;return o=document.getElementById("countdown-timer").textContent,e=setInterval(function(){o--,10>o&&(o="0"+o),document.getElementById("countdown-timer").textContent=o,$(".resend-tag").hide(),$(".timer").show(),0>=o&&(clearInterval(e),$(".resend-tag").show(),$(".timer").hide(),document.getElementById("countdown-timer").textContent=15)},1e3)},e=function(){return $("#otpBox1").val(""),$("#otpBox2").val(""),$("#otpBox3").val(""),$("#otpBox4").val(""),$("#otpBox1").focus()}}).call(this);