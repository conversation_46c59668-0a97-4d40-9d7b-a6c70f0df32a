/* line 5, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.accordion {
  margin-left: 0px;
}
/* line 7, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.accordion .content {
  color: black;
}

/* line 12, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.ui-pnotify-title {
  color: black;
}

/* line 15, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.ui-pnotify-text {
  font-size: 0.7em;
}

/* line 18, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
button.round, .button.round {
  border-radius: 10px;
}

/* line 21, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.next-stage {
  background: #8f1b1d !important;
  text-transform: uppercase;
  font-size: 14px;
}

/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.img-check, .back-img-check {
  cursor: pointer;
}

/* line 29, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.label {
  display: inline-block;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-align: left;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.875rem;
  background-color: #ffffff;
  color: #303030;
}

/* line 45, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"]:focus, input[type="text"]:hover, input[type="password"]:focus, input[type="password"]:hover, input[type="email"]:focus, input[type="email"]:hover, input[type="number"]:focus, input[type="number"]:hover, input[type="tel"]:focus, input[type="tel"]:hover, input[type="time"]:focus, input[type="time"]:hover, textarea:focus, textarea:hover, select:focus, select:hover {
  box-shadow: 0 1px 0 0 #670e19;
  border-bottom: 1px solid #670e19;
  background-color: white;
}

/* line 50, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"], input[type="password"], input[type="email"], input[type="number"], input[type="tel"], input[type="time"], textarea, select {
  margin: 0 0 0.5em 0;
  border: 0;
  border-bottom: 1px solid #9e9e9e;
  background-color: white;
  box-shadow: none;
  outline: none;
}
/* line 57, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"]::-webkit-input-placeholder, input[type="password"]::-webkit-input-placeholder, input[type="email"]::-webkit-input-placeholder, input[type="number"]::-webkit-input-placeholder, input[type="tel"]::-webkit-input-placeholder, input[type="time"]::-webkit-input-placeholder, textarea::-webkit-input-placeholder, select::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: grey;
}
/* line 60, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"]::-moz-placeholder, input[type="password"]::-moz-placeholder, input[type="email"]::-moz-placeholder, input[type="number"]::-moz-placeholder, input[type="tel"]::-moz-placeholder, input[type="time"]::-moz-placeholder, textarea::-moz-placeholder, select::-moz-placeholder {
  /* Firefox 19+ */
  color: grey;
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"]:-ms-input-placeholder, input[type="password"]:-ms-input-placeholder, input[type="email"]:-ms-input-placeholder, input[type="number"]:-ms-input-placeholder, input[type="tel"]:-ms-input-placeholder, input[type="time"]:-ms-input-placeholder, textarea:-ms-input-placeholder, select:-ms-input-placeholder {
  /* IE 10+ */
  color: grey;
}
/* line 66, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
input[type="text"]:-moz-placeholder, input[type="password"]:-moz-placeholder, input[type="email"]:-moz-placeholder, input[type="number"]:-moz-placeholder, input[type="tel"]:-moz-placeholder, input[type="time"]:-moz-placeholder, textarea:-moz-placeholder, select:-moz-placeholder {
  /* Firefox 18- */
  color: grey;
}

/* line 70, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.off-canvas-wrap #container {
  width: 96vw;
  overflow-y: hidden;
  padding: 1em 0em;
  margin: 2%;
  margin-top: 2em;
  overflow-x: hidden;
}

/* line 79, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.conversion {
  color: #c13e7f;
}

/* line 83, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.mapping_error {
  color: red;
}

/* line 87, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.measurement_type {
  font-size: 0.8em;
}

/* line 90, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.disable_accordion {
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;
  background-color: grey;
}

/* line 98, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.measurement_list {
  list-style: none;
}

/* line 101, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.measurement_li {
  color: black;
  background-color: #d8d8d8;
  padding: 6px;
  margin-bottom: 4px;
  border-radius: 0.1em;
  text-align: center;
}

/* line 109, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.other_button {
  webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 0.6em !important;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  margin-left: -6.5em !important;
  transition: background-color 300ms ease-out;
}

/* line 133, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.login_hr:after {
  content: "OR";
  /* section sign */
  color: #999;
  display: inline;
  /* for vertical centering and background knockout */
  background-color: white;
  /* same as background color */
  padding: 0 0.5em;
  /* size of background color knockout */
}

/* line 140, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.login_hr {
  font-family: Arial, sans-serif;
  /* choose the font you like */
  text-align: center;
  /* horizontal centering */
  line-height: 1px;
  /* vertical centering */
  height: 1px;
  /* gap between the lines */
  font-size: 1em;
  /* choose font size you like */
  border-width: 1px 0;
  /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px;
  /* 20px space above/below, 10px left/right */
  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}

/* line 159, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.active_li {
  background-color: #670e19;
  color: white;
}

/* line 163, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.info {
  color: #670f19;
}

/* line 167, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
#loadingImage {
  position: fixed;
  left: 45%;
  top: 30%;
}

/* line 173, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.btn-primary {
  border: antiquewhite;
  border-style: groove;
}

/* line 177, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.accordion .accordion-navigation > a:hover, .accordion dd > a:hover {
  background: black;
  color: white;
}

/* line 181, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.accordion .accordion-navigation > a, .accordion dd > a {
  background: grey;
  color: white;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;
}

/* line 191, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.accordion .accordion-navigation.active > a, .accordion dd.active > a {
  background-color: grey;
}

/* line 194, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.product_type_btn {
  height: 12em;
  color: black !important;
  background-color: white;
}
/* line 198, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.product_type_btn:hover {
  background-color: #8c8c8c;
}

/* line 203, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.product_type_btn_bottom {
  height: 12em;
  color: black !important;
  background-color: white;
}
/* line 207, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.product_type_btn_bottom:hover {
  background-color: #8c8c8c;
}

/* line 211, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.selected_type {
  background-color: #8c8c8c;
}

/* line 215, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_1 {
  background-position: -30px -43px !important;
}

/* line 218, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_2 {
  background-position: -174px -43px !important;
}

/* line 221, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_3 {
  background-position: -311px -43px !important;
}

/* line 224, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_4 {
  background-position: -453px -43px !important;
}

/* line 227, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_5 {
  background-position: -592px -43px !important;
}

/* line 230, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_6 {
  background-position: -730px -43px !important;
}

/* line 233, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_7 {
  background-position: -32px -153px !important;
}

/* line 236, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_8 {
  background-position: -172px -153px !important;
}

/* line 239, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_9 {
  background-position: -313px -153px !important;
}

/* line 242, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_10 {
  background-position: -452px -153px !important;
}

/* line 245, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_11 {
  background-position: -592px -153px !important;
}

/* line 248, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_12 {
  background-position: -732px -153px !important;
}

/* line 251, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_13 {
  background-position: -33px -257px !important;
}

/* line 254, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_14 {
  background-position: -172px -258px !important;
}

/* line 257, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_15 {
  background-position: -313px -259px !important;
}

/* line 260, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_16 {
  background-position: -453px -258px !important;
}

/* line 263, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_17 {
  background-position: -591px -259px !important;
}

/* line 266, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_18 {
  background-position: -733px -257px !important;
}

/* line 270, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_19 {
  background-position: -250px -367px !important;
}

/* line 273, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_20 {
  background-position: -392px -368px !important;
}

/* line 276, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_types_21 {
  background-position: -533px -367px !important;
}

/* line 279, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.blouse_image {
  width: 133px;
  height: 94px;
  background: url(/assets/stitching/blouse_front-59b042a933c5345fbecf6ac89ee97b9561d441fb1806088b3cdace1e7e962ec5.jpg) no-repeat;
}

/* line 285, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.back_blouse_image {
  width: 133px;
  height: 94px;
  background: url(/assets/stitching/blouse_back-55432edc62039a725bf8b10af139a56d04d9c01af1b3b3f717877ab93db3b911.jpg) no-repeat;
}

@media only screen and (max-width: 40em) {
  /* line 293, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
  .orbit-container .orbit-next, .orbit-container .orbit-prev {
    display: block !important;
  }
}
/* line 299, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.unfilled_measurements {
  color: red;
}

/* line 302, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.table_head {
  background-color: grey;
  color: white;
  padding: 15px;
}

/* line 307, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.selected_style {
  border-color: #670f19;
  border-style: double;
  border-width: 5px;
}

/* line 313, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.touch .orbit-container .orbit-prev, .touch .orbit-container .orbit-next {
  background-color: rgba(97, 0, 0, 0.22) !important;
}

@media only screen and (orientation: landscape) {
  /* line 318, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
  .reveal-modal {
    max-height: 70%;
    overflow-y: scroll;
  }
}
/* line 325, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
.view-measurements .view-btn {
  margin-top: 10px;
  background-color: #8f1b1d !important;
}

/* line 332, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience h4 {
  padding: 1em;
  margin: 0;
  font-size: 1em;
  background: grey;
  color: white;
}
/* line 340, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options {
  list-style-type: none;
  margin: 0;
  margin-top: 1em;
  border: 1px solid #d8d8d8;
}
/* line 347, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options li {
  border-bottom: 1px solid #d8d8d8;
}
/* line 350, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options li:last-child {
  border-bottom: none;
}
/* line 354, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options li label {
  display: block;
  padding: 1em 1.4em;
  margin: 0;
}
/* line 360, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options li input[type=radio] {
  display: none;
}
/* line 363, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience ul.measurement-experience-options li input[type=radio]:checked + label {
  background: #670e19;
  color: white;
}
/* line 371, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience dl.measurement-experience-options-explanation {
  margin-top: 1.5em;
}
/* line 374, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience dl.measurement-experience-options-explanation dt {
  font-size: 1.2em;
}
/* line 378, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience dl.measurement-experience-options-explanation dd {
  margin-bottom: 1.2em;
}
/* line 381, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience dl.measurement-experience-options-explanation dd ul {
  margin-bottom: 0.6em;
}
/* line 387, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/stitching_measurement_red.scss */
form.measurement-experience input[type=submit] {
  display: block;
  margin: 1em auto 0;
}
