/* line 1, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new {
  font-size: 16px;
  max-width: 500px;
  margin: auto;
  margin-top: 3.5em;
}
/* line 8, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group {
  position: relative;
}
/* line 11, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group label {
  display: none;
}
/* line 15, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group label.gift-card-thumb {
  display: initial;
}
/* line 19, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group small {
  position: relative;
  top: -0.75em;
  font-size: 0.7em;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group .form-button {
  padding: 0.8em;
  display: block;
  width: 100%;
  border: 1px solid #670b19;
  background-color: #b11f2b;
  color: white;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 42, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list {
  list-style-type: none;
  text-align: center;
  margin: auto;
  position: relative;
}
/* line 49, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list .gift-card-selection-list-item {
  display: inline;
  margin: 0 0.5em;
}
/* line 54, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list input {
  display: none;
}
/* line 57, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list input:checked ~ label:nth-child(3) {
  border: 2px solid #b11f2b;
}
/* line 61, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list input ~ label:nth-child(3) {
  border: 2px solid transparent;
  padding: 2px;
  display: inline-block;
}
/* line 67, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list input:checked ~ .gift-card-selection-preview {
  display: initial;
}
/* line 72, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new fieldset .gift-card-selection-list .gift-card-selection-preview {
  float: left;
  display: none;
  position: relative;
  margin-top: 1em;
  left: 0;
}
/* line 85, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-new .form-group:last-child .form-text {
  top: unset;
}

/* line 91, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-show {
  font-size: 16px;
  margin-bottom: 2em;
}
/* line 96, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/gift_card_orders.scss */
.gift-card-order-show .acknowledgement {
  font-size: 1.2em;
  line-height: 1;
  text-align: center;
  background-color: #eeeeee;
  max-width: 700px;
  margin: auto;
  padding: 1.5em;
  margin-top: 3em;
}
