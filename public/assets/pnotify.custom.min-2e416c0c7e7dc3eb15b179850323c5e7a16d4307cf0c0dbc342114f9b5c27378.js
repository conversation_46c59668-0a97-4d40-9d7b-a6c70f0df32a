!function(i,t){"function"==typeof define&&define.amd?define("pnotify",["jquery"],function(o){return t(o,i)}):"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("jquery"),global||i):i.PNotify=t(i.jQuery,i)}("undefined"!=typeof window?window:this,function(i,t){var o=function(t){var s,e,n={dir1:"down",dir2:"left",push:"bottom",spacing1:36,spacing2:36,context:i("body"),modal:!1},c=i(t),a=function(){e=i("body"),p.prototype.options.stack.context=e,c=i(t),c.bind("resize",function(){s&&clearTimeout(s),s=setTimeout(function(){p.positionAll(!0)},10)})},l=function(t){var o=i("<div />",{"class":"ui-pnotify-modal-overlay"});return o.prependTo(t.context),t.overlay_close&&o.click(function(){p.removeStack(t)}),o},p=function(i){this.state="initializing",this.timer=null,this.animTimer=null,this.styles=null,this.elem=null,this.container=null,this.title_container=null,this.text_container=null,this.animating=!1,this.timerHide=!1,this.parseOptions(i),this.init()};return i.extend(p.prototype,{version:"3.2.0",options:{title:!1,title_escape:!1,text:!1,text_escape:!1,styling:"brighttheme",addclass:"",cornerclass:"",auto_display:!0,width:"300px",min_height:"16px",type:"notice",icon:!0,animation:"fade",animate_speed:"normal",shadow:!0,hide:!0,delay:8e3,mouse_reset:!0,remove:!0,insert_brs:!0,destroy:!0,stack:n},modules:{},runModules:function(i,t){var o;for(var s in this.modules)o="object"==typeof t&&s in t?t[s]:t,"function"==typeof this.modules[s][i]&&(this.modules[s].notice=this,this.modules[s].options="object"==typeof this.options[s]?this.options[s]:{},this.modules[s][i](this,"object"==typeof this.options[s]?this.options[s]:{},o))},init:function(){var t=this;return this.modules={},i.extend(!0,this.modules,p.prototype.modules),this.styles="object"==typeof this.options.styling?this.options.styling:p.styling[this.options.styling],this.elem=i("<div />",{"class":"ui-pnotify "+this.options.addclass,css:{display:"none"},"aria-live":"assertive","aria-role":"alertdialog",mouseenter:function(){if(t.options.mouse_reset&&"out"===t.animating){if(!t.timerHide)return;t.cancelRemove()}t.options.hide&&t.options.mouse_reset&&t.cancelRemove()},mouseleave:function(){t.options.hide&&t.options.mouse_reset&&"out"!==t.animating&&t.queueRemove(),p.positionAll()}}),"fade"===this.options.animation&&this.elem.addClass("ui-pnotify-fade-"+this.options.animate_speed),this.container=i("<div />",{"class":this.styles.container+" ui-pnotify-container "+("error"===this.options.type?this.styles.error:"info"===this.options.type?this.styles.info:"success"===this.options.type?this.styles.success:this.styles.notice),role:"alert"}).appendTo(this.elem),""!==this.options.cornerclass&&this.container.removeClass("ui-corner-all").addClass(this.options.cornerclass),this.options.shadow&&this.container.addClass("ui-pnotify-shadow"),!1!==this.options.icon&&i("<div />",{"class":"ui-pnotify-icon"}).append(i("<span />",{"class":!0===this.options.icon?"error"===this.options.type?this.styles.error_icon:"info"===this.options.type?this.styles.info_icon:"success"===this.options.type?this.styles.success_icon:this.styles.notice_icon:this.options.icon})).prependTo(this.container),this.title_container=i("<h4 />",{"class":"ui-pnotify-title"}).appendTo(this.container),!1===this.options.title?this.title_container.hide():this.options.title_escape?this.title_container.text(this.options.title):this.title_container.html(this.options.title),this.text_container=i("<div />",{"class":"ui-pnotify-text","aria-role":"alert"}).appendTo(this.container),!1===this.options.text?this.text_container.hide():this.options.text_escape?this.text_container.text(this.options.text):this.text_container.html(this.options.insert_brs?String(this.options.text).replace(/\n/g,"<br />"):this.options.text),"string"==typeof this.options.width&&this.elem.css("width",this.options.width),"string"==typeof this.options.min_height&&this.container.css("min-height",this.options.min_height),p.notices="top"===this.options.stack.push?i.merge([this],p.notices):i.merge(p.notices,[this]),"top"===this.options.stack.push&&this.queuePosition(!1,1),this.options.stack.animation=!1,this.runModules("init"),this.state="closed",this.options.auto_display&&this.open(),this},update:function(t){var o=this.options;return this.parseOptions(o,t),this.elem.removeClass("ui-pnotify-fade-slow ui-pnotify-fade-normal ui-pnotify-fade-fast"),"fade"===this.options.animation&&this.elem.addClass("ui-pnotify-fade-"+this.options.animate_speed),this.options.cornerclass!==o.cornerclass&&this.container.removeClass("ui-corner-all "+o.cornerclass).addClass(this.options.cornerclass),this.options.shadow!==o.shadow&&(this.options.shadow?this.container.addClass("ui-pnotify-shadow"):this.container.removeClass("ui-pnotify-shadow")),!1===this.options.addclass?this.elem.removeClass(o.addclass):this.options.addclass!==o.addclass&&this.elem.removeClass(o.addclass).addClass(this.options.addclass),!1===this.options.title?this.title_container.slideUp("fast"):this.options.title!==o.title&&(this.options.title_escape?this.title_container.text(this.options.title):this.title_container.html(this.options.title),!1===o.title&&this.title_container.slideDown(200)),!1===this.options.text?this.text_container.slideUp("fast"):this.options.text!==o.text&&(this.options.text_escape?this.text_container.text(this.options.text):this.text_container.html(this.options.insert_brs?String(this.options.text).replace(/\n/g,"<br />"):this.options.text),!1===o.text&&this.text_container.slideDown(200)),this.options.type!==o.type&&this.container.removeClass(this.styles.error+" "+this.styles.notice+" "+this.styles.success+" "+this.styles.info).addClass("error"===this.options.type?this.styles.error:"info"===this.options.type?this.styles.info:"success"===this.options.type?this.styles.success:this.styles.notice),(this.options.icon!==o.icon||!0===this.options.icon&&this.options.type!==o.type)&&(this.container.find("div.ui-pnotify-icon").remove(),!1!==this.options.icon&&i("<div />",{"class":"ui-pnotify-icon"}).append(i("<span />",{"class":!0===this.options.icon?"error"===this.options.type?this.styles.error_icon:"info"===this.options.type?this.styles.info_icon:"success"===this.options.type?this.styles.success_icon:this.styles.notice_icon:this.options.icon})).prependTo(this.container)),this.options.width!==o.width&&this.elem.animate({width:this.options.width}),this.options.min_height!==o.min_height&&this.container.animate({minHeight:this.options.min_height}),this.options.hide?o.hide||this.queueRemove():this.cancelRemove(),this.queuePosition(!0),this.runModules("update",o),this},open:function(){this.state="opening",this.runModules("beforeOpen");var i=this;return this.elem.parent().length||this.elem.appendTo(this.options.stack.context?this.options.stack.context:e),"top"!==this.options.stack.push&&this.position(!0),this.animateIn(function(){i.queuePosition(!0),i.options.hide&&i.queueRemove(),i.state="open",i.runModules("afterOpen")}),this},remove:function(o){this.state="closing",this.timerHide=!!o,this.runModules("beforeClose");var s=this;return this.timer&&(t.clearTimeout(this.timer),this.timer=null),this.animateOut(function(){if(s.state="closed",s.runModules("afterClose"),s.queuePosition(!0),s.options.remove&&s.elem.detach(),s.runModules("beforeDestroy"),s.options.destroy&&null!==p.notices){var t=i.inArray(s,p.notices);-1!==t&&p.notices.splice(t,1)}s.runModules("afterDestroy")}),this},get:function(){return this.elem},parseOptions:function(t,o){this.options=i.extend(!0,{},p.prototype.options),this.options.stack=p.prototype.options.stack;for(var s,e=[t,o],n=0;n<e.length&&void 0!==(s=e[n]);n++)if("object"!=typeof s)this.options.text=s;else for(var c in s)this.modules[c]?i.extend(!0,this.options[c],s[c]):this.options[c]=s[c]},animateIn:function(i){this.animating="in";var t=this,o=function(){t.animTimer&&clearTimeout(t.animTimer),"in"===t.animating&&(t.elem.is(":visible")?(i&&i.call(),t.animating=!1):t.animTimer=setTimeout(o,40))};"fade"===this.options.animation?(this.elem.one("webkitTransitionEnd mozTransitionEnd MSTransitionEnd oTransitionEnd transitionend",o).addClass("ui-pnotify-in"),this.elem.css("opacity"),this.elem.addClass("ui-pnotify-fade-in"),this.animTimer=setTimeout(o,650)):(this.elem.addClass("ui-pnotify-in"),o())},animateOut:function(t){this.animating="out";var o=this,s=function(){if(o.animTimer&&clearTimeout(o.animTimer),"out"===o.animating)if("0"!=o.elem.css("opacity")&&o.elem.is(":visible"))o.animTimer=setTimeout(s,40);else{if(o.elem.removeClass("ui-pnotify-in"),o.options.stack.overlay){var e=!1;i.each(p.notices,function(i,t){t!=o&&t.options.stack===o.options.stack&&"closed"!=t.state&&(e=!0)}),e||o.options.stack.overlay.hide()}t&&t.call(),o.animating=!1}};"fade"===this.options.animation?(this.elem.one("webkitTransitionEnd mozTransitionEnd MSTransitionEnd oTransitionEnd transitionend",s).removeClass("ui-pnotify-fade-in"),this.animTimer=setTimeout(s,650)):(this.elem.removeClass("ui-pnotify-in"),s())},position:function(i){var t=this.options.stack,o=this.elem;if(void 0===t.context&&(t.context=e),t){"number"!=typeof t.nextpos1&&(t.nextpos1=t.firstpos1),"number"!=typeof t.nextpos2&&(t.nextpos2=t.firstpos2),"number"!=typeof t.addpos2&&(t.addpos2=0);var s=!o.hasClass("ui-pnotify-in");if(!s||i){t.modal&&(t.overlay?t.overlay.show():t.overlay=l(t)),o.addClass("ui-pnotify-move");var n,a,p;switch(t.dir1){case"down":p="top";break;case"up":p="bottom";break;case"left":p="right";break;case"right":p="left"}n=parseInt(o.css(p).replace(/(?:\..*|[^0-9.])/g,"")),isNaN(n)&&(n=0),void 0!==t.firstpos1||s||(t.firstpos1=n,t.nextpos1=t.firstpos1);var r;switch(t.dir2){case"down":r="top";break;case"up":r="bottom";break;case"left":r="right";break;case"right":r="left"}switch(a=parseInt(o.css(r).replace(/(?:\..*|[^0-9.])/g,"")),isNaN(a)&&(a=0),void 0!==t.firstpos2||s||(t.firstpos2=a,t.nextpos2=t.firstpos2),("down"===t.dir1&&t.nextpos1+o.height()>(t.context.is(e)?c.height():t.context.prop("scrollHeight"))||"up"===t.dir1&&t.nextpos1+o.height()>(t.context.is(e)?c.height():t.context.prop("scrollHeight"))||"left"===t.dir1&&t.nextpos1+o.width()>(t.context.is(e)?c.width():t.context.prop("scrollWidth"))||"right"===t.dir1&&t.nextpos1+o.width()>(t.context.is(e)?c.width():t.context.prop("scrollWidth")))&&(t.nextpos1=t.firstpos1,t.nextpos2+=t.addpos2+(void 0===t.spacing2?25:t.spacing2),t.addpos2=0),"number"==typeof t.nextpos2&&(t.animation?o.css(r,t.nextpos2+"px"):(o.removeClass("ui-pnotify-move"),o.css(r,t.nextpos2+"px"),o.css(r),o.addClass("ui-pnotify-move"))),t.dir2){case"down":case"up":o.outerHeight(!0)>t.addpos2&&(t.addpos2=o.height());break;case"left":case"right":o.outerWidth(!0)>t.addpos2&&(t.addpos2=o.width())}switch("number"==typeof t.nextpos1&&(t.animation?o.css(p,t.nextpos1+"px"):(o.removeClass("ui-pnotify-move"),o.css(p,t.nextpos1+"px"),o.css(p),o.addClass("ui-pnotify-move"))),t.dir1){case"down":case"up":t.nextpos1+=o.height()+(void 0===t.spacing1?25:t.spacing1);break;case"left":case"right":t.nextpos1+=o.width()+(void 0===t.spacing1?25:t.spacing1)}}return this}},queuePosition:function(i,t){return s&&clearTimeout(s),t||(t=10),s=setTimeout(function(){p.positionAll(i)},t),this},cancelRemove:function(){return this.timer&&t.clearTimeout(this.timer),this.animTimer&&t.clearTimeout(this.animTimer),"closing"===this.state&&(this.state="open",this.animating=!1,this.elem.addClass("ui-pnotify-in"),"fade"===this.options.animation&&this.elem.addClass("ui-pnotify-fade-in")),this},queueRemove:function(){var i=this;return this.cancelRemove(),this.timer=t.setTimeout(function(){i.remove(!0)},isNaN(this.options.delay)?0:this.options.delay),this}}),i.extend(p,{notices:[],reload:o,removeAll:function(){i.each(p.notices,function(i,t){t.remove&&t.remove(!1)})},removeStack:function(t){i.each(p.notices,function(i,o){o.remove&&o.options.stack===t&&o.remove(!1)})},positionAll:function(t){if(s&&clearTimeout(s),s=null,p.notices&&p.notices.length)i.each(p.notices,function(i,o){var s=o.options.stack;s&&(s.overlay&&s.overlay.hide(),s.nextpos1=s.firstpos1,s.nextpos2=s.firstpos2,s.addpos2=0,s.animation=t)}),i.each(p.notices,function(i,t){t.position()});else{var o=p.prototype.options.stack;o&&(delete o.nextpos1,delete o.nextpos2)}},styling:{brighttheme:{container:"brighttheme",notice:"brighttheme-notice",notice_icon:"brighttheme-icon-notice",info:"brighttheme-info",info_icon:"brighttheme-icon-info",success:"brighttheme-success",success_icon:"brighttheme-icon-success",error:"brighttheme-error",error_icon:"brighttheme-icon-error"},bootstrap3:{container:"alert",notice:"alert-warning",notice_icon:"glyphicon glyphicon-exclamation-sign",info:"alert-info",info_icon:"glyphicon glyphicon-info-sign",success:"alert-success",success_icon:"glyphicon glyphicon-ok-sign",error:"alert-danger",error_icon:"glyphicon glyphicon-warning-sign"}}}),p.styling.fontawesome=i.extend({},p.styling.bootstrap3),i.extend(p.styling.fontawesome,{notice_icon:"fa fa-exclamation-circle",info_icon:"fa fa-info",success_icon:"fa fa-check",error_icon:"fa fa-warning"}),t.document.body?a():i(a),p};return o(t)}),!function(i,t){"function"==typeof define&&define.amd?define("pnotify.buttons",["jquery","pnotify"],t):"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("jquery"),require("./pnotify")):t(i.jQuery,i.PNotify)}("undefined"!=typeof window?window:this,function(i,t){return t.prototype.options.buttons={closer:!0,closer_hover:!0,sticker:!0,sticker_hover:!0,show_on_nonblock:!1,labels:{close:"Close",stick:"Stick",unstick:"Unstick"},classes:{closer:null,pin_up:null,pin_down:null}},t.prototype.modules.buttons={init:function(t,o){var s=this;t.elem.on({mouseenter:function(){!s.options.sticker||t.options.nonblock&&t.options.nonblock.nonblock&&!s.options.show_on_nonblock||s.sticker.trigger("pnotify:buttons:toggleStick").css("visibility","visible"),!s.options.closer||t.options.nonblock&&t.options.nonblock.nonblock&&!s.options.show_on_nonblock||s.closer.css("visibility","visible")},mouseleave:function(){s.options.sticker_hover&&s.sticker.css("visibility","hidden"),s.options.closer_hover&&s.closer.css("visibility","hidden")}}),this.sticker=i("<div />",{"class":"ui-pnotify-sticker","aria-role":"button","aria-pressed":t.options.hide?"false":"true",tabindex:"0",title:t.options.hide?o.labels.stick:o.labels.unstick,css:{cursor:"pointer",visibility:o.sticker_hover?"hidden":"visible"},click:function(){t.options.hide=!t.options.hide,t.options.hide?t.queueRemove():t.cancelRemove(),i(this).trigger("pnotify:buttons:toggleStick")}}).bind("pnotify:buttons:toggleStick",function(){var o=null===s.options.classes.pin_up?t.styles.pin_up:s.options.classes.pin_up,e=null===s.options.classes.pin_down?t.styles.pin_down:s.options.classes.pin_down;i(this).attr("title",t.options.hide?s.options.labels.stick:s.options.labels.unstick).children().attr("class","").addClass(t.options.hide?o:e).attr("aria-pressed",t.options.hide?"false":"true")}).append("<span />").trigger("pnotify:buttons:toggleStick").prependTo(t.container),(!o.sticker||t.options.nonblock&&t.options.nonblock.nonblock&&!o.show_on_nonblock)&&this.sticker.css("display","none"),this.closer=i("<div />",{"class":"ui-pnotify-closer","aria-role":"button",tabindex:"0",title:o.labels.close,css:{cursor:"pointer",visibility:o.closer_hover?"hidden":"visible"},click:function(){t.remove(!1),s.sticker.css("visibility","hidden"),s.closer.css("visibility","hidden")}}).append(i("<span />",{"class":null===o.classes.closer?t.styles.closer:o.classes.closer})).prependTo(t.container),(!o.closer||t.options.nonblock&&t.options.nonblock.nonblock&&!o.show_on_nonblock)&&this.closer.css("display","none")},update:function(i,t){!t.closer||i.options.nonblock&&i.options.nonblock.nonblock&&!t.show_on_nonblock?this.closer.css("display","none"):t.closer&&this.closer.css("display","block"),!t.sticker||i.options.nonblock&&i.options.nonblock.nonblock&&!t.show_on_nonblock?this.sticker.css("display","none"):t.sticker&&this.sticker.css("display","block"),this.sticker.trigger("pnotify:buttons:toggleStick"),this.closer.find("span").attr("class","").addClass(null===t.classes.closer?i.styles.closer:t.classes.closer),t.sticker_hover?this.sticker.css("visibility","hidden"):i.options.nonblock&&i.options.nonblock.nonblock&&!t.show_on_nonblock||this.sticker.css("visibility","visible"),t.closer_hover?this.closer.css("visibility","hidden"):i.options.nonblock&&i.options.nonblock.nonblock&&!t.show_on_nonblock||this.closer.css("visibility","visible")}},i.extend(t.styling.brighttheme,{closer:"brighttheme-icon-closer",pin_up:"brighttheme-icon-sticker",pin_down:"brighttheme-icon-sticker brighttheme-icon-stuck"}),i.extend(t.styling.bootstrap3,{closer:"glyphicon glyphicon-remove",pin_up:"glyphicon glyphicon-pause",pin_down:"glyphicon glyphicon-play"}),i.extend(t.styling.fontawesome,{closer:"fa fa-times",pin_up:"fa fa-pause",pin_down:"fa fa-play"}),t}),!function(i,t){"function"==typeof define&&define.amd?define("pnotify.desktop",["jquery","pnotify"],t):"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("jquery"),require("./pnotify")):t(i.jQuery,i.PNotify)}("undefined"!=typeof window?window:this,function(i,t){var o,s=function(i,t){return(s="Notification"in window?function(i,t){return new Notification(i,t)}:"mozNotification"in navigator?function(i,t){return navigator.mozNotification.createNotification(i,t.body,t.icon).show()}:"webkitNotifications"in window?function(i,t){return window.webkitNotifications.createNotification(t.icon,i,t.body)}:function(){return null})(i,t)};return t.prototype.options.desktop={desktop:!1,fallback:!0,icon:null,tag:null,title:null,text:null},t.prototype.modules.desktop={genNotice:function(i,t){this.icon=null===t.icon?"http://sciactive.com/pnotify/includes/desktop/"+i.options.type+".png":!1===t.icon?null:t.icon,null!==this.tag&&null===t.tag||(this.tag=null===t.tag?"PNotify-"+Math.round(1e6*Math.random()):t.tag),i.desktop=s(t.title||i.options.title,{icon:this.icon,body:t.text||i.options.text,tag:this.tag}),!("close"in i.desktop)&&"cancel"in i.desktop&&(i.desktop.close=function(){i.desktop.cancel()}),i.desktop.onclick=function(){i.elem.trigger("click")},i.desktop.onclose=function(){"closing"!==i.state&&"closed"!==i.state&&i.remove()}},init:function(i,s){if(s.desktop){if(0!==(o=t.desktop.checkPermission()))return void(s.fallback||(i.options.auto_display=!1));this.genNotice(i,s)}},update:function(i,t){0!==o&&t.fallback||!t.desktop||this.genNotice(i,t)},beforeOpen:function(i,t){0!==o&&t.fallback||!t.desktop||i.elem.css({left:"-10000px"}).removeClass("ui-pnotify-in")},afterOpen:function(i,t){0!==o&&t.fallback||!t.desktop||(i.elem.css({left:"-10000px"}).removeClass("ui-pnotify-in"),"show"in i.desktop&&i.desktop.show())},beforeClose:function(i,t){0!==o&&t.fallback||!t.desktop||i.elem.css({left:"-10000px"}).removeClass("ui-pnotify-in")},afterClose:function(i,t){0!==o&&t.fallback||!t.desktop||(i.elem.css({left:"-10000px"}).removeClass("ui-pnotify-in"),"close"in i.desktop&&i.desktop.close())}},t.desktop={permission:function(){"undefined"!=typeof Notification&&"requestPermission"in Notification?Notification.requestPermission():"webkitNotifications"in window&&window.webkitNotifications.requestPermission()},checkPermission:function(){return"undefined"!=typeof Notification&&"permission"in Notification?"granted"===Notification.permission?0:1:"webkitNotifications"in window&&0==window.webkitNotifications.checkPermission()?0:1}},o=t.desktop.checkPermission(),t}),!function(i,t){"function"==typeof define&&define.amd?define("pnotify.mobile",["jquery","pnotify"],t):"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("jquery"),require("./pnotify")):t(i.jQuery,i.PNotify)}("undefined"!=typeof window?window:this,function(i,t){return t.prototype.options.mobile={swipe_dismiss:!0,styling:!0},t.prototype.modules.mobile={init:function(i,t){var o=this,s=null,e=null,n=null;this.swipe_dismiss=t.swipe_dismiss,this.doMobileStyling(i,t),i.elem.on({touchstart:function(t){o.swipe_dismiss&&(s=t.originalEvent.touches[0].screenX,n=i.elem.width(),i.container.css("left","0"))},touchmove:function(t){if(s&&o.swipe_dismiss){var c=t.originalEvent.touches[0].screenX;e=c-s;var a=(1-Math.abs(e)/n)*i.options.opacity;i.elem.css("opacity",a),i.container.css("left",e)}},touchend:function(){if(s&&o.swipe_dismiss){if(Math.abs(e)>40){var t=0>e?-2*n:2*n;i.elem.animate({opacity:0},100),i.container.animate({left:t},100),i.remove()}else i.elem.animate({opacity:i.options.opacity},100),i.container.animate({left:0},100);s=null,e=null,n=null}},touchcancel:function(){s&&o.swipe_dismiss&&(i.elem.animate({opacity:i.options.opacity},100),i.container.animate({left:0},100),s=null,e=null,n=null)}})},update:function(i,t){this.swipe_dismiss=t.swipe_dismiss,this.doMobileStyling(i,t)},doMobileStyling:function(t,o){o.styling?(t.elem.addClass("ui-pnotify-mobile-able"),i(window).width()<=480?(t.options.stack.mobileOrigSpacing1||(t.options.stack.mobileOrigSpacing1=t.options.stack.spacing1,t.options.stack.mobileOrigSpacing2=t.options.stack.spacing2),t.options.stack.spacing1=0,t.options.stack.spacing2=0):(t.options.stack.mobileOrigSpacing1||t.options.stack.mobileOrigSpacing2)&&(t.options.stack.spacing1=t.options.stack.mobileOrigSpacing1,delete t.options.stack.mobileOrigSpacing1,t.options.stack.spacing2=t.options.stack.mobileOrigSpacing2,delete t.options.stack.mobileOrigSpacing2)):(t.elem.removeClass("ui-pnotify-mobile-able"),t.options.stack.mobileOrigSpacing1&&(t.options.stack.spacing1=t.options.stack.mobileOrigSpacing1,delete t.options.stack.mobileOrigSpacing1),t.options.stack.mobileOrigSpacing2&&(t.options.stack.spacing2=t.options.stack.mobileOrigSpacing2,delete t.options.stack.mobileOrigSpacing2))}},t});