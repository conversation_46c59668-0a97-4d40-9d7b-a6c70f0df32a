(function(){window.Selectize=function(){function t(t){this.$container=t,this.$options=this.$container.children(".selectize-option"),this.$otherOption=this.$options.last(),this.$otherOptionLabel=this.$otherOption.find("label"),this.$otherOptionRadioButton=this.$otherOption.find("input[type=radio]"),this.$otherOptionTextBox=this.$otherOption.find("input[type=text]"),i.call(this)}var i,o,n,e,h,s,r;return i=function(){return this.$options.on("change","input[type=radio]",function(){return e.call(this)?r.call(this):o.call(this)}.bind(this)),this.$otherOptionTextBox.on("keyup",function(t){return"Enter"===t.key?n.call(this):"Escape"===t.key?o.call(this):void 0}.bind(this))},e=function(){return this.$otherOptionRadioButton.is(":checked")},r=function(){return this.$otherOptionTextBox.val(this.$otherOptionRadioButton.val()),s.call(this)},n=function(){var t;return t=this.$otherOptionTextBox.val(),0!==t.length&&(this.$otherOptionLabel.text(t),this.$otherOptionRadioButton.val(t)),h.call(this)},o=function(){return h.call(this)},s=function(){return this.$otherOptionLabel.addClass("is-hidden"),this.$otherOptionTextBox.removeClass("is-hidden"),this.$otherOptionTextBox.focus()},h=function(){return this.$otherOptionTextBox.addClass("is-hidden"),this.$otherOptionLabel.removeClass("is-hidden")},t.prototype.value=function(){return this.$options.find("input[type=radio]:checked").val()},t.prototype.isValid=function(){return void 0!==this.value()},t}()}).call(this);