(function(){var t,e,r,i,n,o,s;$(function(){return $(".submit_question_group .submit").on("click",function(t){var e,i,a,c,u,d,l,f,h;return t.preventDefault(),h=!1,d={},l=$(this),u=$(this).closest(".question_group"),f=u.data("next-classes").split(","),u.find("input:checked, .grey-stars-design[selected='selected']").each(function(){return d[$(this).attr("name").split("_")[0]]=$(this).attr("value"),o(this)?h=!0:void 0}),e=$(this).data("association-id"),i=$(this).data("association-type"),a=$(this).data("issue-type"),c=$(this).data("order-id"),$.ajax({type:"POST",data:{answers:d,association_id:e,type:i,issue_type:a,sample_form:location.href.includes("sample_form")},url:"/surveys/survey_answers",datatype:"JSON",success:function(t){return $("#done-box").hide(),200===t.status?h?s(l):(n(f),r(u),u.slideUp(500,function(){return u.remove()}),u.find(".submit_question_group .submit").remove()):($(".alert-danger").text(t.notice),$(".alert-danger").slideDown(500,function(){return setTimeout(function(){return $(".alert-danger").slideUp(500)},3e3)})),$("html, body").animate({scrollTop:0},"slow")},error:function(t){return $(".alert-danger").text(t.notice),$(".alert-danger").slideDown(500,function(){return setTimeout(function(){return $(".alert-danger").slideUp(500)},3e3)})}})})}),e=function(t,e){if(void 0!==t)switch(t=t.split("_"),t[0]){case"gte":return e>=+t[1];case"lte":return e<=+t[1]}return!1},o=function(t){var r,i;return i=+$(t).data("terminator"),r=$(t).closest(".nps_questions").data("condition"),e(r,i)},n=function(t){return t.forEach(function(t){return $(t).first().show()})},i=function(t){return t.forEach(function(t){return t=$(t).first(),t.slideUp(500,function(){return t.remove()})})},r=function(t){var e;return e=t.closest(".accordion-navigation"),1===e.find(".question_group").length?e.slideUp(500,function(){var t;return t=e.closest(".review_table"),1===t.find(".question_group").length&&t.find(".review_text_box").slideDown(500),e.remove(),$(".review_text .accordion a:first").trigger("click"),$(".review_text .accordion .accordion-navigation:first-child .question_group:first-child").show()}):t.slideUp(500,function(){return t.remove(),$(".review_text .accordion .accordion-navigation:first-child .question_group:first-child").show()})},s=function(e){var r,i;return r=e.closest(".accordion-navigation"),r.length<=0&&(r=e.closest(".review_table")),i=e.closest(".review_table"),1===i.find(".accordion-navigation").length&&i.find(".review_text_box").slideDown(50),r.html("Thank you"),r.slideUp(500,function(){return r.remove(),t(),$(".review_text .accordion a:first").trigger("click"),$(".review_text .accordion .accordion-navigation:first-child .question_group:first-child").show()})},$(function(){return $("input[type = 'radio']").prop("checked",!1),$(".accordion .accordion-navigation").each(function(){return 0===$(this).find(".question_group").length?$(this).remove():void 0}),$(".review_text .accordion .accordion-navigation:first-child .question_group:first-child").show(),$(".review_text .accordion a:first").trigger("click")}),$(function(){return $("body").on("click",'.question_group input[type="radio"],.rating-tab-design .grey-stars-design',function(){return $(this).closest(".question_group").find(".submit_question_group").show()})}),$(function(){return $(".rating-tab-design .grey-stars-design").hover(function(){var t,e;return e=$(this).data("star-value-design"),t=$(this).data("row-number"),$(this).closest(".answer").find(".grey-stars-design").each(function(){return $(this).data("star-value-design")<=e&&"rgb(53, 53, 53)"===$(this).css("color")?$(this).css("color","#FFC315"):void 0})},function(){return $(this).closest(".answer").find(".grey-stars-design").each(function(){return"rgb(255, 195, 21)"===$(this).css("color")?$(this).css("color","#353535"):void 0})})}),$(function(){return $(".rating-tab-design .grey-stars-design").on("click",function(){var t,e;return e=$(this).data("star-value-design"),t=$(this).data("row-number"),$(this).closest(".answer").find(".grey-stars-design").each(function(){return $(this).removeAttr("selected"),$(this).data("star-value-design")<=e?$(this).css("color","#FFD316"):$(this).css("color","#353535")}),$(this).attr("selected","true")})}),$(function(){return $(".submit_review_text_done").on("click",function(e){var r,i,n,o;e.preventDefault(),o=$(this).data("row-number-text"),n=$(this).data("product-id"),i=$(this).data("order-id"),r=$("#customer_review_"+o).val(),$.ajax({type:"POST",url:"/surveys/save_review_text",data:{comment:r,order_id:i,product_id:n,sample_form:location.href.includes("sample_form")},success:function(){$(".review_table_"+o).remove(),t()},error:function(){$("#survey_alert").text(data.notice)}})})}),$(function(){return $(".submit_review_text_cancel").on("click",function(t){var e;return t.preventDefault(),e=$(this).data("row-number-text"),$(".review_table_"+e).remove(),0===$(".review_table").length?($("#review_designs").text("Thank You!"),"promoters"===$("#last-box").data("type")&&$("#last-box").show(),$(".review-the-products").remove()):void 0})}),t=function(){return 0===$(".review_table").length&&($("#review_designs").text("Thank You!"),$(".review-the-products").remove(),"promoters"===$("#last-box").data("type"))?$("#last-box").show():void 0}}).call(this);