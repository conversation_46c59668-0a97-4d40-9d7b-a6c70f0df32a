@charset "UTF-8";
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 1, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize {
  list-style-type: none;
  margin: 0;
}
/* line 5, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option {
  font-size: 1.2em;
}
/* line 8, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option:first-child label {
  border-radius: 0.5em 0.5em 0 0;
}
/* line 12, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option:last-child label, .selectize .selectize-option:last-child input[type=text] {
  border-radius: 0 0 0.5em 0.5em;
}
/* line 16, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option input[type=radio] {
  display: none;
}
/* line 19, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option input[type=radio]:checked + label {
  background: #670e19;
  color: white;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option label, .selectize .selectize-option input[type=text] {
  width: 100%;
  padding: 1em 1.2em;
  background-color: #f4f4f4;
}
/* line 31, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option label {
  cursor: pointer;
  margin: 0;
}
/* line 36, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option input[type=text] {
  border: none;
}
/* line 41, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/_selectize.scss */
.selectize .selectize-option .selectize-other-option-toggle.is-hidden {
  display: none;
}

@font-face {
  font-family: "PaymentFont";
  src: url(/assets/paymentfont-webfont-03eb1d6b2b330ea611766452bdc66c9236ab695e80e43af1e6609250b8cc09e9.ttf) format("truetype"), url(/assets/paymentfont-webfont-79494a645131f81a8cc38261cea65212c29427b22cbd2077e53a4b910d4aa92b.woff) format("woff");
}
/* line 6, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.card-block.row {
  padding: 0 1em;
  margin-bottom: 2em;
}
/* line 10, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.card-block.row .form-control {
  background-color: transparent;
}
/* line 16, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.card-block.row .card-info .row:first-child .columns {
  position: relative;
}
/* line 19, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.card-block.row .card-info .row:first-child .columns .card-number {
  padding-right: 3em;
}
/* line 23, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.card-block.row .card-info .row:first-child .columns .card-icon {
  position: absolute;
  right: 0;
  top: 0;
}

/* line 33, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf {
  font-family: "PaymentFont";
  font-size: 1.8em;
}

/* line 39, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-unknown::after {
  content: "\f012";
}
/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-amex::after {
  content: "\f001";
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-diners::after {
  content: "\f013";
}
/* line 51, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-discover::after {
  content: "\f014";
}
/* line 55, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-mastercard::after {
  content: "\f02d";
}
/* line 59, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-visa::after {
  content: "\f045";
}
/* line 63, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-jcb::after {
  content: "\f028";
}
/* line 67, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-maestro::after {
  content: "\f02c";
}
/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-dankort::after {
  content: "\f04f";
}
/* line 75, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.pf-unionPay::after {
  content: "\f041";
}

/* line 80, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-option {
  padding: 0.65rem;
  background-color: #f4f4f4;
  color: #303030;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.825rem;
  display: inherit !important;
  margin-left: 0 !important;
  width: 100%;
}

/* line 92, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.saved-cards {
  padding: 0.65rem;
  color: #303030;
  text-transform: uppercase;
  font-weight: bold;
  font-size: 0.825rem;
  display: inherit !important;
  margin-left: 0 !important;
  width: 100%;
}

/* line 102, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.saved_card_message {
  padding-left: 0.65rem;
  margin-top: -0.5rem;
  display: none;
  color: #076915;
}

/* line 114, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.cvv-input {
  width: 70% !important;
  display: inline-block !important;
  margin: 0px !important;
  float: right;
  background-color: transparent !important;
}

/* line 123, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-upi-id [type=text] {
  background-color: transparent;
}

/* line 128, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-radio-options {
  -webkit-appearance: none;
  width: 1.15rem;
  height: 1.15rem;
  border: 1px solid #545151;
  border-radius: 50%;
  outline: none;
  display: inline-block;
  margin: 10.4px 10.4px 10.4px 0 !important;
}
/* line 137, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-radio-options:after {
  content: "✔";
  color: #5f5c5c;
  padding: 0.19rem;
}
/* line 142, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-radio-options:checked {
  background-color: #8f1b1d;
  border: 1px solid #8f1b1d;
}
/* line 145, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-radio-options:checked:after {
  color: #fff;
}

/* line 151, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list {
  list-style-type: none;
  display: inline-flex;
  text-align: center;
  vertical-align: top;
}
/* line 156, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list .columns {
  margin-left: 0rem;
  padding: 0 0.5rem;
  display: inline-block;
}
/* line 160, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list .columns .title {
  text-transform: uppercase;
  color: #968d8d;
  margin: 0.15em 0.8em;
  font-size: 0.7rem;
}
/* line 168, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list [type=radio] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}
/* line 175, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list [type=radio] + img {
  border: 3px solid #eee;
  border-radius: 8px;
  cursor: pointer;
  width: 4em;
  height: 4em;
  padding: 0.125em;
}
/* line 184, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.row.inline-list [type=radio]:checked + img {
  border-color: #8f1b1d;
}

/* line 189, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
[type=radio]:checked + label {
  background-color: #fff;
}

/* line 194, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
#WALLET .title, #UPI .title {
  font-size: 0.6rem;
  margin: 0px;
  padding-top: 3px;
  padding-bottom: 2px;
}
/* line 200, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
#WALLET .columns, #UPI .columns {
  min-width: 7%;
  padding-left: unset;
  padding-right: unset;
}
/* line 206, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
#WALLET .inline-list li, #UPI .inline-list li {
  margin-left: 1.2rem;
}
/* line 209, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
#WALLET .inline-list label, #UPI .inline-list label {
  margin-right: 0.2rem;
}
@media screen and (min-width: 320px) and (max-width: 340px) {
  /* line 215, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
  #WALLET .inline-list li, #UPI .inline-list li {
    margin-left: 0.6rem !important;
  }
  /* line 218, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
  #WALLET .inline-list [type=radio] + img, #UPI .inline-list [type=radio] + img {
    width: 3.5em;
    height: 3.5em;
  }
}

/* line 228, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd {
  margin-bottom: 0.25rem !important;
  border: 1px solid #eee;
}
/* line 231, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd .content {
  padding: 0.25rem 0.9375rem;
}
/* line 235, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd.active a {
  background: transparent;
  color: #670e19;
}
/* line 238, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd.active a:after {
  transform: rotate(-45deg);
}
/* line 243, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd a {
  background: #f4f4f4;
  color: #303030;
  display: block;
  font-family: "Lato", sans-serif;
  font-size: 0.825rem;
  padding: 0.65rem;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 252, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payments .accordion dd a:after {
  content: "";
  float: right;
  width: 8px;
  height: 8px;
  border-left: 2px solid;
  border-bottom: 2px solid;
  transform: rotate(230deg);
  margin: 4px 0px;
  margin-right: 8px;
  margin-top: 6px;
  color: #670e19;
}

/* line 269, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.payment-error {
  display: none;
  padding: 0.25rem 0.9375rem;
  color: red;
}

/* line 274, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.dom-payment-error {
  display: none;
  color: red;
}

/* shake effect */
/* line 279, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/payments.scss */
.shake-effect {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-fill-mode: none;
  animation-fill-mode: none;
  -webkit-animation-name: shake;
  animation-name: shake;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    transform: translateX(5px);
    color: #ed8f03;
  }
}
@-webkit-keyframes shake {
  0%, 100% {
    -webkit-transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    -webkit-transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    -webkit-transform: translateX(5px);
    color: #ed8f03;
  }
}
/* line 9, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-open {
  overflow: hidden !important;
}

/* line 12, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#container {
  margin-top: 0.5em !important;
}

/* line 17, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypal_button {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  display: table;
}

/* line 24, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.btn {
  background: #670b19;
  border: 0px;
  width: 100%;
  color: #fff;
  display: inline-block;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 15px;
  text-decoration: none;
  text-align: center;
  position: relative;
  transition: color .1s ease;
}

/* line 38, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.btn.btn-big {
  font-size: 18px;
  padding: 15px 20px;
  min-width: 100px;
}

/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.btn-close {
  color: #303030;
  font-size: 30px;
  text-decoration: none;
  position: absolute;
  right: 5px;
  top: 0;
}

/* line 51, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.btn2 {
  background-color: #fff;
  color: #8f1b1d;
  border: 2px solid #8f1b1d;
}

/* line 57, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal:target:before {
  display: none;
}

/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal:before {
  content: "";
  display: block;
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}

/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal .modal-dialog {
  background: #fefefe;
  border: #333333 solid 1px;
  border-radius: 5px;
  position: fixed;
  bottom: 0;
  left: -0.5px;
  z-index: 11;
  width: 100%;
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}

/* line 88, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal:target .modal-dialog {
  top: -100%;
  -webkit-transform: translate(0, -500%);
  -ms-transform: translate(0, -500%);
  transform: translate(0, -500%);
}

/* line 94, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-body {
  padding: 0px 25px;
  font-size: 14px;
}

/* line 98, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-header {
  border-bottom: #eeeeee solid 1px;
  background: #E0E0E0;
  padding: 5px 10px;
}
/* line 102, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-header p {
  margin: 0px;
}

/* line 106, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-footer {
  padding: 15px 25px;
}
/* line 108, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-footer form {
  padding-bottom: 7px;
}

/* line 113, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modal-header h2 {
  font-size: 20px;
}

/* line 118, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.domestic-offer-modal.reveal-modal {
  height: 80%;
  min-height: 80vh;
  width: 80%;
  left: 10%;
}

/* line 125, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab {
  border-left-style: solid;
  border-left-color: #670B19;
  border-left-width: 3px;
  border-radius: 2px;
  padding-left: 0.5rem;
  box-shadow: 0 2px 2px 0 #E0E0E0;
}
/* line 126, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .hidden_offers {
  display: none;
}
/* line 134, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .offer_row, .offers_tab .offers_title {
  padding-bottom: 0.5rem;
}
/* line 137, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .offer_text_box {
  display: flex;
  padding-right: 0px;
}
/* line 140, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .offer_text_box .offer_text {
  padding-left: 0.5rem;
  font-size: 13px;
  padding-top: 0.3rem;
}
/* line 146, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .offer_image {
  min-height: 20px;
  min-width: 20px;
}
/* line 151, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .tnc_offer {
  font-size: 12px;
  padding-top: 0.3rem;
}
/* line 155, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .hide_up {
  display: none;
}
/* line 158, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .hide_up #up_arrow_toggle {
  border: solid black;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(-135deg);
  -webkit-transform: rotate(-135deg);
  margin-left: 10px;
  margin-top: 8px;
  border-color: #670B19;
}
/* line 170, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab #down_arrow_toggle {
  border: solid black;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
  transform: rotate(45deg);
  -webkit-transform: rotate(45deg);
  margin-left: 10px;
  margin-bottom: 2px;
  border-color: #670B19;
}
/* line 181, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.offers_tab .domestic_offers_toggle {
  display: flex;
  color: #670B19;
  font-size: 13px;
}

/* line 188, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal {
  overflow-y: scroll;
  background-color: #ffffff;
  top: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
  border: 0;
}
/* line 196, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal #cod-confirmation-message {
  color: #303030;
}
/* line 199, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal #cod-confirmation-message {
  margin-top: 5%;
}
/* line 202, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .close-reveal-modal {
  font-size: 1.8em;
  padding: 1% 4% 3% 10%;
  right: 0.3em;
}
/* line 207, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form {
  font-size: 0.9em;
  text-align: center;
  padding-top: 10%;
}
/* line 211, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form .error-msg-for-otp {
  display: none;
  color: red;
}
/* line 215, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form .cod-otp-input {
  width: 50%;
  font-size: 1.2em;
  text-align: center;
  color: #303030;
  background-color: inherit;
  border-width: 0 0 3px;
  margin: 5% auto;
  box-shadow: none !important;
}
/* line 225, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form #otp-phone-change-form-and-content {
  display: none;
}
/* line 228, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form .cod-otp-modal-buttons {
  width: 50%;
  border-radius: 0 !important;
  text-transform: uppercase;
}
/* line 233, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form #otp-phone-text-value {
  border: 0;
  padding: 0;
  background: 0;
  width: auto;
  font-size: 1.2em;
  font-weight: 800;
  clear: both;
  margin: 0;
  display: inline;
}
/* line 244, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form #phone-change-button {
  font-size: 0.9em;
  margin: 0;
}
/* line 248, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .cod-otp-verification-form .button-as-text[type=button] {
  background: 0;
  display: inline;
  padding: 0;
  color: #670b19;
}
/* line 255, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .footer {
  margin-top: 40px;
  float: right;
}
/* line 259, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#codModal .footer #successButton {
  background: linear-gradient(to bottom, #0E9A7D, #267363);
  border-radius: 0.1rem;
  font-size: 1rem;
  padding: 18px 52px;
}

/* line 269, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal {
  -webkit-text-size-adjust: 100%;
  background: transparent;
  padding: 0px;
  border: 0px;
}
/* line 274, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal {
  background: #8f1b1d;
  width: 90%;
  margin-right: auto;
  margin-left: auto;
}
/* line 279, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body {
  padding: 15px;
}
/* line 282, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body #error-message .error-text {
  font-weight: bold;
  margin-bottom: 10px;
}
/* line 286, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body #error-message .paypal-success-reasons {
  margin-left: 10px;
}
/* line 290, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body .text-message {
  margin-bottom: 15px;
}
/* line 293, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body p {
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}
/* line 298, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-body .close-modal {
  float: right;
  line-height: 0px !important;
  font-size: 30px;
  color: #ffffff;
  margin-right: -10px;
}
/* line 306, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-footer {
  text-align: center;
}
/* line 308, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-footer .retry-payment {
  font-weight: bold;
}
/* line 311, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#paypalSuccessModal #paypalModal .modal-footer .cancel {
  color: white;
  background-color: transparent;
}

/* line 320, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link {
  display: none;
}
/* line 322, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
  padding-right: 8px;
}
/* line 325, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel h1 {
  color: #303030;
}
/* line 328, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel .close {
  float: right;
  border-radius: 50%;
  background-color: transparent;
  padding: 2px 6px;
  color: #303030;
  font-weight: bold;
}
/* line 336, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel p {
  padding-right: 2rem;
  text-align: justify;
  color: #303030;
}
/* line 341, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel .success {
  margin-left: 19%;
  text-align: center;
  font-size: 1.2em;
  display: inline-block;
  padding: 0px 20px 0px 25px;
  border-radius: 4px;
  background-color: #670b19;
  color: #ffffff;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
}
/* line 351, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel .success .fi-social-android {
  font-size: 30px;
  color: #ffffff;
  vertical-align: middle;
}
/* line 356, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#get_app_link .panel .success .fi-social-apple {
  font-size: 30px;
  color: #ffffff;
  vertical-align: middle;
}

/* line 365, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.card_message {
  display: none;
  color: #076915;
}

/* line 371, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .accordion {
  padding-bottom: 1em;
}
/* line 405, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .item_block {
  border: 0.1em solid #dcdcdc;
  margin-bottom: .5em;
  color: #303030;
  padding: 3px;
}
/* line 410, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .item_block .truncate {
  color: #303030;
}
/* line 414, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block ul.addons-notes {
  padding-right: 0;
}
/* line 417, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block ul.addons-notes li.accordion-navigation a {
  font-size: 14px;
}
/* line 420, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block ul.addons-notes li.accordion-navigation .text-right {
  float: right;
}
/* line 425, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block ul.addons-notes li.accordion-navigation .content .row .left {
  margin-left: 5px;
}
/* line 432, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .order_total {
  padding: 0.2em 1em;
  margin-top: 1em;
}
/* line 436, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block #totals_block {
  color: #303030;
}
/* line 439, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block #totals_block .row .columns {
  padding-right: 0px;
}
/* line 444, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .notice_rts {
  color: #670b19;
  font-size: 12px;
  line-height: 15px;
  font-style: italic;
}
/* line 450, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .grand_total, #create_orders_block .grand_total_with_cod, #create_orders_block .grand_total_pd {
  color: #303030;
  font-size: 15px;
  font-weight: 600;
}
/* line 455, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .shipping_address {
  margin-bottom: 2em;
  padding: 1em;
}
/* line 458, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .shipping_address .address-text {
  width: 100%;
  font-size: 14px;
  color: #303030;
}
/* line 463, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .shipping_address table {
  background: inherit;
  border: none;
  margin-bottom: 0em;
}
/* line 468, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .shipping_address table tr:nth-of-type(even) {
  background: inherit;
}
/* line 471, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#create_orders_block .shipping_address table tr td {
  color: inherit;
  padding: 0.1em;
}

/* line 480, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block {
  font-size: 0.875rem;
}
/* line 482, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block .order-ack-title {
  color: #303030;
}
/* line 486, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block .panel_block .panel_content {
  padding: 1em;
}
/* line 490, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block hr {
  border: #4d4d4d solid;
  border-width: 0.1em 0 0;
}
/* line 494, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block table {
  width: 100%;
  border: 0em;
  background: inherit;
}
/* line 498, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block table tr {
  background: inherit;
}
/* line 500, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block table tr td, #order_show_block table tr th {
  color: inherit;
  line-height: 1em;
  padding: 0.3em;
}
/* line 507, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block .line_item_details {
  padding: 0.4em;
  color: #303030;
  border: none;
}
/* line 512, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block .designer-order-block {
  background-color: #ffffff;
}
/* line 514, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_show_block .designer-order-block .panel_content {
  background-color: #ffffff;
}

/* line 520, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.notice_class {
  padding-left: 10px;
  color: orange;
  font-size: 14px;
  line-height: 15px;
}

/* line 527, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.quantity_total {
  margin-right: 0em;
}

/* line 531, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
table.customer_order {
  border: 1px solid #4d4d4d;
  border-collapse: collapse;
}

/* line 537, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
table.customer_order tr td {
  border: 1px solid #4d4d4d;
  color: white;
}
/* line 541, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
table.customer_order tr th {
  border: 1px solid #4d4d4d;
  color: white;
}
/* line 545, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
table.customer_order tr td.cost {
  text-align: right;
}

/* line 550, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
table.customer_order tr {
  background: #333333;
  color: white;
}

/* The Modal (background) */
/* line 557, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modalForm {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: black;
  /* Modal Content (Iframe) */
  /* The Close Button */
  /* 100% Image Width on Smaller Screens */
}
/* line 571, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modalForm .modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}
@-webkit-keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
/* line 592, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modalForm .close {
  position: absolute;
  top: 25px;
  right: 5px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  transition: 0.3s;
}
/* line 602, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.modalForm .close:hover,
.modalForm .close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}
@media only screen and (max-width: 700px) {
  /* line 610, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
  .modalForm .modal-content {
    width: 100%;
  }
}
@media only screen and (min-width: 1200px) {
  /* line 616, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
  .modalForm .modal-content {
    width: auto;
    height: auto;
  }
}

/* Order status on order show page */
/* line 624, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status {
  float: left;
  margin-left: 1%;
}
/* line 628, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div {
  position: relative;
  float: left;
  width: 16%;
}
/* line 633, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .circle_stage {
  display: inline-block;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  -o-border-radius: 50%;
  font-size: 12px;
  line-height: 50px;
  text-align: center;
  border: 2px solid;
}
/* line 644, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .circle_stage_domestic {
  display: inline-block;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  -o-border-radius: 50%;
  color: #303030;
  text-align: center;
  border: 2px solid #f4f4f4;
  font-size: 0.7em;
  vertical-align: middle;
}
/* line 656, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .square_stage {
  display: inline-block;
  font-size: 12px;
  line-height: 50px;
  text-align: center;
  border: 2px solid;
}
/* line 664, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .step_image {
  background: transparent;
  position: relative;
  top: -4px;
}
/* line 670, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .step_number {
  position: relative;
  top: -14px;
  color: black;
}
/* line 676, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #circle_div .vertical_line {
  width: 6px;
  height: 40px;
  margin-left: 52px;
  margin-bottom: -8px;
  display: inline-block;
  border: 1px solid;
}
/* line 686, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #notes_div {
  width: 82%;
  margin-left: 80px;
}
/* line 690, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#order_status #notes_div .status_note {
  margin-left: 18px;
  color: #303030;
  padding-top: 2px;
  font-size: small;
  margin-top: -3px;
  margin-bottom: 25px;
}

/* line 701, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.all_order_stages {
  display: inline-block;
}
/* line 703, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.all_order_stages .horizontal-line {
  width: 2em;
  height: 0.3em;
  background: yellowgreen;
  border: 1px solid white;
  border-radius: 1px;
  float: right;
}
/* line 711, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.all_order_stages .base {
  background: yellowgreen;
  display: inline-block;
  height: 1em;
  margin-left: 5%;
  position: relative;
  width: 2.5em;
  float: left;
  margin-right: 5%;
}
/* line 720, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.all_order_stages .base .pointer {
  border-left: 15px solid yellowgreen;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  content: "";
  height: 0;
  left: 40px;
  position: absolute;
  top: 0px;
  width: 0;
}
/* line 731, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.all_order_stages .base .uc_base {
  font-size: x-small;
  color: black;
  padding: 2px;
}

/* line 738, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.status_text {
  margin: 2% 10% 0% 10%;
  width: 80%;
  text-align: center;
  color: #303030;
  border-radius: 2px;
  font-size: 0.8em;
  font-weight: 600;
  border: 1px solid #d8d8d8;
}

/* line 749, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_panel .bordered_block {
  background-color: transparent;
  box-shadow: none;
}
/* line 752, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_panel .bordered_block .policy_button {
  background-color: #b11f2d;
  color: #ffffff;
}

/* Return Button */
/* line 759, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 764, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;
}
/* line 770, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.reveal-modal .close-reveal-modal {
  color: #4d4d4d;
}

/* line 775, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#bank_deposit_order_box {
  display: none;
  margin-right: 30px;
  background-color: #cecece;
  color: #000000;
  text-align: center;
  padding: 8px;
  position: fixed;
  z-index: 10;
  bottom: 1%;
  font-size: 16px;
  line-height: 21px;
  border: 2px solid #30924A;
}
/* line 788, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#bank_deposit_order_box .bank_deposit_order_text_close {
  float: right;
  text-align: right;
  position: relative;
  left: 25px;
  bottom: 25px;
  padding: 0px 3px;
  cursor: pointer;
  border-radius: 50%;
  background-color: #a9a9a9;
}
/* line 800, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#bank_deposit_order_box .bank_deposit_order_text span {
  color: #30924A;
  text-transform: uppercase;
  font-weight: 500;
}

/* line 809, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.shipping_options .shipping_option {
  padding: 8px 8px 0px 8px;
}
/* line 811, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.shipping_options .shipping_option .shipping_radio_button {
  font-size: 14px;
}
/* line 814, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.shipping_options .shipping_option .shipping_option_price {
  float: right;
  font-size: 14px;
}
/* line 819, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.shipping_options .delivery_message {
  padding-left: 35px;
  font-size: 13px;
  color: #670b19;
}

/* line 826, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.row .cashback-reminder {
  background-color: #eee;
  padding: 1.3em 1.6em;
  margin-bottom: 1em;
  text-align: center;
  border: 2px solid #bdbdbd;
}

/* line 835, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#cancel-confirmation-modal .cancel-reason-request {
  margin-bottom: 0.45em;
}
/* line 839, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#cancel-confirmation-modal .cancel-helper-info {
  margin-top: 1.2em;
}
/* line 843, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#cancel-confirmation-modal .cancel-order-reason-select-warning {
  font-size: 0.9em;
  color: #a94442;
  margin-top: 0.5em;
}

/* line 851, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-form-and-content {
  padding-top: 10%;
  font-size: 0.9em;
  text-align: center;
}
/* line 857, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-show-phone-details {
  font-size: 1.2em;
  font-weight: bold;
}
/* line 862, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-error-msg {
  display: none;
  color: red;
}
/* line 868, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-form .cod-otp-input {
  width: 50%;
  font-size: 3em;
  font-family: monospace;
  padding: 1em 0em;
  text-align: center;
  color: #303030;
  background-color: inherit;
  border-width: 0 0 3px;
  margin: 2% auto;
}
/* line 880, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-form #otp-submit-button {
  width: 50%;
  text-transform: uppercase;
}
/* line 885, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#otp-verification-modal.reveal-modal #otp-form #resend-otp {
  color: #670b19;
  display: inline;
  padding: 0;
  background: none;
}

/* line 894, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.addon_form_link {
  padding: 0.5em;
  border: 2px solid #670b19;
}
/* line 898, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.addon_form_link a {
  color: #670b19;
}
/* line 902, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.addon_form_link.filled {
  border-color: #808080;
}
/* line 907, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
.addon_form_link.filled a {
  color: #1a1a1a;
}

/* line 913, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#returnStatusModal {
  height: auto;
  min-height: auto;
  max-height: 90%;
  overflow-y: auto;
}

/* line 920, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return-track-button {
  width: 100%;
}

/* line 926, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status #horizontal_line {
  width: 4px;
  height: 40px;
  position: relative;
  top: 0%;
  z-index: 0;
  left: 50%;
  right: 0%;
  border-radius: 0% !important;
}
/* line 936, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status table {
  width: 100%;
}
/* line 938, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status table tr, #return_status table td {
  text-align: center !important;
}
/* line 942, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status .stage_name {
  top: 15px;
  font-weight: bold;
  font-size: 20px;
  color: #670b19;
}
/* line 948, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status .timestamp {
  display: block;
  font-size: 13px;
}
/* line 952, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status .note {
  display: block;
  font-size: 13px;
  color: lightslategrey;
}
/* line 957, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/orders_red.scss */
#return_status .step_image {
  min-width: 35px;
}
