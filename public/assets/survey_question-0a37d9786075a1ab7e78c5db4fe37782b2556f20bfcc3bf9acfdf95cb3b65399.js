(function() {
  var CheckAllDone, condition_parser, next_question, remove_element, show_class, survey_terminator, terminate_survey;

  $(function() {
    return $('.submit_question_group .submit').on('click', function(e) {
      var association_id, association_type, issue_type, order_id, question_group, selected_issue, self, show_class_name, terminator_flag;
      e.preventDefault();
      terminator_flag = false;
      selected_issue = {};
      self = $(this);
      question_group = $(this).closest('.question_group');
      show_class_name = question_group.data('next-classes').split(',');
      question_group.find("input:checked, .grey-stars-design[selected='selected']").each(function() {
        selected_issue[$(this).attr('name').split('_')[0]] = $(this).attr('value');
        if (survey_terminator(this)) {
          return terminator_flag = true;
        }
      });
      association_id = $(this).data('association-id');
      association_type = $(this).data('association-type');
      issue_type = $(this).data('issue-type');
      order_id = $(this).data('order-id');
      return $.ajax({
        type: 'POST',
        data: {
          answers: selected_issue,
          association_id: association_id,
          type: association_type,
          issue_type: issue_type,
          sample_form: location.href.includes('sample_form')
        },
        url: '/surveys/survey_answers',
        datatype: 'JSON',
        success: function(data) {
          $('#done-box').hide();
          if (data.status === 200) {
            if (terminator_flag) {
              terminate_survey(self);
            } else {
              show_class(show_class_name);
              next_question(question_group);
              question_group.slideUp(500, function() {
                return question_group.remove();
              });
              question_group.find('.submit_question_group .submit').remove();
            }
          } else {
            $('.alert-danger').text(data.notice);
            $('.alert-danger').slideDown(500, function() {
              return setTimeout((function() {
                return $('.alert-danger').slideUp(500);
              }), 3000);
            });
          }
          return $("html, body").animate({
            scrollTop: 0
          }, "slow");
        },
        error: function(data) {
          $('.alert-danger').text(data.notice);
          return $('.alert-danger').slideDown(500, function() {
            return setTimeout((function() {
              return $('.alert-danger').slideUp(500);
            }), 3000);
          });
        }
      });
    });
  });

  condition_parser = function(condition, value) {
    if (condition !== void 0) {
      condition = condition.split('_');
      switch (condition[0]) {
        case 'gte':
          return value >= +condition[1];
        case 'lte':
          return value <= +condition[1];
      }
    }
    return false;
  };

  survey_terminator = function(object) {
    var condition, terminator;
    terminator = +$(object).data('terminator');
    condition = $(object).closest('.nps_questions').data('condition');
    return condition_parser(condition, terminator);
  };

  show_class = function(classes) {
    return classes.forEach(function(class_name) {
      return $(class_name).first().show();
    });
  };

  remove_element = function(classes) {
    return classes.forEach(function(class_name) {
      class_name = $(class_name).first();
      return class_name.slideUp(500, function() {
        return class_name.remove();
      });
    });
  };

  next_question = function(object) {
    var panel;
    panel = object.closest('.accordion-navigation');
    if (panel.find('.question_group').length === 1) {
      return panel.slideUp(500, function() {
        var review_table;
        review_table = panel.closest('.review_table');
        if (review_table.find('.question_group').length === 1) {
          review_table.find('.review_text_box').slideDown(500);
        }
        panel.remove();
        $(".review_text .accordion a:first").trigger("click");
        return $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show();
      });
    } else {
      return object.slideUp(500, function() {
        object.remove();
        return $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show();
      });
    }
  };

  terminate_survey = function(object) {
    var review_table, text_box;
    review_table = object.closest('.accordion-navigation');
    if (review_table.length <= 0) {
      review_table = object.closest('.review_table');
    }
    text_box = object.closest('.review_table');
    if (text_box.find('.accordion-navigation').length === 1) {
      text_box.find('.review_text_box').slideDown(50);
    }
    review_table.html('Thank you');
    return review_table.slideUp(500, function() {
      review_table.remove();
      CheckAllDone();
      $(".review_text .accordion a:first").trigger("click");
      return $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show();
    });
  };

  $(function() {
    $("input[type = 'radio']").prop('checked', false);
    $('.accordion .accordion-navigation').each(function() {
      if ($(this).find('.question_group').length === 0) {
        return $(this).remove();
      }
    });
    $('.review_text .accordion .accordion-navigation:first-child .question_group:first-child').show();
    return $(".review_text .accordion a:first").trigger("click");
  });

  $(function() {
    return $('body').on('click', '.question_group input[type="radio"],.rating-tab-design .grey-stars-design', function(e) {
      return $(this).closest('.question_group').find('.submit_question_group').show();
    });
  });

  $(function() {
    return $('.rating-tab-design .grey-stars-design').hover((function() {
      var row_number, star_value_design;
      star_value_design = $(this).data('star-value-design');
      row_number = $(this).data('row-number');
      return $(this).closest('.answer').find('.grey-stars-design').each(function() {
        if ($(this).data('star-value-design') <= star_value_design && $(this).css('color') === 'rgb(53, 53, 53)') {
          return $(this).css('color', '#FFC315');
        }
      });
    }), function() {
      return $(this).closest('.answer').find('.grey-stars-design').each(function() {
        if ($(this).css('color') === 'rgb(255, 195, 21)') {
          return $(this).css('color', '#353535');
        }
      });
    });
  });

  $(function() {
    return $('.rating-tab-design .grey-stars-design').on('click', function(e) {
      var row_number, star_value_design;
      star_value_design = $(this).data('star-value-design');
      row_number = $(this).data('row-number');
      $(this).closest('.answer').find('.grey-stars-design').each(function() {
        $(this).removeAttr('selected');
        if ($(this).data('star-value-design') <= star_value_design) {
          return $(this).css('color', '#FFD316');
        } else {
          return $(this).css('color', '#353535');
        }
      });
      return $(this).attr('selected', 'true');
    });
  });

  $(function() {
    return $('.submit_review_text_done').on('click', function(e) {
      var comment_data, order_id, product_id, row_number_text;
      e.preventDefault();
      row_number_text = $(this).data('row-number-text');
      product_id = $(this).data('product-id');
      order_id = $(this).data('order-id');
      comment_data = $('#customer_review_' + row_number_text).val();
      $.ajax({
        type: 'POST',
        url: '/surveys/save_review_text',
        data: {
          comment: comment_data,
          order_id: order_id,
          product_id: product_id,
          sample_form: location.href.includes('sample_form')
        },
        success: function(result) {
          $('.review_table_' + row_number_text).remove();
          CheckAllDone();
        },
        error: function(result) {
          $('#survey_alert').text(data.notice);
        }
      });
    });
  });

  $(function() {
    return $('.submit_review_text_cancel').on('click', function(e) {
      var row_number_text;
      e.preventDefault();
      row_number_text = $(this).data('row-number-text');
      $('.review_table_' + row_number_text).remove();
      if ($('.review_table').length === 0) {
        $('#review_designs').text('Thank You!');
        if ($('#last-box').data('type') === 'promoters') {
          $('#last-box').show();
        }
        return $('.review-the-products').remove();
      }
    });
  });

  CheckAllDone = function() {
    if ($('.review_table').length === 0) {
      $('#review_designs').text('Thank You!');
      $('.review-the-products').remove();
      if ($('#last-box').data('type') === 'promoters') {
        return $('#last-box').show();
      }
    }
  };

}).call(this);
