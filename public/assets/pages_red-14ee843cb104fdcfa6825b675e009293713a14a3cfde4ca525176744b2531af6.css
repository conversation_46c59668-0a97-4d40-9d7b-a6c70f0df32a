@charset "UTF-8";
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 11, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_eid #eid-container .design-block {
  margin: 4px 0px;
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_eid #eid-container .featured-products {
  border: 1px solid gray;
}
/* line 16, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_eid #eid-container .featured-products .bestseller-title, .pages_eid #eid-container .featured-products .view-more {
  text-align: center;
}
/* line 20, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_eid #eid-container img {
  width: 100%;
}

@media only screen and (max-width: 620px) {
  /* line 28, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
  .pages_eid #eid-container .columns {
    padding: 0px;
  }
  /* line 31, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
  .pages_eid #eid-container .pad-right {
    padding-right: 4px !important;
  }
  /* line 34, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
  .pages_eid #eid-container .pad-left {
    padding-left: 4px !important;
  }
  /* line 38, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
  .pages_eid #eid-container .row .row {
    margin: 0 auto;
  }
}
/* line 46, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information #container {
  margin-top: 1.8em;
}
/* line 49, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_steps {
  background-color: #f4f4f4;
  font-size: 0.875rem;
}
/* line 52, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_steps .title {
  font-size: 150%;
  text-align: center;
  color: #303030;
}
/* line 57, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_steps .step_circle {
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 100%;
  font-size: 3%;
  line-height: 50px;
  text-align: center;
  margin-left: 10%;
  background: #670b19;
}
/* line 69, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_steps .step_text {
  color: #303030;
  font: inherit;
}
/* line 75, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .size_image .button {
  background-color: #670b19;
  padding: 8px 12px;
  margin-bottom: 0px;
}
/* line 82, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block {
  background: #f4f4f4;
  margin-bottom: 20px;
}
/* line 86, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block a:hover, .pages_stitching_information .listing_panel_block a:focus {
  color: #303030;
}
/* line 90, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block li, .pages_stitching_information .listing_panel_block label {
  cursor: auto;
}
/* line 94, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block table {
  border: none;
  color: #303030;
}
/* line 98, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block p {
  text-align: left;
  background: none repeat scroll 0% 0%;
  margin: -7px 0px 0px;
  border-radius: 2px 2px 0px 0px;
  padding: 6px;
}
/* line 106, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block ul {
  font-size: 14px;
}
/* line 110, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .listing_panel_block td {
  vertical-align: baseline;
}
/* line 116, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .accordion table {
  display: none;
}
/* line 120, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .accordion a {
  outline: none;
  color: inherit;
}
/* line 125, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .accordion .expand {
  display: none;
  float: right;
}
/* line 130, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .accordion .collapse {
  float: right;
}
/* line 136, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_details .panel_content {
  padding: 0px;
}
/* line 139, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_details .stitching_question {
  color: #303030;
  font-weight: bold;
}
/* line 144, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_details .stitching_answer {
  text-align: justify;
  color: #303030;
  font: inherit;
}
/* line 152, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .statement {
  border: 1px solid;
  text-align: center;
  color: #303030;
  font-size: 14px;
  height: 24px;
  padding: 7px;
  margin: 0 auto;
  width: 80%;
  font-style: inherit;
  font-weight: bold;
}
/* line 165, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .message {
  padding: 2px;
  text-align: center;
  background: #f4f4f4;
  color: #303030;
  font-weight: bold;
}
/* line 172, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_stitching_information .stitching_number {
  color: #e47450;
  font-weight: 400;
  text-align: left;
}

/* line 179, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#contactUs, #emailUs {
  background-color: #ffffff;
  min-height: 20vh;
  width: 95.8%;
  left: 10px;
  right: 10px;
  top: 50px;
}
/* line 186, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#contactUs .close-reveal-modal, #emailUs .close-reveal-modal {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}
/* line 190, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#contactUs .closeEbtn, #emailUs .closeEbtn {
  background: #D44F67;
  border: #D44F67;
  color: white;
  font-size: small;
  border-radius: 4%;
  margin: 1rem;
  margin-left: 5rem;
  padding: 1rem 2.1rem;
}
/* line 200, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#contactUs .submitEbtn, #emailUs .submitEbtn {
  border-radius: 4%;
  background: #670b19;
  border: #670b19;
  color: white;
  padding: 1rem 2.1rem;
  margin: 1rem;
}
/* line 208, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#contactUs hr, #emailUs hr {
  margin: 0;
}

/* line 213, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#trackModal {
  background-color: #ffffff;
  padding-top: 50px;
  min-height: 33vh;
  top: 50px;
}
/* line 218, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#trackModal .close-reveal-modal {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}
/* line 222, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#trackModal #order_number_field {
  margin-bottom: 22px;
}

/* line 227, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#list_of_order_modal {
  overflow: auto;
  position: fixed;
  height: 100%;
  background-color: #ffffff;
  top: 0px !important;
  width: 100%;
}
/* line 234, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#list_of_order_modal .close-reveal-modal {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}

/* line 243, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div {
  font-size: 0.65em;
}
/* line 245, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .questions_ul {
  list-style: none;
  margin-left: 0px;
}
/* line 250, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .question_li_content h3 {
  font-size: 0.875rem;
}
/* line 252, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .question_li_content h3:before {
  content: "[+]  ";
}
/* line 258, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .question_selected h3 {
  font-size: 0.875rem;
}
/* line 260, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .question_selected h3:before {
  content: "[-]  ";
}
/* line 265, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div #search_ques {
  font-size: 1.5em;
}
/* line 269, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div #search_answer span {
  font-size: 0.875rem;
}
/* line 273, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .search_res {
  padding: 12px 8px;
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 277, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faq_head {
  font-size: 1.5em;
  padding: 5px;
  margin-bottom: 10px;
}
/* line 282, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .quick_links {
  text-align: center;
}
/* line 284, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .quick_links .quick_link {
  height: 110px;
  border: 1px solid gray;
}
/* line 287, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .quick_links .quick_link h2 {
  font-size: 1em;
}
/* line 292, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row {
  font-size: 1.2em;
}
/* line 294, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row .faqs_all {
  margin-top: 30px;
}
/* line 297, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row .view_more {
  margin-left: -17px;
  background-color: #1D2A29;
  padding: 10px;
  border-radius: 12px;
}
/* line 305, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faq_link_selected {
  color: #303030;
  background-color: #d3b9be;
}
/* line 309, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .faq_link_selected::before {
  content: "";
  position: absolute;
  right: 40%;
  top: 102px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-bottom: 10px solid #ffffff;
  border-right: 8px solid transparent;
}
/* line 320, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .answer_selected {
  padding: 12px;
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 323, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .answer_selected span {
  font-size: 0.9em !important;
}
/* line 327, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .navigation {
  height: 300px;
  overflow: auto;
}
/* line 331, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div hr {
  margin: 0.7rem 0 0.5rem 0;
}
/* line 334, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
.pages_faq .main-section #container .faq_main_div .answer_nav {
  height: 200px;
  overflow: auto;
}

/* line 343, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container {
  margin-left: 0px !important;
}
/* line 345, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center {
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
}
/* line 347, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .accordion_title {
  background-color: #7e7e7e;
  border: solid;
  border: #7e7e7e;
  color: white;
}
/* line 355, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .tab-title.active a {
  background-color: #670b19;
  border-bottom: none;
  color: white;
}
/* line 362, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .tab-title {
  width: 50%;
}
/* line 364, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .tab-title a {
  padding: 0.5em;
  text-align: center;
}
/* line 369, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .tabs-content {
  background-color: white;
  width: 100%;
  padding: 5px;
  display: inline;
}
/* line 375, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .accordion {
  margin-left: -0.3em;
}
/* line 379, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #issue_panel .columns {
  padding-left: 0px;
  padding-right: 0px;
}
/* line 384, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category-links {
  height: 110px;
  border: 1px solid #6e6e6e;
  background: white;
  color: black;
  font-size: 4.5vw;
  width: 45%;
  margin-bottom: 3.7vw;
  margin-left: 3.7vw;
  text-align: center;
}
/* line 395, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .content_replace {
  padding: 0.9375rem;
  display: block;
  color: black;
  background-color: #cacaca;
}
/* line 401, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .answer_category_query {
  display: none;
  margin-left: 1.5rem;
  color: black;
  margin-bottom: 0.5rem;
}
/* line 407, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .answers_category_query {
  display: none;
  margin-left: 1.5rem;
  color: black;
  margin-bottom: 0.5rem;
}
/* line 413, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query_level_two {
  margin-left: 0.7rem;
  color: black;
  list-style: none;
  display: none;
}
/* line 418, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query_level_two hr {
  margin-top: 0.1rem;
}
/* line 422, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query {
  display: none;
  list-style: none;
  color: black;
}
/* line 426, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query hr {
  margin: 0.1rem 0 1rem;
}
/* line 430, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query:first-child {
  padding-top: 1em;
}
/* line 433, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .stage_passed {
  background-color: #009688;
}
/* line 436, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .stage_passed:before {
  content: "✓ ";
}
/* line 439, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query:before {
  content: "›";
}
/* line 442, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_query_level_two:before {
  content: "›";
}
/* line 445, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .category_list {
  margin-left: 0;
}
/* line 448, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .blur {
  opacity: 0.6;
}
/* line 451, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .button.info {
  background-color: #670b19 !important;
  color: white;
}
/* line 455, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #loadingImage {
  position: fixed;
  left: 45%;
  top: 30%;
  font-size: 4em;
}
/* line 461, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #error_order_not_found_email {
  color: #D84315;
  padding: 3px;
}
/* line 465, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .login_hr:after {
  content: "OR";
  /* section sign */
  color: #999;
  display: inline;
  /* for vertical centering and background knockout */
  background-color: #cacaca;
  /* same as background color */
  padding: 0 0.5em;
  /* size of background color knockout */
}
/* line 472, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .login_hr {
  font-family: Arial, sans-serif;
  /* choose the font you like */
  text-align: center;
  /* horizontal centering */
  line-height: 1px;
  /* vertical centering */
  height: 1px;
  /* gap between the lines */
  font-size: 1em;
  /* choose font size you like */
  border-width: 1px 0;
  /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px;
  /* 20px space above/below, 10px left/right */
  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
/* line 491, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .track_btn {
  background-color: #670b19;
  text-align: center;
  color: white;
  width: 65%;
  margin: auto;
  display: table;
  padding: 3px;
  border-radius: 5px;
}
/* line 501, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #issue_text_help_center, #container #help_center #order_number_text, #container #help_center #email_id_help_center {
  color: black;
}
/* line 503, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #issue_text_help_center:hover, #container #help_center #order_number_text:hover, #container #help_center #email_id_help_center:hover {
  color: white;
}
/* line 507, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna {
  overflow: auto;
  display: none;
  background: #eeeeee81;
  margin-left: 0.3em;
  border: 0.1px solid #6e6e6e;
  width: 99%;
  height: 350px;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
}
/* line 516, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .no_order_found {
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  margin: auto;
  font-size: 21px;
  margin-top: 150px;
  display: table;
}
/* line 523, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu {
  margin-top: 0.5em;
  height: auto;
  width: 100%;
  background: white;
  border: 0.8px solid black;
  line-height: 2;
  display: inline-flex;
}
/* line 531, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .order_info {
  width: 20%;
  text-align: left;
  margin: 5px;
}
/* line 535, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .order_info #product_no {
  font-size: 12px;
  font-weight: bolder;
}
/* line 540, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_detail {
  margin: 5px;
  text-align: left;
  width: 50%;
  font-size: 12px;
}
/* line 545, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_detail #item_name {
  font-weight: bolder;
}
/* line 548, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_detail #item_total {
  font-size: 12px;
  font-weight: bolder;
}
/* line 553, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_status {
  margin-top: 10px;
  margin-right: 5px;
  font-size: 12px;
  text-align: center;
  width: 30%;
}
/* line 559, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_status #confirmed {
  font-weight: bolder;
  margin-top: 10px;
}
/* line 563, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .helpcenter_qna .order_menu .product_status #status {
  padding: 0px 10px;
  border-color: #670b19;
  font-weight: 400;
}
/* line 571, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .support_info {
  display: inline-flex;
  width: 100%;
  text-align: center;
}
/* line 575, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .support_info #contact_us, #container #help_center .support_info #email_us {
  background-color: #670b19;
  border: 0;
  padding: 6px;
  border-radius: 6px;
  margin: 2rem;
  margin-top: 16px;
  margin-bottom: 10px;
  width: 30%;
  color: white;
}
/* line 587, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center #category_title {
  color: white;
  background: #670b19;
  border: 0.1px solid black;
  width: fit-content;
  border-left: 0;
  padding: 0.3rem 1rem;
  display: none;
}
/* line 596, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .page_title {
  background-color: white;
  color: #670b19;
  padding: 0.5rem 0.5rem 0.8rem 0.5rem;
  width: 100%;
  border-bottom: 2px solid #670b19;
  text-align: center;
  display: inline-flex;
}
/* line 604, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .page_title #main {
  width: 87%;
  font-size: 24px;
  padding-left: 3rem;
  margin: 0;
}
/* line 610, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .page_title #faq {
  width: 13%;
  padding: 6px;
  border-radius: 5%;
  text-align: center;
  font-size: 13px;
  font-weight: 1000;
  border: 1px solid;
}
/* line 619, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .page_title #nav_arrow {
  display: none;
  width: 10%;
  color: #670b19;
  font-size: 28px;
  padding: 0;
  margin: 0;
  border: 0.2px solid #670b19;
  background: white;
}
/* line 630, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center center {
  margin: 10px;
  padding-top: 20px;
  height: auto;
  width: 97%;
  line-height: 2;
}
/* line 636, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center center .login_req {
  width: 50%;
  margin-top: 20px;
}
/* line 639, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center center .login_req p {
  font-size: 1.2em;
  color: black;
}
/* line 645, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #help_center .step_image {
  margin-top: 1.5rem;
}
/* line 649, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #track_order {
  width: 94%;
  margin: 3.7vw;
  background: #670e19;
  border-radius: 10px;
}
/* line 656, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
#container #faq_direct a {
  border-radius: 10px;
  width: 94%;
  margin: 3.7vw;
  background: #670e19;
}

/* line 665, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
table {
  width: 100%;
  text-align: center;
  background: unset;
  border: 0;
  margin: 0;
  text-align: center;
}
/* line 673, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages_red.scss */
table tr td {
  border: 0;
  background: white;
  line-height: 50px;
  font-weight: 500;
  font-size: 14px;
}
