function registerElements(e){function r(){Array.prototype.forEach.call(form.querySelectorAll("input[type='text'], input[type='email'], input[type='tel']"),function(e){e.setAttribute("disabled","true")})}var t={};e.forEach(function(e,r){e.on("change",function(e){if(e.error)error.classList.add("visible"),t[r]=e.error.message,errorMessage.innerText=e.error.message;else{t[r]=null;var o=Object.keys(t).sort().reduce(function(e,r){return e||t[r]},null);o?errorMessage.innerText=o:error.classList.remove("visible")}})}),form.addEventListener("submit",function(t){t.preventDefault(),wrapper.classList.add("submitting"),loader.style.display="block",r(),pay(stripe,e[0],clientSecret)})}var key=gon.key,stripe=window.Stripe(key),elements=stripe.elements(),clientSecret=gon.clientSecret,url="/orders/stripe_response",formClass=".payment-form-wrapper",wrapper=document.querySelector(formClass),loader=document.getElementsByClassName("progress_img")[0],form=wrapper.querySelector("form"),error=form.querySelector(".error"),errorMessage=error.querySelector(".message");!function(){"use strict";var e=stripe.elements({fonts:[{cssSrc:"https://fonts.googleapis.com/css?family=Source+Code+Pro"}],locale:window.__exampleLocale}),r=document.querySelectorAll(".cell.payment-form-wrapper .input");Array.prototype.forEach.call(r,function(e){e.addEventListener("focus",function(){e.classList.add("focused")}),e.addEventListener("blur",function(){e.classList.remove("focused")}),e.addEventListener("keyup",function(){0===e.value.length?e.classList.add("empty"):e.classList.remove("empty")})});var t={base:{color:"#32325D",fontWeight:500,fontFamily:"Source Code Pro, Consolas, Menlo, monospace",fontSize:"16px",fontSmoothing:"antialiased","::placeholder":{color:"#CFD7DF"},":-webkit-autofill":{color:"#e39f48"}},invalid:{color:"#E25950","::placeholder":{color:"#FFCCA5"}}},o={focus:"focused",empty:"empty",invalid:"invalid"},s=e.create("cardNumber",{style:t,classes:o});s.mount("#example2-card-number");var a=e.create("cardExpiry",{style:t,classes:o});a.mount("#example2-card-expiry");var n=e.create("cardCvc",{style:t,classes:o});n.mount("#example2-card-cvc"),registerElements([s,a,n],"payment-form-wrapper")}();var pay=function(e,r,t){e.confirmCardPayment(t,{payment_method:{card:r}}).then(function(e){e.error&&!e.error.message.includes("unable to authenticate")?(wrapper.classList.remove("submitting"),loader.style.display="none",error.classList.add("visible"),errorMessage.innerText=e.error.message):window.location.replace(url)})};