/*
 * Unbxd Autosuggest JS 0.0.1
 *
 * Copyright 2015, Unbxd
 *
*/
var unbxdAutoSuggestFunction=function(t,s,e){function n(t,s){this.input=t,this.init(t,s)}function r(t,s,e){var i;return function(){var o=this,n=arguments,r=function(){i=null,e||t.apply(o,n)},a=e&&!i;clearTimeout(i),i=setTimeout(r,s),a&&t.apply(o,n)}}window.Unbxd=window.Unbxd||{},Unbxd.autosuggestVersion=1,window.location.origin||(window.location.origin=window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")),Array.prototype.forEach||(Array.prototype.forEach=function(t,s){var e,i;if(null==this)throw new TypeError(" this is null or not defined");var o=Object(this),n=o.length>>>0;if("function"!=typeof t)throw new TypeError(t+" is not a function");for(arguments.length>1&&(e=s),i=0;n>i;){var r;i in o&&(r=o[i],t.call(e,r,i,o)),i++}}),Array.prototype.indexOf||(Array.prototype.indexOf=function(t,s){var e;if(null==this)throw new TypeError('"this" is null or not defined');var i=Object(this),o=i.length>>>0;if(0===o)return-1;var n=+s||0;if(1/0===Math.abs(n)&&(n=0),n>=o)return-1;for(e=Math.max(n>=0?n:o-Math.abs(n),0);o>e;){if(e in i&&i[e]===t)return e;e++}return-1});var a={Android:function(){return navigator.userAgent.match(/Android/i)},BlackBerry:function(){return navigator.userAgent.match(/BlackBerry/i)},iOS:function(){return navigator.userAgent.match(/iPhone|iPad|iPod/i)},Opera:function(){return navigator.userAgent.match(/Opera Mini/i)},Windows:function(){return navigator.userAgent.match(/IEMobile/i)},any:function(){return a.Android()||a.BlackBerry()||a.iOS()||a.Opera()||a.Windows()}};s.registerHelper("unbxdIf",function(t,s,e){return t===s?e.fn(this):e.inverse(this)}),s.registerHelper("safestring",function(t){return new s.SafeString(t)}),t.extend(n.prototype,{default_options:{siteName:"dev_mirraw_com-u1448534285001",APIKey:"56e1119f28a42ee3f7412d7542ada25fbf123",integrations:{},resultsClass:"unbxd-as-wrapper",minChars:3,delay:100,loadingClass:"unbxd-as-loading",mainWidth:0,sideWidth:180,zIndex:0,position:"absolute",sideContentOn:"right",template:"1column",theme:"#ff8400",mainTpl:["inFields","keywordSuggestions","topQueries","popularProducts"],sideTpl:[],showCarts:!0,cartType:"inline",onCartClick:function(){},hbsHelpers:null,onSimpleEnter:null,onItemSelect:null,noResultTpl:null,inFields:{count:2,fields:{brand:3,category:3,color:3},header:"",tpl:"{{{safestring highlighted}}}"},topQueries:{count:3,hidden:!1,header:"Top searches",tpl:"{{{safestring highlighted}}}"},keywordSuggestions:{count:2,header:"By Keyword",tpl:"{{{safestring highlighted}}}"},popularProducts:{count:1,price:!0,priceFunctionOrKey:"price",image:!0,imageUrlOrFunction:"imageUrl",currency:"Rs.",header:"By Popularity",view:"list",tpl:["{{#if ../showCarts}}",'{{#unbxdIf ../../cartType "inline"}}','<div class="unbxd-as-popular-product-inlinecart">','<div class="unbxd-as-popular-product-image-container">',"{{#if image}}",'<img src="{{image}}"/>',"{{/if}}","</div>",'<div  class="unbxd-as-popular-product-name">','<div style="table-layout:fixed;width:100%;display:table;">','<div style="display:table-row">','<div style="display:table-cell;text-overflow:ellipsis;overflow: hidden;white-space: nowrap;">',"{{{safestring highlighted}}}","</div>","</div>","</div>","</div>","{{#if price}}",'<div class="unbxd-as-popular-product-price">',"{{currency}}{{price}}","</div>","{{/if}}",'<div class="unbxd-as-popular-product-quantity">','<div class="unbxd-as-popular-product-quantity-container">',"<span>Qty</span>",'<input class="unbxd-popular-product-qty-input" value="1"/>',"</div>","</div>",'<div class="unbxd-as-popular-product-cart-action">','<button class="unbxd-as-popular-product-cart-button">Add to cart</button>',"</div>","</div>","{{else}}",'<div class="unbxd-as-popular-product-info">','<div class="unbxd-as-popular-product-image-container">',"{{#if image}}",'<img src="{{image}}"/>',"{{/if}}","</div>","<div>",'<div  class="unbxd-as-popular-product-name">',"{{{safestring highlighted}}}","</div>",'<div class="unbxd-as-popular-product-cart">','<div class="unbxd-as-popular-product-cart-action">','<button class="unbxd-as-popular-product-cart-button">Add to cart</button>',"</div>",'<div class="unbxd-as-popular-product-quantity">','<div class="unbxd-as-popular-product-quantity-container">',"<span>Qty</span>",'<input class="unbxd-popular-product-qty-input" value="1"/>',"</div>","</div>","{{#if price}}",'<div class="unbxd-as-popular-product-price">',"{{currency}}{{price}}","</div>","{{/if}}","</div>","</div>","</div>","{{/unbxdIf}}","{{else}}",'<div class="unbxd-as-popular-product-info">','<div class="unbxd-as-popular-product-image-container">',"{{#if image}}",'<img src="{{image}}"/>',"{{/if}}","</div>",'<div  class="unbxd-as-popular-product-name">',"{{{safestring highlighted}}}","</div>","{{#if price}}",'<div class="unbxd-as-popular-product-price">',"{{currency}}{{price}}","</div>","{{/if}}","</div>","{{/if}}"].join("")},filtered:!1,resultsContainerSelector:null,processResultsStyles:null},$input:null,$results:null,timeout:null,previous:"",activeRow:-1,activeColumn:0,keyb:!1,hasFocus:!1,lastKeyPressCode:null,ajaxCall:null,currentResults:[],currentTopResults:[],cache:{},params:{query:"*",filters:{}},selectedClass:"unbxd-ac-selected",scrollbarWidth:null,init:function(s,e){this.options=t.extend({},this.default_options,e),this.$input=t(s).attr("autocomplete","off"),this.$results=t("<div/>",{"class":this.options.resultsClass}).css("position","relative"===this.options.position?"absolute":this.options.position).hide(),this.options.zIndex>0&&this.$results.css("zIndex",this.options.zIndex),"string"==typeof this.options.resultsContainerSelector&&this.options.resultsContainerSelector.length?t(this.options.resultsContainerSelector).append(this.$results):t("body").append(this.$results),"function"==typeof this.options.hbsHelpers&&this.options.hbsHelpers.call(this),this.wire()},wire:function(){var e=this;this.$input.bind("keydown.auto",this.keyevents()),this.$input.bind("select.auto",function(){e.log("select : setting focus"),e.hasFocus=!0}),t(".unbxd-as-wrapper").on("mouseover","unbxd-as-maincontent",function(i){if(t.contains(e.$results[0],i.target)&&e.options.filtered){t("."+e.selectedClass).removeClass(e.selectedClass),t(i.target).addClass(e.selectedClass);var o=t(i.target),n=o;e.hasFocus=!1,"LI"!==i.target.tagName&&(n=o.parents("li"));var r=t(n).attr("data-value")?t(n).attr("data-value"):"",a=t(n).attr("data-filtername")?t(n).attr("data-filtername"):"",u=t(n).attr("data-filtervalue")?t(n).attr("data-filtervalue"):"";if(!n||n.hasClass("unbxd-as-header")||n.hasClass("unbxd-as-popular-product")||n.hasClass("topproducts")||"INPUT"===i.target.tagName)return;if(r){var l=r+(""!=a?":"+a+":"+u:""),p=s.compile(e.preparefilteredPopularProducts());t(".unbxd-as-sidecontent").html(e.currentTopResults[l]&&e.currentTopResults[l].length>0?p({data:e.currentTopResults[l],showCarts:e.options.showCarts,cartType:e.options.cartType}):p({data:e.currentResults.POPULAR_PRODUCTS,showCarts:e.options.showCarts,cartType:e.options.cartType}))}}}),t(document).bind("click.auto",function(s){if(s.target==e.input)e.log("clicked on input : focused"),e.hasFocus=!0,e.previous===e.$input.val()&&e.showResults();else if(s.target==e.$results[0])e.log("clicked on results block : selecting"),e.hasFocus=!1;else if(t.contains(e.$results[0],s.target)){e.log("clicked on element for selection",s.target.tagName);var i=t(s.target),o=i;if(e.hasFocus=!1,"LI"!==s.target.tagName&&(o=i.parents("li")),!o||o.hasClass(".unbxd-as-header")||"INPUT"==s.target.tagName)return;if("BUTTON"==s.target.tagName&&i.hasClass("unbxd-as-popular-product-cart-button")&&"function"==typeof e.options.onCartClick){e.log("BUTTON click");var n=o.data();return n.quantity=parseFloat(o.find("input.unbxd-popular-product-qty-input").val()),e.addToAnalytics("click",{pr:parseInt(n.index)+1,pid:n.pid||null,url:window.location.href}),e.options.onCartClick.call(e,n,e.currentResults.POPULAR_PRODUCTS[parseInt(n.index)]._original)&&e.hideResults(),void e.addToAnalytics("addToCart",{pid:n.pid||null,url:window.location.href})}e.selectItem(o.data(),s)}else e.hasFocus=!1,e.hideResults()})},keyevents:function(){var t=this;return function(s){switch(t.lastKeyPressCode=s.keyCode,t.lastKeyEvent=s,s.keyCode){case 38:s.preventDefault(),t.moveSelect(-1);break;case 40:s.preventDefault(),t.moveSelect(1);break;case 39:t.activeRow>-1&&(s.preventDefault(),t.moveSide(1));break;case 37:t.activeRow>-1&&(s.preventDefault(),t.moveSide(-1));break;case 9:case 13:t.selectCurrent(s)?s.preventDefault():t.hideResultsNow();break;default:t.activeRow=-1,t.hasFocus=!0,t.timeout&&clearTimeout(t.timeout),t.timeout=setTimeout(r(function(){t.onChange()},250),t.options.delay)}}},moveSide:function(t){var s=this.activeColumn;"2column"==this.options.template&&("left"==this.options.sideContentOn?(0==this.activeColumn&&-1==t&&(s=1),1==this.activeColumn&&1==t&&(s=0)):(0==this.activeColumn&&1==t&&(s=1),1==this.activeColumn&&-1==t&&(s=0)),s!=this.activeColumn&&(this.activeColumn=s,this.activeRow=-1,this.moveSelect(1)))},moveSelect:function(e){var i=this.$results.find("ul."+(this.activeColumn?"unbxd-as-sidecontent":"unbxd-as-maincontent")).find("li:not(.unbxd-as-header)");if(i)if(this.activeRow+=e,this.activeRow<-1?this.activeRow=i.size()-1:-1==this.activeRow?this.$input.focus():this.activeRow>=i.size()&&(this.activeRow=-1,this.$input.focus()),t("."+this.selectedClass).removeClass(this.selectedClass),t(i[this.activeRow]).addClass(this.selectedClass),this.activeRow>=0&&this.activeRow<i.size()){if(this.$input.val(t(i[this.activeRow]).data("value")),this.options.filtered&&0===this.activeColumn){var o=t(i[this.activeRow]).attr("data-value")?t(i[this.activeRow]).attr("data-value"):"",n=t(i[this.activeRow]).attr("data-filtername")?t(i[this.activeRow]).attr("data-filtername"):"",r=t(i[this.activeRow]).attr("data-filtervalue")?t(i[this.activeRow]).attr("data-filtervalue"):"",a=o+(""!=n?":"+n+":"+r:""),u=s.compile(this.preparefilteredPopularProducts());t(".unbxd-as-sidecontent").html(this.currentTopResults[a]&&this.currentTopResults[a].length>0?u({data:this.currentTopResults[a],showCarts:this.options.showCarts,cartType:this.options.cartType}):u({data:this.currentResults.POPULAR_PRODUCTS,showCarts:this.options.showCarts,cartType:this.options.cartType}))}}else if(-1==this.activeRow&&(this.$input.val(this.previous),this.options.filtered)){var u=s.compile(this.preparefilteredPopularProducts());t(".unbxd-as-sidecontent").html(this.currentTopResults[this.previous]&&this.currentTopResults[this.previous].length>0?u({data:this.currentTopResults[this.previous],showCarts:this.options.showCarts,cartType:this.options.cartType}):u({data:this.currentResults.POPULAR_PRODUCTS,showCarts:this.options.showCarts,cartType:this.options.cartType}))}},selectCurrent:function(t){var s=this.$results.find("li."+this.selectedClass),e=this;return s.length?(this.selectItem(s.data(),t),!0):("function"!=typeof this.options.onSimpleEnter||10!=this.lastKeyPressCode&&13!=this.lastKeyPressCode||(this.lastKeyEvent.preventDefault(),e.options.onSimpleEnter.call(e,t)),!1)},selectItem:function(s,e){if("value"in s){this.log("selected Item : ",s);var i=t.trim(s.value),o=this.previous;this.previous=i,this.input.lastSelected=s,this.$results.html(""),this.$input.val(i),this.hideResultsNow(this),this.addToAnalytics("search",{query:s.value,autosuggestParams:{autosuggest_type:s.type,autosuggest_suggestion:s.value,field_value:s.filtervalue||null,field_name:s.filtername||null,src_field:s.source||null,pid:s.pid||null,unbxdprank:parseInt(s.index,10)+1||0,internal_query:o}}),"function"==typeof this.options.onItemSelect&&this.options.onItemSelect.call(this,s,this.currentResults[s.type][parseInt(s.index)]._original,e)}},addToAnalytics:function(t,s){"Unbxd"in window&&"track"in window.Unbxd&&"function"==typeof window.Unbxd.track&&(this.log("Pushing data to analytics",t,s),Unbxd.track(t,s)),"search"===t&&("classical"in this.options.integrations&&this.trackclassical(t,s),"universal"in this.options.integrations&&this.trackuniversal(t,s))},getEventAction:function(t){var s={IN_FIELD:"Scope_Click",POPULAR_PRODUCTS:"Pop_Click",KEYWORD_SUGGESTION:"TQ_Click",TOP_SEARCH_QUERIES:"TQ_Click",POPULAR_PRODUCTS_FILTERED:"Filtered_Pop_Click"};return s[t]},getEventLabel:function(t){var s=t.autosuggestParams,i=s.autosuggest_suggestion,o=s.unbxdprank,n=s.field_name&&s.field_value?s.field_name+":"+s.field_value:e,r={IN_FIELD:i+(n?"&filter="+n:"")+"-"+o,POPULAR_PRODUCTS:i+"-"+o,KEYWORD_SUGGESTION:i+"-"+o,TOP_SEARCH_QUERIES:i+"-"+o,POPULAR_PRODUCTS_FILTERED:i+"-"+o};return r[s.autosuggest_type]},trackclassical:function(t,s){var e=this.options.integrations.classical,i=this.getEventAction(s.autosuggestParams.autosuggest_type),o=this.getEventLabel(s),n=1;e&&(e===!0&&(e="_gaq"),window[e]&&window[e].push(["_trackEvent","U_Autocomplete",i,o,n,!0]))},trackuniversal:function(t,s){var e=this.options.integrations.universal,i=this.getEventAction(s.autosuggestParams.autosuggest_type),o=this.getEventLabel(s),n=1;e&&(e===!0&&(e="ga"),window[e]&&window[e]("send","event","U_Autocomplete",i,o,n,{nonInteraction:1}))},showResults:function(){this.options.width&&(this.options.mainWidth=this.options.width);var t=this.$input.offset(),s=this.options.mainWidth>0?this.options.mainWidth:this.$input.innerWidth(),e=parseInt(this.$input.css("border-top-width"),10),i=parseInt(this.$input.css("border-left-width"),10),o=parseInt(this.$input.css("border-right-width"),10),n=(parseInt(this.$input.css("padding-bottom"),10),parseInt(s)-2+i+o),r={top:t.top+(isNaN(e)?0:e)+this.$input.innerHeight()+"px",left:t.left+"px"};this.$results.find("ul.unbxd-as-maincontent").css("width",n+"px"),null==this.scrollbarWidth&&this.setScrollWidth(),"2column"==this.options.template&&(this.$results.find("ul.unbxd-as-sidecontent").css("width",this.options.sideWidth+"px"),this.$results.removeClass("unbxd-as-extra-left unbxd-as-extra-right"),this.$results.addClass("unbxd-as-extra-"+this.options.sideContentOn),this.$results.find("ul.unbxd-as-sidecontent").length>0&&"left"==this.options.sideContentOn&&(r.left=t.left-this.options.sideWidth+"px")),this.options.showCarts&&this.$results.find(".unbxd-as-popular-product-cart-button").css("background-color",this.options.theme),"function"==typeof this.options.processResultsStyles&&(r=this.options.processResultsStyles.call(this,r)),this.$results.css(r).show()},setScrollWidth:function(){var t=document.createElement("div");t.setAttribute("style","width: 100px;height: 100px;overflow: scroll;position: absolute;top: -9999px;"),document.body.appendChild(t),this.scrollbarWidth=t.offsetWidth-t.clientWidth,document.body.removeChild(t)},hideResults:function(){this.timeout&&clearTimeout(this.timeout);var t=this;this.timeout=setTimeout(function(){t.hideResultsNow()},200)},hideResultsNow:function(){this.log("hideResultsNow"),this.timeout&&clearTimeout(this.timeout),this.$input.removeClass(this.options.loadingClass),this.$results.is(":visible")&&this.$results.hide(),this.ajaxCall&&this.ajaxCall.abort()},addFilter:function(t,s){return t in this.params.filters||(this.params.filters[t]={}),this.params.filters[t][s]=t,this},removeFilter:function(t,s){return s in this.params.filters[t]&&delete this.params.filters[t][s],0==Object.keys(this.params.filters[t]).length&&delete this.params.filters[t],this},clearFilters:function(){return this.params.filters={},this},onChange:function(){if(46==this.lastKeyPressCode||this.lastKeyPressCode>8&&this.lastKeyPressCode<32)return 27==this.lastKeyPressCode&&"object"==typeof this.input.lastSelected&&this.$input.val(this.input.lastSelected.value),this.$results.hide();var t=this.$input.val();t!=this.previous&&(this.params.q=t,this.previous=t,this.currentResults={},this.inCache(t)?(this.log("picked from cache : "+t),this.currentResults=this.getFromCache(t),this.$results.html(this.prepareHTML()),this.showResults()):(this.ajaxCall&&this.ajaxCall.abort(),t.length>=this.options.minChars?(this.$input.addClass(this.options.loadingClass),this.requestData(t)):(this.$input.removeClass(this.options.loadingClass),this.$results.hide())))},getClass:function(t){return Object.prototype.toString.call(t).match(/^\[object\s(.*)\]$/)[1]},requestData:function(){var s=this,e=s.autosuggestUrl();this.log("requestData",e),this.ajaxCall=t.ajax({url:e,dataType:"jsonp",jsonp:"json.wrf"}).done(function(t){s.receiveData(t)}).fail(function(){s.$input.removeClass(s.options.loadingClass),s.$results.hide()})},autosuggestUrl:function(){var t=this.getHostNPath(),s="q="+encodeURIComponent(this.params.q);s+=this.options.maxSuggestions?"&inFields.count="+this.options.maxSuggestions+"&topQueries.count="+this.options.maxSuggestions+"&keywordSuggestions.count="+this.options.maxSuggestions+"&popularProducts.count="+this.options.popularProducts.count:"&inFields.count="+this.options.inFields.count+"&topQueries.count="+this.options.topQueries.count+"&keywordSuggestions.count="+this.options.keywordSuggestions.count+"&popularProducts.count="+this.options.popularProducts.count;for(var e in this.params.filters)if(this.params.filters.hasOwnProperty(e)){var i=[];for(var o in this.params.filters[e])this.params.filters[e].hasOwnProperty(o)&&i.push((e+':"'+encodeURIComponent(o.replace(/(^")|("$)/g,""))+'"').replace(/\"{2,}/g,'"'));s+="&filter="+i.join(" OR ")}return t+"?"+s},getHostNPath:function(){return"//search.unbxdapi.com/"+this.options.APIKey+"/"+this.options.siteName+"/autosuggest"},receiveData:function(t){if(t){if(this.$input.removeClass(this.options.loadingClass),this.$results.html(""),(!this.hasFocus||0==t.response.numberOfProducts||"error"in t)&&!this.options.noResultTpl)return this.hideResultsNow(this);this.processData(t),this.addToCache(this.params.q,this.currentResults),this.$results.html(this.prepareHTML()),this.showResults()}else this.hideResultsNow(this)},max_suggest:function(t){for(var s=0,e=0,i=0,o=Math.floor(.2*this.options.maxSuggestions),n=Math.floor(.4*this.options.maxSuggestions),r=Math.ceil(.4*this.options.maxSuggestions),a=0,u=0,l=0;l<t.response.products.length;l++)"IN_FIELD"==t.response.products[l].doctype?s++:"KEYWORD_SUGGESTION"==t.response.products[l].doctype?i++:"TOP_SEARCH_QUERIES"==t.response.products[l].doctype&&e++;if(o>s){for(var p=o-s;p>0;)i>n?i-n>=p?(n+=p,p=0):(p=p-i+n,n=i):e>r?e-r>=p?(r+=p,p=0):(p=p-e+r,r=e):p=0;o=s}if(r>e){for(var u=r-e;u>0&&i>n;)i>n&&(i-n>=u?(n+=u,u=0):(u=u-i+n,n=i));r=e}if(n>i){for(a=n-i;a>0&&e>r;)e>r&&(e-r>=a?(r+=a,a=0):(a=a-e+r,r=e));n=i}var c={};return c.infields=o,c.topquery=r,c.keyword=n,c.key_rem=a,c.top_rem=u,c},isUnique:function(t,s){try{t=t.toLowerCase();for(var e=!0,i=0;i<s.length;i++){var o=s[i];if(Math.abs(o.length-t.length)<3&&(-1!=o.indexOf(t)||-1!=t.indexOf(o))){e=!1;break}}return e&&s.push(t),e}catch(n){return!0}},isTempUnique:function(t,s){return t=t.toLowerCase(),-1===s.indexOf(t)?s.push(t):!1},getfilteredPopularProducts:function(){var s=this,e="http://search.unbxdapi.com/"+this.options.APIKey+"/"+this.options.siteName+"/search?q="+encodeURIComponent(this.params.q)+"&rows="+this.options.popularProducts.count;t.ajax({url:e,dataType:"jsonp",jsonp:"json.wrf"}).done(function(t){var e=s.params.q;s.processfilteredPopularProducts(e,t)});for(i in this.currentResults)if("POPULAR_PRODUCTS"!=i)for(j in this.currentResults[i]){if(this.currentResults[i][j].filtername)var e="http://search.unbxdapi.com/"+this.options.APIKey+"/"+this.options.siteName+"/search?q="+encodeURIComponent(this.currentResults[i][j].autosuggest)+"&filter="+this.currentResults[i][j].filtername+":"+encodeURIComponent(this.currentResults[i][j].filtervalue)+"&rows="+this.options.popularProducts.count;else var e="http://search.unbxdapi.com/"+this.options.APIKey+"/"+this.options.siteName+"/search?q="+encodeURIComponent(this.currentResults[i][j].autosuggest)+"&rows="+this.options.popularProducts.count;t.ajax({url:e,dataType:"jsonp",jsonp:"json.wrf"}).done(function(t){var e=t.searchMetaData.queryParams.q+(t.searchMetaData.queryParams.filter?":"+t.searchMetaData.queryParams.filter:"");s.processfilteredPopularProducts(e,t)})}},processfilteredPopularProducts:function(t,s){this.currentTopResults[t]=[];for(var e=0;e<s.response.products.length;e++){var i=s.response.products[e];o={autosuggest:this.options.popularProducts.autosuggestName?i[this.options.popularProducts.autosuggestName]:i.title?i.title:"",highlighted:this.highlightStr(i.title),_original:i,type:"POPULAR_PRODUCTS_FILTERED"},this.options.popularProducts.price&&(o.price="function"==typeof this.options.popularProducts.priceFunctionOrKey?this.options.popularProducts.priceFunctionOrKey(i):"string"==typeof this.options.popularProducts.priceFunctionOrKey&&this.options.popularProducts.priceFunctionOrKey?this.options.popularProducts.priceFunctionOrKey in i?i[this.options.popularProducts.priceFunctionOrKey]:null:"price"in i?i.price:null,this.options.popularProducts.currency&&(o.currency=this.options.popularProducts.currency)),this.options.popularProducts.image&&("function"==typeof this.options.popularProducts.imageUrlOrFunction?o.image=this.options.popularProducts.imageUrlOrFunction(i):"string"==typeof this.options.popularProducts.imageUrlOrFunction&&this.options.popularProducts.imageUrlOrFunction&&(o.image=this.options.popularProducts.imageUrlOrFunction in i?i[this.options.popularProducts.imageUrlOrFunction]:null)),this.currentTopResults[t].push(o)}},processTopSearchQuery:function(t){o={autosuggest:t.autosuggest,highlighted:this.highlightStr(t.autosuggest),type:"TOP_SEARCH_QUERIES",_original:t.doctype},this.currentResults.TOP_SEARCH_QUERIES.push(o)},processKeywordSuggestion:function(t){o={autosuggest:t.autosuggest,highlighted:this.highlightStr(t.autosuggest),type:"KEYWORD_SUGGESTION",_original:t,source:t.unbxdAutosuggestSrc||""},this.currentResults.KEYWORD_SUGGESTION.push(o)},processPopularProducts:function(t){o={autosuggest:t.autosuggest,highlighted:this.highlightStr(t.autosuggest),type:t.doctype,pid:t.uniqueId.replace("popularProduct_",""),_original:t},this.options.popularProducts.price&&(o.price="function"==typeof this.options.popularProducts.priceFunctionOrKey?this.options.popularProducts.priceFunctionOrKey(t):"string"==typeof this.options.popularProducts.priceFunctionOrKey&&this.options.popularProducts.priceFunctionOrKey?this.options.popularProducts.priceFunctionOrKey in t?t[this.options.popularProducts.priceFunctionOrKey]:null:"price"in t?t.price:null,this.options.popularProducts.currency&&(o.currency=this.options.popularProducts.currency)),this.options.popularProducts.image&&("function"==typeof this.options.popularProducts.imageUrlOrFunction?o.image=this.options.popularProducts.imageUrlOrFunction(t):"string"==typeof this.options.popularProducts.imageUrlOrFunction&&this.options.popularProducts.imageUrlOrFunction&&(o.image=this.options.popularProducts.imageUrlOrFunction in t?t[this.options.popularProducts.imageUrlOrFunction]:null)),this.currentResults.POPULAR_PRODUCTS.push(o)},processInFields:function(s){var e={},i=" "+s.unbxdAutosuggestSrc+" ",o=this.highlightStr(s.autosuggest);for(var n in this.options.inFields.fields)n+"_in"in s&&s[n+"_in"].length&&-1==i.indexOf(" "+n+" ")&&(e[n]=s[n+"_in"].slice(0,parseInt(this.options.inFields.fields[n])));if(t.isEmptyObject(e))this.currentResults.KEYWORD_SUGGESTION.push({autosuggest:s.autosuggest,highlighted:o,type:"KEYWORD_SUGGESTION",source:s.unbxdAutosuggestSrc});else{this.currentResults.IN_FIELD.push({autosuggest:s.autosuggest,highlighted:o,type:"keyword",source:s.unbxdAutosuggestSrc}),infieldsCount++;for(var n in e)for(var r=0;r<e[n].length;r++)""!==e[n][r]&&this.currentResults.IN_FIELD.push({autosuggest:s.autosuggest,highlighted:e[n][r],type:s.doctype,filtername:n,filtervalue:e[n][r],_original:s,source:s.unbxdAutosuggestSrc})}},processData:function(t){var s;this.options.maxSuggestions&&(s=this.max_suggest(t)),this.currentResults={KEYWORD_SUGGESTION:[],TOP_SEARCH_QUERIES:[],POPULAR_PRODUCTS:[],IN_FIELD:[]},infieldsCount=0;for(var e=[],i=[],o=0;o<t.response.products.length;o++){var n=t.response.products[o];this.options.maxSuggestions?"TOP_SEARCH_QUERIES"==n.doctype&&s.topquery>this.currentResults.TOP_SEARCH_QUERIES.length&&this.isUnique(n.autosuggest,i)?this.processTopSearchQuery(n):"IN_FIELD"==n.doctype&&s.infields+s.key_rem+s.top_rem>infieldsCount&&this.isUnique(n.autosuggest,e)?s.infields>infieldsCount?this.processInFields(n):s.key_rem+s.top_rem>this.currentResults.KEYWORD_SUGGESTION.length&&this.isUnique(n.autosuggest,i)&&this.processKeywordSuggestion(n):"KEYWORD_SUGGESTION"==n.doctype&&s.keyword>this.currentResults.KEYWORD_SUGGESTION.length&&this.isUnique(n.autosuggest,e)?this.processKeywordSuggestion(n):"POPULAR_PRODUCTS"==n.doctype&&this.options.popularProducts.count>this.currentResults.POPULAR_PRODUCTS.length&&this.processPopularProducts(n):"TOP_SEARCH_QUERIES"==n.doctype&&this.options.topQueries.count>this.currentResults.TOP_SEARCH_QUERIES.length&&this.isUnique(n.autosuggest,i)?this.processTopSearchQuery(n):"IN_FIELD"==n.doctype&&this.options.inFields.count>infieldsCount&&this.isTempUnique(n.autosuggest,e)?this.processInFields(n):"KEYWORD_SUGGESTION"==n.doctype&&this.options.keywordSuggestions.count>this.currentResults.KEYWORD_SUGGESTION.length&&this.isUnique(n.autosuggest,i)?this.processKeywordSuggestion(n):"POPULAR_PRODUCTS"==n.doctype&&this.options.popularProducts.count>this.currentResults.POPULAR_PRODUCTS.length&&this.processPopularProducts(n)}this.options.filtered&&this.getfilteredPopularProducts(),outLength=this.currentResults.POPULAR_PRODUCTS.length+this.currentResults.IN_FIELD.length},escapeStr:function(t){return t.replace(/([\\{}()|.?*+\-\^$\[\]])/g,"\\$1")},highlightStr:function(s){var e=s,i=t.trim(this.params.q+"");if(i.indexOf(" ")){var o=i.split(" ");for(var n in o)if(o.hasOwnProperty(n)){var r=e.toLowerCase().lastIndexOf("</strong>");-1!=r&&(r+=9),e=e.substring(0,r)+e.substring(r).replace(new RegExp(this.escapeStr(o[n]),"gi"),function(t){return"<strong>"+t+"</strong>"})}}else{var a=e.toLowerCase().indexOf(i);e=a>=0?e.substring(0,a)+"<strong>"+e.substring(a,a+i.length)+"</strong>"+e.substring(a+i.length):e}return e},prepareinFieldsHTML:function(){return"{{#if data.IN_FIELD}}"+(this.options.inFields.header?'<li class="unbxd-as-header">'+this.options.inFields.header+"</li>":"")+'{{#each data.IN_FIELD}}{{#unbxdIf type "keyword"}}<li class="unbxd-as-keysuggestion" data-index="{{@index}}" data-value="{{autosuggest}}" data-type="IN_FIELD" data-source="{{source}}">'+(this.options.inFields.tpl?this.options.inFields.tpl:this.default_options.inFields.tpl)+'</li>{{else}}<li class="unbxd-as-insuggestion" style="color:'+this.options.theme+';" data-index="{{@index}}" data-type="{{type}}" data-value="{{autosuggest}}" data-filtername="{{filtername}}" data-filtervalue="{{filtervalue}}"  data-source="{{source}}">in '+(this.options.inFields.tpl?this.options.inFields.tpl:this.default_options.inFields.tpl)+"</li>{{/unbxdIf}}{{/each}}{{/if}}"},preparekeywordSuggestionsHTML:function(){return"{{#if data.KEYWORD_SUGGESTION}}"+(this.options.keywordSuggestions.header?'<li class="unbxd-as-header">'+this.options.keywordSuggestions.header+"</li>":"")+'{{#each data.KEYWORD_SUGGESTION}}<li class="unbxd-as-keysuggestion" data-value="{{autosuggest}}" data-index="{{@index}}" data-type="{{type}}"  data-source="{{source}}">'+(this.options.keywordSuggestions.tpl?this.options.keywordSuggestions.tpl:this.default_options.keywordSuggestions.tpl)+"</li>{{/each}}{{/if}}"},preparetopQueriesHTML:function(){return"{{#if data.TOP_SEARCH_QUERIES}}"+(this.options.topQueries.header?'<li class="unbxd-as-header">'+this.options.topQueries.header+"</li>":"")+'{{#each data.TOP_SEARCH_QUERIES}}<li class="unbxd-as-keysuggestion" data-type="{{type}}" data-index="{{@index}}" data-value="{{autosuggest}}">'+(this.options.topQueries.tpl?this.options.topQueries.tpl:this.default_options.topQueries.tpl)+"</li>{{/each}}{{/if}}"},preparefilteredPopularProducts:function(){return(this.options.popularProducts.header?'<li class="unbxd-as-header">'+this.options.popularProducts.header+"</li>":"")+'{{#data}}<li class="unbxd-as-popular-product '+("grid"===this.options.popularProducts.view?"unbxd-as-popular-product-grid":"")+'" data-value="{{autosuggest}}" data-index="{{@index}}" data-type="{{type}}" data-pid="{{pid}}" >'+(this.options.popularProducts.tpl?this.options.popularProducts.tpl:this.default_options.popularProducts.tpl)+"</li>{{/data}}"},preparepopularProductsHTML:function(){return"{{#if data.POPULAR_PRODUCTS}}"+(this.options.popularProducts.header?'<li class="unbxd-as-header">'+this.options.popularProducts.header+"</li>":"")+'{{#data.POPULAR_PRODUCTS}}<li class="unbxd-as-popular-product '+("grid"===this.options.popularProducts.view?"unbxd-as-popular-product-grid":"")+'" data-value="{{autosuggest}}" data-index="{{@index}}" data-type="{{type}}" data-pid="{{pid}}" >'+(this.options.popularProducts.tpl?this.options.popularProducts.tpl:this.default_options.popularProducts.tpl)+"</li>{{/data.POPULAR_PRODUCTS}}{{/if}}"},prepareHTML:function(){var t='<ul class="unbxd-as-maincontent">',e=this,i=0,o=0;e.currentResults.IN_FIELD.length||e.currentResults.KEYWORD_SUGGESTION.length||e.currentResults.POPULAR_PRODUCTS.length||e.currentResults.TOP_SEARCH_QUERIES.length||!this.options.noResultTpl||("function"==typeof this.options.noResultTpl?t=t+"<li>"+this.options.noResultTpl.call(e,encodeURIComponent(e.params.q))+"</li>":"string"==typeof this.options.noResultTpl&&(t=t+"<li>"+this.options.noResultTpl+"</li>")),this.options.mainTpl.forEach(function(t){t="inFields"===t?"IN_FIELD":"popularProducts"===t?"POPULAR_PRODUCTS":"topQueries"===t?"TOP_SEARCH_QUERIES":"KEYWORD_SUGGESTION",i+=e.currentResults[t].length}),this.options.sideTpl.forEach(function(t){t="inFields"===t?"IN_FIELD":"popularProducts"===t?"POPULAR_PRODUCTS":"topQueries"===t?"TOP_SEARCH_QUERIES":"KEYWORD_SUGGESTION",o+=e.currentResults[t].length}),a.any()&&(this.options.template="1column"),"2column"!==this.options.template||this.options.sideTpl.length||this.options.mainTpl||(this.options.sideTpl=["keywordSuggestions","topQueries"],this.options.mainTpl=["inFields","popularProducts"]),"2column"===this.options.template&&(0==i&&0!=o?this.options.sideTpl.forEach(function(s){s="prepare"+s+"HTML",t+=e[s]()}):0!=o&&(t='<ul class="unbxd-as-sidecontent">',this.options.sideTpl.forEach(function(s){s="prepare"+s+"HTML",t+=e[s]()}),t+='</ul><ul class="unbxd-as-maincontent">')),this.options.mainTpl.forEach(function(s){s="prepare"+s+"HTML",t+=e[s]()}),t+="</ul>";var n=s.compile(t);return this.log("prepraing html :-> template : "+this.options.template+" ,carts : "+this.options.showCarts+" ,cartType : "+this.options.cartType),this.log("html data : ",this.currentResults),n({data:this.currentResults,showCarts:this.options.showCarts,cartType:this.options.cartType})},addToCache:function(s,e){s in this.cache||(this.cache[s]=t.extend({},e))},inCache:function(t){return t in this.cache&&this.cache.hasOwnProperty(t)},getFromCache:function(t){return this.cache[t]},destroy:function(t){t.$input.unbind(".auto"),t.input.lastSelected=null,t.$input.removeAttr("autocomplete","off"),t.$results.remove(),t.$input.removeData("autocomplete")},setOption:function(t,s){var e=t.split(".");if(e.length>1){for(var i=this.options,o=0;o<e.length-1;o++)e[o]in i||(i[e[o]]={}),i=i[e[o]];i[e[e.length-1]]=s}else this.options[t]=s;this.previous="",this.$results.html(""),this.cache={},this.cache.length=0},log:function(){}}),t.fn.unbxdautocomplete=function(t){return this.each(function(){try{this.auto=new n(this,t)}catch(s){}})}};