@charset "UTF-8";
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 11, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.carts_show.page #container {
  margin-top: 0 !important;
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.carts_show.page .font-gray {
  color: #303030;
}

/* line 18, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.enableLink {
  color: #c13960 !important;
  font-weight: bold !important;
  cursor: pointer !important;
}

/* line 24, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block h2.heading {
  color: #303030;
}
/* line 27, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion {
  padding-left: 0em;
  color: #303030;
  margin-left: 0em;
  font-size: 14px;
  padding-right: 0;
}
/* line 34, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation > a {
  background: inherit;
  color: inherit;
  padding: 0rem;
  font-size: 0.8125rem;
}
/* line 39, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation > a:before {
  content: '»';
  float: left;
  color: #670b19;
  font-size: 14px;
  font-weight: 700;
  padding-right: 2px;
  margin-top: -2px;
}
/* line 49, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation .text-right {
  float: right;
}
/* line 52, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation > .content {
  padding: 0rem;
}
/* line 54, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation > .content.active {
  background: inherit;
}
/* line 58, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block ul.addons-notes.accordion .accordion-navigation.active > a:before {
  content: '«';
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block {
  background-color: #f4f4f4;
  margin-bottom: 1em;
  padding: 0.5em 0em;
  margin-left: 0;
  margin-right: 0;
}
/* line 69, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .image-box {
  padding: 0 5px 0 0px;
}
/* line 71, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .image-box .truncate {
  color: #303030;
  text-align: center;
  padding: 5px;
}
/* line 77, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .design_quantity {
  color: #303030;
}
/* line 79, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .design_quantity .quantity_list {
  width: 42%;
}
/* line 83, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .design-title {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 89, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .right {
  padding-right: 2px;
  text-align: right;
}
/* line 93, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .item-price-font {
  font-size: 12px;
}
/* line 97, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .action-buttons-container {
  border-top: 1px solid #9c9c9c;
  height: 35px;
  text-align: center;
  font-size: 0.8rem;
  text-transform: uppercase;
  font-weight: bold;
}
/* line 104, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .action-buttons-container .action-button {
  margin: 5px 0;
  padding: 5px 0;
}
/* line 108, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .action-buttons-container .action-button-cart {
  border-right: 1px solid #9c9c9c;
}
/* line 110, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .action-buttons-container .action-button-cart a {
  color: black;
}
/* line 115, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-addon-checkbox {
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  width: 22px;
  height: 22px;
  border: 2px solid grey;
  border-radius: 2px;
  margin: 0.3em;
}
/* line 125, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-addon-checkbox:not(:checked):after {
  content: "+";
  color: grey;
  padding: 0 0.25em;
  font-size: 1.3em;
  font-family: 'Lato Black';
}
/* line 133, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-addon-checkbox:checked {
  border: 2px solid #670b19;
  background: #670b19;
}
/* line 136, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-addon-checkbox:checked:after {
  content: "✔";
  color: white;
  padding: 0 0.15em;
  font-size: 1em;
  font-family: 'Lato Black';
}
/* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-bag-addon {
  margin: 0;
}
/* line 148, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .saree-bag-addon-quantity {
  width: 80% !important;
  margin: 0.3em 0;
}
/* line 152, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item_block .design-pr {
  margin: 0.4em 0;
}
/* line 156, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .close {
  margin-top: -6px;
  line-height: 1em;
  font-size: 22px;
  color: #303030;
}
/* line 163, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block {
  margin-top: 0px !important;
}
/* line 165, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button {
  right: 0;
  left: 0;
  top: auto;
  background-color: white;
  padding: 0px !important;
  user-select: none;
  -o-user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
}
/* line 176, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button a {
  margin: 0 !important;
  color: white;
}
/* line 180, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button .cart_checkout_button {
  background-color: #8f1b1d;
  padding: 14px !important;
}
/* line 184, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button .cart-checkout-btn {
  padding: 0px;
}
/* line 187, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button .view_details_button {
  background-color: #ffffff;
  color: #303030;
  font-size: 16px;
  margin-top: 3px !important;
}
/* line 192, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button .view_details_button .view_details_text {
  font-size: 12px;
  letter-spacing: 1px;
  font-weight: normal;
  margin-top: 7px;
}
/* line 199, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block #fixed_checkout_button .button {
  padding: 0px;
  margin: 0px;
  width: 100%;
  font-weight: bold;
  font-size: 0.875rem;
}
/* line 207, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .fixed_cart_button {
  position: fixed;
  bottom: 0;
  z-index: 10;
}
/* line 212, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .save_cart {
  border-bottom: 1px solid grey;
  font-size: 0.8125rem;
}
/* line 215, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .save_cart span {
  vertical-align: -webkit-baseline-middle;
}
/* line 219, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading {
  padding: 0px;
}
/* line 221, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading .save_email {
  padding: 8px 10px;
  margin-bottom: 0px;
  background-color: #670b19;
  border: none;
  font-size: 0.75rem;
  float: right;
  text-transform: uppercase;
}
/* line 230, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading .save_email_box {
  padding: 0px 5px 0px 5px;
  margin: 0px 0px 0px 5px;
  border: none;
  box-shadow: 0px 3px 1px -1px grey;
  line-height: 0px;
  width: 95%;
}
/* line 238, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading input[type="email"] {
  display: inline;
  padding: 0.1rem 0.5rem;
  margin: 0px;
}
/* line 242, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading input[type="email"]:focus {
  box-shadow: 0 1px 0 0 #670b19;
  border-bottom: 1px solid #670b19;
}
/* line 247, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading .email_form {
  display: none;
}
/* line 250, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_heading .email_display {
  padding: 1px 2px;
  font-size: 0.9rem;
  display: none;
}
/* line 256, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content {
  padding: 1em 0px 0px 0px;
  font-size: 0.8125rem;
}
/* line 259, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content img {
  width: 100%;
}
/* line 262, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content .minimum_cart_value_message {
  background-color: #b11f2d;
  color: white;
  padding: 12px;
  font-size: 13px;
  border-radius: 3px;
  margin-bottom: 10px;
}
/* line 270, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content .cart-discount-message {
  margin: 2px 0px;
}
/* line 272, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content .cart-discount-message h6.text-center {
  margin-bottom: 0.2rem;
}
/* line 276, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content .design_quantity {
  padding: 0;
}
/* line 278, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .panel_block .panel_content .design_quantity select {
  padding: 0.1em;
  height: inherit;
}
/* line 298, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .blue-link {
  color: #4c4b4b;
}
/* line 302, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block #totals_block table {
  border: none !important;
  float: right;
  width: 90%;
  background: none !important;
  margin-bottom: 0px !important;
}
/* line 309, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block #totals_block tr {
  background: none !important;
}
/* line 312, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block #totals_block td, #carts_block #totals_block th {
  text-align: right !important;
  padding: 0px !important;
}
/* line 316, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block #totals_block .add_more_items {
  float: right;
}
/* line 318, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block #totals_block .add_more_items .charges {
  color: #670e19;
}
/* line 323, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item-total {
  font-size: 95%;
  padding-left: 0px !important;
  padding-right: 0px !important;
}
/* line 328, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .coupon, #carts_block .wallet {
  color: #303030;
  background: transparent;
  border: 1px solid #b7b6b6;
  height: 2.3125rem;
  padding: 0.5rem 0.75rem 0.5rem 0.75rem;
}
/* line 335, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block input[name=coupon_code], #carts_block .small-4 > .button {
  margin-bottom: 0.5rem;
  border: none;
  border-bottom: 1px solid #9e9e9e;
  box-shadow: none;
}
/* line 340, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block input[name=coupon_code]:focus, #carts_block .small-4 > .button:focus {
  box-shadow: 0 1px 0 0 #670b19;
  border-bottom: 1px solid #670b19;
}
/* line 344, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block input[name=coupon_code] .small-4 > .button, #carts_block .small-4 > .button .small-4 > .button {
  padding: 0px;
  box-shadow: 0px 3px 1px -1px grey;
}
/* line 348, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block input[name=coupon_code] .coupon-box, #carts_block input[name=coupon_code] .wallet-box, #carts_block .small-4 > .button .coupon-box, #carts_block .small-4 > .button .wallet-box {
  display: none;
}
/* line 352, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .item-text {
  padding: 0px;
}
/* line 355, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#carts_block .featured_products > *:first-child {
  position: static;
}

/* line 360, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.apply_wallet .usable-reward-text {
  font-size: 14px;
  padding-right: 10px;
}
/* line 363, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.apply_wallet .usable-reward-text span {
  font-weight: bold;
}

/* line 368, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.coupon-box, .wallet-box {
  display: none;
}

/* line 371, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.use-msg {
  font-size: 12px;
}
/* line 373, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.use-msg .apply-btn-link {
  background: transparent;
  border: none !important;
  padding: 0px !important;
}
/* line 377, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.use-msg .apply-btn-link:focus {
  outline: none;
}

/* line 380, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.giftwrap {
  border: 1px solid #b7b6b7;
  margin-bottom: 15px;
  text-align: center;
  padding: 5px;
}
/* line 385, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.giftwrap .giftwrapped-text {
  border-right: 1px solid;
}
/* line 388, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.giftwrap .giftwrap-remove {
  color: #670b19 !important;
}
/* line 392, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.giftwrap a.giftwrapped .giftwrapped-col {
  color: #303030;
}
/* line 396, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.giftwrap .f_gift_wrap {
  background: url(/assets/sprite-f7e7721dd2d9b1d316fd41711a5c2fd08099db15a0dd34b4ac115bc94c6e17b8.png) no-repeat;
  -webkit-filter: invert(45%);
  -moz-filter: invert(45%);
  -o-filter: invert(45%);
  -ms-filter: invert(45%);
  filter: invert(45%);
  display: inline-block;
  width: 20px;
  height: 18px;
  background-position: 28% 0;
  background-size: 110px;
}

/* line 410, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.design_offer_panel_content {
  padding: 0px;
}

/* line 413, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer {
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  text-align: center;
  padding: 0px;
  font-size: 15px;
  background-color: #ffffff;
  display: flex;
  color: #303030;
  font-size: 14px;
}
/* line 424, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer .info_icon {
  border-radius: 50%;
  background-color: #670b19;
  padding: 1px 8px;
  font-size: 13px;
  font-style: italic;
  font-weight: 700;
  font-family: 'Times New Roman';
  color: #ffffff;
  height: 50%;
  margin-left: 5px;
}
/* line 436, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer .design_offer_cart_offer_msg {
  color: white;
  background: #8f1c1e;
}
/* line 439, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer .design_offer_cart_offer_msg a {
  font-style: italic;
  color: white;
  font-family: 'Times New Roman';
  text-decoration: underline;
}
/* line 446, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer span {
  margin-left: 5px;
}
/* line 448, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.best_offer span .hyperlink_text {
  font-style: italic;
  text-decoration: underline;
}

/* line 454, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.offer_messages_block {
  background-color: #f1f1f1;
  padding: 5px;
}
/* line 457, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.offer_messages_block li {
  padding: 5px;
}
/* line 460, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.offer_messages_block i {
  font-size: 12px;
}

/* The Modal (background) */
/* line 466, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.modal {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: #ffffff;
}

/* line 479, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
.reveal-modal-bg {
  background: rgba(0, 0, 0, 0.45);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 99;
  left: 0;
}

/* line 491, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal, #qpmTncModal, #pdTncModal, #shippingTncModal {
  display: none;
  background-color: transparent;
  height: 100%;
  overflow: auto;
  width: 90%;
  margin: 0px 5%;
  outline: none;
  top: 0 !important;
}
/* line 501, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-content, #qpmTncModal .modal-dialog .modal-content, #pdTncModal .modal-dialog .modal-content, #shippingTncModal .modal-dialog .modal-content {
  height: 100%;
  background-color: #ffffff;
  color: #303030;
  font-size: 1.25em;
  margin-top: 45px;
}
/* line 507, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-header, #qpmTncModal .modal-dialog .modal-content .modal-header, #pdTncModal .modal-dialog .modal-content .modal-header, #shippingTncModal .modal-dialog .modal-content .modal-header {
  float: right;
  position: absolute;
  right: 5px;
  font-size: 30px;
}
/* line 513, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body, #qpmTncModal .modal-dialog .modal-content .modal-body, #pdTncModal .modal-dialog .modal-content .modal-body, #shippingTncModal .modal-dialog .modal-content .modal-body {
  padding: 16px;
  font-size: 12px;
}
/* line 517, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body .modal-text .ans, #qpmTncModal .modal-dialog .modal-content .modal-body .modal-text .ans, #pdTncModal .modal-dialog .modal-content .modal-body .modal-text .ans, #shippingTncModal .modal-dialog .modal-content .modal-body .modal-text .ans {
  margin-bottom: 20px;
}
/* line 520, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body .modal-text ul, #qpmTncModal .modal-dialog .modal-content .modal-body .modal-text ul, #pdTncModal .modal-dialog .modal-content .modal-body .modal-text ul, #shippingTncModal .modal-dialog .modal-content .modal-body .modal-text ul {
  font-size: 12px;
}
/* line 526, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-footer, #qpmTncModal .modal-dialog .modal-footer, #pdTncModal .modal-dialog .modal-footer, #shippingTncModal .modal-dialog .modal-footer {
  text-align: center;
  background: #f4f4f4;
  padding: 15px 0px;
}
/* line 530, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/carts_red.scss */
#bmgnTncModal .modal-dialog .modal-footer a, #qpmTncModal .modal-dialog .modal-footer a, #pdTncModal .modal-dialog .modal-footer a, #shippingTncModal .modal-dialog .modal-footer a {
  padding: 20px 0px;
}

/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 10, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend, .recommended-design-box {
  border-top: 1px solid #636060;
  border-bottom: 1px solid #636060;
  margin: 0 auto;
  background-color: transparent;
  box-shadow: none;
  max-width: 65.5rem;
  padding: 8px 0px;
}
/* line 19, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .title-block h1, .recommended-design-box .title-block h1 {
  color: #303030;
  font-size: 18px;
  text-transform: capitalize;
}

/* line 27, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader {
  display: inline-block;
  width: 49%;
  margin-bottom: 10px;
}
/* line 31, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader .image-container {
  text-align: center;
  background-color: #ffffff;
  padding: 15px 0px;
}
/* line 35, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader .image-container img {
  animation: spin 8s ease-in-out infinite;
}
/* line 40, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .product-1 {
  padding-right: 4px;
}
/* line 43, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .product-2 {
  padding-left: 4px;
}

/* line 48, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.no-design-box {
  text-align: center;
  font-style: italic;
}

/* line 53, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;
}
/* line 58, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products {
  margin: 0px;
}
/* line 60, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list {
  display: inline-block;
  margin-right: 8px;
  width: 153px;
}
/* line 64, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box {
  position: relative;
  /*border: 1px solid #171616;*/
  margin: 0;
  width: 153px;
  border-radius: 2px;
}
/* line 70, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box {
  width: 151px;
  border-radius: 2px 2px 0px 0px;
}
/* line 73, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box img {
  width: 100%;
  border-radius: 2px 2px 0px 0px;
}
/* line 77, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating {
  position: absolute;
  width: 40px !important;
  height: 40px;
  margin-top: -25px;
}
/* line 82, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 5%;
  font-weight: bold;
}
/* line 90, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .green-rating {
  background-color: #16be48;
}
/* line 93, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .red-rating {
  background-color: #FF5722;
}
/* line 96, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .orange-rating {
  background-color: #FFA000;
}
/* line 101, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel {
  height: 3.8em;
  position: relative;
  padding: 0.6em;
  border: none;
  margin-bottom: 0em;
  border-radius: 0px 0px 2px 2px;
  text-align: center;
}
/* line 110, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel.design_desc a {
  color: white;
}
/* line 114, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel .price-block {
  font-size: 14px;
  color: #303030;
}
/* line 122, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn {
  position: relative;
  font-size: 26px;
  line-height: 32px;
  background-color: #4c4b4b;
  color: #eaeaea;
  text-align: center;
  bottom: 160px;
  width: 5%;
  box-shadow: -1px 0px 2px 0px #383737;
  display: none;
}
/* line 134, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn-left {
  left: 1px;
  float: right;
}
/* line 138, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn-right {
  right: 1px;
  float: left;
}

@media only screen and (max-width: 989px) {
  /* line 146, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow-x: scroll;
  }
  /* line 151, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 2px;
  }
}
@media only screen and (max-width: 989px) {
  /* line 159, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  #box-more_like_these {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-wrap: wrap;
    overflow-x: hidden;
  }
  /* line 165, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  #box-more_like_these .product-list {
    margin-bottom: 2px;
    margin-right: 0 !important;
    flex: 40%;
  }
  /* line 169, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  #box-more_like_these .product-list .product-box {
    margin: auto !important;
  }
}
@media only screen and (min-width: 980px) {
  /* line 178, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .row {
    text-align: center;
  }
  /* line 181, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products {
    overflow-x: visible;
    width: auto;
  }
  /* line 184, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 10px;
  }

  /* line 189, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .scroll-btn {
    display: none;
  }
}
