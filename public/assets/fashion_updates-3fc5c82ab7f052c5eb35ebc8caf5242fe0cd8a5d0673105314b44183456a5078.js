(function() {
  $(document).on('ready', function() {
    $(window).scroll(function() {
      if ($(this).scrollTop() > 200) {
        return $("#back-top").fadeIn();
      } else {
        return $("#back-top").fadeOut();
      }
    });
    $("#back-top a").click(function() {
      $("body,html").animate({
        scrollTop: 0
      }, 500);
      return false;
    });
    if ($('#fashion-blog-show').length > 0) {
      return $('p:has(img)').addClass('has-image');
    }
  });

}).call(this);
