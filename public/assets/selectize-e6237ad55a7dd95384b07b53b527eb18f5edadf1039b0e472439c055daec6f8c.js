(function() {
  window.Selectize = (function() {
    var bindings, cancelEditingOtherOption, doneEditingOtherOption, isOtherOptionSelected, showOtherOptionLabelInstead, showOtherOptionTextBoxInstead, startEditingOtherOption;

    bindings = function() {
      this.$options.on('change', 'input[type=radio]', (function() {
        if (isOtherOptionSelected.call(this)) {
          return startEditingOtherOption.call(this);
        } else {
          return cancelEditingOtherOption.call(this);
        }
      }).bind(this));
      return this.$otherOptionTextBox.on('keyup', (function(event) {
        if (event.key === 'Enter') {
          return doneEditingOtherOption.call(this);
        } else if (event.key === 'Escape') {
          return cancelEditingOtherOption.call(this);
        }
      }).bind(this));
    };

    isOtherOptionSelected = function() {
      return this.$otherOptionRadioButton.is(':checked');
    };

    startEditingOtherOption = function() {
      this.$otherOptionTextBox.val(this.$otherOptionRadioButton.val());
      return showOtherOptionTextBoxInstead.call(this);
    };

    doneEditingOtherOption = function() {
      var userEnteredValue;
      userEnteredValue = this.$otherOptionTextBox.val();
      if (userEnteredValue.length !== 0) {
        this.$otherOptionLabel.text(userEnteredValue);
        this.$otherOptionRadioButton.val(userEnteredValue);
      }
      return showOtherOptionLabelInstead.call(this);
    };

    cancelEditingOtherOption = function() {
      return showOtherOptionLabelInstead.call(this);
    };

    showOtherOptionTextBoxInstead = function() {
      this.$otherOptionLabel.addClass('is-hidden');
      this.$otherOptionTextBox.removeClass('is-hidden');
      return this.$otherOptionTextBox.focus();
    };

    showOtherOptionLabelInstead = function() {
      this.$otherOptionTextBox.addClass('is-hidden');
      return this.$otherOptionLabel.removeClass('is-hidden');
    };

    function Selectize($selectize, name) {
      this.$container = $selectize;
      this.$options = this.$container.children('.selectize-option');
      this.$otherOption = this.$options.last();
      this.$otherOptionLabel = this.$otherOption.find('label');
      this.$otherOptionRadioButton = this.$otherOption.find('input[type=radio]');
      this.$otherOptionTextBox = this.$otherOption.find('input[type=text]');
      bindings.call(this);
    }

    Selectize.prototype.value = function() {
      return this.$options.find('input[type=radio]:checked').val();
    };

    Selectize.prototype.isValid = function() {
      return this.value() !== void 0;
    };

    return Selectize;

  })();

}).call(this);
