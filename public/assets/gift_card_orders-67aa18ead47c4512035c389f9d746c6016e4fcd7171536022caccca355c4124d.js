(function() {
  $(function() {
    var $form, $recipientEmailConfirmationField, $recipientEmailField, validateRecipientEmailConfirmation;
    $form = $('#new_gift_card_order');
    $recipientEmailField = $form.find('#gift_card_order_recipient_email');
    $recipientEmailConfirmationField = $form.find('#gift_card_order_recipient_email_confirmation');
    validateRecipientEmailConfirmation = function() {
      if ($recipientEmailField.val().trim() !== $recipientEmailConfirmationField.val().trim()) {
        $recipientEmailConfirmationField.get(0).setCustomValidity("Recipient email and confirmation doesn't match.");
        return false;
      } else {
        $recipientEmailConfirmationField.get(0).setCustomValidity('');
        return true;
      }
    };
    $recipientEmailField.on('change', validateRecipientEmailConfirmation);
    return $recipientEmailConfirmationField.on('keyup', validateRecipientEmailConfirmation);
  });

}).call(this);
