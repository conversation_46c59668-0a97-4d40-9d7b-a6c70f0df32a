input {
  font-size: 14px;
}
a{
  text-decoration: none;
}
/*.unbxd-autocomplete-list-ul{
  font-family: arial, tahoma, verdana, sans-serif;
  font-size: 11px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 16px;
  height: auto;
  margin: 0px;
  z-index: 99999;
  background-color: #fff;
  border-radius: 4px;
  padding-top: 4px !important;
  text-align: left;
}*/


/*li.unbxd-autocomplete-list-li, .unbxd-prouct-suggest {
  z-index: 10;
  padding-left: 10px;
  cursor: default;
  color: #000000;
  font-size: 13px;
  font-weight: normal;
  border: none;
  left: 577.59375px;
  line-height: 1.5em;
  list-style-type: none;
  height: auto;
  font-family:inherit;
  text-overflow: ellipsis;
  padding-bottom: 10px;
}*/

/*.unbxd-prouct-suggest{
  padding-top: 3px;
  padding-bottom: 3px;
}*/
/*
li.unbxd-autocomplete-list-li:hover,  li.unbxd-autoComplt-hint-selected {
  background-color: #f2f2f2 !important;
  color: #000 !important;
  cursor: pointer !important;
  }
li.unbxd-autocomplete-list-li span:hover {
  background-color: #f2f2f2;
  cursor: pointer !important;
}
li.unbxd-autoComplt-hint-selected span:hover {
  background-color: #f2f2f2;
  cursor: pointer !important;
}

li.unbxd-autocomplete-list-li img:hover {
  background-color: #f2f2f2;
  color: #000;
  cursor: pointer !important;
}
li.unbxd-autoComplt-hint-selected img:hover {
  background-color: #f2f2f2;
  color: #000;
  cursor: pointer !important;
}

li#unbxd_hint:hover, li.autoComplt-hint-selected{
  color: #000;
  cursor: pointer ;
}*/

/*em{
  font-weight: 900;
  font-style: normal;
}

.autocomplete-category {
  text-transform: uppercase;
  font-weight: 500;
  font-size: 14px;
  padding: 0 10px 10px 10px;
  font-family: "Helvetica Neue",Arial,sans-serif  ;
  font-size: 14px;
  line-height: 16px;
  background-color:#33CCFF;
}
.unbxd-prouct-suggest{
  height: auto;
  padding-left: 11px;
}
.product-autosuggest{

}
.image{
  height: 0;
  padding-top: 4px;
  margin-top: 2px;
  margin-bottom: 2px;
}
.title{
  float: right;
  text-align: -webkit-auto;
  padding-right: 6px;
  font-size: 15px;
  font-style: italic;
}
.autoCompltHintSelected {
  color : #fff;
  background-color : red  ;
}*/


/*enable this thing if product suggestion is required in search suggestion*/
/*
.autoCmplt-input.input-1 {
  width: 180px;
}
.autoCmplt-input.input-2 {
  width: 300px;
}

.product-name{
  font-size: 14px;
}
.unbxd-product-name{
    word-wrap: break-word !important;
    padding-left: 25%;
}
.unbxd-product-img{
  cursor: default;
  display: block;
  float: left;
  font-family: arial, tahoma, verdana, sans-serif;
  font-size: 13px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  line-height: 18px;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin-bottom: 2px;
  margin-left: 0px;
  margin-right: 5px;
  padding-bottom: 0px;
  padding-left: 0px;
  text-align: center;
  vertical-align: baseline;
  width: 20%;
  min-height: 40px;
  padding-top: 3px
}
.unbxd-product-img > img {
  cursor: pointer;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin-bottom: 0px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  max-height: 40px;
  max-width: 40px;
  padding-bottom: 0px;
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
  text-align: center;
  vertical-align: baseline;
}
.unbxd-product-img:hover{
  background-color: #f2f2f2 !important;
}

.unbxd-product-price{
  font-size: 13px;
  font-weight: bold;
  color: #d2322d;
  font-family: arial, tahoma, verdana, sans-serif;
  padding-left: 25%;
}


.unbxd-product-suggest{
  min-height: 40px;
  height: auto;
}
*/

/*.product-imageas{
  float: left;
  width: 40px;
  height: 40px;
  padding-right: 6px;
}

.product-priceas{
  float: left;
}
.product-nameas{
  width: auto;
  height: 40px;
  float:left;

}
*/

/*li.unbxd-autocomplete-list-li > div {
  border-top: 1px solid #e2e2e2;
 }

span.unbxd-autocomplete-list-li, span.unbxd-autocomplete-list-li:hover{
  font-weight: bold;
  color:#d2322d;
  font-family: arial, tahoma, verdana, sans-serif;
  font-size: 13px;
  font-style: normal;
  padding-left: 0;
}
.unbxd-header {
  padding-bottom: 8px;
  border-bottom-color: rgb(51, 51, 51);
  border-bottom-style: none;
  border-bottom-width: 0px;
  border-collapse: collapse;
  border-image-outset: 0px;
  border-image-repeat: stretch;
  border-image-slice: 100%;
  border-image-source: none;
  border-image-width: 1;
  border-left-color: rgb(51, 51, 51);
  border-left-style: none;
  border-left-width: 0px;
  border-right-color: rgb(51, 51, 51);
  border-right-style: none;
  border-right-width: 0px;
  border-top-color: rgb(204, 204, 204);
  border-top-style: solid;
  border-top-width: 1px;
  color: rgb(51, 51, 51);
  cursor: default;
  display: block;
  font-family: arial, tahoma, verdana, sans-serif;
  font-size: 13px;
  font-style: normal;
  font-variant: normal;
  font-weight: normal;
  height: 5px;
  line-height: 18px;
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
  margin-bottom: 0px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 6px;
  overflow-x: visible;
  overflow-y: visible;
  padding-left: 10px;
  padding-right: 5px;
  padding-top: 2px;
  text-align: left;
  vertical-align: baseline;
  width: 485px;
}*/

/*.unbxd-header .text {
  top: -11px;
  display: inline-block;
  position: relative;
  background: #fff;
  padding: 0 5px;
  font-size: 13px;
  color: #6d6d6d;
}*/

._unbxd-hide{
  display: none;
}

@media only screen and (max-device-width: 480px) {
    html {
       
    }

    body{
      overflow: hidden;
      width: 100%;
      height: 100%;
      padding: 0;
      margin: 0;
    }

/*    .autoCmplt-input{
      width: 99%;
      height: 29px;
    }

    .unbxd-autocomplete-list-ul{
      width: 93% !important;
      border: 0;
      -moz-box-shadow:    3px 3px 5px 6px #ccc;
      -webkit-box-shadow: 3px 3px 5px 6px #ccc;
      box-shadow:         3px 3px 5px 6px #ccc;
      list-style: square;
      margin-left: -15px;
      line-height: 1.5em !important;
      list-style-image: url(https://s3-us-west-2.amazonaws.com/s.cdpn.io/4621/treehouse-marker.png) !important;
    }

    .unbxd-autocomplete-list-li-in{
      list-style: none;
    }

    li.unbxd-autocomplete-list-li{
      color: rgb(88, 88, 189);
    }*/

}
.unbxd-as-wrapper {
  background: #ffffff none repeat scroll 0 0;
  border-radius: 0px 0px 2px 2px;
  color: #271f35;
  width: 96%;
  top: 133px !important;
}

.unbxd_as_wrapper_scroll{
  top: 50px !important;
  width: 74%;
}
 
ul.unbxd-as-maincontent {
  list-style: none;
  padding-top: 5px ;
  padding-left: 2px;
  cursor: pointer ;
}

li.unbxd-as-header {
  color: #b11f2d;
  font-weight: 600;
  margin-top: 6px;
  padding-top: 6px;
}

li.unbxd-as-header:not(:first-of-type){
  border-top: 1px solid #bbbbbb;
}

.unbxd-as-popular-product{
  list-style: none;
  line-height: 2;
}

@media only screen and (min-width: 64.063em) {
  .unbxd-as-wrapper {
    width: 40%;
    top: 40px !important;
  }
}
