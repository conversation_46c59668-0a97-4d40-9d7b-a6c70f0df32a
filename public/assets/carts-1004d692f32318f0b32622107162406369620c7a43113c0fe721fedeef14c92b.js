(function() {
  $(function() {
    return $('.quantity_list').on('change', function(e) {
      var id, url;
      id = this.id.split('_').pop();
      url = '/line_items/' + id;
      return $.ajax({
        url: url,
        type: 'PUT',
        dataType: 'json',
        data: {
          line_items: {
            id: id,
            quantity: $(this).val()
          }
        },
        success: function(response) {
          return window.location.href = response.redirect_url;
        }
      });
    });
  });

  $(function() {
    var $container, $footer, $win, ipos, scrollHeight;
    $footer = $('#secondary_action_buttons');
    $win = $('#main-section');
    $container = $('#container');
    ipos = $container.height();
    scrollHeight = function(e) {
      var space, winh, wpos;
      wpos = $win.scrollTop();
      winh = $win.height();
      space = $win.height() - $footer.height() / 2;
      if (ipos - wpos < winh) {
        return $footer.slideDown();
      } else {
        return $footer.slideUp();
      }
    };
    $(window).ready(scrollHeight).resize(scrollHeight);
    return $win.scroll(scrollHeight);
  });

}).call(this);
