/*! lightgallery - v1.2.9 - 2015-12-18
* http://sachinchoolur.github.io/lightGallery/
* Copyright (c) 2015 Sachin N; Licensed Apache 2.0 */
!function(o,e){"use strict";var t={scale:1,zoom:!0,enableZoomAfter:300},a=function(a){return this.core=o(a).data("lightGallery"),this.core.s=o.extend({},t,this.core.s),this.core.s.zoom&&this.core.doCss()&&(this.init(),this.zoomabletimeout=!1,this.pageX=o(e).width()/2,this.pageY=o(e).height()/2+o(e).scrollTop()),this};a.prototype.init=function(){var t=this,a='<span id="lg-zoom-in" class="lg-icon"></span><span id="lg-zoom-out" class="lg-icon"></span>';this.core.$outer.find(".lg-toolbar").append(a),t.core.$el.on("onSlideItemLoad.lg.tm.zoom",function(e,a,r){var s=t.core.s.enableZoomAfter+r;o("body").hasClass("lg-from-hash")&&r?s=0:o("body").removeClass("lg-from-hash"),t.zoomabletimeout=setTimeout(function(){t.core.$slide.eq(a).addClass("lg-zoomable")},s+30)});var r=1,s=function(a){var r,s,i=t.core.$outer.find(".lg-current .lg-image"),l=(o(e).width()-i.width())/2,n=(o(e).height()-i.height())/2+o(e).scrollTop();r=t.pageX-l,s=t.pageY-n;var c=(a-1)*r,g=(a-1)*s;i.css("transform","scale3d("+a+", "+a+", 1)").attr("data-scale",a),i.parent().css("transform","translate3d(-"+c+"px, -"+g+"px, 0)").attr("data-x",c).attr("data-y",g)},i=function(){r>1?t.core.$outer.addClass("lg-zoomed"):t.resetZoom(),1>r&&(r=1),s(r)};t.core.$el.on("onAferAppendSlide.lg.tm.zoom",function(o,e){var a=t.core.$slide.eq(e).find(".lg-image");a.dblclick(function(o){var s,l=a.width();s=t.core.s.dynamic?t.core.s.dynamicEl[e].width||a[0].naturalWidth||l:t.core.$items.eq(e).attr("data-width")||a[0].naturalWidth||l;var n;t.core.$outer.hasClass("lg-zoomed")?r=1:s>l&&(n=s/l,r=n||2),t.pageX=o.pageX,t.pageY=o.pageY,i(),setTimeout(function(){t.core.$outer.removeClass("lg-grabbing").addClass("lg-grab")},10)})}),o(e).on("resize.lg.zoom scroll.lg.zoom orientationchange.lg.zoom",function(){t.pageX=o(e).width()/2,t.pageY=o(e).height()/2+o(e).scrollTop(),s(r)}),o("#lg-zoom-out").on("click.lg",function(){t.core.$outer.find(".lg-current .lg-image").length&&(r-=t.core.s.scale,i())}),o("#lg-zoom-in").on("click.lg",function(){t.core.$outer.find(".lg-current .lg-image").length&&(r+=t.core.s.scale,i())}),t.core.$el.on("onBeforeSlide.lg.tm",function(){t.resetZoom()}),t.core.isTouch||t.zoomDrag(),t.core.isTouch&&t.zoomSwipe()},a.prototype.resetZoom=function(){this.core.$outer.removeClass("lg-zoomed"),this.core.$slide.find(".lg-img-wrap").removeAttr("style data-x data-y"),this.core.$slide.find(".lg-image").removeAttr("style data-scale"),this.pageX=o(e).width()/2,this.pageY=o(e).height()/2+o(e).scrollTop()},a.prototype.zoomSwipe=function(){var o=this,e={},t={},a=!1,r=!1,s=!1;o.core.$slide.on("touchstart.lg",function(t){if(o.core.$outer.hasClass("lg-zoomed")){var a=o.core.$slide.eq(o.core.index).find(".lg-object");s=a.outerHeight()*a.attr("data-scale")>o.core.$outer.find(".lg").height(),r=a.outerWidth()*a.attr("data-scale")>o.core.$outer.find(".lg").width(),(r||s)&&(t.preventDefault(),e={x:t.originalEvent.targetTouches[0].pageX,y:t.originalEvent.targetTouches[0].pageY})}}),o.core.$slide.on("touchmove.lg",function(i){if(o.core.$outer.hasClass("lg-zoomed")){var l,n,c=o.core.$slide.eq(o.core.index).find(".lg-img-wrap");i.preventDefault(),a=!0,t={x:i.originalEvent.targetTouches[0].pageX,y:i.originalEvent.targetTouches[0].pageY},o.core.$outer.addClass("lg-zoom-dragging"),n=s?-Math.abs(c.attr("data-y"))+(t.y-e.y):-Math.abs(c.attr("data-y")),l=r?-Math.abs(c.attr("data-x"))+(t.x-e.x):-Math.abs(c.attr("data-x")),c.css("transform","translate3d("+l+"px, "+n+"px, 0)")}}),o.core.$slide.on("touchend.lg",function(){o.core.$outer.hasClass("lg-zoomed")&&a&&(a=!1,o.core.$outer.removeClass("lg-zoom-dragging"),o.touchendZoom(e,t,r,s))})},a.prototype.zoomDrag=function(){var t=this,a={},r={},s=!1,i=!1,l=!1,n=!1;t.core.$slide.on("mousedown.lg.zoom",function(e){var r=t.core.$slide.eq(t.core.index).find(".lg-object");n=r.outerHeight()*r.attr("data-scale")>t.core.$outer.find(".lg").height(),l=r.outerWidth()*r.attr("data-scale")>t.core.$outer.find(".lg").width(),t.core.$outer.hasClass("lg-zoomed")&&o(e.target).hasClass("lg-object")&&(l||n)&&(e.preventDefault(),a={x:e.pageX,y:e.pageY},s=!0,t.core.$outer.scrollLeft+=1,t.core.$outer.scrollLeft-=1,t.core.$outer.removeClass("lg-grab").addClass("lg-grabbing"))}),o(e).on("mousemove.lg.zoom",function(o){if(s){var e,c,g=t.core.$slide.eq(t.core.index).find(".lg-img-wrap");i=!0,r={x:o.pageX,y:o.pageY},t.core.$outer.addClass("lg-zoom-dragging"),c=n?-Math.abs(g.attr("data-y"))+(r.y-a.y):-Math.abs(g.attr("data-y")),e=l?-Math.abs(g.attr("data-x"))+(r.x-a.x):-Math.abs(g.attr("data-x")),g.css("transform","translate3d("+e+"px, "+c+"px, 0)")}}),o(e).on("mouseup.lg.zoom",function(o){s&&(s=!1,t.core.$outer.removeClass("lg-zoom-dragging"),!i||a.x===r.x&&a.y===r.y||(r={x:o.pageX,y:o.pageY},t.touchendZoom(a,r,l,n)),i=!1),t.core.$outer.removeClass("lg-grabbing").addClass("lg-grab")})},a.prototype.touchendZoom=function(o,e,t,a){var r=this,s=r.core.$slide.eq(r.core.index).find(".lg-img-wrap"),i=r.core.$slide.eq(r.core.index).find(".lg-object"),l=-Math.abs(s.attr("data-x"))+(e.x-o.x),n=-Math.abs(s.attr("data-y"))+(e.y-o.y),c=(r.core.$outer.find(".lg").height()-i.outerHeight())/2,g=Math.abs(i.outerHeight()*Math.abs(i.attr("data-scale"))-r.core.$outer.find(".lg").height()+c),d=(r.core.$outer.find(".lg").width()-i.outerWidth())/2,h=Math.abs(i.outerWidth()*Math.abs(i.attr("data-scale"))-r.core.$outer.find(".lg").width()+d);a&&(-g>=n?n=-g:n>=-c&&(n=-c)),t&&(-h>=l?l=-h:l>=-d&&(l=-d)),a?s.attr("data-y",Math.abs(n)):n=-Math.abs(s.attr("data-y")),t?s.attr("data-x",Math.abs(l)):l=-Math.abs(s.attr("data-x")),s.css("transform","translate3d("+l+"px, "+n+"px, 0)")},a.prototype.destroy=function(){var t=this;t.core.$el.off(".lg.zoom"),o(e).off(".lg.zoom"),t.core.$slide.off(".lg.zoom"),t.core.$el.off(".lg.tm.zoom"),t.resetZoom(),clearTimeout(t.zoomabletimeout),t.zoomabletimeout=!1},o.fn.lightGallery.modules.zoom=a}(jQuery,window,document);