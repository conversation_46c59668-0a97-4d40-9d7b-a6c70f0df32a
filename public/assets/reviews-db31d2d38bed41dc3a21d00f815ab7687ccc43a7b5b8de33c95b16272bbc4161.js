(function() {
  var createSiteReview, create_element, delete_element, toggle_scroll;

  $('.review-container .tabbed-btn-row #product-btn').click(function() {
    $('#site_reviews').hide();
    $(this).addClass('active-btn').removeClass('passive-btn');
    $('.review-container .tabbed-btn-row #site-btn').addClass('passive-btn').removeClass('active-btn');
    $('#product_reviews').show();
    $('.best-products').hide();
    $('.best_designs_btn').show();
  });

  $('.review-container .tabbed-btn-row #site-btn').click(function() {
    $('#product_reviews').hide();
    $(this).addClass('active-btn').removeClass('passive-btn');
    $('.review-container .tabbed-btn-row #product-btn').addClass('passive-btn').removeClass('active-btn');
    $('#site_reviews').show();
    $('.best-products').hide();
    $('.best_designs_btn').hide();
    $('.toggle-btn').text('Top Reviews');
  });

  $(window).load(function() {
    $('.review-container .tabbed-btn-row #product-btn').addClass('active-btn');
  });

  $('.best_designs_btn').click(function() {
    if ($('.all-products').is(':visible')) {
      $('.all-products').hide();
      $('#site_reviews').hide();
      $('.best-products').show();
      return $('.toggle-btn').text('All Reviews');
    } else {
      $('.best-products').hide();
      $('#site_reviews').hide();
      $('.all-products').show();
      return $('.toggle-btn').text('Top Reviews');
    }
  });

  $('#write_review #submit-review-btn').click(function() {
    var email, review, review_id, star, user_name;
    star = $('#form-star input[name=score]')[0].value;
    review = $('#review-text').val();
    user_name = $(this).data('username');
    email = $(this).data('email');
    review_id = $(this).data('review-id');
    if (review === '' || review.length < 2) {
      alert('Please give some review');
    } else {
      $('body').css('overflow-y', 'scroll !important');
      $('body').css('position', 'inherit !important');
      $.ajax(createSiteReview(star, review, user_name, email, review_id));
    }
  });

  createSiteReview = function(star, review, user_name, email, review_id) {
    return {
      type: 'POST',
      data: {
        star: star,
        review: review
      },
      url: '/customers/reviews/create_review',
      datatype: 'json',
      success: function(response) {
        if (response.success) {
          $('a.close-reveal-modal').trigger('click');
          if (review.length > 60) {
            window.location = window.location.href.split('?')[0] + '?site_page=1';
          } else {
            if (review !== 'none') {
              delete_element(review_id);
            }
            create_element(star, review, user_name, email, review_id);
          }
        } else {
          alert('Something went wrong!!!');
        }
      },
      error: function(error) {
        alert('Something went wrong!!!');
      }
    };
  };

  create_element = function(star, review, user_name, email, review_id) {
    var cln, i, itm, tstar;
    tstar = star;
    if ($('#' + review_id).length === 0) {
      itm = document.getElementById('hidden_site_review');
      cln = itm.cloneNode(true);
    } else {
      cln = document.getElementById(review_id);
    }
    cln.childNodes[1].childNodes[1].innerHTML = user_name;
    cln.childNodes[3].childNodes[1].childNodes[1].setAttribute('dscore', star);
    i = 0;
    while (tstar > 0) {
      cln.childNodes[3].childNodes[1].childNodes[1].childNodes[i].setAttribute('src', '/assets/star-on-fd26bf0ea0990cfd808f7540f958eed324b86fc609bf56ec2b3a5612cdfde5f5.jpg');
      i += 2;
      tstar -= 1;
    }
    cln.childNodes[5].innerHTML = review;
    cln.childNodes[3].childNodes[3].childNodes[1].innerHTML = email;
    cln.id = review_id;
    $('#upper_review_block').append(cln);
  };

  delete_element = function(review_id) {
    if ($('#' + review_id).length > 0) {
      return $('#' + review_id).remove();
    }
  };

  $('.toggle_scroll').click(function() {
    return toggle_scroll();
  });

  $('#add_review').click(function() {
    return toggle_scroll();
  });

  toggle_scroll = function() {
    if ($('body').css('overflow-y') === 'hidden') {
      $('body').css('overflow-y', 'scroll');
      return $('body').css('position', 'inherit');
    } else {
      $('body').css('overflow-y', 'hidden');
      return $('body').css('position', 'fixed');
    }
  };

  $(document).foundation();

  $(document).foundation('reveal', {
    animation: false
  });

  $(document).on('closed.fndtn.reveal', '.black-content', function() {
    toggle_scroll();
  });

}).call(this);
