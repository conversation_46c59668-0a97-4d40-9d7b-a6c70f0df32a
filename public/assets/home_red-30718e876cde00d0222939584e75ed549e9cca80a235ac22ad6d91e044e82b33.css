@charset "UTF-8";
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* Slider */
/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slider {
  position: relative;
  display: block;
  box-sizing: border-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

/* line 17, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-list {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0;
  padding: 0;
}

/* line 33, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* line 42, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-track {
  position: relative;
  left: 0;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
/* line 50, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-track:before, .slick-track:after {
  content: "";
  display: table;
}
/* line 56, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-track:after {
  clear: both;
}
/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-loading .slick-track {
  visibility: hidden;
}

/* line 64, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slide {
  float: left;
  height: 100%;
  min-height: 1px;
  display: none;
}
/* line 68, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
[dir="rtl"] .slick-slide {
  float: right;
}
/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slide img {
  display: block;
}
/* line 74, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slide.slick-loading img {
  display: none;
}
/* line 80, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-slide.dragging img {
  pointer-events: none;
}
/* line 84, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-initialized .slick-slide {
  display: block;
}
/* line 88, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-loading .slick-slide {
  visibility: hidden;
}
/* line 92, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

/* line 98, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick.scss */
.slick-arrow.slick-hidden {
  display: none;
}

/* Slider */
/* line 45, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-loading .slick-list {
  background: #fff url(/assets/preloader-80d7ed3f3f4b50628f219778db814955e7d2007c05be88556778f90ee290715c.gif) center center no-repeat;
}

/* Icons */
/* Arrows */
/* Dots */
/* line 137, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-dots {
  position: absolute;
  bottom: 20px;
  list-style: none;
  display: block;
  text-align: center;
  padding: 0;
  margin: 0;
  width: 100%;
}
/* line 147, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-dots li {
  position: relative;
  display: inline-block;
  height: 15px;
  width: 15px;
  padding: 0;
  cursor: pointer;
}
/* line 155, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-dots li button {
  border: 0;
  background: transparent;
  display: block;
  height: 15px;
  width: 15px;
  outline: none;
  line-height: 0px;
  font-size: 0px;
  color: transparent;
  padding: 0;
  cursor: pointer;
}
/* line 173, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-dots li button:before {
  position: absolute;
  top: 0;
  left: 0;
  content: "•";
  width: 15px;
  height: 15px;
  font-family: "Lato";
  font-size: 0.675rem;
  line-height: 20px;
  text-align: center;
  color: black;
  opacity: 0.25;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* line 190, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/slick-theme.scss */
.slick-dots li.slick-active button:before {
  color: black;
  opacity: 0.75;
}

/*Home_page specific css */
/* line 9, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body {
  overflow: scroll !important;
  background: #ffffff;
  color: #303030;
  cursor: auto;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container div:focus, body .pages_home #container a:focus {
  outline: none;
}
/* line 29, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .js-lazy {
  display: inline-block;
}
/* line 32, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .user_scrollable_banner {
  scroll-behavior: smooth;
}
/* line 34, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .user_scrollable_banner .banner-slider-box {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
}
/* line 39, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .user_scrollable_banner .banner-slider-box .banner-wrapper {
  display: inline-block;
  width: 310px;
  overflow: hidden;
  text-align: center;
}
/* line 44, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .user_scrollable_banner .banner-slider-box .banner-wrapper .user_scrollable_banner_slide {
  margin: 0px 2px;
  display: flex;
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .user_scrollable_banner .banner-slider-box .banner-wrapper .user_scrollable_banner_slide img {
  border: 1px solid #d8d8d8;
}
/* line 55, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .tag_slider .tag-slider {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
}
/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .tag_slider .tag-slider .tag-slide {
  margin: 0px 15px;
  display: inline-block;
  text-align: center;
}
/* line 64, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .tag_slider .tag-slider .tag-slide .tag-slider-title {
  font-size: 12px;
  font-weight: bold;
  text-align: center;
}
/* line 72, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item {
  background: white;
  padding: 5px 0px;
  margin: 5px 0px;
}
/* line 76, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .front_home_page {
  display: inline-flex;
}
/* line 80, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .banner-timer .countdown {
  position: absolute;
  top: -60px;
  text-align: center;
  left: 0;
  right: 0;
  font-weight: 500;
  letter-spacing: 2px;
  font-size: 22px;
  font-family: 'Lato Bold';
}
/* line 90, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .banner-timer .countdown .deal_timer {
  margin: 0px 8px;
  padding: 0px 4px;
  height: 30px;
  display: inline-block;
}
/* line 96, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .banner-timer .countdown .deal_text {
  display: none;
}
/* line 99, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .banner-timer .countdown .clock {
  width: 100% !important;
}
/* line 105, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item #homepage_cart .homepage-cart-title {
  font-weight: bold;
  margin-left: 5px;
  line-height: 30px;
  margin: 10px;
  border-bottom: 1px solid #eee;
}
/* line 113, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item #homepage_cart .cart-slider .quantity, body .pages_home #container .board-item #homepage_cart .cart-slider .item-price-font {
  font-weight: bold;
}
/* line 117, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item #homepage_cart .cart-slider .item_block .image-box {
  padding-left: 0.9375em;
  padding-right: 0.9375em;
}
/* line 123, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item #homepage_cart .view-cart {
  text-align: center;
  background: #670b19;
  color: white;
  padding: 10px;
  margin: 0px 10px;
}
/* line 132, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .design-col2, body .pages_home #container .board-item .homepage-blocks .design-col2 {
  font-size: 12px !important;
  background: url(/assets/sprite-f7e7721dd2d9b1d316fd41711a5c2fd08099db15a0dd34b4ac115bc94c6e17b8.png) no-repeat;
  padding: 9px 9px;
  line-height: 12px;
  position: relative;
  color: #ffffff;
  width: 54px;
  height: 40px;
  float: right;
  top: 40px;
  margin-top: -40px;
  background-position: 78% 45%;
  text-align: left;
}
/* line 147, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .design-details, body .pages_home #container .board-item .homepage_design_blocks .block-headers, body .pages_home #container .board-item .homepage-blocks .design-details, body .pages_home #container .board-item .homepage-blocks .block-headers {
  display: flex;
  font-weight: bold;
  margin: 10px;
  border-bottom: 1px solid #eee;
  width: auto;
}
/* line 153, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .design-details .view-more-designs, body .pages_home #container .board-item .homepage_design_blocks .block-headers .view-more-designs, body .pages_home #container .board-item .homepage-blocks .design-details .view-more-designs, body .pages_home #container .board-item .homepage-blocks .block-headers .view-more-designs {
  font-size: 14px;
  text-align: right;
}
/* line 160, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .design-slider .design-slides img, body .pages_home #container .board-item .homepage_design_blocks .flash_deals .design-slides img, body .pages_home #container .board-item .homepage-blocks .design-slider .design-slides img, body .pages_home #container .board-item .homepage-blocks .flash_deals .design-slides img {
  margin: auto;
  width: 100%;
}
/* line 166, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .flash_deals, body .pages_home #container .board-item .homepage_design_blocks .design-slider, body .pages_home #container .board-item .homepage-blocks .flash_deals, body .pages_home #container .board-item .homepage-blocks .design-slider {
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
  scroll-behavior: smooth;
}
/* line 171, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .flash_deals .design-slider-wrapper, body .pages_home #container .board-item .homepage_design_blocks .design-slider .design-slider-wrapper, body .pages_home #container .board-item .homepage-blocks .flash_deals .design-slider-wrapper, body .pages_home #container .board-item .homepage-blocks .design-slider .design-slider-wrapper {
  display: inline-block;
  width: 47%;
  overflow: hidden;
  text-align: center;
  border: 1px solid #eee;
}
/* line 177, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .flash_deals .design-slider-wrapper .design-col2, body .pages_home #container .board-item .homepage_design_blocks .design-slider .design-slider-wrapper .design-col2, body .pages_home #container .board-item .homepage-blocks .flash_deals .design-slider-wrapper .design-col2, body .pages_home #container .board-item .homepage-blocks .design-slider .design-slider-wrapper .design-col2 {
  white-space: initial;
}
/* line 181, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .flash_deals .design-slider-wrapper .design_desc .details_block, body .pages_home #container .board-item .homepage_design_blocks .design-slider .design-slider-wrapper .design_desc .details_block, body .pages_home #container .board-item .homepage-blocks .flash_deals .design-slider-wrapper .design_desc .details_block, body .pages_home #container .board-item .homepage-blocks .design-slider .design-slider-wrapper .design_desc .details_block {
  float: left;
}
/* line 184, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container .board-item .homepage_design_blocks .flash_deals .design-slider-wrapper .design_desc .truncate, body .pages_home #container .board-item .homepage_design_blocks .design-slider .design-slider-wrapper .design_desc .truncate, body .pages_home #container .board-item .homepage-blocks .flash_deals .design-slider-wrapper .design_desc .truncate, body .pages_home #container .board-item .homepage-blocks .design-slider .design-slider-wrapper .design_desc .truncate {
  font-size: 0.875rem;
}
/* line 192, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .pages_home #container #more-items-loader {
  display: none;
  border: 4px dotted #670b19;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}
/* line 206, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .ongoing-fd-timer .deal_timer {
  margin: 0.3em;
  padding: 0.3em;
  background: #670b19;
  color: #ffffff;
  border-radius: 0.2em;
  font-size: 0.875rem;
  font-family: 'Lato Black';
}
/* line 217, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .truncate {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: initial;
}
/* line 224, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .truncate a {
  color: #303030;
}
/* line 229, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .opera_footer_fix {
  position: relative;
  bottom: 0;
  width: 100%;
  z-index: 9999;
  margin-top: 12px;
}
/* line 230, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .opera_footer_fix a {
  margin-bottom: 0em;
  width: 100%;
  left: 0px;
  font-weight: bold;
  line-height: inherit;
}
/* line 231, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .opera_checkout_position_fix {
  width: 100%;
  margin-top: 18px;
  background-color: #0E9A7D !important;
}
/* line 233, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .design_desc {
  padding: 0.5em;
  margin-bottom: 0rem;
}
/* line 237, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .design_desc .wishlist-forms .wishlist-heart-button {
  font-size: 1.5rem;
  margin: 0;
  padding: 0;
  float: right;
  background: transparent;
  color: #8f1b1d;
  position: absolute;
  right: 0;
}
/* line 246, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .design_desc .wishlist-forms .wishlist-heart-button:focus {
  outline: none;
}
/* line 250, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .design_desc .wishlist-forms .wishlist-heart-button.empty-heart {
  color: gray;
}
/* line 255, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .details_block {
  font-size: 0.875rem;
}
/* line 257, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
body .details_block .actual_price {
  display: inline-block;
  color: #595959;
  text-decoration: line-through;
  margin-left: 3px;
  font-weight: 100 !important;
}

/* line 266, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/home_red.scss */
*::-webkit-scrollbar {
  display: none;
}
