/* line 4, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-icon {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 30px 30px;
  display: inline-block;
  height: 30px;
  width: 30px;
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-twitter {
  background-image: url(/assets/social-share-button/twitter-7b64ce0117a85c5d52ed45c27707af61d9b0e0d590284baa287cfe87867b9ae1.svg);
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-facebook {
  background-image: url(/assets/social-share-button/facebook-03210e1663ee772e93ed5d344cdb36657b68342821aaebe982f2f984915990b3.svg);
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-google_plus {
  background-image: url(/assets/social-share-button/google_plus-50c049fbb29cd3346f1bf9349017b644bcd00a53b56bd156728850f57bd85c0a.svg);
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-linkedin {
  background-image: url(/assets/social-share-button/linkedin-b7fd42895b291003c444a9c2acf867ea813671e0b725a5d7c05dbb13f5c0fdd3.svg);
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-pinterest {
  background-image: url(/assets/social-share-button/pinterest-86203d156197cce4087fee058b920d275c535df5fd59d8caa83da0ef41d1ec7f.svg);
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-email {
  background-image: url(/assets/social-share-button/email-7067143cbe69d9d8cf25e67fda1a315d133c2b79074a09088bd712cc5c81135d.svg);
}

/*! lightslider - v1.1.3 - 2015-04-14
* https://github.com/sachinchoolur/lightslider
* Copyright (c) 2015 Sachin N; Licensed MIT */
/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper, .lSSlideWrapper .lSFade {
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide, .lSSlideWrapper.usingCss .lSFade > * {
  -webkit-transition-timing-function: inherit !important;
  transition-timing-function: inherit !important;
  -webkit-transition-duration: inherit !important;
  transition-duration: inherit !important;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter, .lSSlideOuter .lSPager.lSGallery {
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideWrapper > .lightSlider:after {
  clear: both;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter {
  overflow: hidden;
  user-select: none;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider:after, .lightSlider:before {
  content: " ";
  display: table;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider {
  overflow: hidden;
  margin: 0;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper {
  max-width: 100%;
  overflow: hidden;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: all 1s;
  -webkit-transition-property: -webkit-transform,height;
  -moz-transition-property: -moz-transform,height;
  transition-property: transform,height;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > * {
  position: absolute !important;
  top: 0;
  left: 0;
  z-index: 9;
  margin-right: 0;
  width: 100%;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > * {
  opacity: 0;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-transition-property: opacity;
  transition-property: opacity;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > .active {
  z-index: 10;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > .active {
  opacity: 1;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg {
  margin: 10px 0 0;
  padding: 0;
  text-align: center;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li {
  cursor: pointer;
  display: inline-block;
  padding: 0 5px;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li a {
  background-color: #222;
  border-radius: 30px;
  display: inline-block;
  height: 8px;
  overflow: hidden;
  text-indent: -999em;
  width: 8px;
  position: relative;
  z-index: 99;
  -webkit-transition: all .5s linear 0s;
  transition: all .5s linear 0s;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li.active a, .lSSlideOuter .lSPager.lSpg > li:hover a {
  background-color: #428bca;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media {
  opacity: .8;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media.active {
  opacity: 1;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery {
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  user-select: none;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li {
  overflow: hidden;
  -webkit-transition: border-radius .12s linear 0s .35s linear 0s;
  transition: border-radius .12s linear 0s .35s linear 0s;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li.active, .lSSlideOuter .lSPager.lSGallery li:hover {
  border-radius: 5px;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery img {
  display: block;
  height: auto;
  max-width: 100%;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideOuter .lSPager.lSGallery:before {
  content: " ";
  display: table;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a {
  width: 32px;
  display: block;
  top: 50%;
  height: 32px;
  background-image: url(/assets/controls-f4c3996ad997182376dddc345b138b599bae61a44ac41dadb19bc18710908226.png);
  cursor: pointer;
  position: absolute;
  z-index: 98;
  margin-top: -16px;
  opacity: .5;
  -webkit-transition: opacity .35s linear 0s;
  transition: opacity .35s linear 0s;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a:hover {
  opacity: 1;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSPrev {
  background-position: 0 0;
  left: 10px;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSNext {
  background-position: -32px 0;
  right: 10px;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a.disabled {
  pointer-events: none;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.cS-hidden {
  /*height:1px;opacity:0;*/
  filter: alpha(opacity=0);
  overflow: hidden;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical {
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical.noPager {
  padding-right: 0 !important;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSGallery {
  position: absolute !important;
  right: 0;
  top: 0;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lightSlider > * {
  width: 100% !important;
  max-width: none !important;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > a {
  left: 50%;
  margin-left: -14px;
  margin-top: 0;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSNext {
  background-position: 31px -31px;
  bottom: 10px;
  top: auto;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSPrev {
  background-position: 0 -31px;
  bottom: auto;
  top: 10px;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl {
  direction: rtl;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager, .lSSlideOuter .lightSlider {
  padding-left: 0;
  list-style: none;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSPager, .lSSlideOuter.lSrtl .lightSlider {
  padding-right: 0;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSGallery li, .lSSlideOuter .lightSlider > * {
  float: left;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSGallery li, .lSSlideOuter.lSrtl .lightSlider > * {
  float: right !important;
}

@-webkit-keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@-webkit-keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@-webkit-keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@-webkit-keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
@keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .rightEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .leftEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .rightEnd {
  -webkit-animation: topEnd .3s;
  animation: topEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .leftEnd {
  -webkit-animation: bottomEnd .3s;
  animation: bottomEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .rightEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .leftEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrab > * {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: -o-grab;
  cursor: -ms-grab;
  cursor: grab;
}

/* line 3, /home/<USER>/Mirraw/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrabbing > * {
  cursor: move;
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: -o-grabbing;
  cursor: -ms-grabbing;
  cursor: grabbing;
}

/* line 7, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 12, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body {
  overflow: scroll;
}
/* line 16, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .off-canvas-wrap #container {
  width: 96vw;
  overflow-y: hidden;
  padding: 1em 0em;
  margin: 2%;
  margin-top: 3.5em;
  overflow-x: hidden;
  padding: 0em 0em !important;
}
/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .hidden {
  display: none;
}
/* line 29, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .action_button_btn {
  padding-bottom: 0px;
  text-align: center;
}
/* line 33, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .overflow_messages {
  background: #008cba;
  text-align: center;
  padding: 3%;
  z-index: 100 !important;
}
/* line 39, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #add_to_cart_message {
  padding: 10px;
}
/* line 42, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons {
  transform: translate3d(0px, 0px, 0px);
}
/* line 44, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons .cart_button {
  margin-bottom: 0;
  background-color: #0d997c;
  width: 100%;
  font-weight: 700;
}
/* line 50, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons .right-button {
  margin-bottom: 0;
  background-color: #0d997c;
  width: 100%;
  padding: 1.2em 0;
  font-weight: 700;
}
/* line 57, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons .action_button_btn {
  padding-bottom: 0px;
}
/* line 60, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons .left-button {
  background-color: #101010;
  color: #0d997c;
  margin-bottom: 0;
  width: 100%;
  padding-left: 0%;
  font-weight: 700;
  padding-right: 0%;
  padding: 1.2em 0;
}
/* line 71, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .heading_underline {
  text-decoration: underline;
}
/* line 75, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons.fixed, body .designs_show.page #secondary_action_buttons.fixed {
  bottom: 0;
  top: auto;
  margin-bottom: 0;
}
/* line 78, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons.fixed .add_place_order, body .designs_show.page #secondary_action_buttons.fixed .add_place_order {
  background-color: #0e9a7d;
}
/* line 81, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons.fixed .add_to_cart, body .designs_show.page #secondary_action_buttons.fixed .add_to_cart {
  background-color: black;
  color: #0e9a7d;
}
/* line 86, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons.fixed a, body .designs_show.page #action_buttons.fixed input[type='submit'], body .designs_show.page #secondary_action_buttons.fixed a, body .designs_show.page #secondary_action_buttons.fixed input[type='submit'] {
  margin-bottom: 0em;
  width: 100%;
  font-weight: bold;
  line-height: inherit;
}
/* line 92, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #action_buttons.fixed li, body .designs_show.page #secondary_action_buttons.fixed li {
  padding-bottom: 0em;
  padding: 0em;
}
/* line 98, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page #secondary_action_buttons {
  margin: 3.7em 0;
}
/* line 101, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .panel_block {
  background-color: #010710;
  border: 0.1em solid #010710;
  margin-top: 1em;
  border-radius: .5em;
}
/* line 106, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .panel_block .panel_content {
  background-color: #4c4b4b;
  border: 1px solid black;
  border-radius: 6px;
}
/* line 112, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .panel_block .panel_heading {
  padding: 0em .2em;
}
/* line 114, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .panel_block .panel_heading ul {
  margin: .2em auto;
}
/* line 119, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .line_through_text {
  text-decoration: line-through;
}
/* line 122, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .truncate {
  width: 100%;
  color: #f1f1f1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 130, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .no-bullet.shipping_desc li {
  color: white;
}
/* line 134, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .close-icon {
  background-image: url(/assets/close-32-4b75407a3032b7b5d98bd2162d3607306997e2fd591ff6cad0cebce6ad3a300c.png);
  background-repeat: no-repeat;
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  background-size: 1em;
  background-color: black;
  border-radius: 5em;
}
/* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .bordered_block {
  padding: 1em;
  margin-bottom: 1em;
  box-shadow: 0 0 2em black;
  border: 0.1em solid #4d4d4d;
}
/* line 152, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .opera_checkout_position_fix {
  width: 100%;
  margin-top: 18px;
  background-color: #0E9A7D !important;
}
/* line 158, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .mirraw_cert_logo {
  display: inline;
  opacity: 0.85;
}
/* line 163, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .small_msg {
  font-size: 12px;
}
/* line 167, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .wrap_total {
  white-space: nowrap;
}
/* line 171, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body .designs_show.page .sign_in_button a {
  color: #b5fcb5;
}
/* line 176, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
body #disclaimer-box {
  font-size: 13px;
  text-align: center;
  margint-top: 8px;
  font-style: italic;
}

/* line 184, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#dynamicSizeChartModal, #sizeChartModal {
  background-color: #4c4b4b;
}

/* line 188, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.design_wrap {
  position: relative;
  display: inline-block;
}

/* line 192, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block {
  padding: 0.5em;
}
/* line 194, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_image_div li {
  list-style: none;
  padding: 0;
}
/* line 197, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_image_div li .small-centered {
  padding: 0 10px;
}
/* line 201, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel {
  background-color: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  margin-bottom: 24px;
}
/* line 205, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .designer-title {
  padding: 0.5rem 0 0 1.5rem;
}
/* line 208, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .rating-box {
  padding: 0.5rem 0 0 1.5rem;
}
/* line 210, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .rating-box .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 21%;
  font-weight: bold;
}
/* line 217, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .rating-box .small_rating.green-rating {
  background-color: #16be48;
}
/* line 220, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .rating-box .small_rating.red-rating {
  background-color: #FF5722;
}
/* line 223, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .rating-box .small_rating.orange-rating {
  background-color: #FFA000;
}
/* line 228, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .designer-name {
  padding: 0.5rem 0 0 1.8rem;
}
/* line 231, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .designer-panel .designer-sub-panel {
  margin-bottom: 10px;
  font-size: 14px;
  color: #f1f1f1;
  padding: 0.5rem 0 0 1.8rem;
}
/* line 238, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .tap_text {
  text-align: center;
}
/* line 240, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .tap_text small {
  color: #f1f1f1;
}
/* line 244, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_images {
  text-align: center;
  margin: 0;
}
/* line 247, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_images img {
  margin: auto;
}
/* line 252, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .th {
  border: none !important;
}
/* line 255, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .lSGallery {
  margin-left: auto;
  margin-right: auto;
  border-radius: 2px;
}
/* line 261, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block ul.lSPager li {
  margin: 0 2px;
}
/* line 263, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block ul.lSPager li a {
  background-color: #969696;
  height: 6px;
  width: 6px;
  z-index: 9;
}
/* line 270, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block ul.lSPager li.active a {
  background-color: #ca3366;
}
/* line 276, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons {
  background-color: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  padding: 6px 3px 6px 8px;
  margin-bottom: 16px;
}
/* line 281, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons .hsw-block {
  margin-left: -3px;
}
/* line 283, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons .hsw-block a {
  border-radius: 50%;
  background-color: #ca3366;
  padding: 1px 6px;
  font-size: 14px;
  font-style: italic;
  font-weight: 700;
  font-family: 'Times New Roman';
  cursor: pointer;
  color: #f1f1f1;
}
/* line 295, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons select {
  padding: 0.5em;
  color: #f1f1f1;
}
/* line 299, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons .addon_option_values {
  border: none;
  margin: 0px 0px 10px 10px;
}
/* line 302, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons .addon_option_values .atov-name {
  font-weight: 700;
  font-size: 14px;
}
/* line 307, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_addons .stitching_note {
  font-size: 13px;
  color: #f1f1f1;
}
/* line 312, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .discount_details {
  margin-top: 6px;
}
/* line 315, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .product_price_wo_discount {
  text-decoration: line-through;
  padding: 0 2% 0 0;
  color: #f1f1f1;
  font-size: 0.9em;
}
/* line 321, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .offer-message {
  padding-bottom: 2%;
}
/* line 324, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .product_discount_price {
  font-size: 1.3em;
  color: #f1f1f1;
  font-weight: bolder;
  padding: 0;
  padding-right: 2%;
}
/* line 331, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .changeInPriceNote {
  display: none;
  position: absolute;
}
/* line 334, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .changeInPriceNote span {
  background: #3ebe49;
  font-size: 12px;
  margin-left: -10px;
  padding: 0px 7px 0px 7px;
  border-radius: 4px;
  font-weight: bold;
  color: white;
  z-index: 10;
}
/* line 344, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .changeInPriceNote .arrow {
  background-color: #3ebe49 !important;
  width: 10px;
  height: 10px;
  left: 45%;
  top: -5px;
  transform: rotate(45deg);
  position: relative;
  display: inline-flex;
}
/* line 355, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .product_discount_percent {
  font-size: 0.9em;
  border-radius: 1em;
  width: 21%;
  padding: 0.7em;
  text-align: center;
}
/* line 363, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block .line_through_text {
  text-decoration: line-through;
  margin-top: 0.1em;
  font-size: 18px;
}
/* line 368, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_price_block {
  border-top: 2px solid black;
  border-bottom: 2px solid black;
  margin-top: 1em;
}
/* line 372, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #design_price_block li {
  padding-bottom: 0em;
}
/* line 376, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block {
  background: #4c4b4b;
  padding: 4px 3px;
  margin-bottom: 16px;
}
/* line 380, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block .var-title {
  padding-left: 4px;
  font-weight: 700;
}
/* line 384, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block .size-chart-text {
  font-size: 13px;
  margin-left: 12px;
}
/* line 388, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block a {
  padding: 0.625em .85em;
  margin: 0.3em;
  box-shadow: none;
  font-weight: 700;
}
/* line 393, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block a.selected, #design_image_block #variants_block a:focus, #design_image_block #variants_block a:hover {
  background-color: #9a9999;
}
/* line 397, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #variants_block .variant {
  font-size: small;
}
/* line 401, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #deal_ends {
  background: linear-gradient(to left, #ED8F03, #ED8F03);
  margin-bottom: 20px;
  font-size: 18px;
  box-shadow: 0 0 0.5em #2d2d2d;
  border-radius: 0px;
  padding: 4px 2px;
}
/* line 408, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #deal_ends #countdown {
  color: #080808;
  text-align: center;
}
/* line 411, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #deal_ends #countdown #deal_text {
  display: inline-block;
  font-family: inherit;
  color: #080808;
  vertical-align: bottom;
  font-weight: bold;
  text-align: left;
}
/* line 419, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #deal_ends #countdown #clock {
  text-align: right;
  color: #103a4f;
  font-weight: bold;
}
/* line 426, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #line_items_count {
  display: none;
  width: 290px;
  background-color: #cecece;
  color: #000000;
  text-align: center;
  padding: 16px;
  position: fixed;
  z-index: 10;
  right: 8px;
  bottom: 1%;
  font-size: 16px;
  line-height: 21px;
  border: 2px solid #e29cb4;
}
/* line 440, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #line_items_count .line_item_close {
  float: right;
  text-align: right;
  position: relative;
  left: 25px;
  bottom: 25px;
  padding: 0px 3px;
  cursor: pointer;
  border-radius: 50%;
  background-color: #a9a9a9;
}
/* line 452, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #line_items_count .line_items_count_text span {
  color: #F44336;
  text-transform: uppercase;
  font-weight: 700;
}
/* line 460, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_image_block #add_review_button button#add_review {
  margin-bottom: 0px;
}

/* line 466, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#write_review #rating-alert-message, #write_review #review-alert-message, #write_review #save-alert-message {
  display: none;
  color: #f54e1a;
  font-size: 14px;
}
/* line 471, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#write_review .alert-border {
  border: 2px solid #f54e1a;
}
/* line 475, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#write_review #form-rating-star img {
  width: 20px;
}
/* line 479, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#write_review #review-text {
  background-color: #565656;
}

/* line 483, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.percent_disc {
  background: #ca3366;
  font-size: 0.75em;
  color: #fff;
  padding: 0 1%;
  margin-bottom: 0;
  color: #f1eded;
}

/* line 493, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details .tabs .tab-title > a {
  padding: 0.5em 1em;
  margin: .03em;
  background-color: inherit;
  color: inherit;
  border: .1em solid;
}
/* line 500, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details .tabs .tab-title.active > a {
  background-color: #FFFFFF;
  color: #222222;
}
/* line 506, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details ul.no-bullet label b {
  text-decoration: underline;
}
/* line 509, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details ul.no-bullet label {
  color: white;
}
/* line 513, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details table {
  background: inherit;
  border: none;
}
/* line 517, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details table tr:nth-of-type(even) {
  background: inherit;
}
/* line 520, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details table tr td {
  color: #f1f1f1;
  vertical-align: baseline;
}

/* line 528, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block {
  background: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  margin-bottom: 16px;
  color: #f1f1f1;
}
/* line 534, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block li, .listing_panel_block label {
  cursor: auto;
}
/* line 537, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block .variant-price-text {
  font-size: 12px;
}
/* line 541, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block p {
  text-align: left;
  /*background: rgba(96, 96, 96, 0.9) none repeat scroll 0% 0%;*/
  color: #f1f1f1;
  margin: -7px 0px 0px;
  border-radius: 2px 2px 0px 0px;
  padding: 6px;
}
/* line 550, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block ul {
  font-size: 14px;
  padding: 4px;
  list-style-type: circle;
  color: #f1f1f1;
  margin-left: 1.5rem;
}
/* line 558, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.listing_panel_block td {
  vertical-align: baseline;
}

/* line 564, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table, .accordion ul.sub-specs-table {
  display: none;
}
/* line 568, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table.sub-specs-line {
  display: table;
}
/* line 572, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion a, .accordion .sub-grp {
  outline: none;
  color: inherit;
}
/* line 577, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .expand {
  display: none;
  float: right;
}
/* line 582, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .collapse {
  float: right;
}
/* line 586, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .spec-collapse {
  display: block;
}
/* line 591, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .sub-grp p {
  font-size: 0.9rem;
  margin-left: 0.6rem;
}
/* line 594, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .sub-grp p .button-icon {
  transition: all .5s ease-in-out;
  display: inline-block;
  font-weight: 700;
  font-size: 1.1rem;
  line-height: 1.8;
}
/* line 601, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion .sub-grp p .button-toggle {
  transform: rotate(90deg);
}
/* line 606, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table.sub-specs-table, .accordion .sub-specs-line {
  margin: 0 0 0.5rem 0.6rem;
}
/* line 608, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table.sub-specs-table td.spec, .accordion .sub-specs-line td.spec {
  padding: 0.2rem 0.4rem;
}
/* line 611, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table.sub-specs-table td.spec.first, .accordion .sub-specs-line td.spec.first {
  padding-right: 0px;
}
/* line 615, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.accordion table.sub-specs-table {
  padding-left: 0.7rem;
}

/* line 620, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#designable_details ul {
  margin-left: 1rem;
}

/* line 624, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.pre-order {
  padding: 7px 20px;
}
/* line 626, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.pre-order #pre-order-check {
  vertical-align: middle;
  margin: 0px -2px 0px 0px;
}
/* line 630, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.pre-order label {
  color: #f1f1f1;
}

/* line 637, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myImg {
  cursor: pointer;
  transition: 0.3s;
  display: inline-block;
}

/* The Modal (background) */
/* line 644, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.modal {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: #424242;
}

/* Modal Content (Image) */
/* line 658, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
}

/* line 665, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#loadingImage {
  position: fixed;
  left: 45%;
  top: 30%;
  border: 4px dotted #ffffff;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}

/* Add Animation - Zoom in the Modal */
/* line 678, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.modal-content {
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
/* shake effect */
/* line 695, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.shake-effect {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-fill-mode: none;
  animation-fill-mode: none;
  -webkit-animation-name: shake;
  animation-name: shake;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    transform: translateX(5px);
    color: #ed8f03;
  }
}
@-webkit-keyframes shake {
  0%, 100% {
    -webkit-transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    -webkit-transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    -webkit-transform: translateX(5px);
    color: #ed8f03;
  }
}
/* The Close Button */
/* line 715, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.close {
  font-size: 2em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
  display: block;
  text-align: right;
}

/* line 723, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}

/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px) {
  /* line 733, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
  .modal-content {
    width: 100%;
  }
}
@media only screen and (min-width: 1200px) {
  /* line 739, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
  .modal-content {
    width: auto;
    height: auto;
  }
}
/* line 745, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#branch-banner-iframe {
  z-index: 1 !important;
}

/* line 749, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.notice_class {
  color: orange;
  font-size: 14px;
  line-height: 15px;
}

/* line 755, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.product_design_price {
  margin: 0;
}

/* line 759, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.font_greyed {
  color: #f1f1f1;
}
/* line 761, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.font_greyed.delivery_day {
  margin-top: 5px;
}

/* line 765, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.rating_message {
  font-size: 14px;
  padding-left: 8px;
}

/* line 769, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.reviews_ratings {
  display: inline;
}
/* line 771, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.reviews_ratings .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 21%;
  font-weight: bold;
}
/* line 779, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.reviews_ratings .green-rating {
  background-color: #16be48;
}
/* line 782, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.reviews_ratings .red-rating {
  background-color: #FF5722;
}
/* line 785, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.reviews_ratings .orange-rating {
  background-color: #FFA000;
}

/* line 789, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.review-opinion {
  font-size: 12px;
  font-weight: 600;
  margin-top: 5px;
}

/* line 794, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.size-text {
  margin: 8px 0px 8px 16px;
  color: #f1f1f1;
  font-weight: 700;
  font-size: 14px;
}

/* line 800, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
button.btn-view-size {
  background: transparent;
  color: #7EDFFF;
  padding: 4px 2px;
  font-size: 13px;
  margin: 0px;
}

/* line 807, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
button.btn-view-size:hover {
  background: #009688;
  border: 1px solid #088074;
  color: #1c1b1c;
}

/* line 812, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.size-chart-div {
  margin-bottom: 30px;
  margin: 0px 0px 10px 10px;
  padding: 0px;
  height: 50px;
}
/* line 820, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.size-chart-div .size-chart a.size {
  background-color: #4c4b4b;
  border: 2px solid #000;
  color: #f1f1f1;
  font-weight: 700;
  margin-left: 6px;
  margin-top: 2px;
  float: left;
  border-radius: 1%;
  padding: 5px;
  cursor: pointer;
  display: inline-table;
}
/* line 833, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.size-chart-div .size-chart a.size.selected, .size-chart-div .size-chart a.size:hover {
  background-color: #9a9999;
  color: #2b2a2a;
  border-color: #a2a0a1;
}

/* line 840, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
span.size-left, span.size-right {
  position: absolute;
  padding: 4px 8px;
  font-size: 18px;
  font-weight: 700;
  background-color: #d2d2d2;
  top: 38px;
  color: #2d2b2b;
  z-index: 2;
  cursor: pointer;
  border-radius: 50%;
}

/* line 852, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
span.size-left {
  left: -2px;
  display: none;
}

/* line 856, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
span.size-right {
  right: -28px;
  display: block;
}

/* line 860, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
span.size-left:hover, span.size-right:hover {
  background-color: #afafaf;
}

/* line 864, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart {
  -webkit-overflow-scrolling: touch;
  height: 100%;
}
/* line 867, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .btn_close_top {
  float: right;
  padding: 0px 7px;
  margin: 10px 4px 0px 0px;
  font-size: 24px;
  border-radius: 50%;
}
/* line 874, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .btn_close {
  padding: 8px 30px;
  margin: 20px 0px 80px;
  font-size: 20px;
  border-radius: 5px;
}
/* line 881, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .modal-sm-size .modal-content {
  width: 900px;
  background: #4c4b4b;
  color: white;
}
/* line 886, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-header h4 {
  color: #f1f1f1;
}
/* line 891, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-body .radio-buttons {
  margin: 10px 0px;
}
/* line 893, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-body .radio-buttons .size-label {
  color: #f1f1f1;
  text-align: center;
}
/* line 901, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#modal-size-chart .head_style {
  text-align: center;
  padding: 3px;
  font-size: 12px;
}

/* line 907, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.stitching_offer {
  color: #EE9209;
  font-size: 15px;
  margin-top: 10px;
}

/* line 912, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.design_offer_label {
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

/* line 924, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal {
  background-color: rgba(146, 140, 140, 0.84);
  height: 100%;
  overflow: auto;
}
/* line 928, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-dialog {
  margin-top: 85px;
}
/* line 931, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-header {
  font-size: 13px;
  display: inline-flex;
  background-color: #3e3d3d;
  width: 100%;
}
/* line 936, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-header #stitching-confirmation-message {
  padding: 11px;
  color: #f1f1f1;
}
/* line 940, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-header a {
  padding-left: 45%;
}
/* line 944, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-content {
  width: 288px;
  height: 100%;
  background-color: #191919;
  color: #f1f1f1;
  font-size: 1.25em;
}
/* line 951, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .closeStitchingModal {
  color: white;
  font-size: 1.8em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
}
/* line 957, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-footer {
  text-align: center;
  padding: 2% 0 2%;
}
/* line 960, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-footer .btn {
  padding: 7px !important;
  width: 46%;
  font-size: 12px !important;
  border-radius: 2px;
}
/* line 966, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-footer .stitch {
  background: #0e9a7d;
}
/* line 969, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-footer #add-cart {
  background: #f1f1f1;
  color: #3e3d3d !important;
}
/* line 974, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-body {
  text-align: center;
  padding: 20px;
  font-size: 14px;
}
/* line 978, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#stitchingModal .modal-body .modal-message {
  padding-top: 20px;
}

/* line 984, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal, #qpmTncModal {
  background-color: transparent;
  height: 100%;
  overflow: auto;
  width: 90%;
  margin: 0px 5%;
  outline: none;
}
/* line 992, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-content, #qpmTncModal .modal-dialog .modal-content {
  height: 100%;
  background-color: #424242;
  color: #f1f1f1;
  font-size: 1.25em;
  margin-top: 45px;
}
/* line 998, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-header, #qpmTncModal .modal-dialog .modal-content .modal-header {
  float: right;
  position: absolute;
  right: 5px;
}
/* line 1003, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body, #qpmTncModal .modal-dialog .modal-content .modal-body {
  padding: 16px;
  font-size: 12px;
}
/* line 1007, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body .modal-text .ans, #qpmTncModal .modal-dialog .modal-content .modal-body .modal-text .ans {
  margin-bottom: 20px;
}
/* line 1010, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-content .modal-body .modal-text ul, #qpmTncModal .modal-dialog .modal-content .modal-body .modal-text ul {
  font-size: 12px;
}
/* line 1016, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-footer, #qpmTncModal .modal-dialog .modal-footer {
  text-align: center;
  background: #7e7e7e;
  padding: 15px 0px;
}
/* line 1020, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#bmgnTncModal .modal-dialog .modal-footer a, #qpmTncModal .modal-dialog .modal-footer a {
  padding: 20px 0px;
  color: white;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 1034, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.price_match_guarantee .price_match_description {
  display: none;
  background: white;
  color: black;
  padding: 10px;
  z-index: 1;
  position: absolute;
  margin-top: 10px;
}

/* line 1045, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#design_images_zoom {
  height: 100% !important;
}

/* line 1049, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myModal {
  height: 100%;
}
/* line 1067, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myModal .lSAction > .lSNext {
  background-color: white;
  border-radius: 25px;
  width: 40px;
  height: 40px;
  opacity: 0.6;
  background-image: none;
}
/* line 1069, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myModal .lSAction > .lSNext:before {
  content: '';
  border-right: 5px solid #a99b9b;
  border-top: 5px solid #a99b9b;
  width: 15px;
  height: 15px;
  position: absolute;
  transform: rotate(45deg);
  bottom: 13px;
  left: 12px;
}
/* line 1076, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myModal .lSAction > .lSPrev {
  background-color: white;
  border-radius: 25px;
  width: 40px;
  height: 40px;
  opacity: 0.6;
  background-image: none;
}
/* line 1078, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#myModal .lSAction > .lSPrev:before {
  content: '';
  border-right: 5px solid #a99b9b;
  border-top: 5px solid #a99b9b;
  width: 15px;
  height: 15px;
  position: absolute;
  transform: rotate(-135deg);
  bottom: 13px;
  left: 14px;
}

/* line 1087, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.label_for_image_box {
  position: absolute;
  display: none;
  top: -4px;
  right: 0px;
  height: 125px;
  overflow: hidden;
  z-index: 1;
  width: 128px;
}

/* line 1098, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
.label_text {
  display: none;
  top: 35px;
  width: 170px;
  padding: 5px 29px 5px;
  font-size: 15px;
  text-align: center;
  margin-left: -24px;
  color: #fff;
  z-index: 1;
  background-color: #e92d4c;
  position: absolute;
  left: 19px;
  -webkit-transform: rotate(45deg) translate3d(0, 0, 0);
  -moz-transform: rotate(45deg) translate3d(0, 0, 0);
  -ms-transform: rotate(45deg) translate3d(0, 0, 0);
  transform: rotate(45deg);
}

/* line 1117, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#standard_height_notice {
  background-color: #ffffff;
  position: absolute;
  border-radius: 5px;
  padding: 3px;
  z-index: 1;
  right: 0px;
  top: 111%;
}

/* line 1127, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/designs_black.scss */
#standard_height_notice:after {
  content: '';
  width: 0px;
  height: 0px;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-top: 10px solid transparent;
  border-bottom: 10px solid #ffffff;
  position: absolute;
  top: 0%;
  right: 7%;
  margin-top: -20px;
}

/* line 5, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend, .recommended-design-box {
  border-top: 1px solid #636060;
  border-bottom: 1px solid #636060;
  margin: 0 auto;
  background-color: transparent;
  box-shadow: none;
  max-width: 65.5rem;
  padding: 8px 0px;
}
/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend .title-block h1, .recommended-design-box .title-block h1 {
  color: #f1f1f1;
  font-size: 18px;
  text-transform: capitalize;
}

/* line 22, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend .unbxd-img-loader {
  display: inline-block;
  width: 49%;
  margin-bottom: 10px;
}
/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend .unbxd-img-loader .image-container {
  text-align: center;
  background-color: #4c4b4b;
  padding: 15px 0px;
}
/* line 32, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend .product-1 {
  padding-right: 4px;
}
/* line 35, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.ebox-recommend .product-2 {
  padding-left: 4px;
}

/* line 40, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.no-design-box {
  text-align: center;
  font-style: italic;
}

/* line 45, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;
}
/* line 50, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products {
  margin: 0px;
}
/* line 52, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list {
  display: inline-block;
  margin-right: 8px;
  width: 153px;
}
/* line 56, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box {
  position: relative;
  /*border: 1px solid #171616;*/
  margin: 0;
  width: 153px;
  border-radius: 2px;
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box {
  width: 151px;
  border-radius: 2px 2px 0px 0px;
}
/* line 66, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box img {
  width: 100%;
  border-radius: 2px 2px 0px 0px;
}
/* line 70, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating {
  position: absolute;
  width: 40px !important;
  height: 40px;
  margin-top: -25px;
}
/* line 75, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 5%;
  font-weight: bold;
}
/* line 83, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .green-rating {
  background-color: #16be48;
}
/* line 86, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .red-rating {
  background-color: #FF5722;
}
/* line 89, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .orange-rating {
  background-color: #FFA000;
}
/* line 94, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel {
  height: 3.8em;
  position: relative;
  padding: 0.6em;
  background: #4c4b4b;
  border: none;
  margin-bottom: 0em;
  border-radius: 0px 0px 2px 2px;
  text-align: center;
}
/* line 104, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel.design_desc a {
  color: white;
}
/* line 108, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel .price-block {
  font-size: 14px;
  color: #f1f1f1;
}
/* line 116, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .scroll-btn {
  position: relative;
  font-size: 26px;
  line-height: 32px;
  background-color: #4c4b4b;
  color: #eaeaea;
  text-align: center;
  bottom: 160px;
  width: 5%;
  box-shadow: -1px 0px 2px 0px #383737;
  display: none;
}
/* line 128, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .scroll-btn-left {
  left: 1px;
  float: right;
}
/* line 132, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
.recommended-design-box .scroll-btn-right {
  right: 1px;
  float: left;
}

@media only screen and (max-width: 989px) {
  /* line 140, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .recommended-design-box .recommended-products {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow-x: scroll;
  }
  /* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 2px;
  }
}
@media only screen and (min-width: 980px) {
  /* line 154, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .recommended-design-box .row {
    text-align: center;
  }
  /* line 157, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .recommended-design-box .recommended-products {
    overflow-x: visible;
    width: auto;
  }
  /* line 160, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 10px;
  }

  /* line 165, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_black.scss */
  .scroll-btn {
    display: none;
  }
}
/* line 11, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.page.designs_reviews #container, .page.reviews_site_review #container {
  margin-top: 1.8em;
}

/* line 15, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
#site_review_top_content {
  float: left;
  /*border: 1px dotted grey;*/
  padding: 20px;
  background-color: #4c4b4b;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  box-shadow: 0 0 0.5em #2d2d2d;
  width: 100%;
}
/* line 25, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
#site_review_top_content #top_content {
  font-size: 0.9rem;
}
/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
#site_review_top_content #top_content.read-more {
  height: 125px;
  overflow: hidden;
}

/* line 33, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
#review_of_design {
  background-color: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  padding: 3px 0px 0 0px !important;
}

/* line 38, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container {
  width: 100%;
  height: 100%;
  color: #f1f1f1;
  padding: 10px;
}
/* line 43, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .heading {
  font-size: 18px;
  font-weight: bold;
}
/* line 47, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .heading_review {
  padding-bottom: 8px;
  margin-left: 7px;
}
/* line 50, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .heading_review .view_all {
  font-size: 15px;
  float: right;
  margin-right: 10px;
}
/* line 56, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .heading-content {
  font-size: 15px;
  font-weight: bold;
}
/* line 60, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-btn-row {
  width: 100%;
  margin-top: 2%;
}
/* line 63, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-btn-row .tabbed-btn {
  width: 50%;
  text-align: center;
  vertical-align: middle;
  padding: 5px 2px;
  font-size: 16px;
  float: left;
  cursor: pointer;
  color: #f1f1f1;
}
/* line 73, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-btn-row .active-btn {
  background-color: #4c4b4b;
}
/* line 76, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-btn-row .passive-btn {
  background-color: transparent;
  border: none;
}
/* line 81, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view {
  float: left;
  width: 100%;
  color: #f1f1f1;
  background-color: #4c4b4b;
  padding-bottom: 5px;
}
/* line 87, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row:first-child {
  border-bottom: none;
}
/* line 90, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view #hidden_site_review {
  display: none;
}
/* line 93, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row {
  padding: 2%;
  width: 100%;
  clear: both;
  margin-bottom: -10px;
}
/* line 98, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .add-new-review {
  float: right;
}
/* line 102, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block {
  background-color: #424242;
  box-shadow: 0 0 0.5em #2d2d2d;
  display: inline-block;
  width: 96%;
  padding: 15px 15px 0px 15px;
  margin-left: 2%;
}
/* line 109, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .margin-left-top {
  margin-top: 1%;
  margin-left: 8%;
}
/* line 113, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .view-more-btn {
  position: relative;
  width: 100px;
  padding: 5px 3px;
  float: right;
  height: 25px;
  font-size: 12px;
  color: #7EDFFF;
  text-align: right;
}
/* line 124, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .user-sign {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #000000;
  text-align: center;
  float: left;
  margin: 5px 0px 0px 0px;
}
/* line 132, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .user-sign .user-name {
  position: relative;
  top: 25%;
  font-size: 1rem;
}
/* line 136, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .user-sign .user-name .user_image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  top: -12.5px;
  position: relative;
}
/* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .design-img {
  float: left;
  margin-top: 7px;
  overflow: hidden;
}
/* line 149, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .design-img .image_block_hidden {
  width: 50px;
  height: 50px;
}
/* line 154, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .stars {
  width: 55%;
  float: left;
  font-size: 12px;
}
/* line 160, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .stars .star img {
  background: none;
}
/* line 163, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .stars .user-name-full {
  overflow: hidden;
}
/* line 166, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .stars .rated-ago {
  margin-top: 0px;
}
/* line 170, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container .tabbed-view .review-row .block .review-text {
  float: left;
  width: 100%;
  height: 56px;
  overflow: hidden;
  font-size: 12px;
  text-align: justify;
  padding-right: 3%;
  margin-left: 0px;
}
/* line 183, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container #product_reviews {
  display: block;
}
/* line 186, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.review-container #site_reviews {
  display: none;
}

/* line 190, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.reveal-modal {
  color: #f1f1f1;
  background-color: #4c4b4b;
}

/* line 194, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.black-content {
  position: absolute;
  top: 0px;
  height: 100%;
  overflow: scroll;
}
/* line 195, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.black-content #modalTitle {
  color: #f1f1f1 !important;
}
/* line 198, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.black-content #modal_star {
  border-bottom: 1px solid white;
  padding-bottom: 15px;
}
@media (max-height: 600px) {
  /* line 203, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
  .black-content .modal-review-text {
    height: 100%;
    overflow-y: scroll;
  }
}
@media only screen and (min-device-width: 500px) and (orientation: landscape) {
  /* line 210, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
  .black-content .modal-review-text {
    height: 100%;
    overflow: scroll;
  }
}

/* line 220, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-green {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #2ecc71;
  border-color: #2ecc71;
}

/* line 226, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-blue {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #3498db;
  border-color: #3498db;
}

/* line 232, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-red {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #EC407A;
  border-color: #EC407A;
}

/* line 238, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-green:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

/* line 242, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-blue:hover {
  background-color: #2980b9;
  border-color: #2980b9;
}

/* line 246, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/reviews_black.scss */
.btn-custom-red:hover {
  background-color: #E91E63;
  border-color: #E91E63;
}
