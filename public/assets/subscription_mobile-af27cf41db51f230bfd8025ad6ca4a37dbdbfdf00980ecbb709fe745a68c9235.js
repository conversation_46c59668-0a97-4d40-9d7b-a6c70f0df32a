(function(){$(function(){var e,n,o,t,i,s;return n=function(e){var n;return n=setInterval(function(){var i;i=o(e),i>parseFloat(10)/100&&(clearInterval(n),setTimeout(t,1e3))},1e3)},o=function(e){var n;return n=$(window).scrollTop()-e,0>n&&(n=0,e=$(window).scrollTop()),parseFloat(n)/parseFloat($(document).height())},e=function(){var e,t;return t=$(window).scrollTop(),e=$(document).height(),function(){var i;return e>0?i=setInterval(function(){var e;e=o(t),e>parseFloat(60)/100&&$(".pages_home").length>0&&(clearInterval(i),n(t))},1e3):void 0}}(),e(),s=function(e){var n,o;o="/subscriptions",n={subscriptions:{source_url:window.location.href,email:e,appsource:"mobile"}},$.post(o,n,function(e){e.error?$(".sub-msg").text(e.error_message):(e.data.coupon_code?($(".sub-msg").text(e.data.message+e.data.coupon_code),$("#mobile-subscribe-window").foundation("reveal","close"),$(".coupon_code").html(e.data.coupon_code),$(".offer_msg").html(e.data.message),$("#notice_banner").slideDown(500,function(){setTimeout(function(){$("#notice_banner").slideUp(500,function(){$("#notice_banner").remove()})},5e3)})):$(".sub-msg").text(e.data),$(".subscribe_text_message").hide(),e.already_subscribed.length?($(".sub-msg").text("We noticed.!!!!You have already subscribed!"),setTimeout(function(){$("#mobile-subscribe-window").foundation("reveal","close")},5e3)):(setTimeout(function(){$("#mobile-subscribe-window").foundation("reveal","close")},5e3),$(".sub-msg").addClass("subscribe_success"),$("#mobile-subscribe-window input").hide(),ga("send","event","Subscription","subscription_successfull",{nonInteraction:1,metric1:1})))},"json")},t=function(){var e;getCookie("subscribe").length||/cart*|order*|account*/i.test(window.location.href)||($("#mobile-subscribe-window").foundation("reveal","open"),e||(e=!0,ga("send","event","Subscription","subscription_form_view",{nonInteraction:1,metric2:1})))},$(document).on("click","#email-subscribe-button",function(e){var n,o;return e.preventDefault(),"undefined"!=typeof $("#subscribe-input").val()?(o=$("#subscribe-input").val(),n=/^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,o.trim()&&!o.endsWith(".co")&&n.test(o)?s(o):$(".sub-msg").text("Please enter a valid Email address.")):void 0}),$(document).on("click","#subscribe-input",function(){return $("#modal-subscribe-box").animate({scrollTop:$(this).offset().top},"slow")}),$(document).foundation("reveal"),i=!1,$(document).on("click","#notice_close_btn",function(){return $("#notice_banner").slideUp(500,function(){return $("#notice_banner").remove()})}),$(document).on("click",".close_subscribe, #email-cancel-button",function(){$("#mobile-subscribe-window").foundation("reveal","close"),setCookie("subscribe","closed",7),ga("send","event","Subscription","subscription_window_close",{nonInteraction:1,metric3:1})})})}).call(this);