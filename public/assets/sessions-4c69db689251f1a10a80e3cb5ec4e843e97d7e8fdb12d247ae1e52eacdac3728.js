(function(){var e,n,t;$(function(){return $("#send_otp_form").on("submit",function(n){return n.preventDefault(),$(".otp-sent-phone").html($("#phone").val()),$.ajax(e($("#phone").val(),$(this).attr("action"),!1))}),$(".closebtn").on("click",function(){return $("#error-message").hide()}),$("#verify_otp_form").on("submit",function(e){return ga("set","dimension12","logged in with otp"),$(".overlay").show(),$(".progress_img1").show(),e.preventDefault(),$("#user_phone").val($("#phone").val()),$("#one_time_password").val(n()),e.currentTarget.submit()}),$("#new_account").on("submit",function(){return $(".overlay").show(),$(".progress_img1").show(),ga("set","dimension12","logged in with email")}),$("#resend_otp").on("click",function(){return $.ajax(e($("#phone").val(),"/accounts/send_otp",!0)),t(),$(".resend-tag").hide(),$(".timer").show()}),$("#email-login-link").on("click",function(){return $(".mobile-number-login").hide(),$(".email-login").show(),ga("set","dimension12","email login form view")}),$("#number-login-link").on("click",function(){return $(".email-login").hide(),$(".mobile-number-login").show(),ga("set","dimension12","otp login form view")})}),e=function(e,n,o){return{type:"POST",data:{phone:e},url:n,dataType:"JSON",success:function(n){return n.error===!0?$("#error-message").show():n.sms_sent===!0&&o===!1?($(".new-login-form").hide(),$(".verify-otp-form").show(),$(".otp-sent-phone").text(e),t()):void 0}}},n=function(){var e;return e=$("#otpBox1").val()+$("#otpBox2").val()+$("#otpBox3").val()+$("#otpBox4").val()},t=function(){var e,n;return n=document.getElementById("countdown-timer").textContent,e=setInterval(function(){n--,10>n&&(n="0"+n),document.getElementById("countdown-timer").textContent=n,$(".resend-tag").hide(),$(".timer").show(),0>=n&&(clearInterval(e),$(".resend-tag").show(),$(".timer").hide(),document.getElementById("countdown-timer").textContent=15)},1e3)}}).call(this);