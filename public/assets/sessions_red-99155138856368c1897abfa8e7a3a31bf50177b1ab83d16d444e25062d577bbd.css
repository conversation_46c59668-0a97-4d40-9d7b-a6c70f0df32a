/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 4, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.sign-in-links {
  text-align: center;
}
/* line 7, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.sign-in-links .sign-in-link {
  display: inline-block;
  width: 45%;
  height: auto;
  margin: 0 4px 10px;
  text-align: center;
}

/* line 18, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.bordered_block label {
  color: white;
}

/* line 22, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.login_btn, .sign-up-btn, .reset-password {
  background: #8f1b1d !important;
  border-radius: 0 !important;
  text-transform: uppercase;
  font-size: 14px;
}

/* line 29, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
hr {
  border: #bdb3b3 solid;
  border-width: 0.1em 0 0;
  margin-top: 0.4em;
}

/* line 36, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
#new_account label {
  color: #303030;
}

/* line 40, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.registration-block {
  display: inline-flex;
  padding: 0.75em;
  width: 100%;
  text-align: center;
}
/* line 45, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/sessions_red.scss */
.registration-block li:first-child {
  border-right: 1px solid #8f1b1d;
}
