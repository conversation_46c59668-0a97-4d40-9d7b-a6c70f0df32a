(function() {
  var disableExpressForWallet, generateOtp, handleCodOrder, hideKeyboard, paypal_smartpay_available, paypal_smartpay_hide, paypal_smartpay_show, togglePrepaidPromotion, updateShippingDetails;

  $(function() {
    return ga('send', 'event', {
      eventCategory: 'Checkout',
      eventAction: 'order-new',
      nonInteraction: true
    });
  });

  $(function() {
    var cod_is_enabled, getCookie, setCookie, toggleCodCharges, togglePaypalMessage, wallet_error;
    $('.payment_options input:radio:first').prop('checked', 'checked');
    togglePrepaidPromotion();
    togglePaypalMessage = function() {
      if ($('input[name="order[pay_type]"]:checked').attr('id') === 'order_pay_type_paypal' || $('input[name="order[pay_type]"]:checked').attr('id') === 'order_pay_type_creditdebit_cardnet_banking') {
        return $('#paypal_message').show();
      } else {
        return $('#paypal_message').hide();
      }
    };
    $('.domestic_offers_toggle').click(function() {
      if ($('.domestic_offers_toggle .show_toggle_text').hasClass('hidden_offer_class')) {
        $('.domestic_offers_toggle .show_toggle_text').removeClass('hidden_offer_class');
        $('.offers_tab .hidden_offers').show();
        $('.domestic_offers_toggle .show_toggle_text').text('Show Less');
        $('.domestic_offers_toggle #down_arrow_toggle').hide();
        $('.domestic_offers_toggle .hide_up').show();
        return $('.domestic_offers_toggle #up_arrow_toggle').show();
      } else {
        $('.domestic_offers_toggle .show_toggle_text').addClass('hidden_offer_class');
        $('.offers_tab .hidden_offers').hide();
        $('.domestic_offers_toggle .show_toggle_text').text($('#show_more_text').val());
        $('.domestic_offers_toggle #up_arrow_toggle').hide();
        return $('.domestic_offers_toggle #down_arrow_toggle').show();
      }
    });
    toggleCodCharges = function() {
      if ($('input[name="order[pay_type]"]:checked').attr('id') === 'order_pay_type_cash_on_delivery') {
        $('.grand_total').hide();
        $('.grand_total_with_cod.hide').show();
        return $('.cod_charges').show();
      } else {
        $('.grand_total').show();
        $('.grand_total_with_cod.hide').hide();
        return $('.cod_charges').hide();
      }
    };
    $('input[name="order[pay_type]"]').on('click', function(e) {
      toggleCodCharges();
      togglePrepaidPromotion();
      return togglePaypalMessage();
    });
    toggleCodCharges();
    togglePaypalMessage();
    setCookie = function(cname, cvalue, exdays) {
      var d, expires;
      d = new Date;
      d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
      expires = 'expires=' + d.toUTCString();
      document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/';
    };
    getCookie = function(name) {
      var parts, value;
      value = '; ' + document.cookie;
      parts = value.split('; ' + name + '=');
      if (parts.length === 2) {
        return parts.pop().split(';').shift();
      }
    };
    if ($('.payment_options').length > 0) {
      window.onload = function() {
        var selected_payment_option;
        selected_payment_option = getCookie('selected_payment_option');
        if ($('#auto_select_retry_cod').val()) {
          $('#order_pay_type_cash_on_delivery').attr('checked', true);
        } else if (selected_payment_option === void 0) {
          $('.payment_options input:radio:first').prop('checked', 'checked');
        } else {
          $('.payment_options #' + selected_payment_option).prop('checked', 'checked');
          togglePrepaidPromotion();
          toggleCodCharges();
        }
        if ($('#wallet_payment_option').length > 0) {
          cod_is_enabled();
        }
        if ($('.payment_options input:radio:checked').val() === "PayPal" && paypal_smartpay_available()) {
          paypal_smartpay_show();
        }
      };
      $('input[name="order[pay_type]"]').on('click', function(e) {
        setCookie('selected_payment_option', $('input[name="order[pay_type]"]:checked').attr('id'), 7);
      });
      cod_is_enabled = function() {
        var amount, express_set;
        if ($('input[name="order[pay_type]"]:checked').attr('id') === 'order_pay_type_cash_on_delivery') {
          $('.grand_total, #wallet_discount_order_page, #wallet_payment_option').hide();
          $('.cod_charges, .grand_total_with_cod.hide, #wallet_cant_be_used_for_cod').show();
          if ($('#wallet_payment_option').length > 0) {
            amount = $('#wallet_payment_option').data();
            $('#you_pay_grand_total_with_cod').text(parseFloat(amount.total) + parseFloat(amount.referralAmount) + parseFloat(amount.codCharges));
          }
        } else {
          if ($('#delivery_type_express').length > 0 && $('#delivery_type_express').is(':checked')) {
            $('#wallet_payment_option').hide();
            amount.total += data('extraa_shipping');
            express_set = true;
          } else {
            if (express_set) {
              amount.total -= data('extraa_shipping');
              express_set = false;
            }
            $('#wallet_payment_option').show();
          }
          $('#wallet_discount_order_page, .grand_total').show();
          $('#wallet_cant_be_used_for_cod, .cod_charges, .grand_total_with_cod.hide').hide();
          if ($('#wallet_payment_option').length > 0 && $('#wallet_return').prop('checked') !== true) {
            amount = $('#wallet_payment_option').data();
            $('#grand_total_without_cod').text('Grand Total : ' + amount.total);
            $('#you_pay_grand_total').text(parseFloat(amount.total));
          }
        }
      };
      $('input[name="order[pay_type]"]').on('click', function(e) {
        var pay_type_value;
        pay_type_value = $(this).val();
        paypal_smartpay_hide();
        if (pay_type_value === 'PayPal') {
          if (paypal_smartpay_available()) {
            paypal_smartpay_show();
          }
        }
        cod_is_enabled();
      });
      wallet_error = function(element) {
        element.removeProp('checked');
        $('#wallet_error').text('*Wallet not applicable').show();
      };
      $('#wallet_return').click(function() {
        setWalletDiscount();
        disableExpressForWallet();
      });
    }
    if (!$('#otp-form').length) {
      $('#codModal').css('height', 'auto').css('min-height', 0);
    }
    $('#new_order').submit(function(e, submit) {
      if (($('#order_pay_type_cash_on_delivery').is(":checked") || $('#cashondelivery').is(":checked")) && $('#codModal').length && !submit) {
        e.preventDefault();
        return handleCodOrder();
      }
    });
    $('#successButton').click(function() {
      return $('#new_order').trigger('submit', true);
    });
    $(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
      $("#action_buttons input[type='submit']").removeAttr('disabled');
      $("#action_buttons input[type='submit']").attr('value', 'PLACE ORDER');
      $('#cod-otp').val('');
      $('#otp-error-msg').hide();
      return $('html').removeClass('modal-open');
    });
    $(document).on('open.fndtn.reveal', '[data-reveal]', function() {
      $('html').addClass('modal-open');
      $('#otp-phone-change-form-and-content', '#phone-change-error-msg').hide();
      return $('#otp-form-and-content').show();
    });
    $(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
      return $('#cod-otp').focus();
    });
    $('#otp-form').on('submit', function(e) {
      e.preventDefault();
      return $.ajax({
        type: "POST",
        data: {
          "cod-otp": $('#cod-otp').val()
        },
        url: $(this).attr('action'),
        dataType: 'JSON',
        success: function(data, status, jqxhr) {
          if (data['verified'] === true) {
            return $('#new_order').trigger('submit', true);
          } else {
            return $('#otp-error-msg').show();
          }
        }
      });
    });
    $('#cod-otp').on('keydown', function() {
      return $('#otp-error-msg').hide();
    });
    $('#resend-otp').on('click', function() {
      $(this).attr('disabled', true).css('opacity', 0.5);
      setTimeout(function() {
        return $('#resend-otp').attr('disabled', false).css('opacity', 1);
      }, 10000);
      return $.ajax(generateOtp($('#order_shipping_address').val(), $('#otp-phone').val(), true));
    });
    $('#cod-otp').on('input', function() {
      if (($(this).val()).match(/^[\d]{5}$/)) {
        return hideKeyboard($(this));
      }
    });
    $('#phone-change-button').on('click', function() {
      $('#otp-form-and-content').hide();
      $('#otp-phone-change-form-and-content').show();
      return $('#phone_for_otp').val($('#otp-phone').val());
    });
    $("#otp-phone-change-form").on('submit', function() {
      if ($("#phone_for_otp").val().length < 8 || $("#phone_for_otp").val().length > 15) {
        $('#phone-change-error-msg').show();
        return false;
      }
    });
    $('#otp-phone-change-form').on('submit', function(e) {
      e.preventDefault();
      return $.ajax({
        type: "POST",
        data: {
          'phone': $('#phone_for_otp').val(),
          'address_id': $('#address-phone-change-form').val()
        },
        url: $(this).attr('action'),
        dataType: 'JSON',
        success: function(data, status, jqxhr) {
          if (data['otp_sent']) {
            $('#otp-phone-change-form-and-content').hide();
            $('#otp-phone-text-value').html($('#phone_for_otp').val());
            return $('#otp-form-and-content').show();
          } else {
            return $('#phone-change-error-msg').show();
          }
        }
      });
    });
    $('#otp-phone-change-form').on('ajax:success', function(evt, data) {
      if (data['otp_sent']) {
        $('#otp-phone-change-form-and-content').hide();
        $('#otp-phone-text-value').html($('#phone_for_otp').val());
        return $('#otp-form-and-content').show();
      } else {
        return $('#phone-change-error-msg').show();
      }
    });
    $('#phone_for_otp').on('click', function() {
      return $('#phone-change-error-msg').hide();
    });
    return $('#otp-form-link').on('click', function() {
      $('#otp-phone-change-form-and-content').hide();
      return $('#otp-form-and-content').show();
    });
  });

  handleCodOrder = function() {
    $('#codModal').foundation('reveal', 'open');
    if ($('#otp-form').length) {
      return $.ajax(generateOtp($('#order_shipping_address').val(), $('#otp-phone').val(), false));
    }
  };

  generateOtp = function(addressId, phone, resend) {
    return {
      type: 'POST',
      data: {
        phone: phone,
        address_id: addressId,
        resend: resend
      },
      url: '/carts/generate_otp'
    };
  };

  hideKeyboard = function(element) {
    element.attr('readonly', 'readonly');
    element.attr('disabled', 'true');
    setTimeout((function() {
      element.blur();
      element.removeAttr('readonly');
      return element.removeAttr('disabled');
    }), 100);
  };

  $(function() {
    return $('.add_checkout').on('click', function(e) {
      if (typeof fbq !== 'undefined') {
        return fbq('track', 'AddPaymentInfo');
      }
    });
  });

  updateShippingDetails = function() {
    var data_tag_shipping, grand_total, shipping_cost;
    data_tag_shipping = $('input[name="delivery_type"]:checked');
    shipping_cost = data_tag_shipping.data('shipping');
    grand_total = data_tag_shipping.data('total');
    if (shipping_cost !== void 0 && grand_total !== void 0) {
      $('.grand_total').html('Grand Total : ' + grand_total);
      $('#shipping_charge').html('Shipping : ' + shipping_cost);
      $('.grand_total.top').html(grand_total);
      $('#estd_days').html('Estimated Delivery : ' + data_tag_shipping.data('delivery-days') + ' days');
    }
    if ($('#delivery_type_express').is(':checked')) {
      $("#wallet_return").prop('disabled', true);
      $('#wallet_payment_option').hide();
      return $('#wallet_cant_be_used_for_express_delivery').show();
    } else {
      $("#wallet_return").prop('disabled', false);
      $('#wallet_payment_option').show();
      return $('#wallet_cant_be_used_for_express_delivery').hide();
    }
  };

  togglePrepaidPromotion = function() {
    var grand_total, grand_total_with_prepaid_discount, grand_total_without_shipping, selected_option;
    selected_option = $('input[name="order[pay_type]"]:checked');
    if (selected_option.data('domestic')) {
      if ($('input[name="order[pay_type]"]:checked').attr('id') !== 'order_pay_type_cash_on_delivery') {
        if (selected_option.data('prepaidShippingPromo') === 'available') {
          grand_total_without_shipping = selected_option.data('grandtotalWithoutShipping');
          $('#shipping_charge').html('Shipping : ' + 'FREE'.fontcolor('green').bold());
          $('.grand_total').html('Grand Total : ' + grand_total_without_shipping);
          $('.grand_total.top').html(grand_total_without_shipping);
          $('#prepaid_discount').val(gon.prepaid_discount);
          return $('.prepaid_discount').show();
        } else {
          $('#shipping_charge').html('Shipping : ' + selected_option.data('shipping'));
          if (selected_option.data('prepaidPromotion')) {
            grand_total_with_prepaid_discount = selected_option.data('grandtotalWithPrepaidDiscount');
            $('.grand_total').html('Grand Total : ' + grand_total_with_prepaid_discount);
            $('.grand_total.top').html(grand_total_with_prepaid_discount);
            $('#prepaid_discount').val(gon.prepaid_discount);
            return $('.prepaid_discount').show();
          } else {
            grand_total = selected_option.data('grandtotal');
            $('.grand_total').html('Grand Total : ' + grand_total);
            $('.grand_total.top').html(grand_total);
            $('#prepaid_discount').val(0);
            return $('.prepaid_discount').hide();
          }
        }
      } else {
        $('#shipping_charge').html('Shipping : ' + selected_option.data('shipping'));
        $('#prepaid_discount').val(0);
        return $('.prepaid_discount').hide();
      }
    }
  };

  $(function() {
    updateShippingDetails();
    return disableExpressForWallet();
  });

  $(function() {
    $('input[name="delivery_type"]').on('click', function(e) {
      return updateShippingDetails();
    });
    return $(window).scroll(function() {
      stickyButton(action_buttons, totals_block, 3.3);
      if (paypal_smartpay_available()) {
        return stickyButton(paypal_button, totals_block, 3.1);
      }
    });
  });

  disableExpressForWallet = function() {
    if ($('#wallet_return').length > 0 && document.getElementById("wallet_return").checked) {
      $('#express_delivery_division').hide();
      return $('#express_delievery_cannot_be_applied_for_wallet').show();
    } else if ($('#delivery_type_express').length === 1) {
      document.getElementById('express_delivery_division').style.display = 'block';
      return $('#express_delievery_cannot_be_applied_for_wallet').hide();
    }
  };

  paypal_smartpay_available = function() {
    return $('#paypal_button').length > 0;
  };

  paypal_smartpay_show = function() {
    $('#action_buttons').hide();
    return $('#paypal_button').show();
  };

  paypal_smartpay_hide = function() {
    $('#paypal_button').hide();
    return $('#action_buttons').show();
  };

}).call(this);
