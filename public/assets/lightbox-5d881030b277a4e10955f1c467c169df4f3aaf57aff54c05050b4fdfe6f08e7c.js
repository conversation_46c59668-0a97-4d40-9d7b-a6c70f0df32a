(function(){!function(t,i){return"function"==typeof define&&define.amd?define(["jquery"],i):"object"==typeof exports?module.exports=i(require("jquery")):t.lightbox=i(t.jQuery)}(this,function(t){var i;return i=function(i){return this.album=[],this.currentImageIndex=void 0,this.init(),this.options=t.extend({},this.constructor.defaults),this.option(i)},i.defaults={albumLabel:"Image %1 of %2",alwaysShowNavOnTouchDevices:!0,fadeDuration:500,fitImagesInViewport:!0,positionFromTop:50,resizeDuration:700,showImageNumberLabel:!0,wrapAround:!1},i.prototype.option=function(i){return t.extend(this.options,i)},i.prototype.imageCountLabel=function(t,i){return this.options.albumLabel.replace(/%1/g,t).replace(/%2/g,i)},i.prototype.init=function(){return this.enable(),this.build()},i.prototype.enable=function(){var i;return i=this,t("body").on("click","a[rel^=lightbox], area[rel^=lightbox], a[data-lightbox], area[data-lightbox], div[data-lightbox]",function(e){return i.start(t(e.currentTarget)),!1})},i.prototype.build=function(){var i;return i=this,t('<div id="lightboxOverlay" class="lightboxOverlay">\n</div>\n<div id="lightbox" class="lightbox">\n  <div class="lb-dataContainer">\n    <div class="lb-data">\n      <div class="lb-details">\n        <span class="lb-caption">\n        </span>\n        <span class="lb-number">\n        </span>\n      </div>\n      <div class="lb-closeContainer">\n        <a class="lb-close">\n        </a>\n      </div>\n    </div>\n  </div>\n  <div class="lb-outerContainer">\n    <div class="lb-container">\n      <img class="lb-image" src="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==" />\n      <div class="lb-nav">\n        <a class="lb-prev" href="" ></a>\n        <a class="lb-next" href="" ></a>\n      </div>\n      <div class="lb-loader">\n        <a class="lb-cancel">\n        </a>\n      </div>\n    </div>\n  </div>\n</div>').appendTo(t("body")),this.$lightbox=t("#lightbox"),this.$overlay=t("#lightboxOverlay"),this.$outerContainer=this.$lightbox.find(".lb-outerContainer"),this.$container=this.$lightbox.find(".lb-container"),this.containerTopPadding=parseInt(this.$container.css("padding-top"),10),this.containerRightPadding=parseInt(this.$container.css("padding-right"),10),this.containerBottomPadding=parseInt(this.$container.css("padding-bottom"),10),this.containerLeftPadding=parseInt(this.$container.css("padding-left"),10),this.$overlay.hide().on("click",function(){return i.end(),!1}),this.$lightbox.hide().on("click",function(e){return"lightbox"===t(e.target).attr("id")&&i.end(),!1}),this.$outerContainer.on("click",function(e){return"lightbox"===t(e.target).attr("id")&&i.end(),!1}),this.$lightbox.find(".lb-prev").on("click",function(){return i.changeImage(0===i.currentImageIndex?i.album.length-1:i.currentImageIndex-1),!1}),this.$lightbox.find(".lb-next").on("click",function(){return i.changeImage(i.currentImageIndex===i.album.length-1?0:i.currentImageIndex+1),!1}),this.$lightbox.find(".lb-loader, .lb-close").on("click",function(){return i.end(),!1})},i.prototype.start=function(i){var e,n,o,a,r,s,h,l,d,u;if(o=function(t){return d.album.push({link:t.attr("href"),title:t.attr("data-title")||t.attr("title")})},d=this,n=t(window),n.on("resize",t.proxy(this.sizeOverlay,this)),t("select, object, embed").css({visibility:"hidden"}),this.sizeOverlay(),this.album=[],s=0,a=i.attr("data-lightbox"),e=void 0,a)for(t(".slick-cloned").removeAttr("data-lightbox"),e=t(i.prop("tagName")+'[data-lightbox="'+a+'"]'),r=0;r<e.length;)o(t(e[r])),e[r]===i[0]&&(s=r),r=++r;else if("lightbox"===i.attr("rel"))o(i);else for(e=t(i.prop("tagName")+'[rel="'+i.attr("rel")+'"]'),h=0;h<e.length;)o(t(e[h])),e[h]===i[0]&&(s=h),h=++h;return u=n.scrollTop()+this.options.positionFromTop,l=n.scrollLeft(),this.$lightbox.css({top:u+"px",left:l+"px"}).fadeIn(this.options.fadeDuration),this.changeImage(s)},i.prototype.changeImage=function(i){var e,n,o;return o=this,this.disableKeyboardNav(),e=this.$lightbox.find(".lb-image"),this.$overlay.fadeIn(this.options.fadeDuration),t(".lb-loader").fadeIn("slow"),this.$lightbox.find(".lb-image, .lb-nav, .lb-prev, .lb-next, .lb-dataContainer, .lb-numbers, .lb-caption").hide(),this.$outerContainer.addClass("animating"),n=new Image,n.onload=function(){var a,r,s,h,l,d,u;return a=void 0,r=void 0,s=void 0,h=void 0,l=void 0,d=void 0,u=void 0,e.attr("src",o.album[i].link),a=t(n),e.width(n.width),e.height(n.height),o.options.fitImagesInViewport&&(u=t(window).width(),d=t(window).height(),l=u-o.containerLeftPadding-o.containerRightPadding-20,h=d-o.containerTopPadding-o.containerBottomPadding-120,o.options.maxWidth&&o.options.maxWidth<l&&(l=o.options.maxWidth),o.options.maxHeight&&o.options.maxHeight<l&&(h=o.options.maxHeight),(n.width>l||n.height>h)&&(n.width/l>n.height/h?(s=l,r=parseInt(n.height/(n.width/s),10),e.width(s),e.height(r)):(r=h,s=parseInt(n.width/(n.height/r),10),e.width(s),e.height(r)))),o.sizeContainer(e.width(),e.height())},n.src=this.album[i].link,this.currentImageIndex=i},i.prototype.sizeOverlay=function(){return this.$overlay.width(t(window).width()).height(t(document).height())},i.prototype.sizeContainer=function(t,i){var e,n,o,a,r,s;return r=function(){return s.$lightbox.find(".lb-dataContainer").width(n),s.$lightbox.find(".lb-prevLink").height(e),s.$lightbox.find(".lb-nextLink").height(e),s.showImage()},s=this,a=this.$outerContainer.outerWidth(),o=this.$outerContainer.outerHeight(),n=t+this.containerLeftPadding+this.containerRightPadding,e=i+this.containerTopPadding+this.containerBottomPadding,a!==n||o!==e?this.$outerContainer.animate({width:n,height:e},this.options.resizeDuration,"swing",function(){return r()}):r()},i.prototype.showImage=function(){return this.$lightbox.find(".lb-loader").stop(!0).hide(),this.$lightbox.find(".lb-image").fadeIn("slow"),this.updateNav(),this.updateDetails(),this.preloadNeighboringImages(),this.enableKeyboardNav()},i.prototype.updateNav=function(){var t;t=!1;try{document.createEvent("TouchEvent"),t=this.options.alwaysShowNavOnTouchDevices?!0:!1}catch(i){}if(this.$lightbox.find(".lb-nav").show(),this.album.length>1){if(this.options.wrapAround)return t&&this.$lightbox.find(".lb-prev, .lb-next").css("opacity","1"),this.$lightbox.find(".lb-prev, .lb-next").show();if(this.currentImageIndex>0&&(this.$lightbox.find(".lb-prev").show(),t&&this.$lightbox.find(".lb-prev").css("opacity","1")),this.currentImageIndex<this.album.length-1&&(this.$lightbox.find(".lb-next").show(),t))return this.$lightbox.find(".lb-next").css("opacity","1")}},i.prototype.updateDetails=function(){var i,e;return e=this,"undefined"!=typeof this.album[this.currentImageIndex].title&&""!==this.album[this.currentImageIndex].title&&this.$lightbox.find(".lb-caption").html(this.album[this.currentImageIndex].title).fadeIn("fast").find("a").on("click",function(){return void 0!==t(this).attr("target")?window.open(t(this).attr("href"),t(this).attr("target")):location.href=t(this).attr("href")}),this.album.length>1&&this.options.showImageNumberLabel?(i=this.imageCountLabel(this.currentImageIndex+1,this.album.length),this.$lightbox.find(".lb-number").text(i).fadeIn("fast")):this.$lightbox.find(".lb-number").hide(),this.$outerContainer.removeClass("animating"),this.$lightbox.find(".lb-dataContainer").fadeIn(this.options.resizeDuration,function(){return e.sizeOverlay()})},i.prototype.preloadNeighboringImages=function(){var t,i;return this.album.length>this.currentImageIndex+1&&(t=new Image,t.src=this.album[this.currentImageIndex+1].link),this.currentImageIndex>0?(i=new Image,i.src=this.album[this.currentImageIndex-1].link):void 0},i.prototype.enableKeyboardNav=function(){return t(document).on("keyup.keyboard",t.proxy(this.keyboardAction,this))},i.prototype.disableKeyboardNav=function(){return t(document).off(".keyboard")},i.prototype.keyboardAction=function(t){var i,e,n,o,a;if(i=27,e=37,n=39,a=t.keyCode,o=String.fromCharCode(a).toLowerCase(),a===i||o.match(/x|o|c/))return this.end();if("p"===o||a===e){if(0!==this.currentImageIndex)return this.changeImage(this.currentImageIndex-1);if(this.options.wrapAround&&this.album.length>1)return this.changeImage(this.album.length-1)}else if("n"===o||a===n){if(this.currentImageIndex!==this.album.length-1)return this.changeImage(this.currentImageIndex+1);if(this.options.wrapAround&&this.album.length>1)return this.changeImage(0)}},i.prototype.end=function(){return this.disableKeyboardNav(),t(window).off("resize",this.sizeOverlay),this.$lightbox.fadeOut(this.options.fadeDuration),this.$overlay.fadeOut(this.options.fadeDuration),t("select, object, embed").css({visibility:"visible"})},new i})}).call(this);