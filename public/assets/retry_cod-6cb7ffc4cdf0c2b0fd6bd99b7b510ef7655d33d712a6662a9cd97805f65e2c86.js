(function(){$(function(){return ga("send","event",{eventCategory:"Checkout",eventAction:"prepaid-popup-view",nonInteraction:!0})}),$(function(){return document.getElementById("retry").addEventListener("submit",function(){return $("input[type='submit']").attr("disabled",!0),$("#retry_button").val("Please wait..."),ga("send","event",{eventCategory:"Checkout",eventAction:"prepaid-popup-retry",nonInteraction:!0}),!0}),document.getElementById("cod").addEventListener("submit",function(){return $("#cod_button").val("Please wait..."),$("input[type='submit']").attr("disabled",!0),ga("send","event",{eventCategory:"Checkout",eventAction:"prepaid-popup-cod",nonInteraction:!0}),!0})})}).call(this);