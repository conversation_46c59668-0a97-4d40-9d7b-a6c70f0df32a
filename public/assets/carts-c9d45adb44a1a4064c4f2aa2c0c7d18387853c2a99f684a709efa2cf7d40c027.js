(function() {
  var addCartAddon, removeCartAddon;

  $(function() {
    var sendRemoveFromCartInfoToGa, sendUpdateCartInfoToGa;
    $(document).on('change', '.quantity_list', function(e) {
      var id, url;
      id = this.id.split('_').pop();
      url = '/line_items/' + id;
      return $.ajax({
        url: url,
        type: 'PUT',
        dataType: 'json',
        data: {
          line_items: {
            id: id,
            quantity: $(this).val()
          }
        },
        success: function(response) {
          if (response.ga_hash) {
            sendUpdateCartInfoToGa(response.ga_hash);
          }
          if (Turbolinks.supported) {
            return Turbolinks.visit(response.redirect_url);
          } else {
            return window.location.href = response.redirect_url;
          }
        }
      });
    });
    $(document).on("ajax:success", "a.remove_from_cart , a.move_to_wishlist", function(evt, data, status, xhr) {
      if (data.ga_hash) {
        sendRemoveFromCartInfoToGa(data.ga_hash);
      }
      if (Turbolinks.supported) {
        return Turbolinks.visit(data.redirect_url);
      } else {
        return window.location.href = data.redirect_url;
      }
    });
    sendUpdateCartInfoToGa = function(ga_hash) {
      var action;
      if (ga_hash.quantity) {
        action = ga_hash.quantity > 0 ? 'add' : 'remove';
        ga_hash.quantity = Math.abs(ga_hash.quantity);
        ga('ec:addProduct', ga_hash);
        ga('ec:setAction', action);
        return ga('send', 'event', 'UX', 'click', 'update cart');
      }
    };
    sendRemoveFromCartInfoToGa = function(ga_hash) {
      ga('ec:addProduct', ga_hash);
      ga('ec:setAction', 'remove');
      return ga('send', 'event', 'UX', 'click', 'remove from cart');
    };
    return $(document).on('click', 'a.giftwrapped', function(e) {
      return $.ajax({
        url: '/carts/add_gift_wrap_price',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
          if (Turbolinks.supported) {
            return Turbolinks.visit(response.redirect_url);
          } else {
            return window.location.href = response.redirect_url;
          }
        }
      });
    });
  });

  $(function() {
    return $(window).scroll(function() {
      return stickyButton(fixed_checkout_button, action_buttons, 1.6);
    });
  });

  $(function() {
    var emailTrim, paramEmailSave;
    $(document).on('click', '.view_details_button', function(event) {
      return $('html, body').animate({
        scrollTop: $('#totals_block').offset().top - 50
      }, 500);
    });
    $(document).on('click', '.coupon', function(event) {
      if ($('.wallet_error').is(':visible')) {
        $('.wallet_error').html('');
      }
      $('.coupon-button').removeClass('enableLink');
      $('.wallet-button').addClass('enableLink');
      $('.apply_coupon .coupon, .apply_wallet .wallet-box, #apply_coupon_or_wallet').hide();
      return $('.apply_coupon .coupon-box').fadeIn(400);
    });
    $(document).on('click', '.wallet', function(event) {
      $('.wallet-button').removeClass('enableLink');
      $('.coupon-button').addClass('enableLink');
      $('.apply_coupon .coupon-box, #apply_coupon_or_wallet').hide();
      return $('.apply_wallet .wallet-box').fadeIn(400);
    });
    emailTrim = $('.panel_heading .email_display').text();
    if ($.trim(emailTrim).length) {
      $('.panel_heading .email_form').hide();
    } else {
      $('.panel_heading .email_form, .email_display').hide();
    }
    $(document).on('click', '.panel_heading .save_email', function(event) {
      $('.panel_heading .save_email, .email_display').hide();
      return $('.panel_heading .email_form').fadeIn(400);
    });
    paramEmailSave = function(email) {
      return $.post('/cart/save_email', {
        cart_id: $('#cart_id').val(),
        email: email
      }, (function(response) {
        var text_msg;
        if (response.success === '1') {
          $('.cart-alert').remove();
          $('.panel_heading .email_form').hide();
          $('.panel_heading .save_email, .email_display').fadeIn(400);
          if ($('#email_box').val() !== '') {
            return $('.panel_heading .email_display').html($('#email_box').val());
          }
        } else if (response.success === '0') {
          text_msg = "<div data-alert class='alert-box warning radius cart-alert'>Oops! Something went wrong.</div>";
          return $('#carts_block').prepend(text_msg);
        } else {
          return $('#email_box').val('');
        }
      }), 'json');
    };
    $(document).on('submit', '#save-cart-email', function(event) {
      var email, reEmail;
      event.preventDefault();
      email = $('#email_box').val();
      reEmail = /^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/;
      if (reEmail.test(email)) {
        paramEmailSave(email);
        return true;
      } else {
        alert('Please enter valid Email');
        return false;
      }
    });
    return $(document).on('click', '.add_place_order', function() {
      if (typeof fbq !== 'undefined') {
        // return fbq('track', 'InitiateCheckout');
      }
    });
  });

  $(function() {
    $('#checkout_button').click(function(e) {
      e.preventDefault();
      if ($('.out_of_stock').length > 0) {
        if ($('.oos_error').length === 0) {
          return $('.columns.panel_content').prepend('<div class="minimum_cart_value_message oos_error">Cart contains out of stock product please remove them before checkout</div>');
        }
      } else {
        return window.location.href = $(this).attr('href');
      }
    });
    return $(document).on('click', '.add_cart_addon', function() {
      var design_id, quantity;
      design_id = $(this).data('id');
      if ($(this).is(":checked")) {
        quantity = +$('#cart_addon_quantity_' + design_id).val();
        return addCartAddon(design_id, quantity);
      } else {
        return removeCartAddon(design_id);
      }
    });
  });

  addCartAddon = function(design_id, quantity) {
    var item;
    item = [
      {
        design_id: design_id,
        quantity: quantity
      }
    ];
    return $.ajax({
      url: '/line_items',
      type: 'POST',
      dataType: 'JSON',
      data: {
        line_items: item,
        design_page: true
      },
      success: function(response, status, jqxhr) {
        return window.location.assign(response.redirect_url);
      }
    });
  };

  removeCartAddon = function(item_id) {
    return $.ajax({
      url: '/line_items/' + item_id,
      type: 'POST',
      dataType: 'script',
      data: {
        _method: "delete"
      }
    });
  };

}).call(this);
