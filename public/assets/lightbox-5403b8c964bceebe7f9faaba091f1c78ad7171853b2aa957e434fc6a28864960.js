(function() {
  (function(root, factory) {
    if (typeof define === "function" && define.amd) {
      return define(["jquery"], factory);
    } else if (typeof exports === "object") {
      return module.exports = factory(require("jquery"));
    } else {
      return root.lightbox = factory(root.jQuery);
    }
  })(this, function($) {
    var Lightbox;
    Lightbox = function(options) {
      this.album = [];
      this.currentImageIndex = void 0;
      this.init();
      this.options = $.extend({}, this.constructor.defaults);
      return this.option(options);
    };
    Lightbox.defaults = {
      albumLabel: "Image %1 of %2",
      alwaysShowNavOnTouchDevices: true,
      fadeDuration: 500,
      fitImagesInViewport: true,
      positionFromTop: 50,
      resizeDuration: 700,
      showImageNumberLabel: true,
      wrapAround: false
    };
    Lightbox.prototype.option = function(options) {
      return $.extend(this.options, options);
    };
    Lightbox.prototype.imageCountLabel = function(currentImageNum, totalImages) {
      return this.options.albumLabel.replace(/%1/g, currentImageNum).replace(/%2/g, totalImages);
    };
    Lightbox.prototype.init = function() {
      this.enable();
      return this.build();
    };
    Lightbox.prototype.enable = function() {
      var self;
      self = this;
      return $("body").on("click", "a[rel^=lightbox], area[rel^=lightbox], a[data-lightbox], area[data-lightbox], div[data-lightbox]", function(event) {
        self.start($(event.currentTarget));
        return false;
      });
    };
    Lightbox.prototype.build = function() {
      var self;
      self = this;
      $("<div id=\"lightboxOverlay\" class=\"lightboxOverlay\">\n</div>\n<div id=\"lightbox\" class=\"lightbox\">\n  <div class=\"lb-dataContainer\">\n    <div class=\"lb-data\">\n      <div class=\"lb-details\">\n        <span class=\"lb-caption\">\n        </span>\n        <span class=\"lb-number\">\n        </span>\n      </div>\n      <div class=\"lb-closeContainer\">\n        <a class=\"lb-close\">\n        </a>\n      </div>\n    </div>\n  </div>\n  <div class=\"lb-outerContainer\">\n    <div class=\"lb-container\">\n      <img class=\"lb-image\" src=\"data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==\" />\n      <div class=\"lb-nav\">\n        <a class=\"lb-prev\" href=\"\" ></a>\n        <a class=\"lb-next\" href=\"\" ></a>\n      </div>\n      <div class=\"lb-loader\">\n        <a class=\"lb-cancel\">\n        </a>\n      </div>\n    </div>\n  </div>\n</div>").appendTo($("body"));
      this.$lightbox = $("#lightbox");
      this.$overlay = $("#lightboxOverlay");
      this.$outerContainer = this.$lightbox.find(".lb-outerContainer");
      this.$container = this.$lightbox.find(".lb-container");
      this.containerTopPadding = parseInt(this.$container.css("padding-top"), 10);
      this.containerRightPadding = parseInt(this.$container.css("padding-right"), 10);
      this.containerBottomPadding = parseInt(this.$container.css("padding-bottom"), 10);
      this.containerLeftPadding = parseInt(this.$container.css("padding-left"), 10);
      this.$overlay.hide().on("click", function() {
        self.end();
        return false;
      });
      this.$lightbox.hide().on("click", function(event) {
        if ($(event.target).attr("id") === "lightbox") {
          self.end();
        }
        return false;
      });
      this.$outerContainer.on("click", function(event) {
        if ($(event.target).attr("id") === "lightbox") {
          self.end();
        }
        return false;
      });
      this.$lightbox.find(".lb-prev").on("click", function() {
        if (self.currentImageIndex === 0) {
          self.changeImage(self.album.length - 1);
        } else {
          self.changeImage(self.currentImageIndex - 1);
        }
        return false;
      });
      this.$lightbox.find(".lb-next").on("click", function() {
        if (self.currentImageIndex === self.album.length - 1) {
          self.changeImage(0);
        } else {
          self.changeImage(self.currentImageIndex + 1);
        }
        return false;
      });
      return this.$lightbox.find(".lb-loader, .lb-close").on("click", function() {
        self.end();
        return false;
      });
    };
    Lightbox.prototype.start = function($link) {
      var $links, $window, addToAlbum, dataLightboxValue, i, imageNumber, j, left, self, top;
      addToAlbum = function($link) {
        return self.album.push({
          link: $link.attr("href"),
          title: $link.attr("data-title") || $link.attr("title")
        });
      };
      self = this;
      $window = $(window);
      $window.on("resize", $.proxy(this.sizeOverlay, this));
      $("select, object, embed").css({
        visibility: "hidden"
      });
      this.sizeOverlay();
      this.album = [];
      imageNumber = 0;
      dataLightboxValue = $link.attr("data-lightbox");
      $links = void 0;
      if (dataLightboxValue) {
        $(".slick-cloned").removeAttr("data-lightbox");
        $links = $($link.prop("tagName") + "[data-lightbox=\"" + dataLightboxValue + "\"]");
        i = 0;
        while (i < $links.length) {
          addToAlbum($($links[i]));
          if ($links[i] === $link[0]) {
            imageNumber = i;
          }
          i = ++i;
        }
      } else {
        if ($link.attr("rel") === "lightbox") {
          addToAlbum($link);
        } else {
          $links = $($link.prop("tagName") + "[rel=\"" + $link.attr("rel") + "\"]");
          j = 0;
          while (j < $links.length) {
            addToAlbum($($links[j]));
            if ($links[j] === $link[0]) {
              imageNumber = j;
            }
            j = ++j;
          }
        }
      }
      top = $window.scrollTop() + this.options.positionFromTop;
      left = $window.scrollLeft();
      this.$lightbox.css({
        top: top + "px",
        left: left + "px"
      }).fadeIn(this.options.fadeDuration);
      return this.changeImage(imageNumber);
    };
    Lightbox.prototype.changeImage = function(imageNumber) {
      var $image, preloader, self;
      self = this;
      this.disableKeyboardNav();
      $image = this.$lightbox.find(".lb-image");
      this.$overlay.fadeIn(this.options.fadeDuration);
      $(".lb-loader").fadeIn("slow");
      this.$lightbox.find(".lb-image, .lb-nav, .lb-prev, .lb-next, .lb-dataContainer, .lb-numbers, .lb-caption").hide();
      this.$outerContainer.addClass("animating");
      preloader = new Image();
      preloader.onload = function() {
        var $preloader, imageHeight, imageWidth, maxImageHeight, maxImageWidth, windowHeight, windowWidth;
        $preloader = void 0;
        imageHeight = void 0;
        imageWidth = void 0;
        maxImageHeight = void 0;
        maxImageWidth = void 0;
        windowHeight = void 0;
        windowWidth = void 0;
        $image.attr("src", self.album[imageNumber].link);
        $preloader = $(preloader);
        $image.width(preloader.width);
        $image.height(preloader.height);
        if (self.options.fitImagesInViewport) {
          windowWidth = $(window).width();
          windowHeight = $(window).height();
          maxImageWidth = windowWidth - self.containerLeftPadding - self.containerRightPadding - 20;
          maxImageHeight = windowHeight - self.containerTopPadding - self.containerBottomPadding - 120;
          if (self.options.maxWidth && self.options.maxWidth < maxImageWidth) {
            maxImageWidth = self.options.maxWidth;
          }
          if (self.options.maxHeight && self.options.maxHeight < maxImageWidth) {
            maxImageHeight = self.options.maxHeight;
          }
          if ((preloader.width > maxImageWidth) || (preloader.height > maxImageHeight)) {
            if ((preloader.width / maxImageWidth) > (preloader.height / maxImageHeight)) {
              imageWidth = maxImageWidth;
              imageHeight = parseInt(preloader.height / (preloader.width / imageWidth), 10);
              $image.width(imageWidth);
              $image.height(imageHeight);
            } else {
              imageHeight = maxImageHeight;
              imageWidth = parseInt(preloader.width / (preloader.height / imageHeight), 10);
              $image.width(imageWidth);
              $image.height(imageHeight);
            }
          }
        }
        return self.sizeContainer($image.width(), $image.height());
      };
      preloader.src = this.album[imageNumber].link;
      return this.currentImageIndex = imageNumber;
    };
    Lightbox.prototype.sizeOverlay = function() {
      return this.$overlay.width($(window).width()).height($(document).height());
    };
    Lightbox.prototype.sizeContainer = function(imageWidth, imageHeight) {
      var newHeight, newWidth, oldHeight, oldWidth, postResize, self;
      postResize = function() {
        self.$lightbox.find(".lb-dataContainer").width(newWidth);
        self.$lightbox.find(".lb-prevLink").height(newHeight);
        self.$lightbox.find(".lb-nextLink").height(newHeight);
        return self.showImage();
      };
      self = this;
      oldWidth = this.$outerContainer.outerWidth();
      oldHeight = this.$outerContainer.outerHeight();
      newWidth = imageWidth + this.containerLeftPadding + this.containerRightPadding;
      newHeight = imageHeight + this.containerTopPadding + this.containerBottomPadding;
      if (oldWidth !== newWidth || oldHeight !== newHeight) {
        return this.$outerContainer.animate({
          width: newWidth,
          height: newHeight
        }, this.options.resizeDuration, "swing", function() {
          return postResize();
        });
      } else {
        return postResize();
      }
    };
    Lightbox.prototype.showImage = function() {
      this.$lightbox.find(".lb-loader").stop(true).hide();
      this.$lightbox.find(".lb-image").fadeIn("slow");
      this.updateNav();
      this.updateDetails();
      this.preloadNeighboringImages();
      return this.enableKeyboardNav();
    };
    Lightbox.prototype.updateNav = function() {
      var alwaysShowNav;
      alwaysShowNav = false;
      try {
        document.createEvent("TouchEvent");
        alwaysShowNav = (this.options.alwaysShowNavOnTouchDevices ? true : false);
      } catch (_error) {}
      this.$lightbox.find(".lb-nav").show();
      if (this.album.length > 1) {
        if (this.options.wrapAround) {
          if (alwaysShowNav) {
            this.$lightbox.find(".lb-prev, .lb-next").css("opacity", "1");
          }
          return this.$lightbox.find(".lb-prev, .lb-next").show();
        } else {
          if (this.currentImageIndex > 0) {
            this.$lightbox.find(".lb-prev").show();
            if (alwaysShowNav) {
              this.$lightbox.find(".lb-prev").css("opacity", "1");
            }
          }
          if (this.currentImageIndex < this.album.length - 1) {
            this.$lightbox.find(".lb-next").show();
            if (alwaysShowNav) {
              return this.$lightbox.find(".lb-next").css("opacity", "1");
            }
          }
        }
      }
    };
    Lightbox.prototype.updateDetails = function() {
      var labelText, self;
      self = this;
      if (typeof this.album[this.currentImageIndex].title !== "undefined" && this.album[this.currentImageIndex].title !== "") {
        this.$lightbox.find(".lb-caption").html(this.album[this.currentImageIndex].title).fadeIn("fast").find("a").on("click", function(event) {
          if ($(this).attr("target") !== undefined) {
            return window.open($(this).attr("href"), $(this).attr("target"));
          } else {
            return location.href = $(this).attr("href");
          }
        });
      }
      if (this.album.length > 1 && this.options.showImageNumberLabel) {
        labelText = this.imageCountLabel(this.currentImageIndex + 1, this.album.length);
        this.$lightbox.find(".lb-number").text(labelText).fadeIn("fast");
      } else {
        this.$lightbox.find(".lb-number").hide();
      }
      this.$outerContainer.removeClass("animating");
      return this.$lightbox.find(".lb-dataContainer").fadeIn(this.options.resizeDuration, function() {
        return self.sizeOverlay();
      });
    };
    Lightbox.prototype.preloadNeighboringImages = function() {
      var preloadNext, preloadPrev;
      if (this.album.length > this.currentImageIndex + 1) {
        preloadNext = new Image();
        preloadNext.src = this.album[this.currentImageIndex + 1].link;
      }
      if (this.currentImageIndex > 0) {
        preloadPrev = new Image();
        return preloadPrev.src = this.album[this.currentImageIndex - 1].link;
      }
    };
    Lightbox.prototype.enableKeyboardNav = function() {
      return $(document).on("keyup.keyboard", $.proxy(this.keyboardAction, this));
    };
    Lightbox.prototype.disableKeyboardNav = function() {
      return $(document).off(".keyboard");
    };
    Lightbox.prototype.keyboardAction = function(event) {
      var KEYCODE_ESC, KEYCODE_LEFTARROW, KEYCODE_RIGHTARROW, key, keycode;
      KEYCODE_ESC = 27;
      KEYCODE_LEFTARROW = 37;
      KEYCODE_RIGHTARROW = 39;
      keycode = event.keyCode;
      key = String.fromCharCode(keycode).toLowerCase();
      if (keycode === KEYCODE_ESC || key.match(/x|o|c/)) {
        return this.end();
      } else if (key === "p" || keycode === KEYCODE_LEFTARROW) {
        if (this.currentImageIndex !== 0) {
          return this.changeImage(this.currentImageIndex - 1);
        } else {
          if (this.options.wrapAround && this.album.length > 1) {
            return this.changeImage(this.album.length - 1);
          }
        }
      } else if (key === "n" || keycode === KEYCODE_RIGHTARROW) {
        if (this.currentImageIndex !== this.album.length - 1) {
          return this.changeImage(this.currentImageIndex + 1);
        } else {
          if (this.options.wrapAround && this.album.length > 1) {
            return this.changeImage(0);
          }
        }
      }
    };
    Lightbox.prototype.end = function() {
      this.disableKeyboardNav();
      $(window).off("resize", this.sizeOverlay);
      this.$lightbox.fadeOut(this.options.fadeDuration);
      this.$overlay.fadeOut(this.options.fadeDuration);
      return $("select, object, embed").css({
        visibility: "visible"
      });
    };
    return new Lightbox();
  });

}).call(this);
