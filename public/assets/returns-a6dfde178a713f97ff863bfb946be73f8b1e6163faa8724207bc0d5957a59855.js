(function(){var e,t,r,n,a,o,i;t=function(e){var t;return t=$("input[value="+e+"]"),t.is(":checked")?(t[0].checked=!1,$("#item_"+e).addClass("hide")):(t[0].checked=!0,$("#item_"+e).removeClass("hide")),$("#return_type_of_refund").trigger("change"),0!==$("input[type=checkbox]:checked").length?($(".submit_reason").removeClass("hide"),$("#reason_"+e).attr("required",!0)):($(".submit_reason").addClass("hide"),$("#reason_"+e).removeAttr("required"))},$(function(){return $(document).on("click",".img-check",function(){var e,r,n;return e=this.id.substring(6),r=$(this).attr("data-notice"),""===r?($(".error-alert").remove(),$(this).toggleClass("check"),$("#done_step_"+e).toggleClass("done"),t(e)):($(".error-alert").remove(),n="<div data-alert class='alert-box row warning error-alert radius'>"+r+"<a href='#' class='close'></a></div>",$("#new_return").prepend(n))})}),$(document).on("change","#new_return .quantity",function(){var e;return e=$(this).attr("data-item-id"),$("#total_"+e).text((Number($("#quantity_"+e+" option:selected").text())*Number($("#item_price_"+e).text())).toFixed(2)),$("#return_type_of_refund").trigger("change")}),$(function(){return $(document).on("change","#new_return .item_reason",function(){var e,t,r,n,a,o,i;if(t=this.id.substring(7),i=$(this).data("return-reason-hash"),$("#upload_panel_"+t).addClass("hide"),$("#uploadimage_"+t).removeAttr("required"),$("#reason_details_panel_"+t).addClass("hide"),$("#reason_details_"+t).find("option").remove(),$("#uploadimage_"+t).val(""),-1!==$.inArray($(this).val(),["Print /Design /Color Mismatch","Damaged Product","Wrong Product"]))$("#upload_panel_"+t).removeClass("hide"),$("#uploadimage_"+t).attr("required",!0);else{for(a=i[$(this).val()],e=0,r=a.length;r>e;e++)n=a[e],o=$("<option>"),o.attr("value",n).text(n),$("#reason_details_"+t).append(o);$("#reason_details_panel_"+t).removeClass("hide")}})}),e=function(){return{type:"GET",data:{bank_name:$("#return_bank_name").val()},url:"/returns/get_account_no_length",datatype:"json",success:function(e){var t;return t=$("#return_error_label_for_refund"),e.length!==$("#return_account_number").val().length?(t.text("Account number should be of "+e.length+" digits"),t.addClass("label alert")):null!==t.text().match(/Account number/)?(t.text(""),t.removeClass("label alert")):void 0}}},i=function(){var t,r;return r=/^[a-zA-Z]{4}[a-zA-Z0-9]{7}$/,t=$("#return_error_label_for_refund"),r.test($("#return_ifsc_code").val())?(null!==t.text().match(/IFSC Code/)&&(t.text(""),t.removeClass("label alert")),$.ajax(e())):(t.addClass("label alert"),t.text("IFSC Code should be of 11 charachters only with first four letters."))},$(function(){return $(document).on("click",".edit_buttons",function(e){return i(),""!==$("#return_error_label_for_refund").text()?(e.preventDefault(),!1):void 0}),$(document).on("change",".refund_type_dropdown",function(){var e,t,r,n;return n=$(".refund_type_dropdown option:selected").text(),e=$(this).data("bank-require"),r=a(),t=$(this).data("otp-required")&&r>0&&r<=$(this).data("max-automated-cod-amount"),$("#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number, #return_user_paypal_email, #verified_phone_number, #otp").removeAttr("required"),$(".bank_details_form").addClass("hide"),$("#otp_verification_form").addClass("hide"),"Refund"===n&&t?($("#otp_verification_form").removeClass("hide"),$(".bank_details_not_needed").addClass("hide"),$("#verified_phone_number, #otp").attr("required",!0)):"Refund"===n&&e&&!t?($("#otp_verification_form").addClass("hide"),$(".bank_details_form").removeClass("hide"),$("#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number").attr("required",!0),$(".bank_details_not_needed").addClass("hide")):($("#otp_verification_form").addClass("hide"),$(".bank_details_form").find("input:text").val(""),$(".bank_details_not_needed").removeClass("hide"),$(".bank_details_form").addClass("hide"))}),$(document).on("click",".account_details",function(){return"Paypal Account"===$(this).text()?($("#paypal_detail").removeClass("hide"),$("#bank_detail").addClass("hide"),$("#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number").removeAttr("required").val(""),$("#return_user_paypal_email").attr("required",!0)):($("#paypal_detail").addClass("hide"),$("#bank_detail").removeClass("hide"),$("#return_account_holder_name, #return_ifsc_code, #return_branch, #return_account_number").attr("required",!0),$("#return_user_paypal_email").removeAttr("required").val(""))}),$(document).on("keyup","#return_ifsc_code",function(){return i()}),$(document).on("keyup","#return_account_number",function(){return $.ajax(e())})}),$(function(){return $(document).on("click",".user_return_panel .no_button",function(){var e;return e=$(this).data("id"),$("body").removeClass("modal-open"),$("#cancel_order_"+e).foundation("reveal","close")})}),r=function(e,t){return{type:"GET",data:{rdo_id:e,tracking_number:t},url:"/returns/check_tracking_number",datatype:"json",success:function(e){return"correct"!==e.new_track?($(".same_track_notice").removeClass("hidden"),$(".same_track_notice").text("* Please enter tracking number of package you will return to us."),!1):($(".same_track_notice").addClass("hidden"),$(".same_track_notice").text(""),$(".return_designer_order_event").submit(),!0)}}},$(function(){return $(document).on("click",".designer_well .track_submit",function(e){var t,n;return t=$(this).attr("data-rdo-id"),n=$('input[id="tracking_number_'+t+'"]').val(),""!==n?(e.preventDefault(),$.ajax(r(t,n))):void 0}),$(document).on("click",".policy_button",function(){return $("body").addClass("modal-open")}),$(document).on("click",".close-reveal-modal",function(){return $("body").removeClass("modal-open")}),$(document).on("change",".item_image",function(){var e,t,r,n,a,o,i;n=$(this),a="This file exceeds the maximum allowed file size (3 MB)",r="Only image file with extension: .jpg, .jpeg, .gif or .png is allowed",e=["jpg","jpeg","gif","png"],o=n.data("max-file-size"),i=!1,t=!1,$.each(this.files,function(){var r;this.size&&o&&this.size>parseInt(o)&&(i=!0),r=this.name.split(".").pop(),-1===$.inArray(r,e)&&(t=!0)}),i&&(window.alert(a),$(n).val("")),t&&(window.alert(r),$(n).val(""))}),$("#get-otp-btn").on("click",function(e){return e.preventDefault(),$("#incorrect-phone-error").addClass("hide"),o($("#verified_phone_number").val())?$.ajax(n($("#verified_phone_number").val(),$("#order_number").val())):$("#incorrect-phone-error").removeClass("hide")})}),n=function(e,t){return{type:"POST",datatype:"json",data:{phone:e,order_number:t},url:"/returns/generate_otp",success:function(e){return e.sms_sent===!0?($("#otp-input-field").removeClass("hide"),$("#submit-otp-btn").removeClass("hide"),$("#get-otp").addClass("hide"),$("#phone-field").addClass("hide")):void 0}}},o=function(e){var t;return t=/^[6-9][0-9]{9}$/,t.test(e.trim())?!0:!1},a=function(){var e,t,r,n,a;for(t=[],$("input[name='return_items[]']:checked").each(function(){return t.push($(this).val())}),a=0,e=0,r=t.length;r>e;e++)n=t[e],a+=parseInt($("#total_"+n).text()||0);return a}}).call(this);