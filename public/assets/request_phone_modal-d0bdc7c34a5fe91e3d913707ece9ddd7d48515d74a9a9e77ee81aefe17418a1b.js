/*!
 * jQuery JavaScript Library v1.11.2
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright 2005, 2014 jQuery Foundation, Inc. and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2014-12-17T15:27Z
 */
!function(t,e){"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(!t.document)throw new Error("jQuery requires a window with a document");return e(t)}:e(t)}("undefined"!=typeof window?window:this,function(t,e){function n(t){var e=t.length,n=se.type(t);return"function"===n||se.isWindow(t)?!1:1===t.nodeType&&e?!0:"array"===n||0===e||"number"==typeof e&&e>0&&e-1 in t}function i(t,e,n){if(se.isFunction(e))return se.grep(t,function(t,i){return!!e.call(t,i,t)!==n});if(e.nodeType)return se.grep(t,function(t){return t===e!==n});if("string"==typeof e){if(he.test(e))return se.filter(e,t,n);e=se.filter(e,t)}return se.grep(t,function(t){return se.inArray(t,e)>=0!==n})}function s(t,e){do t=t[e];while(t&&1!==t.nodeType);return t}function a(t){var e=be[t]={};return se.each(t.match(ye)||[],function(t,n){e[n]=!0}),e}function r(){pe.addEventListener?(pe.removeEventListener("DOMContentLoaded",o,!1),t.removeEventListener("load",o,!1)):(pe.detachEvent("onreadystatechange",o),t.detachEvent("onload",o))}function o(){(pe.addEventListener||"load"===event.type||"complete"===pe.readyState)&&(r(),se.ready())}function l(t,e,n){if(void 0===n&&1===t.nodeType){var i="data-"+e.replace(Te,"-$1").toLowerCase();if(n=t.getAttribute(i),"string"==typeof n){try{n="true"===n?!0:"false"===n?!1:"null"===n?null:+n+""===n?+n:Ce.test(n)?se.parseJSON(n):n}catch(s){}se.data(t,e,n)}else n=void 0}return n}function c(t){var e;for(e in t)if(("data"!==e||!se.isEmptyObject(t[e]))&&"toJSON"!==e)return!1;return!0}function d(t,e,n,i){if(se.acceptData(t)){var s,a,r=se.expando,o=t.nodeType,l=o?se.cache:t,c=o?t[r]:t[r]&&r;if(c&&l[c]&&(i||l[c].data)||void 0!==n||"string"!=typeof e)return c||(c=o?t[r]=U.pop()||se.guid++:r),l[c]||(l[c]=o?{}:{toJSON:se.noop}),("object"==typeof e||"function"==typeof e)&&(i?l[c]=se.extend(l[c],e):l[c].data=se.extend(l[c].data,e)),a=l[c],i||(a.data||(a.data={}),a=a.data),void 0!==n&&(a[se.camelCase(e)]=n),"string"==typeof e?(s=a[e],null==s&&(s=a[se.camelCase(e)])):s=a,s}}function u(t,e,n){if(se.acceptData(t)){var i,s,a=t.nodeType,r=a?se.cache:t,o=a?t[se.expando]:se.expando;if(r[o]){if(e&&(i=n?r[o]:r[o].data)){se.isArray(e)?e=e.concat(se.map(e,se.camelCase)):e in i?e=[e]:(e=se.camelCase(e),e=e in i?[e]:e.split(" ")),s=e.length;for(;s--;)delete i[e[s]];if(n?!c(i):!se.isEmptyObject(i))return}(n||(delete r[o].data,c(r[o])))&&(a?se.cleanData([t],!0):ne.deleteExpando||r!=r.window?delete r[o]:r[o]=null)}}}function h(){return!0}function f(){return!1}function p(){try{return pe.activeElement}catch(t){}}function g(t){var e=Me.split("|"),n=t.createDocumentFragment();if(n.createElement)for(;e.length;)n.createElement(e.pop());return n}function m(t,e){var n,i,s=0,a=typeof t.getElementsByTagName!==ke?t.getElementsByTagName(e||"*"):typeof t.querySelectorAll!==ke?t.querySelectorAll(e||"*"):void 0;if(!a)for(a=[],n=t.childNodes||t;null!=(i=n[s]);s++)!e||se.nodeName(i,e)?a.push(i):se.merge(a,m(i,e));return void 0===e||e&&se.nodeName(t,e)?se.merge([t],a):a}function v(t){Ne.test(t.type)&&(t.defaultChecked=t.checked)}function _(t,e){return se.nodeName(t,"table")&&se.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function y(t){return t.type=(null!==se.find.attr(t,"type"))+"/"+t.type,t}function b(t){var e=Ye.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function x(t,e){for(var n,i=0;null!=(n=t[i]);i++)se._data(n,"globalEval",!e||se._data(e[i],"globalEval"))}function w(t,e){if(1===e.nodeType&&se.hasData(t)){var n,i,s,a=se._data(t),r=se._data(e,a),o=a.events;if(o){delete r.handle,r.events={};for(n in o)for(i=0,s=o[n].length;s>i;i++)se.event.add(e,n,o[n][i])}r.data&&(r.data=se.extend({},r.data))}}function k(t,e){var n,i,s;if(1===e.nodeType){if(n=e.nodeName.toLowerCase(),!ne.noCloneEvent&&e[se.expando]){s=se._data(e);for(i in s.events)se.removeEvent(e,i,s.handle);e.removeAttribute(se.expando)}"script"===n&&e.text!==t.text?(y(e).text=t.text,b(e)):"object"===n?(e.parentNode&&(e.outerHTML=t.outerHTML),ne.html5Clone&&t.innerHTML&&!se.trim(e.innerHTML)&&(e.innerHTML=t.innerHTML)):"input"===n&&Ne.test(t.type)?(e.defaultChecked=e.checked=t.checked,e.value!==t.value&&(e.value=t.value)):"option"===n?e.defaultSelected=e.selected=t.defaultSelected:("input"===n||"textarea"===n)&&(e.defaultValue=t.defaultValue)}}function C(e,n){var i,s=se(n.createElement(e)).appendTo(n.body),a=t.getDefaultComputedStyle&&(i=t.getDefaultComputedStyle(s[0]))?i.display:se.css(s[0],"display");return s.detach(),a}function T(t){var e=pe,n=Ke[t];return n||(n=C(t,e),"none"!==n&&n||(Je=(Je||se("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement),e=(Je[0].contentWindow||Je[0].contentDocument).document,e.write(),e.close(),n=C(t,e),Je.detach()),Ke[t]=n),n}function S(t,e){return{get:function(){var n=t();if(null!=n)return n?void delete this.get:(this.get=e).apply(this,arguments)}}}function A(t,e){if(e in t)return e;for(var n=e.charAt(0).toUpperCase()+e.slice(1),i=e,s=fn.length;s--;)if(e=fn[s]+n,e in t)return e;return i}function j(t,e){for(var n,i,s,a=[],r=0,o=t.length;o>r;r++)i=t[r],i.style&&(a[r]=se._data(i,"olddisplay"),n=i.style.display,e?(a[r]||"none"!==n||(i.style.display=""),""===i.style.display&&je(i)&&(a[r]=se._data(i,"olddisplay",T(i.nodeName)))):(s=je(i),(n&&"none"!==n||!s)&&se._data(i,"olddisplay",s?n:se.css(i,"display"))));for(r=0;o>r;r++)i=t[r],i.style&&(e&&"none"!==i.style.display&&""!==i.style.display||(i.style.display=e?a[r]||"":"none"));return t}function E(t,e,n){var i=cn.exec(e);return i?Math.max(0,i[1]-(n||0))+(i[2]||"px"):e}function N(t,e,n,i,s){for(var a=n===(i?"border":"content")?4:"width"===e?1:0,r=0;4>a;a+=2)"margin"===n&&(r+=se.css(t,n+Ae[a],!0,s)),i?("content"===n&&(r-=se.css(t,"padding"+Ae[a],!0,s)),"margin"!==n&&(r-=se.css(t,"border"+Ae[a]+"Width",!0,s))):(r+=se.css(t,"padding"+Ae[a],!0,s),"padding"!==n&&(r+=se.css(t,"border"+Ae[a]+"Width",!0,s)));return r}function D(t,e,n){var i=!0,s="width"===e?t.offsetWidth:t.offsetHeight,a=tn(t),r=ne.boxSizing&&"border-box"===se.css(t,"boxSizing",!1,a);if(0>=s||null==s){if(s=en(t,e,a),(0>s||null==s)&&(s=t.style[e]),sn.test(s))return s;i=r&&(ne.boxSizingReliable()||s===t.style[e]),s=parseFloat(s)||0}return s+N(t,e,n||(r?"border":"content"),i,a)+"px"}function $(t,e,n,i,s){return new $.prototype.init(t,e,n,i,s)}function H(){return setTimeout(function(){pn=void 0}),pn=se.now()}function F(t,e){var n,i={height:t},s=0;for(e=e?1:0;4>s;s+=2-e)n=Ae[s],i["margin"+n]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function q(t,e,n){for(var i,s=(bn[e]||[]).concat(bn["*"]),a=0,r=s.length;r>a;a++)if(i=s[a].call(n,e,t))return i}function M(t,e,n){var i,s,a,r,o,l,c,d,u=this,h={},f=t.style,p=t.nodeType&&je(t),g=se._data(t,"fxshow");n.queue||(o=se._queueHooks(t,"fx"),null==o.unqueued&&(o.unqueued=0,l=o.empty.fire,o.empty.fire=function(){o.unqueued||l()}),o.unqueued++,u.always(function(){u.always(function(){o.unqueued--,se.queue(t,"fx").length||o.empty.fire()})})),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],c=se.css(t,"display"),d="none"===c?se._data(t,"olddisplay")||T(t.nodeName):c,"inline"===d&&"none"===se.css(t,"float")&&(ne.inlineBlockNeedsLayout&&"inline"!==T(t.nodeName)?f.zoom=1:f.display="inline-block")),n.overflow&&(f.overflow="hidden",ne.shrinkWrapBlocks()||u.always(function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}));for(i in e)if(s=e[i],mn.exec(s)){if(delete e[i],a=a||"toggle"===s,s===(p?"hide":"show")){if("show"!==s||!g||void 0===g[i])continue;p=!0}h[i]=g&&g[i]||se.style(t,i)}else c=void 0;if(se.isEmptyObject(h))"inline"===("none"===c?T(t.nodeName):c)&&(f.display=c);else{g?"hidden"in g&&(p=g.hidden):g=se._data(t,"fxshow",{}),a&&(g.hidden=!p),p?se(t).show():u.done(function(){se(t).hide()}),u.done(function(){var e;se._removeData(t,"fxshow");for(e in h)se.style(t,e,h[e])});for(i in h)r=q(p?g[i]:0,i,u),i in g||(g[i]=r.start,p&&(r.end=r.start,r.start="width"===i||"height"===i?1:0))}}function L(t,e){var n,i,s,a,r;for(n in t)if(i=se.camelCase(n),s=e[i],a=t[n],se.isArray(a)&&(s=a[1],a=t[n]=a[0]),n!==i&&(t[i]=a,delete t[n]),r=se.cssHooks[i],r&&"expand"in r){a=r.expand(a),delete t[i];for(n in a)n in t||(t[n]=a[n],e[n]=s)}else e[i]=s}function z(t,e,n){var i,s,a=0,r=yn.length,o=se.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=pn||H(),n=Math.max(0,c.startTime+c.duration-e),i=n/c.duration||0,a=1-i,r=0,l=c.tweens.length;l>r;r++)c.tweens[r].run(a);return o.notifyWith(t,[c,a,n]),1>a&&l?n:(o.resolveWith(t,[c]),!1)},c=o.promise({elem:t,props:se.extend({},e),opts:se.extend(!0,{specialEasing:{}},n),originalProperties:e,originalOptions:n,startTime:pn||H(),duration:n.duration,tweens:[],createTween:function(e,n){var i=se.Tween(t,c.opts,e,n,c.opts.specialEasing[e]||c.opts.easing);return c.tweens.push(i),i},stop:function(e){var n=0,i=e?c.tweens.length:0;if(s)return this;for(s=!0;i>n;n++)c.tweens[n].run(1);return e?o.resolveWith(t,[c,e]):o.rejectWith(t,[c,e]),this}}),d=c.props;for(L(d,c.opts.specialEasing);r>a;a++)if(i=yn[a].call(c,t,d,c.opts))return i;return se.map(d,q,c),se.isFunction(c.opts.start)&&c.opts.start.call(t,c),se.fx.timer(se.extend(l,{elem:t,anim:c,queue:c.opts.queue})),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always)}function W(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var i,s=0,a=e.toLowerCase().match(ye)||[];if(se.isFunction(n))for(;i=a[s++];)"+"===i.charAt(0)?(i=i.slice(1)||"*",(t[i]=t[i]||[]).unshift(n)):(t[i]=t[i]||[]).push(n)}}function O(t,e,n,i){function s(o){var l;return a[o]=!0,se.each(t[o]||[],function(t,o){var c=o(e,n,i);return"string"!=typeof c||r||a[c]?r?!(l=c):void 0:(e.dataTypes.unshift(c),s(c),!1)}),l}var a={},r=t===In;return s(e.dataTypes[0])||!a["*"]&&s("*")}function P(t,e){var n,i,s=se.ajaxSettings.flatOptions||{};for(i in e)void 0!==e[i]&&((s[i]?t:n||(n={}))[i]=e[i]);return n&&se.extend(!0,t,n),t}function R(t,e,n){for(var i,s,a,r,o=t.contents,l=t.dataTypes;"*"===l[0];)l.shift(),void 0===s&&(s=t.mimeType||e.getResponseHeader("Content-Type"));if(s)for(r in o)if(o[r]&&o[r].test(s)){l.unshift(r);break}if(l[0]in n)a=l[0];else{for(r in n){if(!l[0]||t.converters[r+" "+l[0]]){a=r;break}i||(i=r)}a=a||i}return a?(a!==l[0]&&l.unshift(a),n[a]):void 0}function B(t,e,n,i){var s,a,r,o,l,c={},d=t.dataTypes.slice();if(d[1])for(r in t.converters)c[r.toLowerCase()]=t.converters[r];for(a=d.shift();a;)if(t.responseFields[a]&&(n[t.responseFields[a]]=e),!l&&i&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),l=a,a=d.shift())if("*"===a)a=l;else if("*"!==l&&l!==a){if(r=c[l+" "+a]||c["* "+a],!r)for(s in c)if(o=s.split(" "),o[1]===a&&(r=c[l+" "+o[0]]||c["* "+o[0]])){r===!0?r=c[s]:c[s]!==!0&&(a=o[0],d.unshift(o[1]));break}if(r!==!0)if(r&&t["throws"])e=r(e);else try{e=r(e)}catch(u){return{state:"parsererror",error:r?u:"No conversion from "+l+" to "+a}}}return{state:"success",data:e}}function I(t,e,n,i){var s;if(se.isArray(e))se.each(e,function(e,s){n||Un.test(t)?i(t,s):I(t+"["+("object"==typeof s?e:"")+"]",s,n,i)});else if(n||"object"!==se.type(e))i(t,e);else for(s in e)I(t+"["+s+"]",e[s],n,i)}function X(){try{return new t.XMLHttpRequest}catch(e){}}function Q(){try{return new t.ActiveXObject("Microsoft.XMLHTTP")}catch(e){}}function Y(t){return se.isWindow(t)?t:9===t.nodeType?t.defaultView||t.parentWindow:!1}var U=[],V=U.slice,Z=U.concat,G=U.push,J=U.indexOf,K={},te=K.toString,ee=K.hasOwnProperty,ne={},ie="1.11.2",se=function(t,e){return new se.fn.init(t,e)},ae=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,re=/^-ms-/,oe=/-([\da-z])/gi,le=function(t,e){return e.toUpperCase()};se.fn=se.prototype={jquery:ie,constructor:se,selector:"",length:0,toArray:function(){return V.call(this)},get:function(t){return null!=t?0>t?this[t+this.length]:this[t]:V.call(this)},pushStack:function(t){var e=se.merge(this.constructor(),t);return e.prevObject=this,e.context=this.context,e},each:function(t,e){return se.each(this,t,e)},map:function(t){return this.pushStack(se.map(this,function(e,n){return t.call(e,n,e)}))},slice:function(){return this.pushStack(V.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length,n=+t+(0>t?e:0);return this.pushStack(n>=0&&e>n?[this[n]]:[])},end:function(){return this.prevObject||this.constructor(null)},push:G,sort:U.sort,splice:U.splice},se.extend=se.fn.extend=function(){var t,e,n,i,s,a,r=arguments[0]||{},o=1,l=arguments.length,c=!1;for("boolean"==typeof r&&(c=r,r=arguments[o]||{},o++),"object"==typeof r||se.isFunction(r)||(r={}),o===l&&(r=this,o--);l>o;o++)if(null!=(s=arguments[o]))for(i in s)t=r[i],n=s[i],r!==n&&(c&&n&&(se.isPlainObject(n)||(e=se.isArray(n)))?(e?(e=!1,a=t&&se.isArray(t)?t:[]):a=t&&se.isPlainObject(t)?t:{},r[i]=se.extend(c,a,n)):void 0!==n&&(r[i]=n));return r},se.extend({expando:"jQuery"+(ie+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===se.type(t)},isArray:Array.isArray||function(t){return"array"===se.type(t)},isWindow:function(t){return null!=t&&t==t.window},isNumeric:function(t){return!se.isArray(t)&&t-parseFloat(t)+1>=0},isEmptyObject:function(t){var e;for(e in t)return!1;return!0},isPlainObject:function(t){var e;if(!t||"object"!==se.type(t)||t.nodeType||se.isWindow(t))return!1;try{if(t.constructor&&!ee.call(t,"constructor")&&!ee.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}if(ne.ownLast)for(e in t)return ee.call(t,e);for(e in t);return void 0===e||ee.call(t,e)},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?K[te.call(t)]||"object":typeof t},globalEval:function(e){e&&se.trim(e)&&(t.execScript||function(e){t.eval.call(t,e)})(e)},camelCase:function(t){return t.replace(re,"ms-").replace(oe,le)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e,i){var s,a=0,r=t.length,o=n(t);if(i){if(o)for(;r>a&&(s=e.apply(t[a],i),s!==!1);a++);else for(a in t)if(s=e.apply(t[a],i),s===!1)break}else if(o)for(;r>a&&(s=e.call(t[a],a,t[a]),s!==!1);a++);else for(a in t)if(s=e.call(t[a],a,t[a]),s===!1)break;return t},trim:function(t){return null==t?"":(t+"").replace(ae,"")},makeArray:function(t,e){var i=e||[];return null!=t&&(n(Object(t))?se.merge(i,"string"==typeof t?[t]:t):G.call(i,t)),i},inArray:function(t,e,n){var i;if(e){if(J)return J.call(e,t,n);for(i=e.length,n=n?0>n?Math.max(0,i+n):n:0;i>n;n++)if(n in e&&e[n]===t)return n}return-1},merge:function(t,e){for(var n=+e.length,i=0,s=t.length;n>i;)t[s++]=e[i++];if(n!==n)for(;void 0!==e[i];)t[s++]=e[i++];return t.length=s,t},grep:function(t,e,n){for(var i,s=[],a=0,r=t.length,o=!n;r>a;a++)i=!e(t[a],a),i!==o&&s.push(t[a]);return s},map:function(t,e,i){var s,a=0,r=t.length,o=n(t),l=[];if(o)for(;r>a;a++)s=e(t[a],a,i),null!=s&&l.push(s);else for(a in t)s=e(t[a],a,i),null!=s&&l.push(s);return Z.apply([],l)},guid:1,proxy:function(t,e){var n,i,s;return"string"==typeof e&&(s=t[e],e=t,t=s),se.isFunction(t)?(n=V.call(arguments,2),i=function(){return t.apply(e||this,n.concat(V.call(arguments)))},i.guid=t.guid=t.guid||se.guid++,i):void 0},now:function(){return+new Date},support:ne}),se.each("Boolean Number String Function Array Date RegExp Object Error".split(" "),function(t,e){K["[object "+e+"]"]=e.toLowerCase()});var ce=/*!
 * Sizzle CSS Selector Engine v2.2.0-pre
 * http://sizzlejs.com/
 *
 * Copyright 2008, 2014 jQuery Foundation, Inc. and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: 2014-12-16
 */
function(t){function e(t,e,n,i){var s,a,r,o,l,c,u,f,p,g;if((e?e.ownerDocument||e:O)!==$&&D(e),e=e||$,n=n||[],o=e.nodeType,"string"!=typeof t||!t||1!==o&&9!==o&&11!==o)return n;if(!i&&F){if(11!==o&&(s=_e.exec(t)))if(r=s[1]){if(9===o){if(a=e.getElementById(r),!a||!a.parentNode)return n;if(a.id===r)return n.push(a),n}else if(e.ownerDocument&&(a=e.ownerDocument.getElementById(r))&&z(e,a)&&a.id===r)return n.push(a),n}else{if(s[2])return J.apply(n,e.getElementsByTagName(t)),n;if((r=s[3])&&x.getElementsByClassName)return J.apply(n,e.getElementsByClassName(r)),n}if(x.qsa&&(!q||!q.test(t))){if(f=u=W,p=e,g=1!==o&&t,1===o&&"object"!==e.nodeName.toLowerCase()){for(c=T(t),(u=e.getAttribute("id"))?f=u.replace(be,"\\$&"):e.setAttribute("id",f),f="[id='"+f+"'] ",l=c.length;l--;)c[l]=f+h(c[l]);p=ye.test(t)&&d(e.parentNode)||e,g=c.join(",")}if(g)try{return J.apply(n,p.querySelectorAll(g)),n}catch(m){}finally{u||e.removeAttribute("id")}}}return A(t.replace(le,"$1"),e,n,i)}function n(){function t(n,i){return e.push(n+" ")>w.cacheLength&&delete t[e.shift()],t[n+" "]=i}var e=[];return t}function i(t){return t[W]=!0,t}function s(t){var e=$.createElement("div");try{return!!t(e)}catch(n){return!1}finally{e.parentNode&&e.parentNode.removeChild(e),e=null}}function a(t,e){for(var n=t.split("|"),i=t.length;i--;)w.attrHandle[n[i]]=e}function r(t,e){var n=e&&t,i=n&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||Y)-(~t.sourceIndex||Y);if(i)return i;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function o(t){return function(e){var n=e.nodeName.toLowerCase();return"input"===n&&e.type===t}}function l(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}function c(t){return i(function(e){return e=+e,i(function(n,i){for(var s,a=t([],n.length,e),r=a.length;r--;)n[s=a[r]]&&(n[s]=!(i[s]=n[s]))})})}function d(t){return t&&"undefined"!=typeof t.getElementsByTagName&&t}function u(){}function h(t){for(var e=0,n=t.length,i="";n>e;e++)i+=t[e].value;return i}function f(t,e,n){var i=e.dir,s=n&&"parentNode"===i,a=R++;return e.first?function(e,n,a){for(;e=e[i];)if(1===e.nodeType||s)return t(e,n,a)}:function(e,n,r){var o,l,c=[P,a];if(r){for(;e=e[i];)if((1===e.nodeType||s)&&t(e,n,r))return!0}else for(;e=e[i];)if(1===e.nodeType||s){if(l=e[W]||(e[W]={}),(o=l[i])&&o[0]===P&&o[1]===a)return c[2]=o[2];if(l[i]=c,c[2]=t(e,n,r))return!0}}}function p(t){return t.length>1?function(e,n,i){for(var s=t.length;s--;)if(!t[s](e,n,i))return!1;return!0}:t[0]}function g(t,n,i){for(var s=0,a=n.length;a>s;s++)e(t,n[s],i);return i}function m(t,e,n,i,s){for(var a,r=[],o=0,l=t.length,c=null!=e;l>o;o++)(a=t[o])&&(!n||n(a,i,s))&&(r.push(a),c&&e.push(o));return r}function v(t,e,n,s,a,r){return s&&!s[W]&&(s=v(s)),a&&!a[W]&&(a=v(a,r)),i(function(i,r,o,l){var c,d,u,h=[],f=[],p=r.length,v=i||g(e||"*",o.nodeType?[o]:o,[]),_=!t||!i&&e?v:m(v,h,t,o,l),y=n?a||(i?t:p||s)?[]:r:_;if(n&&n(_,y,o,l),s)for(c=m(y,f),s(c,[],o,l),d=c.length;d--;)(u=c[d])&&(y[f[d]]=!(_[f[d]]=u));if(i){if(a||t){if(a){for(c=[],d=y.length;d--;)(u=y[d])&&c.push(_[d]=u);a(null,y=[],c,l)}for(d=y.length;d--;)(u=y[d])&&(c=a?te(i,u):h[d])>-1&&(i[c]=!(r[c]=u))}}else y=m(y===r?y.splice(p,y.length):y),a?a(null,r,y,l):J.apply(r,y)})}function _(t){for(var e,n,i,s=t.length,a=w.relative[t[0].type],r=a||w.relative[" "],o=a?1:0,l=f(function(t){return t===e},r,!0),c=f(function(t){return te(e,t)>-1},r,!0),d=[function(t,n,i){var s=!a&&(i||n!==j)||((e=n).nodeType?l(t,n,i):c(t,n,i));return e=null,s}];s>o;o++)if(n=w.relative[t[o].type])d=[f(p(d),n)];else{if(n=w.filter[t[o].type].apply(null,t[o].matches),n[W]){for(i=++o;s>i&&!w.relative[t[i].type];i++);return v(o>1&&p(d),o>1&&h(t.slice(0,o-1).concat({value:" "===t[o-2].type?"*":""})).replace(le,"$1"),n,i>o&&_(t.slice(o,i)),s>i&&_(t=t.slice(i)),s>i&&h(t))}d.push(n)}return p(d)}function y(t,n){var s=n.length>0,a=t.length>0,r=function(i,r,o,l,c){var d,u,h,f=0,p="0",g=i&&[],v=[],_=j,y=i||a&&w.find.TAG("*",c),b=P+=null==_?1:Math.random()||.1,x=y.length;for(c&&(j=r!==$&&r);p!==x&&null!=(d=y[p]);p++){if(a&&d){for(u=0;h=t[u++];)if(h(d,r,o)){l.push(d);break}c&&(P=b)}s&&((d=!h&&d)&&f--,i&&g.push(d))}if(f+=p,s&&p!==f){for(u=0;h=n[u++];)h(g,v,r,o);if(i){if(f>0)for(;p--;)g[p]||v[p]||(v[p]=Z.call(l));v=m(v)}J.apply(l,v),c&&!i&&v.length>0&&f+n.length>1&&e.uniqueSort(l)}return c&&(P=b,j=_),g};return s?i(r):r}var b,x,w,k,C,T,S,A,j,E,N,D,$,H,F,q,M,L,z,W="sizzle"+1*new Date,O=t.document,P=0,R=0,B=n(),I=n(),X=n(),Q=function(t,e){return t===e&&(N=!0),0},Y=1<<31,U={}.hasOwnProperty,V=[],Z=V.pop,G=V.push,J=V.push,K=V.slice,te=function(t,e){for(var n=0,i=t.length;i>n;n++)if(t[n]===e)return n;return-1},ee="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",ne="[\\x20\\t\\r\\n\\f]",ie="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",se=ie.replace("w","w#"),ae="\\["+ne+"*("+ie+")(?:"+ne+"*([*^$|!~]?=)"+ne+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+se+"))|)"+ne+"*\\]",re=":("+ie+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ae+")*)|.*)\\)|)",oe=new RegExp(ne+"+","g"),le=new RegExp("^"+ne+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ne+"+$","g"),ce=new RegExp("^"+ne+"*,"+ne+"*"),de=new RegExp("^"+ne+"*([>+~]|"+ne+")"+ne+"*"),ue=new RegExp("="+ne+"*([^\\]'\"]*?)"+ne+"*\\]","g"),he=new RegExp(re),fe=new RegExp("^"+se+"$"),pe={ID:new RegExp("^#("+ie+")"),CLASS:new RegExp("^\\.("+ie+")"),TAG:new RegExp("^("+ie.replace("w","w*")+")"),ATTR:new RegExp("^"+ae),PSEUDO:new RegExp("^"+re),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ne+"*(even|odd|(([+-]|)(\\d*)n|)"+ne+"*(?:([+-]|)"+ne+"*(\\d+)|))"+ne+"*\\)|)","i"),bool:new RegExp("^(?:"+ee+")$","i"),needsContext:new RegExp("^"+ne+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ne+"*((?:-\\d)?\\d*)"+ne+"*\\)|)(?=[^-]|$)","i")},ge=/^(?:input|select|textarea|button)$/i,me=/^h\d$/i,ve=/^[^{]+\{\s*\[native \w/,_e=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ye=/[+~]/,be=/'|\\/g,xe=new RegExp("\\\\([\\da-f]{1,6}"+ne+"?|("+ne+")|.)","ig"),we=function(t,e,n){var i="0x"+e-65536;return i!==i||n?e:0>i?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320)},ke=function(){D()};try{J.apply(V=K.call(O.childNodes),O.childNodes),V[O.childNodes.length].nodeType}catch(Ce){J={apply:V.length?function(t,e){G.apply(t,K.call(e))}:function(t,e){for(var n=t.length,i=0;t[n++]=e[i++];);t.length=n-1}}}x=e.support={},C=e.isXML=function(t){var e=t&&(t.ownerDocument||t).documentElement;return e?"HTML"!==e.nodeName:!1},D=e.setDocument=function(t){var e,n,i=t?t.ownerDocument||t:O;return i!==$&&9===i.nodeType&&i.documentElement?($=i,H=i.documentElement,n=i.defaultView,n&&n!==n.top&&(n.addEventListener?n.addEventListener("unload",ke,!1):n.attachEvent&&n.attachEvent("onunload",ke)),F=!C(i),x.attributes=s(function(t){return t.className="i",!t.getAttribute("className")}),x.getElementsByTagName=s(function(t){return t.appendChild(i.createComment("")),!t.getElementsByTagName("*").length}),x.getElementsByClassName=ve.test(i.getElementsByClassName),x.getById=s(function(t){return H.appendChild(t).id=W,!i.getElementsByName||!i.getElementsByName(W).length}),x.getById?(w.find.ID=function(t,e){if("undefined"!=typeof e.getElementById&&F){var n=e.getElementById(t);return n&&n.parentNode?[n]:[]}},w.filter.ID=function(t){var e=t.replace(xe,we);return function(t){return t.getAttribute("id")===e}}):(delete w.find.ID,w.filter.ID=function(t){var e=t.replace(xe,we);return function(t){var n="undefined"!=typeof t.getAttributeNode&&t.getAttributeNode("id");return n&&n.value===e}}),w.find.TAG=x.getElementsByTagName?function(t,e){return"undefined"!=typeof e.getElementsByTagName?e.getElementsByTagName(t):x.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,i=[],s=0,a=e.getElementsByTagName(t);if("*"===t){for(;n=a[s++];)1===n.nodeType&&i.push(n);return i}return a},w.find.CLASS=x.getElementsByClassName&&function(t,e){return F?e.getElementsByClassName(t):void 0},M=[],q=[],(x.qsa=ve.test(i.querySelectorAll))&&(s(function(t){H.appendChild(t).innerHTML="<a id='"+W+"'></a><select id='"+W+"-\f]' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&q.push("[*^$]="+ne+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||q.push("\\["+ne+"*(?:value|"+ee+")"),t.querySelectorAll("[id~="+W+"-]").length||q.push("~="),t.querySelectorAll(":checked").length||q.push(":checked"),t.querySelectorAll("a#"+W+"+*").length||q.push(".#.+[+~]")}),s(function(t){var e=i.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&q.push("name"+ne+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||q.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),q.push(",.*:")})),(x.matchesSelector=ve.test(L=H.matches||H.webkitMatchesSelector||H.mozMatchesSelector||H.oMatchesSelector||H.msMatchesSelector))&&s(function(t){x.disconnectedMatch=L.call(t,"div"),L.call(t,"[s!='']:x"),M.push("!=",re)}),q=q.length&&new RegExp(q.join("|")),M=M.length&&new RegExp(M.join("|")),e=ve.test(H.compareDocumentPosition),z=e||ve.test(H.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t,i=e&&e.parentNode;return t===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):t.compareDocumentPosition&&16&t.compareDocumentPosition(i)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},Q=e?function(t,e){if(t===e)return N=!0,0;var n=!t.compareDocumentPosition-!e.compareDocumentPosition;return n?n:(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1,1&n||!x.sortDetached&&e.compareDocumentPosition(t)===n?t===i||t.ownerDocument===O&&z(O,t)?-1:e===i||e.ownerDocument===O&&z(O,e)?1:E?te(E,t)-te(E,e):0:4&n?-1:1)}:function(t,e){if(t===e)return N=!0,0;var n,s=0,a=t.parentNode,o=e.parentNode,l=[t],c=[e];if(!a||!o)return t===i?-1:e===i?1:a?-1:o?1:E?te(E,t)-te(E,e):0;if(a===o)return r(t,e);for(n=t;n=n.parentNode;)l.unshift(n);for(n=e;n=n.parentNode;)c.unshift(n);for(;l[s]===c[s];)s++;return s?r(l[s],c[s]):l[s]===O?-1:c[s]===O?1:0},i):$},e.matches=function(t,n){return e(t,null,null,n)},e.matchesSelector=function(t,n){if((t.ownerDocument||t)!==$&&D(t),n=n.replace(ue,"='$1']"),!(!x.matchesSelector||!F||M&&M.test(n)||q&&q.test(n)))try{var i=L.call(t,n);if(i||x.disconnectedMatch||t.document&&11!==t.document.nodeType)return i}catch(s){}return e(n,$,null,[t]).length>0},e.contains=function(t,e){return(t.ownerDocument||t)!==$&&D(t),z(t,e)},e.attr=function(t,e){(t.ownerDocument||t)!==$&&D(t);var n=w.attrHandle[e.toLowerCase()],i=n&&U.call(w.attrHandle,e.toLowerCase())?n(t,e,!F):void 0;return void 0!==i?i:x.attributes||!F?t.getAttribute(e):(i=t.getAttributeNode(e))&&i.specified?i.value:null},e.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},e.uniqueSort=function(t){var e,n=[],i=0,s=0;if(N=!x.detectDuplicates,E=!x.sortStable&&t.slice(0),t.sort(Q),N){for(;e=t[s++];)e===t[s]&&(i=n.push(s));for(;i--;)t.splice(n[i],1)}return E=null,t},k=e.getText=function(t){var e,n="",i=0,s=t.nodeType;if(s){if(1===s||9===s||11===s){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=k(t)}else if(3===s||4===s)return t.nodeValue}else for(;e=t[i++];)n+=k(e);return n},w=e.selectors={cacheLength:50,createPseudo:i,match:pe,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(xe,we),t[3]=(t[3]||t[4]||t[5]||"").replace(xe,we),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||e.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&e.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return pe.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&he.test(n)&&(e=T(n,!0))&&(e=n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var e=t.replace(xe,we).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===e}},CLASS:function(t){var e=B[t+" "];return e||(e=new RegExp("(^|"+ne+")"+t+"("+ne+"|$)"))&&B(t,function(t){return e.test("string"==typeof t.className&&t.className||"undefined"!=typeof t.getAttribute&&t.getAttribute("class")||"")})},ATTR:function(t,n,i){return function(s){var a=e.attr(s,t);return null==a?"!="===n:n?(a+="","="===n?a===i:"!="===n?a!==i:"^="===n?i&&0===a.indexOf(i):"*="===n?i&&a.indexOf(i)>-1:"$="===n?i&&a.slice(-i.length)===i:"~="===n?(" "+a.replace(oe," ")+" ").indexOf(i)>-1:"|="===n?a===i||a.slice(0,i.length+1)===i+"-":!1):!0}},CHILD:function(t,e,n,i,s){var a="nth"!==t.slice(0,3),r="last"!==t.slice(-4),o="of-type"===e;return 1===i&&0===s?function(t){return!!t.parentNode}:function(e,n,l){var c,d,u,h,f,p,g=a!==r?"nextSibling":"previousSibling",m=e.parentNode,v=o&&e.nodeName.toLowerCase(),_=!l&&!o;if(m){if(a){for(;g;){for(u=e;u=u[g];)if(o?u.nodeName.toLowerCase()===v:1===u.nodeType)return!1;p=g="only"===t&&!p&&"nextSibling"}return!0}if(p=[r?m.firstChild:m.lastChild],r&&_){for(d=m[W]||(m[W]={}),c=d[t]||[],f=c[0]===P&&c[1],h=c[0]===P&&c[2],u=f&&m.childNodes[f];u=++f&&u&&u[g]||(h=f=0)||p.pop();)if(1===u.nodeType&&++h&&u===e){d[t]=[P,f,h];break}}else if(_&&(c=(e[W]||(e[W]={}))[t])&&c[0]===P)h=c[1];else for(;(u=++f&&u&&u[g]||(h=f=0)||p.pop())&&((o?u.nodeName.toLowerCase()!==v:1!==u.nodeType)||!++h||(_&&((u[W]||(u[W]={}))[t]=[P,h]),u!==e)););return h-=s,h===i||h%i===0&&h/i>=0}}},PSEUDO:function(t,n){var s,a=w.pseudos[t]||w.setFilters[t.toLowerCase()]||e.error("unsupported pseudo: "+t);return a[W]?a(n):a.length>1?(s=[t,t,"",n],w.setFilters.hasOwnProperty(t.toLowerCase())?i(function(t,e){for(var i,s=a(t,n),r=s.length;r--;)i=te(t,s[r]),t[i]=!(e[i]=s[r])}):function(t){return a(t,0,s)}):a}},pseudos:{not:i(function(t){var e=[],n=[],s=S(t.replace(le,"$1"));return s[W]?i(function(t,e,n,i){for(var a,r=s(t,null,i,[]),o=t.length;o--;)(a=r[o])&&(t[o]=!(e[o]=a))}):function(t,i,a){return e[0]=t,s(e,null,a,n),e[0]=null,!n.pop()}}),has:i(function(t){return function(n){return e(t,n).length>0}}),contains:i(function(t){return t=t.replace(xe,we),function(e){return(e.textContent||e.innerText||k(e)).indexOf(t)>-1}}),lang:i(function(t){return fe.test(t||"")||e.error("unsupported lang: "+t),t=t.replace(xe,we).toLowerCase(),function(e){var n;do if(n=F?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return n=n.toLowerCase(),n===t||0===n.indexOf(t+"-");while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===H},focus:function(t){return t===$.activeElement&&(!$.hasFocus||$.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return t.disabled===!1},disabled:function(t){return t.disabled===!0},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,t.selected===!0},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!w.pseudos.empty(t)},header:function(t){return me.test(t.nodeName)},input:function(t){return ge.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){var e;return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(e=t.getAttribute("type"))||"text"===e.toLowerCase())},first:c(function(){return[0]}),last:c(function(t,e){return[e-1]}),eq:c(function(t,e,n){return[0>n?n+e:n]}),even:c(function(t,e){for(var n=0;e>n;n+=2)t.push(n);return t}),odd:c(function(t,e){for(var n=1;e>n;n+=2)t.push(n);return t}),lt:c(function(t,e,n){for(var i=0>n?n+e:n;--i>=0;)t.push(i);return t}),gt:c(function(t,e,n){for(var i=0>n?n+e:n;++i<e;)t.push(i);return t})}},w.pseudos.nth=w.pseudos.eq;for(b in{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})w.pseudos[b]=o(b);for(b in{submit:!0,reset:!0})w.pseudos[b]=l(b);return u.prototype=w.filters=w.pseudos,w.setFilters=new u,T=e.tokenize=function(t,n){var i,s,a,r,o,l,c,d=I[t+" "];if(d)return n?0:d.slice(0);for(o=t,l=[],c=w.preFilter;o;){(!i||(s=ce.exec(o)))&&(s&&(o=o.slice(s[0].length)||o),l.push(a=[])),i=!1,(s=de.exec(o))&&(i=s.shift(),a.push({value:i,type:s[0].replace(le," ")}),o=o.slice(i.length));for(r in w.filter)!(s=pe[r].exec(o))||c[r]&&!(s=c[r](s))||(i=s.shift(),a.push({value:i,type:r,matches:s}),o=o.slice(i.length));if(!i)break}return n?o.length:o?e.error(t):I(t,l).slice(0)},S=e.compile=function(t,e){var n,i=[],s=[],a=X[t+" "];if(!a){for(e||(e=T(t)),n=e.length;n--;)a=_(e[n]),a[W]?i.push(a):s.push(a);a=X(t,y(s,i)),a.selector=t}return a},A=e.select=function(t,e,n,i){var s,a,r,o,l,c="function"==typeof t&&t,u=!i&&T(t=c.selector||t);if(n=n||[],1===u.length){if(a=u[0]=u[0].slice(0),a.length>2&&"ID"===(r=a[0]).type&&x.getById&&9===e.nodeType&&F&&w.relative[a[1].type]){if(e=(w.find.ID(r.matches[0].replace(xe,we),e)||[])[0],!e)return n;c&&(e=e.parentNode),t=t.slice(a.shift().value.length)}for(s=pe.needsContext.test(t)?0:a.length;s--&&(r=a[s],!w.relative[o=r.type]);)if((l=w.find[o])&&(i=l(r.matches[0].replace(xe,we),ye.test(a[0].type)&&d(e.parentNode)||e))){if(a.splice(s,1),t=i.length&&h(a),!t)return J.apply(n,i),n;break}}return(c||S(t,u))(i,e,!F,n,ye.test(t)&&d(e.parentNode)||e),n},x.sortStable=W.split("").sort(Q).join("")===W,x.detectDuplicates=!!N,D(),x.sortDetached=s(function(t){return 1&t.compareDocumentPosition($.createElement("div"))}),s(function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")})||a("type|href|height|width",function(t,e,n){return n?void 0:t.getAttribute(e,"type"===e.toLowerCase()?1:2)}),x.attributes&&s(function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")})||a("value",function(t,e,n){return n||"input"!==t.nodeName.toLowerCase()?void 0:t.defaultValue}),s(function(t){return null==t.getAttribute("disabled")})||a(ee,function(t,e,n){var i;return n?void 0:t[e]===!0?e.toLowerCase():(i=t.getAttributeNode(e))&&i.specified?i.value:null}),e}(t);se.find=ce,se.expr=ce.selectors,se.expr[":"]=se.expr.pseudos,se.unique=ce.uniqueSort,se.text=ce.getText,se.isXMLDoc=ce.isXML,se.contains=ce.contains;var de=se.expr.match.needsContext,ue=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,he=/^.[^:#\[\.,]*$/;se.filter=function(t,e,n){var i=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===i.nodeType?se.find.matchesSelector(i,t)?[i]:[]:se.find.matches(t,se.grep(e,function(t){return 1===t.nodeType}))},se.fn.extend({find:function(t){var e,n=[],i=this,s=i.length;if("string"!=typeof t)return this.pushStack(se(t).filter(function(){for(e=0;s>e;e++)if(se.contains(i[e],this))return!0}));for(e=0;s>e;e++)se.find(t,i[e],n);return n=this.pushStack(s>1?se.unique(n):n),n.selector=this.selector?this.selector+" "+t:t,n},filter:function(t){return this.pushStack(i(this,t||[],!1))},not:function(t){return this.pushStack(i(this,t||[],!0))},is:function(t){return!!i(this,"string"==typeof t&&de.test(t)?se(t):t||[],!1).length}});var fe,pe=t.document,ge=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,me=se.fn.init=function(t,e){var n,i;if(!t)return this;if("string"==typeof t){if(n="<"===t.charAt(0)&&">"===t.charAt(t.length-1)&&t.length>=3?[null,t,null]:ge.exec(t),!n||!n[1]&&e)return!e||e.jquery?(e||fe).find(t):this.constructor(e).find(t);if(n[1]){if(e=e instanceof se?e[0]:e,se.merge(this,se.parseHTML(n[1],e&&e.nodeType?e.ownerDocument||e:pe,!0)),ue.test(n[1])&&se.isPlainObject(e))for(n in e)se.isFunction(this[n])?this[n](e[n]):this.attr(n,e[n]);return this}if(i=pe.getElementById(n[2]),i&&i.parentNode){if(i.id!==n[2])return fe.find(t);this.length=1,this[0]=i}return this.context=pe,this.selector=t,this}return t.nodeType?(this.context=this[0]=t,this.length=1,this):se.isFunction(t)?"undefined"!=typeof fe.ready?fe.ready(t):t(se):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),se.makeArray(t,this))};me.prototype=se.fn,fe=se(pe);var ve=/^(?:parents|prev(?:Until|All))/,_e={children:!0,contents:!0,next:!0,prev:!0};se.extend({dir:function(t,e,n){for(var i=[],s=t[e];s&&9!==s.nodeType&&(void 0===n||1!==s.nodeType||!se(s).is(n));)1===s.nodeType&&i.push(s),s=s[e];return i},sibling:function(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n}}),se.fn.extend({has:function(t){var e,n=se(t,this),i=n.length;return this.filter(function(){for(e=0;i>e;e++)if(se.contains(this,n[e]))return!0})},closest:function(t,e){for(var n,i=0,s=this.length,a=[],r=de.test(t)||"string"!=typeof t?se(t,e||this.context):0;s>i;i++)for(n=this[i];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(r?r.index(n)>-1:1===n.nodeType&&se.find.matchesSelector(n,t))){a.push(n);break}return this.pushStack(a.length>1?se.unique(a):a)},index:function(t){return t?"string"==typeof t?se.inArray(this[0],se(t)):se.inArray(t.jquery?t[0]:t,this):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(se.unique(se.merge(this.get(),se(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),se.each({parent:function(t){var e=t.parentNode;return e&&11!==e.nodeType?e:null},parents:function(t){return se.dir(t,"parentNode")},parentsUntil:function(t,e,n){return se.dir(t,"parentNode",n)},next:function(t){return s(t,"nextSibling")},prev:function(t){return s(t,"previousSibling")},nextAll:function(t){return se.dir(t,"nextSibling")},prevAll:function(t){return se.dir(t,"previousSibling")},nextUntil:function(t,e,n){return se.dir(t,"nextSibling",n)},prevUntil:function(t,e,n){return se.dir(t,"previousSibling",n)},siblings:function(t){return se.sibling((t.parentNode||{}).firstChild,t)},children:function(t){return se.sibling(t.firstChild)},contents:function(t){return se.nodeName(t,"iframe")?t.contentDocument||t.contentWindow.document:se.merge([],t.childNodes)}},function(t,e){se.fn[t]=function(n,i){var s=se.map(this,e,n);return"Until"!==t.slice(-5)&&(i=n),i&&"string"==typeof i&&(s=se.filter(i,s)),this.length>1&&(_e[t]||(s=se.unique(s)),ve.test(t)&&(s=s.reverse())),this.pushStack(s)}});var ye=/\S+/g,be={};se.Callbacks=function(t){t="string"==typeof t?be[t]||a(t):se.extend({},t);var e,n,i,s,r,o,l=[],c=!t.once&&[],d=function(a){for(n=t.memory&&a,i=!0,r=o||0,o=0,s=l.length,e=!0;l&&s>r;r++)if(l[r].apply(a[0],a[1])===!1&&t.stopOnFalse){n=!1;break}e=!1,l&&(c?c.length&&d(c.shift()):n?l=[]:u.disable())},u={add:function(){if(l){var i=l.length;!function a(e){se.each(e,function(e,n){var i=se.type(n);"function"===i?t.unique&&u.has(n)||l.push(n):n&&n.length&&"string"!==i&&a(n)})}(arguments),e?s=l.length:n&&(o=i,d(n))}return this},remove:function(){return l&&se.each(arguments,function(t,n){for(var i;(i=se.inArray(n,l,i))>-1;)l.splice(i,1),e&&(s>=i&&s--,r>=i&&r--)}),this},has:function(t){return t?se.inArray(t,l)>-1:!(!l||!l.length)},empty:function(){return l=[],s=0,this},disable:function(){return l=c=n=void 0,this},disabled:function(){return!l},lock:function(){return c=void 0,n||u.disable(),this},locked:function(){return!c},fireWith:function(t,n){return!l||i&&!c||(n=n||[],n=[t,n.slice?n.slice():n],e?c.push(n):d(n)),this},fire:function(){return u.fireWith(this,arguments),this},fired:function(){return!!i}};return u},se.extend({Deferred:function(t){var e=[["resolve","done",se.Callbacks("once memory"),"resolved"],["reject","fail",se.Callbacks("once memory"),"rejected"],["notify","progress",se.Callbacks("memory")]],n="pending",i={state:function(){return n},always:function(){return s.done(arguments).fail(arguments),this},then:function(){var t=arguments;return se.Deferred(function(n){se.each(e,function(e,a){var r=se.isFunction(t[e])&&t[e];s[a[1]](function(){var t=r&&r.apply(this,arguments);t&&se.isFunction(t.promise)?t.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[a[0]+"With"](this===i?n.promise():this,r?[t]:arguments)})}),t=null}).promise()},promise:function(t){return null!=t?se.extend(t,i):i}},s={};return i.pipe=i.then,se.each(e,function(t,a){var r=a[2],o=a[3];i[a[1]]=r.add,o&&r.add(function(){n=o},e[1^t][2].disable,e[2][2].lock),s[a[0]]=function(){return s[a[0]+"With"](this===s?i:this,arguments),this},s[a[0]+"With"]=r.fireWith}),i.promise(s),t&&t.call(s,s),s},when:function(t){var e,n,i,s=0,a=V.call(arguments),r=a.length,o=1!==r||t&&se.isFunction(t.promise)?r:0,l=1===o?t:se.Deferred(),c=function(t,n,i){return function(s){n[t]=this,i[t]=arguments.length>1?V.call(arguments):s,i===e?l.notifyWith(n,i):--o||l.resolveWith(n,i)}};if(r>1)for(e=new Array(r),n=new Array(r),i=new Array(r);r>s;s++)a[s]&&se.isFunction(a[s].promise)?a[s].promise().done(c(s,i,a)).fail(l.reject).progress(c(s,n,e)):--o;return o||l.resolveWith(i,a),l.promise()}});var xe;se.fn.ready=function(t){return se.ready.promise().done(t),this},se.extend({isReady:!1,readyWait:1,holdReady:function(t){t?se.readyWait++:se.ready(!0)},ready:function(t){if(t===!0?!--se.readyWait:!se.isReady){if(!pe.body)return setTimeout(se.ready);se.isReady=!0,t!==!0&&--se.readyWait>0||(xe.resolveWith(pe,[se]),se.fn.triggerHandler&&(se(pe).triggerHandler("ready"),se(pe).off("ready")))}}}),se.ready.promise=function(e){if(!xe)if(xe=se.Deferred(),"complete"===pe.readyState)setTimeout(se.ready);else if(pe.addEventListener)pe.addEventListener("DOMContentLoaded",o,!1),t.addEventListener("load",o,!1);else{pe.attachEvent("onreadystatechange",o),t.attachEvent("onload",o);var n=!1;try{n=null==t.frameElement&&pe.documentElement}catch(i){}n&&n.doScroll&&!function s(){if(!se.isReady){try{n.doScroll("left")}catch(t){return setTimeout(s,50)}r(),se.ready()}}()}return xe.promise(e)};var we,ke="undefined";for(we in se(ne))break;ne.ownLast="0"!==we,ne.inlineBlockNeedsLayout=!1,se(function(){var t,e,n,i;n=pe.getElementsByTagName("body")[0],n&&n.style&&(e=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(e),typeof e.style.zoom!==ke&&(e.style.cssText="display:inline;margin:0;border:0;padding:1px;width:1px;zoom:1",ne.inlineBlockNeedsLayout=t=3===e.offsetWidth,t&&(n.style.zoom=1)),n.removeChild(i))}),function(){var t=pe.createElement("div");if(null==ne.deleteExpando){ne.deleteExpando=!0;try{delete t.test}catch(e){ne.deleteExpando=!1}}t=null}(),se.acceptData=function(t){var e=se.noData[(t.nodeName+" ").toLowerCase()],n=+t.nodeType||1;return 1!==n&&9!==n?!1:!e||e!==!0&&t.getAttribute("classid")===e};var Ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,Te=/([A-Z])/g;se.extend({cache:{},noData:{"applet ":!0,"embed ":!0,"object ":"clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"},hasData:function(t){return t=t.nodeType?se.cache[t[se.expando]]:t[se.expando],!!t&&!c(t)},data:function(t,e,n){return d(t,e,n)},removeData:function(t,e){return u(t,e)},_data:function(t,e,n){return d(t,e,n,!0)},_removeData:function(t,e){return u(t,e,!0)}}),se.fn.extend({data:function(t,e){var n,i,s,a=this[0],r=a&&a.attributes;if(void 0===t){if(this.length&&(s=se.data(a),1===a.nodeType&&!se._data(a,"parsedAttrs"))){for(n=r.length;n--;)r[n]&&(i=r[n].name,0===i.indexOf("data-")&&(i=se.camelCase(i.slice(5)),l(a,i,s[i])));se._data(a,"parsedAttrs",!0)}return s}return"object"==typeof t?this.each(function(){se.data(this,t)}):arguments.length>1?this.each(function(){se.data(this,t,e)}):a?l(a,t,se.data(a,t)):void 0},removeData:function(t){return this.each(function(){se.removeData(this,t)})}}),se.extend({queue:function(t,e,n){var i;return t?(e=(e||"fx")+"queue",i=se._data(t,e),n&&(!i||se.isArray(n)?i=se._data(t,e,se.makeArray(n)):i.push(n)),i||[]):void 0},dequeue:function(t,e){e=e||"fx";var n=se.queue(t,e),i=n.length,s=n.shift(),a=se._queueHooks(t,e),r=function(){se.dequeue(t,e)};"inprogress"===s&&(s=n.shift(),i--),s&&("fx"===e&&n.unshift("inprogress"),delete a.stop,s.call(t,r,a)),!i&&a&&a.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return se._data(t,n)||se._data(t,n,{empty:se.Callbacks("once memory").add(function(){se._removeData(t,e+"queue"),se._removeData(t,n)})})}}),se.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?se.queue(this[0],t):void 0===e?this:this.each(function(){var n=se.queue(this,t,e);se._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&se.dequeue(this,t)})},dequeue:function(t){return this.each(function(){se.dequeue(this,t)})},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){var n,i=1,s=se.Deferred(),a=this,r=this.length,o=function(){--i||s.resolveWith(a,[a])};for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";r--;)n=se._data(a[r],t+"queueHooks"),n&&n.empty&&(i++,n.empty.add(o));return o(),s.promise(e)}});var Se=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,Ae=["Top","Right","Bottom","Left"],je=function(t,e){return t=e||t,"none"===se.css(t,"display")||!se.contains(t.ownerDocument,t)},Ee=se.access=function(t,e,n,i,s,a,r){var o=0,l=t.length,c=null==n;if("object"===se.type(n)){s=!0;for(o in n)se.access(t,e,o,n[o],!0,a,r)}else if(void 0!==i&&(s=!0,se.isFunction(i)||(r=!0),c&&(r?(e.call(t,i),e=null):(c=e,e=function(t,e,n){return c.call(se(t),n)})),e))for(;l>o;o++)e(t[o],n,r?i:i.call(t[o],o,e(t[o],n)));return s?t:c?e.call(t):l?e(t[0],n):a},Ne=/^(?:checkbox|radio)$/i;!function(){var t=pe.createElement("input"),e=pe.createElement("div"),n=pe.createDocumentFragment();if(e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",ne.leadingWhitespace=3===e.firstChild.nodeType,ne.tbody=!e.getElementsByTagName("tbody").length,ne.htmlSerialize=!!e.getElementsByTagName("link").length,ne.html5Clone="<:nav></:nav>"!==pe.createElement("nav").cloneNode(!0).outerHTML,t.type="checkbox",t.checked=!0,n.appendChild(t),ne.appendChecked=t.checked,e.innerHTML="<textarea>x</textarea>",ne.noCloneChecked=!!e.cloneNode(!0).lastChild.defaultValue,n.appendChild(e),e.innerHTML="<input type='radio' checked='checked' name='t'/>",ne.checkClone=e.cloneNode(!0).cloneNode(!0).lastChild.checked,ne.noCloneEvent=!0,e.attachEvent&&(e.attachEvent("onclick",function(){ne.noCloneEvent=!1}),e.cloneNode(!0).click()),null==ne.deleteExpando){ne.deleteExpando=!0;try{delete e.test}catch(i){ne.deleteExpando=!1}}}(),function(){var e,n,i=pe.createElement("div");for(e in{submit:!0,change:!0,focusin:!0})n="on"+e,(ne[e+"Bubbles"]=n in t)||(i.setAttribute(n,"t"),ne[e+"Bubbles"]=i.attributes[n].expando===!1);i=null}();var De=/^(?:input|select|textarea)$/i,$e=/^key/,He=/^(?:mouse|pointer|contextmenu)|click/,Fe=/^(?:focusinfocus|focusoutblur)$/,qe=/^([^.]*)(?:\.(.+)|)$/;se.event={global:{},add:function(t,e,n,i,s){var a,r,o,l,c,d,u,h,f,p,g,m=se._data(t);if(m){for(n.handler&&(l=n,n=l.handler,s=l.selector),n.guid||(n.guid=se.guid++),(r=m.events)||(r=m.events={}),(d=m.handle)||(d=m.handle=function(t){return typeof se===ke||t&&se.event.triggered===t.type?void 0:se.event.dispatch.apply(d.elem,arguments)},d.elem=t),e=(e||"").match(ye)||[""],o=e.length;o--;)a=qe.exec(e[o])||[],f=g=a[1],p=(a[2]||"").split(".").sort(),f&&(c=se.event.special[f]||{},f=(s?c.delegateType:c.bindType)||f,c=se.event.special[f]||{},u=se.extend({type:f,origType:g,data:i,handler:n,guid:n.guid,selector:s,needsContext:s&&se.expr.match.needsContext.test(s),namespace:p.join(".")},l),(h=r[f])||(h=r[f]=[],h.delegateCount=0,c.setup&&c.setup.call(t,i,p,d)!==!1||(t.addEventListener?t.addEventListener(f,d,!1):t.attachEvent&&t.attachEvent("on"+f,d))),c.add&&(c.add.call(t,u),u.handler.guid||(u.handler.guid=n.guid)),s?h.splice(h.delegateCount++,0,u):h.push(u),se.event.global[f]=!0);t=null}},remove:function(t,e,n,i,s){var a,r,o,l,c,d,u,h,f,p,g,m=se.hasData(t)&&se._data(t);if(m&&(d=m.events)){for(e=(e||"").match(ye)||[""],c=e.length;c--;)if(o=qe.exec(e[c])||[],f=g=o[1],p=(o[2]||"").split(".").sort(),f){for(u=se.event.special[f]||{},f=(i?u.delegateType:u.bindType)||f,h=d[f]||[],o=o[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),l=a=h.length;a--;)r=h[a],!s&&g!==r.origType||n&&n.guid!==r.guid||o&&!o.test(r.namespace)||i&&i!==r.selector&&("**"!==i||!r.selector)||(h.splice(a,1),r.selector&&h.delegateCount--,u.remove&&u.remove.call(t,r));l&&!h.length&&(u.teardown&&u.teardown.call(t,p,m.handle)!==!1||se.removeEvent(t,f,m.handle),delete d[f])}else for(f in d)se.event.remove(t,f+e[c],n,i,!0);se.isEmptyObject(d)&&(delete m.handle,se._removeData(t,"events"))}},trigger:function(e,n,i,s){var a,r,o,l,c,d,u,h=[i||pe],f=ee.call(e,"type")?e.type:e,p=ee.call(e,"namespace")?e.namespace.split("."):[];if(o=d=i=i||pe,3!==i.nodeType&&8!==i.nodeType&&!Fe.test(f+se.event.triggered)&&(f.indexOf(".")>=0&&(p=f.split("."),f=p.shift(),p.sort()),r=f.indexOf(":")<0&&"on"+f,e=e[se.expando]?e:new se.Event(f,"object"==typeof e&&e),e.isTrigger=s?2:3,e.namespace=p.join("."),e.namespace_re=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=i),n=null==n?[e]:se.makeArray(n,[e]),c=se.event.special[f]||{},s||!c.trigger||c.trigger.apply(i,n)!==!1)){if(!s&&!c.noBubble&&!se.isWindow(i)){for(l=c.delegateType||f,Fe.test(l+f)||(o=o.parentNode);o;o=o.parentNode)h.push(o),d=o;
d===(i.ownerDocument||pe)&&h.push(d.defaultView||d.parentWindow||t)}for(u=0;(o=h[u++])&&!e.isPropagationStopped();)e.type=u>1?l:c.bindType||f,a=(se._data(o,"events")||{})[e.type]&&se._data(o,"handle"),a&&a.apply(o,n),a=r&&o[r],a&&a.apply&&se.acceptData(o)&&(e.result=a.apply(o,n),e.result===!1&&e.preventDefault());if(e.type=f,!s&&!e.isDefaultPrevented()&&(!c._default||c._default.apply(h.pop(),n)===!1)&&se.acceptData(i)&&r&&i[f]&&!se.isWindow(i)){d=i[r],d&&(i[r]=null),se.event.triggered=f;try{i[f]()}catch(g){}se.event.triggered=void 0,d&&(i[r]=d)}return e.result}},dispatch:function(t){t=se.event.fix(t);var e,n,i,s,a,r=[],o=V.call(arguments),l=(se._data(this,"events")||{})[t.type]||[],c=se.event.special[t.type]||{};if(o[0]=t,t.delegateTarget=this,!c.preDispatch||c.preDispatch.call(this,t)!==!1){for(r=se.event.handlers.call(this,t,l),e=0;(s=r[e++])&&!t.isPropagationStopped();)for(t.currentTarget=s.elem,a=0;(i=s.handlers[a++])&&!t.isImmediatePropagationStopped();)(!t.namespace_re||t.namespace_re.test(i.namespace))&&(t.handleObj=i,t.data=i.data,n=((se.event.special[i.origType]||{}).handle||i.handler).apply(s.elem,o),void 0!==n&&(t.result=n)===!1&&(t.preventDefault(),t.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(t,e){var n,i,s,a,r=[],o=e.delegateCount,l=t.target;if(o&&l.nodeType&&(!t.button||"click"!==t.type))for(;l!=this;l=l.parentNode||this)if(1===l.nodeType&&(l.disabled!==!0||"click"!==t.type)){for(s=[],a=0;o>a;a++)i=e[a],n=i.selector+" ",void 0===s[n]&&(s[n]=i.needsContext?se(n,this).index(l)>=0:se.find(n,this,null,[l]).length),s[n]&&s.push(i);s.length&&r.push({elem:l,handlers:s})}return o<e.length&&r.push({elem:this,handlers:e.slice(o)}),r},fix:function(t){if(t[se.expando])return t;var e,n,i,s=t.type,a=t,r=this.fixHooks[s];for(r||(this.fixHooks[s]=r=He.test(s)?this.mouseHooks:$e.test(s)?this.keyHooks:{}),i=r.props?this.props.concat(r.props):this.props,t=new se.Event(a),e=i.length;e--;)n=i[e],t[n]=a[n];return t.target||(t.target=a.srcElement||pe),3===t.target.nodeType&&(t.target=t.target.parentNode),t.metaKey=!!t.metaKey,r.filter?r.filter(t,a):t},props:"altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var n,i,s,a=e.button,r=e.fromElement;return null==t.pageX&&null!=e.clientX&&(i=t.target.ownerDocument||pe,s=i.documentElement,n=i.body,t.pageX=e.clientX+(s&&s.scrollLeft||n&&n.scrollLeft||0)-(s&&s.clientLeft||n&&n.clientLeft||0),t.pageY=e.clientY+(s&&s.scrollTop||n&&n.scrollTop||0)-(s&&s.clientTop||n&&n.clientTop||0)),!t.relatedTarget&&r&&(t.relatedTarget=r===t.target?e.toElement:r),t.which||void 0===a||(t.which=1&a?1:2&a?3:4&a?2:0),t}},special:{load:{noBubble:!0},focus:{trigger:function(){if(this!==p()&&this.focus)try{return this.focus(),!1}catch(t){}},delegateType:"focusin"},blur:{trigger:function(){return this===p()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return se.nodeName(this,"input")&&"checkbox"===this.type&&this.click?(this.click(),!1):void 0},_default:function(t){return se.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}},simulate:function(t,e,n,i){var s=se.extend(new se.Event,n,{type:t,isSimulated:!0,originalEvent:{}});i?se.event.trigger(s,null,e):se.event.dispatch.call(e,s),s.isDefaultPrevented()&&n.preventDefault()}},se.removeEvent=pe.removeEventListener?function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n,!1)}:function(t,e,n){var i="on"+e;t.detachEvent&&(typeof t[i]===ke&&(t[i]=null),t.detachEvent(i,n))},se.Event=function(t,e){return this instanceof se.Event?(t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&t.returnValue===!1?h:f):this.type=t,e&&se.extend(this,e),this.timeStamp=t&&t.timeStamp||se.now(),void(this[se.expando]=!0)):new se.Event(t,e)},se.Event.prototype={isDefaultPrevented:f,isPropagationStopped:f,isImmediatePropagationStopped:f,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=h,t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=h,t&&(t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0)},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=h,t&&t.stopImmediatePropagation&&t.stopImmediatePropagation(),this.stopPropagation()}},se.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(t,e){se.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,i=this,s=t.relatedTarget,a=t.handleObj;return(!s||s!==i&&!se.contains(i,s))&&(t.type=a.origType,n=a.handler.apply(this,arguments),t.type=e),n}}}),ne.submitBubbles||(se.event.special.submit={setup:function(){return se.nodeName(this,"form")?!1:void se.event.add(this,"click._submit keypress._submit",function(t){var e=t.target,n=se.nodeName(e,"input")||se.nodeName(e,"button")?e.form:void 0;n&&!se._data(n,"submitBubbles")&&(se.event.add(n,"submit._submit",function(t){t._submit_bubble=!0}),se._data(n,"submitBubbles",!0))})},postDispatch:function(t){t._submit_bubble&&(delete t._submit_bubble,this.parentNode&&!t.isTrigger&&se.event.simulate("submit",this.parentNode,t,!0))},teardown:function(){return se.nodeName(this,"form")?!1:void se.event.remove(this,"._submit")}}),ne.changeBubbles||(se.event.special.change={setup:function(){return De.test(this.nodeName)?(("checkbox"===this.type||"radio"===this.type)&&(se.event.add(this,"propertychange._change",function(t){"checked"===t.originalEvent.propertyName&&(this._just_changed=!0)}),se.event.add(this,"click._change",function(t){this._just_changed&&!t.isTrigger&&(this._just_changed=!1),se.event.simulate("change",this,t,!0)})),!1):void se.event.add(this,"beforeactivate._change",function(t){var e=t.target;De.test(e.nodeName)&&!se._data(e,"changeBubbles")&&(se.event.add(e,"change._change",function(t){!this.parentNode||t.isSimulated||t.isTrigger||se.event.simulate("change",this.parentNode,t,!0)}),se._data(e,"changeBubbles",!0))})},handle:function(t){var e=t.target;return this!==e||t.isSimulated||t.isTrigger||"radio"!==e.type&&"checkbox"!==e.type?t.handleObj.handler.apply(this,arguments):void 0},teardown:function(){return se.event.remove(this,"._change"),!De.test(this.nodeName)}}),ne.focusinBubbles||se.each({focus:"focusin",blur:"focusout"},function(t,e){var n=function(t){se.event.simulate(e,t.target,se.event.fix(t),!0)};se.event.special[e]={setup:function(){var i=this.ownerDocument||this,s=se._data(i,e);s||i.addEventListener(t,n,!0),se._data(i,e,(s||0)+1)},teardown:function(){var i=this.ownerDocument||this,s=se._data(i,e)-1;s?se._data(i,e,s):(i.removeEventListener(t,n,!0),se._removeData(i,e))}}}),se.fn.extend({on:function(t,e,n,i,s){var a,r;if("object"==typeof t){"string"!=typeof e&&(n=n||e,e=void 0);for(a in t)this.on(a,e,n,t[a],s);return this}if(null==n&&null==i?(i=e,n=e=void 0):null==i&&("string"==typeof e?(i=n,n=void 0):(i=n,n=e,e=void 0)),i===!1)i=f;else if(!i)return this;return 1===s&&(r=i,i=function(t){return se().off(t),r.apply(this,arguments)},i.guid=r.guid||(r.guid=se.guid++)),this.each(function(){se.event.add(this,t,i,n,e)})},one:function(t,e,n,i){return this.on(t,e,n,i,1)},off:function(t,e,n){var i,s;if(t&&t.preventDefault&&t.handleObj)return i=t.handleObj,se(t.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"==typeof t){for(s in t)this.off(s,e,t[s]);return this}return(e===!1||"function"==typeof e)&&(n=e,e=void 0),n===!1&&(n=f),this.each(function(){se.event.remove(this,t,n,e)})},trigger:function(t,e){return this.each(function(){se.event.trigger(t,e,this)})},triggerHandler:function(t,e){var n=this[0];return n?se.event.trigger(t,e,n,!0):void 0}});var Me="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",Le=/ jQuery\d+="(?:null|\d+)"/g,ze=new RegExp("<(?:"+Me+")[\\s/>]","i"),We=/^\s+/,Oe=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,Pe=/<([\w:]+)/,Re=/<tbody/i,Be=/<|&#?\w+;/,Ie=/<(?:script|style|link)/i,Xe=/checked\s*(?:[^=]|=\s*.checked.)/i,Qe=/^$|\/(?:java|ecma)script/i,Ye=/^true\/(.*)/,Ue=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g,Ve={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],area:[1,"<map>","</map>"],param:[1,"<object>","</object>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:ne.htmlSerialize?[0,"",""]:[1,"X<div>","</div>"]},Ze=g(pe),Ge=Ze.appendChild(pe.createElement("div"));Ve.optgroup=Ve.option,Ve.tbody=Ve.tfoot=Ve.colgroup=Ve.caption=Ve.thead,Ve.th=Ve.td,se.extend({clone:function(t,e,n){var i,s,a,r,o,l=se.contains(t.ownerDocument,t);if(ne.html5Clone||se.isXMLDoc(t)||!ze.test("<"+t.nodeName+">")?a=t.cloneNode(!0):(Ge.innerHTML=t.outerHTML,Ge.removeChild(a=Ge.firstChild)),!(ne.noCloneEvent&&ne.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||se.isXMLDoc(t)))for(i=m(a),o=m(t),r=0;null!=(s=o[r]);++r)i[r]&&k(s,i[r]);if(e)if(n)for(o=o||m(t),i=i||m(a),r=0;null!=(s=o[r]);r++)w(s,i[r]);else w(t,a);return i=m(a,"script"),i.length>0&&x(i,!l&&m(t,"script")),i=o=s=null,a},buildFragment:function(t,e,n,i){for(var s,a,r,o,l,c,d,u=t.length,h=g(e),f=[],p=0;u>p;p++)if(a=t[p],a||0===a)if("object"===se.type(a))se.merge(f,a.nodeType?[a]:a);else if(Be.test(a)){for(o=o||h.appendChild(e.createElement("div")),l=(Pe.exec(a)||["",""])[1].toLowerCase(),d=Ve[l]||Ve._default,o.innerHTML=d[1]+a.replace(Oe,"<$1></$2>")+d[2],s=d[0];s--;)o=o.lastChild;if(!ne.leadingWhitespace&&We.test(a)&&f.push(e.createTextNode(We.exec(a)[0])),!ne.tbody)for(a="table"!==l||Re.test(a)?"<table>"!==d[1]||Re.test(a)?0:o:o.firstChild,s=a&&a.childNodes.length;s--;)se.nodeName(c=a.childNodes[s],"tbody")&&!c.childNodes.length&&a.removeChild(c);for(se.merge(f,o.childNodes),o.textContent="";o.firstChild;)o.removeChild(o.firstChild);o=h.lastChild}else f.push(e.createTextNode(a));for(o&&h.removeChild(o),ne.appendChecked||se.grep(m(f,"input"),v),p=0;a=f[p++];)if((!i||-1===se.inArray(a,i))&&(r=se.contains(a.ownerDocument,a),o=m(h.appendChild(a),"script"),r&&x(o),n))for(s=0;a=o[s++];)Qe.test(a.type||"")&&n.push(a);return o=null,h},cleanData:function(t,e){for(var n,i,s,a,r=0,o=se.expando,l=se.cache,c=ne.deleteExpando,d=se.event.special;null!=(n=t[r]);r++)if((e||se.acceptData(n))&&(s=n[o],a=s&&l[s])){if(a.events)for(i in a.events)d[i]?se.event.remove(n,i):se.removeEvent(n,i,a.handle);l[s]&&(delete l[s],c?delete n[o]:typeof n.removeAttribute!==ke?n.removeAttribute(o):n[o]=null,U.push(s))}}}),se.fn.extend({text:function(t){return Ee(this,function(t){return void 0===t?se.text(this):this.empty().append((this[0]&&this[0].ownerDocument||pe).createTextNode(t))},null,t,arguments.length)},append:function(){return this.domManip(arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=_(this,t);e.appendChild(t)}})},prepend:function(){return this.domManip(arguments,function(t){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var e=_(this,t);e.insertBefore(t,e.firstChild)}})},before:function(){return this.domManip(arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this)})},after:function(){return this.domManip(arguments,function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)})},remove:function(t,e){for(var n,i=t?se.filter(t,this):this,s=0;null!=(n=i[s]);s++)e||1!==n.nodeType||se.cleanData(m(n)),n.parentNode&&(e&&se.contains(n.ownerDocument,n)&&x(m(n,"script")),n.parentNode.removeChild(n));return this},empty:function(){for(var t,e=0;null!=(t=this[e]);e++){for(1===t.nodeType&&se.cleanData(m(t,!1));t.firstChild;)t.removeChild(t.firstChild);t.options&&se.nodeName(t,"select")&&(t.options.length=0)}return this},clone:function(t,e){return t=null==t?!1:t,e=null==e?t:e,this.map(function(){return se.clone(this,t,e)})},html:function(t){return Ee(this,function(t){var e=this[0]||{},n=0,i=this.length;if(void 0===t)return 1===e.nodeType?e.innerHTML.replace(Le,""):void 0;if(!("string"!=typeof t||Ie.test(t)||!ne.htmlSerialize&&ze.test(t)||!ne.leadingWhitespace&&We.test(t)||Ve[(Pe.exec(t)||["",""])[1].toLowerCase()])){t=t.replace(Oe,"<$1></$2>");try{for(;i>n;n++)e=this[n]||{},1===e.nodeType&&(se.cleanData(m(e,!1)),e.innerHTML=t);e=0}catch(s){}}e&&this.empty().append(t)},null,t,arguments.length)},replaceWith:function(){var t=arguments[0];return this.domManip(arguments,function(e){t=this.parentNode,se.cleanData(m(this)),t&&t.replaceChild(e,this)}),t&&(t.length||t.nodeType)?this:this.remove()},detach:function(t){return this.remove(t,!0)},domManip:function(t,e){t=Z.apply([],t);var n,i,s,a,r,o,l=0,c=this.length,d=this,u=c-1,h=t[0],f=se.isFunction(h);if(f||c>1&&"string"==typeof h&&!ne.checkClone&&Xe.test(h))return this.each(function(n){var i=d.eq(n);f&&(t[0]=h.call(this,n,i.html())),i.domManip(t,e)});if(c&&(o=se.buildFragment(t,this[0].ownerDocument,!1,this),n=o.firstChild,1===o.childNodes.length&&(o=n),n)){for(a=se.map(m(o,"script"),y),s=a.length;c>l;l++)i=o,l!==u&&(i=se.clone(i,!0,!0),s&&se.merge(a,m(i,"script"))),e.call(this[l],i,l);if(s)for(r=a[a.length-1].ownerDocument,se.map(a,b),l=0;s>l;l++)i=a[l],Qe.test(i.type||"")&&!se._data(i,"globalEval")&&se.contains(r,i)&&(i.src?se._evalUrl&&se._evalUrl(i.src):se.globalEval((i.text||i.textContent||i.innerHTML||"").replace(Ue,"")));o=n=null}return this}}),se.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(t,e){se.fn[t]=function(t){for(var n,i=0,s=[],a=se(t),r=a.length-1;r>=i;i++)n=i===r?this:this.clone(!0),se(a[i])[e](n),G.apply(s,n.get());return this.pushStack(s)}});var Je,Ke={};!function(){var t;ne.shrinkWrapBlocks=function(){if(null!=t)return t;t=!1;var e,n,i;return n=pe.getElementsByTagName("body")[0],n&&n.style?(e=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(e),typeof e.style.zoom!==ke&&(e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:1px;width:1px;zoom:1",e.appendChild(pe.createElement("div")).style.width="5px",t=3!==e.offsetWidth),n.removeChild(i),t):void 0}}();var tn,en,nn=/^margin/,sn=new RegExp("^("+Se+")(?!px)[a-z%]+$","i"),an=/^(top|right|bottom|left)$/;t.getComputedStyle?(tn=function(e){return e.ownerDocument.defaultView.opener?e.ownerDocument.defaultView.getComputedStyle(e,null):t.getComputedStyle(e,null)},en=function(t,e,n){var i,s,a,r,o=t.style;return n=n||tn(t),r=n?n.getPropertyValue(e)||n[e]:void 0,n&&(""!==r||se.contains(t.ownerDocument,t)||(r=se.style(t,e)),sn.test(r)&&nn.test(e)&&(i=o.width,s=o.minWidth,a=o.maxWidth,o.minWidth=o.maxWidth=o.width=r,r=n.width,o.width=i,o.minWidth=s,o.maxWidth=a)),void 0===r?r:r+""}):pe.documentElement.currentStyle&&(tn=function(t){return t.currentStyle},en=function(t,e,n){var i,s,a,r,o=t.style;return n=n||tn(t),r=n?n[e]:void 0,null==r&&o&&o[e]&&(r=o[e]),sn.test(r)&&!an.test(e)&&(i=o.left,s=t.runtimeStyle,a=s&&s.left,a&&(s.left=t.currentStyle.left),o.left="fontSize"===e?"1em":r,r=o.pixelLeft+"px",o.left=i,a&&(s.left=a)),void 0===r?r:r+""||"auto"}),function(){function e(){var e,n,i,s;n=pe.getElementsByTagName("body")[0],n&&n.style&&(e=pe.createElement("div"),i=pe.createElement("div"),i.style.cssText="position:absolute;border:0;width:0;height:0;top:0;left:-9999px",n.appendChild(i).appendChild(e),e.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;display:block;margin-top:1%;top:1%;border:1px;padding:1px;width:4px;position:absolute",a=r=!1,l=!0,t.getComputedStyle&&(a="1%"!==(t.getComputedStyle(e,null)||{}).top,r="4px"===(t.getComputedStyle(e,null)||{width:"4px"}).width,s=e.appendChild(pe.createElement("div")),s.style.cssText=e.style.cssText="-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",s.style.marginRight=s.style.width="0",e.style.width="1px",l=!parseFloat((t.getComputedStyle(s,null)||{}).marginRight),e.removeChild(s)),e.innerHTML="<table><tr><td></td><td>t</td></tr></table>",s=e.getElementsByTagName("td"),s[0].style.cssText="margin:0;border:0;padding:0;display:none",o=0===s[0].offsetHeight,o&&(s[0].style.display="",s[1].style.display="none",o=0===s[0].offsetHeight),n.removeChild(i))}var n,i,s,a,r,o,l;n=pe.createElement("div"),n.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",s=n.getElementsByTagName("a")[0],i=s&&s.style,i&&(i.cssText="float:left;opacity:.5",ne.opacity="0.5"===i.opacity,ne.cssFloat=!!i.cssFloat,n.style.backgroundClip="content-box",n.cloneNode(!0).style.backgroundClip="",ne.clearCloneStyle="content-box"===n.style.backgroundClip,ne.boxSizing=""===i.boxSizing||""===i.MozBoxSizing||""===i.WebkitBoxSizing,se.extend(ne,{reliableHiddenOffsets:function(){return null==o&&e(),o},boxSizingReliable:function(){return null==r&&e(),r},pixelPosition:function(){return null==a&&e(),a},reliableMarginRight:function(){return null==l&&e(),l}}))}(),se.swap=function(t,e,n,i){var s,a,r={};for(a in e)r[a]=t.style[a],t.style[a]=e[a];s=n.apply(t,i||[]);for(a in e)t.style[a]=r[a];return s};var rn=/alpha\([^)]*\)/i,on=/opacity\s*=\s*([^)]*)/,ln=/^(none|table(?!-c[ea]).+)/,cn=new RegExp("^("+Se+")(.*)$","i"),dn=new RegExp("^([+-])=("+Se+")","i"),un={position:"absolute",visibility:"hidden",display:"block"},hn={letterSpacing:"0",fontWeight:"400"},fn=["Webkit","O","Moz","ms"];se.extend({cssHooks:{opacity:{get:function(t,e){if(e){var n=en(t,"opacity");return""===n?"1":n}}}},cssNumber:{columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":ne.cssFloat?"cssFloat":"styleFloat"},style:function(t,e,n,i){if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style){var s,a,r,o=se.camelCase(e),l=t.style;if(e=se.cssProps[o]||(se.cssProps[o]=A(l,o)),r=se.cssHooks[e]||se.cssHooks[o],void 0===n)return r&&"get"in r&&void 0!==(s=r.get(t,!1,i))?s:l[e];if(a=typeof n,"string"===a&&(s=dn.exec(n))&&(n=(s[1]+1)*s[2]+parseFloat(se.css(t,e)),a="number"),null!=n&&n===n&&("number"!==a||se.cssNumber[o]||(n+="px"),ne.clearCloneStyle||""!==n||0!==e.indexOf("background")||(l[e]="inherit"),!(r&&"set"in r&&void 0===(n=r.set(t,n,i)))))try{l[e]=n}catch(c){}}},css:function(t,e,n,i){var s,a,r,o=se.camelCase(e);return e=se.cssProps[o]||(se.cssProps[o]=A(t.style,o)),r=se.cssHooks[e]||se.cssHooks[o],r&&"get"in r&&(a=r.get(t,!0,n)),void 0===a&&(a=en(t,e,i)),"normal"===a&&e in hn&&(a=hn[e]),""===n||n?(s=parseFloat(a),n===!0||se.isNumeric(s)?s||0:a):a}}),se.each(["height","width"],function(t,e){se.cssHooks[e]={get:function(t,n,i){return n?ln.test(se.css(t,"display"))&&0===t.offsetWidth?se.swap(t,un,function(){return D(t,e,i)}):D(t,e,i):void 0},set:function(t,n,i){var s=i&&tn(t);return E(t,n,i?N(t,e,i,ne.boxSizing&&"border-box"===se.css(t,"boxSizing",!1,s),s):0)}}}),ne.opacity||(se.cssHooks.opacity={get:function(t,e){return on.test((e&&t.currentStyle?t.currentStyle.filter:t.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":e?"1":""},set:function(t,e){var n=t.style,i=t.currentStyle,s=se.isNumeric(e)?"alpha(opacity="+100*e+")":"",a=i&&i.filter||n.filter||"";n.zoom=1,(e>=1||""===e)&&""===se.trim(a.replace(rn,""))&&n.removeAttribute&&(n.removeAttribute("filter"),""===e||i&&!i.filter)||(n.filter=rn.test(a)?a.replace(rn,s):a+" "+s)}}),se.cssHooks.marginRight=S(ne.reliableMarginRight,function(t,e){return e?se.swap(t,{display:"inline-block"},en,[t,"marginRight"]):void 0}),se.each({margin:"",padding:"",border:"Width"},function(t,e){se.cssHooks[t+e]={expand:function(n){for(var i=0,s={},a="string"==typeof n?n.split(" "):[n];4>i;i++)s[t+Ae[i]+e]=a[i]||a[i-2]||a[0];return s}},nn.test(t)||(se.cssHooks[t+e].set=E)}),se.fn.extend({css:function(t,e){return Ee(this,function(t,e,n){var i,s,a={},r=0;if(se.isArray(e)){for(i=tn(t),s=e.length;s>r;r++)a[e[r]]=se.css(t,e[r],!1,i);return a}return void 0!==n?se.style(t,e,n):se.css(t,e)},t,e,arguments.length>1)},show:function(){return j(this,!0)},hide:function(){return j(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each(function(){je(this)?se(this).show():se(this).hide()})}}),se.Tween=$,$.prototype={constructor:$,init:function(t,e,n,i,s,a){this.elem=t,this.prop=n,this.easing=s||"swing",this.options=e,this.start=this.now=this.cur(),this.end=i,this.unit=a||(se.cssNumber[n]?"":"px")},cur:function(){var t=$.propHooks[this.prop];return t&&t.get?t.get(this):$.propHooks._default.get(this)},run:function(t){var e,n=$.propHooks[this.prop];return this.pos=e=this.options.duration?se.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):$.propHooks._default.set(this),this}},$.prototype.init.prototype=$.prototype,$.propHooks={_default:{get:function(t){var e;return null==t.elem[t.prop]||t.elem.style&&null!=t.elem.style[t.prop]?(e=se.css(t.elem,t.prop,""),e&&"auto"!==e?e:0):t.elem[t.prop]},set:function(t){se.fx.step[t.prop]?se.fx.step[t.prop](t):t.elem.style&&(null!=t.elem.style[se.cssProps[t.prop]]||se.cssHooks[t.prop])?se.style(t.elem,t.prop,t.now+t.unit):t.elem[t.prop]=t.now}}},$.propHooks.scrollTop=$.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},se.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2}},se.fx=$.prototype.init,se.fx.step={};var pn,gn,mn=/^(?:toggle|show|hide)$/,vn=new RegExp("^(?:([+-])=|)("+Se+")([a-z%]*)$","i"),_n=/queueHooks$/,yn=[M],bn={"*":[function(t,e){var n=this.createTween(t,e),i=n.cur(),s=vn.exec(e),a=s&&s[3]||(se.cssNumber[t]?"":"px"),r=(se.cssNumber[t]||"px"!==a&&+i)&&vn.exec(se.css(n.elem,t)),o=1,l=20;if(r&&r[3]!==a){a=a||r[3],s=s||[],r=+i||1;do o=o||".5",r/=o,se.style(n.elem,t,r+a);while(o!==(o=n.cur()/i)&&1!==o&&--l)}return s&&(r=n.start=+r||+i||0,n.unit=a,n.end=s[1]?r+(s[1]+1)*s[2]:+s[2]),n}]};se.Animation=se.extend(z,{tweener:function(t,e){se.isFunction(t)?(e=t,t=["*"]):t=t.split(" ");for(var n,i=0,s=t.length;s>i;i++)n=t[i],bn[n]=bn[n]||[],bn[n].unshift(e)},prefilter:function(t,e){e?yn.unshift(t):yn.push(t)}}),se.speed=function(t,e,n){var i=t&&"object"==typeof t?se.extend({},t):{complete:n||!n&&e||se.isFunction(t)&&t,duration:t,easing:n&&e||e&&!se.isFunction(e)&&e};return i.duration=se.fx.off?0:"number"==typeof i.duration?i.duration:i.duration in se.fx.speeds?se.fx.speeds[i.duration]:se.fx.speeds._default,(null==i.queue||i.queue===!0)&&(i.queue="fx"),i.old=i.complete,i.complete=function(){se.isFunction(i.old)&&i.old.call(this),i.queue&&se.dequeue(this,i.queue)},i},se.fn.extend({fadeTo:function(t,e,n,i){return this.filter(je).css("opacity",0).show().end().animate({opacity:e},t,n,i)},animate:function(t,e,n,i){var s=se.isEmptyObject(t),a=se.speed(e,n,i),r=function(){var e=z(this,se.extend({},t),a);(s||se._data(this,"finish"))&&e.stop(!0)};return r.finish=r,s||a.queue===!1?this.each(r):this.queue(a.queue,r)},stop:function(t,e,n){var i=function(t){var e=t.stop;delete t.stop,e(n)};return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&t!==!1&&this.queue(t||"fx",[]),this.each(function(){var e=!0,s=null!=t&&t+"queueHooks",a=se.timers,r=se._data(this);if(s)r[s]&&r[s].stop&&i(r[s]);else for(s in r)r[s]&&r[s].stop&&_n.test(s)&&i(r[s]);for(s=a.length;s--;)a[s].elem!==this||null!=t&&a[s].queue!==t||(a[s].anim.stop(n),e=!1,a.splice(s,1));(e||!n)&&se.dequeue(this,t)})},finish:function(t){return t!==!1&&(t=t||"fx"),this.each(function(){var e,n=se._data(this),i=n[t+"queue"],s=n[t+"queueHooks"],a=se.timers,r=i?i.length:0;for(n.finish=!0,se.queue(this,t,[]),s&&s.stop&&s.stop.call(this,!0),e=a.length;e--;)a[e].elem===this&&a[e].queue===t&&(a[e].anim.stop(!0),a.splice(e,1));for(e=0;r>e;e++)i[e]&&i[e].finish&&i[e].finish.call(this);delete n.finish})}}),se.each(["toggle","show","hide"],function(t,e){var n=se.fn[e];se.fn[e]=function(t,i,s){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(F(e,!0),t,i,s)}}),se.each({slideDown:F("show"),slideUp:F("hide"),slideToggle:F("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(t,e){se.fn[t]=function(t,n,i){return this.animate(e,t,n,i)}}),se.timers=[],se.fx.tick=function(){var t,e=se.timers,n=0;for(pn=se.now();n<e.length;n++)t=e[n],t()||e[n]!==t||e.splice(n--,1);e.length||se.fx.stop(),pn=void 0},se.fx.timer=function(t){se.timers.push(t),t()?se.fx.start():se.timers.pop()},se.fx.interval=13,se.fx.start=function(){gn||(gn=setInterval(se.fx.tick,se.fx.interval))},se.fx.stop=function(){clearInterval(gn),gn=null},se.fx.speeds={slow:600,fast:200,_default:400},se.fn.delay=function(t,e){return t=se.fx?se.fx.speeds[t]||t:t,e=e||"fx",this.queue(e,function(e,n){var i=setTimeout(e,t);n.stop=function(){clearTimeout(i)}})},function(){var t,e,n,i,s;e=pe.createElement("div"),e.setAttribute("className","t"),e.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",i=e.getElementsByTagName("a")[0],n=pe.createElement("select"),s=n.appendChild(pe.createElement("option")),t=e.getElementsByTagName("input")[0],i.style.cssText="top:1px",ne.getSetAttribute="t"!==e.className,ne.style=/top/.test(i.getAttribute("style")),ne.hrefNormalized="/a"===i.getAttribute("href"),ne.checkOn=!!t.value,ne.optSelected=s.selected,ne.enctype=!!pe.createElement("form").enctype,n.disabled=!0,ne.optDisabled=!s.disabled,t=pe.createElement("input"),t.setAttribute("value",""),ne.input=""===t.getAttribute("value"),t.value="t",t.setAttribute("type","radio"),ne.radioValue="t"===t.value}();var xn=/\r/g;se.fn.extend({val:function(t){var e,n,i,s=this[0];{if(arguments.length)return i=se.isFunction(t),this.each(function(n){var s;1===this.nodeType&&(s=i?t.call(this,n,se(this).val()):t,null==s?s="":"number"==typeof s?s+="":se.isArray(s)&&(s=se.map(s,function(t){return null==t?"":t+""})),e=se.valHooks[this.type]||se.valHooks[this.nodeName.toLowerCase()],e&&"set"in e&&void 0!==e.set(this,s,"value")||(this.value=s))});if(s)return e=se.valHooks[s.type]||se.valHooks[s.nodeName.toLowerCase()],e&&"get"in e&&void 0!==(n=e.get(s,"value"))?n:(n=s.value,"string"==typeof n?n.replace(xn,""):null==n?"":n)}}}),se.extend({valHooks:{option:{get:function(t){var e=se.find.attr(t,"value");return null!=e?e:se.trim(se.text(t))}},select:{get:function(t){for(var e,n,i=t.options,s=t.selectedIndex,a="select-one"===t.type||0>s,r=a?null:[],o=a?s+1:i.length,l=0>s?o:a?s:0;o>l;l++)if(n=i[l],!(!n.selected&&l!==s||(ne.optDisabled?n.disabled:null!==n.getAttribute("disabled"))||n.parentNode.disabled&&se.nodeName(n.parentNode,"optgroup"))){if(e=se(n).val(),a)return e;r.push(e)}return r},set:function(t,e){for(var n,i,s=t.options,a=se.makeArray(e),r=s.length;r--;)if(i=s[r],se.inArray(se.valHooks.option.get(i),a)>=0)try{i.selected=n=!0}catch(o){i.scrollHeight}else i.selected=!1;return n||(t.selectedIndex=-1),s}}}}),se.each(["radio","checkbox"],function(){se.valHooks[this]={set:function(t,e){return se.isArray(e)?t.checked=se.inArray(se(t).val(),e)>=0:void 0}},ne.checkOn||(se.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})});var wn,kn,Cn=se.expr.attrHandle,Tn=/^(?:checked|selected)$/i,Sn=ne.getSetAttribute,An=ne.input;se.fn.extend({attr:function(t,e){return Ee(this,se.attr,t,e,arguments.length>1)},removeAttr:function(t){return this.each(function(){se.removeAttr(this,t)})}}),se.extend({attr:function(t,e,n){var i,s,a=t.nodeType;if(t&&3!==a&&8!==a&&2!==a)return typeof t.getAttribute===ke?se.prop(t,e,n):(1===a&&se.isXMLDoc(t)||(e=e.toLowerCase(),i=se.attrHooks[e]||(se.expr.match.bool.test(e)?kn:wn)),void 0===n?i&&"get"in i&&null!==(s=i.get(t,e))?s:(s=se.find.attr(t,e),null==s?void 0:s):null!==n?i&&"set"in i&&void 0!==(s=i.set(t,n,e))?s:(t.setAttribute(e,n+""),n):void se.removeAttr(t,e))},removeAttr:function(t,e){var n,i,s=0,a=e&&e.match(ye);if(a&&1===t.nodeType)for(;n=a[s++];)i=se.propFix[n]||n,se.expr.match.bool.test(n)?An&&Sn||!Tn.test(n)?t[i]=!1:t[se.camelCase("default-"+n)]=t[i]=!1:se.attr(t,n,""),t.removeAttribute(Sn?n:i)},attrHooks:{type:{set:function(t,e){if(!ne.radioValue&&"radio"===e&&se.nodeName(t,"input")){var n=t.value;return t.setAttribute("type",e),n&&(t.value=n),e}}}}}),kn={set:function(t,e,n){return e===!1?se.removeAttr(t,n):An&&Sn||!Tn.test(n)?t.setAttribute(!Sn&&se.propFix[n]||n,n):t[se.camelCase("default-"+n)]=t[n]=!0,n}},se.each(se.expr.match.bool.source.match(/\w+/g),function(t,e){var n=Cn[e]||se.find.attr;Cn[e]=An&&Sn||!Tn.test(e)?function(t,e,i){var s,a;return i||(a=Cn[e],Cn[e]=s,s=null!=n(t,e,i)?e.toLowerCase():null,Cn[e]=a),s}:function(t,e,n){return n?void 0:t[se.camelCase("default-"+e)]?e.toLowerCase():null}}),An&&Sn||(se.attrHooks.value={set:function(t,e,n){return se.nodeName(t,"input")?void(t.defaultValue=e):wn&&wn.set(t,e,n)}}),Sn||(wn={set:function(t,e,n){var i=t.getAttributeNode(n);return i||t.setAttributeNode(i=t.ownerDocument.createAttribute(n)),i.value=e+="","value"===n||e===t.getAttribute(n)?e:void 0}},Cn.id=Cn.name=Cn.coords=function(t,e,n){var i;return n?void 0:(i=t.getAttributeNode(e))&&""!==i.value?i.value:null},se.valHooks.button={get:function(t,e){var n=t.getAttributeNode(e);return n&&n.specified?n.value:void 0},set:wn.set},se.attrHooks.contenteditable={set:function(t,e,n){wn.set(t,""===e?!1:e,n)}},se.each(["width","height"],function(t,e){se.attrHooks[e]={set:function(t,n){return""===n?(t.setAttribute(e,"auto"),n):void 0}}})),ne.style||(se.attrHooks.style={get:function(t){return t.style.cssText||void 0},set:function(t,e){return t.style.cssText=e+""}});var jn=/^(?:input|select|textarea|button|object)$/i,En=/^(?:a|area)$/i;se.fn.extend({prop:function(t,e){return Ee(this,se.prop,t,e,arguments.length>1)},removeProp:function(t){return t=se.propFix[t]||t,this.each(function(){try{this[t]=void 0,delete this[t]}catch(e){}})}}),se.extend({propFix:{"for":"htmlFor","class":"className"},prop:function(t,e,n){var i,s,a,r=t.nodeType;if(t&&3!==r&&8!==r&&2!==r)return a=1!==r||!se.isXMLDoc(t),a&&(e=se.propFix[e]||e,s=se.propHooks[e]),void 0!==n?s&&"set"in s&&void 0!==(i=s.set(t,n,e))?i:t[e]=n:s&&"get"in s&&null!==(i=s.get(t,e))?i:t[e]},propHooks:{tabIndex:{get:function(t){var e=se.find.attr(t,"tabindex");return e?parseInt(e,10):jn.test(t.nodeName)||En.test(t.nodeName)&&t.href?0:-1}}}}),ne.hrefNormalized||se.each(["href","src"],function(t,e){se.propHooks[e]={get:function(t){return t.getAttribute(e,4)}}}),ne.optSelected||(se.propHooks.selected={get:function(t){var e=t.parentNode;return e&&(e.selectedIndex,e.parentNode&&e.parentNode.selectedIndex),null}}),se.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){se.propFix[this.toLowerCase()]=this}),ne.enctype||(se.propFix.enctype="encoding");var Nn=/[\t\r\n\f]/g;se.fn.extend({addClass:function(t){var e,n,i,s,a,r,o=0,l=this.length,c="string"==typeof t&&t;if(se.isFunction(t))return this.each(function(e){se(this).addClass(t.call(this,e,this.className))});if(c)for(e=(t||"").match(ye)||[];l>o;o++)if(n=this[o],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Nn," "):" ")){for(a=0;s=e[a++];)i.indexOf(" "+s+" ")<0&&(i+=s+" ");r=se.trim(i),n.className!==r&&(n.className=r)}return this},removeClass:function(t){var e,n,i,s,a,r,o=0,l=this.length,c=0===arguments.length||"string"==typeof t&&t;if(se.isFunction(t))return this.each(function(e){se(this).removeClass(t.call(this,e,this.className))});if(c)for(e=(t||"").match(ye)||[];l>o;o++)if(n=this[o],i=1===n.nodeType&&(n.className?(" "+n.className+" ").replace(Nn," "):"")){for(a=0;s=e[a++];)for(;i.indexOf(" "+s+" ")>=0;)i=i.replace(" "+s+" "," ");r=t?se.trim(i):"",n.className!==r&&(n.className=r)}return this},toggleClass:function(t,e){var n=typeof t;return"boolean"==typeof e&&"string"===n?e?this.addClass(t):this.removeClass(t):this.each(se.isFunction(t)?function(n){se(this).toggleClass(t.call(this,n,this.className,e),e)}:function(){if("string"===n)for(var e,i=0,s=se(this),a=t.match(ye)||[];e=a[i++];)s.hasClass(e)?s.removeClass(e):s.addClass(e);
else(n===ke||"boolean"===n)&&(this.className&&se._data(this,"__className__",this.className),this.className=this.className||t===!1?"":se._data(this,"__className__")||"")})},hasClass:function(t){for(var e=" "+t+" ",n=0,i=this.length;i>n;n++)if(1===this[n].nodeType&&(" "+this[n].className+" ").replace(Nn," ").indexOf(e)>=0)return!0;return!1}}),se.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(t,e){se.fn[e]=function(t,n){return arguments.length>0?this.on(e,null,t,n):this.trigger(e)}}),se.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)},bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,i){return this.on(e,t,n,i)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)}});var Dn=se.now(),$n=/\?/,Hn=/(,)|(\[|{)|(}|])|"(?:[^"\\\r\n]|\\["\\\/bfnrt]|\\u[\da-fA-F]{4})*"\s*:?|true|false|null|-?(?!0\d)\d+(?:\.\d+|)(?:[eE][+-]?\d+|)/g;se.parseJSON=function(e){if(t.JSON&&t.JSON.parse)return t.JSON.parse(e+"");var n,i=null,s=se.trim(e+"");return s&&!se.trim(s.replace(Hn,function(t,e,s,a){return n&&e&&(i=0),0===i?t:(n=s||e,i+=!a-!s,"")}))?Function("return "+s)():se.error("Invalid JSON: "+e)},se.parseXML=function(e){var n,i;if(!e||"string"!=typeof e)return null;try{t.DOMParser?(i=new DOMParser,n=i.parseFromString(e,"text/xml")):(n=new ActiveXObject("Microsoft.XMLDOM"),n.async="false",n.loadXML(e))}catch(s){n=void 0}return n&&n.documentElement&&!n.getElementsByTagName("parsererror").length||se.error("Invalid XML: "+e),n};var Fn,qn,Mn=/#.*$/,Ln=/([?&])_=[^&]*/,zn=/^(.*?):[ \t]*([^\r\n]*)\r?$/gm,Wn=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,On=/^(?:GET|HEAD)$/,Pn=/^\/\//,Rn=/^([\w.+-]+:)(?:\/\/(?:[^\/?#]*@|)([^\/?#:]*)(?::(\d+)|)|)/,Bn={},In={},Xn="*/".concat("*");try{qn=location.href}catch(Qn){qn=pe.createElement("a"),qn.href="",qn=qn.href}Fn=Rn.exec(qn.toLowerCase())||[],se.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:qn,type:"GET",isLocal:Wn.test(Fn[1]),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Xn,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":se.parseJSON,"text xml":se.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?P(P(t,se.ajaxSettings),e):P(se.ajaxSettings,t)},ajaxPrefilter:W(Bn),ajaxTransport:W(In),ajax:function(t,e){function n(t,e,n,i){var s,d,v,_,b,w=e;2!==y&&(y=2,o&&clearTimeout(o),c=void 0,r=i||"",x.readyState=t>0?4:0,s=t>=200&&300>t||304===t,n&&(_=R(u,x,n)),_=B(u,_,x,s),s?(u.ifModified&&(b=x.getResponseHeader("Last-Modified"),b&&(se.lastModified[a]=b),b=x.getResponseHeader("etag"),b&&(se.etag[a]=b)),204===t||"HEAD"===u.type?w="nocontent":304===t?w="notmodified":(w=_.state,d=_.data,v=_.error,s=!v)):(v=w,(t||!w)&&(w="error",0>t&&(t=0))),x.status=t,x.statusText=(e||w)+"",s?p.resolveWith(h,[d,w,x]):p.rejectWith(h,[x,w,v]),x.statusCode(m),m=void 0,l&&f.trigger(s?"ajaxSuccess":"ajaxError",[x,u,s?d:v]),g.fireWith(h,[x,w]),l&&(f.trigger("ajaxComplete",[x,u]),--se.active||se.event.trigger("ajaxStop")))}"object"==typeof t&&(e=t,t=void 0),e=e||{};var i,s,a,r,o,l,c,d,u=se.ajaxSetup({},e),h=u.context||u,f=u.context&&(h.nodeType||h.jquery)?se(h):se.event,p=se.Deferred(),g=se.Callbacks("once memory"),m=u.statusCode||{},v={},_={},y=0,b="canceled",x={readyState:0,getResponseHeader:function(t){var e;if(2===y){if(!d)for(d={};e=zn.exec(r);)d[e[1].toLowerCase()]=e[2];e=d[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===y?r:null},setRequestHeader:function(t,e){var n=t.toLowerCase();return y||(t=_[n]=_[n]||t,v[t]=e),this},overrideMimeType:function(t){return y||(u.mimeType=t),this},statusCode:function(t){var e;if(t)if(2>y)for(e in t)m[e]=[m[e],t[e]];else x.always(t[x.status]);return this},abort:function(t){var e=t||b;return c&&c.abort(e),n(0,e),this}};if(p.promise(x).complete=g.add,x.success=x.done,x.error=x.fail,u.url=((t||u.url||qn)+"").replace(Mn,"").replace(Pn,Fn[1]+"//"),u.type=e.method||e.type||u.method||u.type,u.dataTypes=se.trim(u.dataType||"*").toLowerCase().match(ye)||[""],null==u.crossDomain&&(i=Rn.exec(u.url.toLowerCase()),u.crossDomain=!(!i||i[1]===Fn[1]&&i[2]===Fn[2]&&(i[3]||("http:"===i[1]?"80":"443"))===(Fn[3]||("http:"===Fn[1]?"80":"443")))),u.data&&u.processData&&"string"!=typeof u.data&&(u.data=se.param(u.data,u.traditional)),O(Bn,u,e,x),2===y)return x;l=se.event&&u.global,l&&0===se.active++&&se.event.trigger("ajaxStart"),u.type=u.type.toUpperCase(),u.hasContent=!On.test(u.type),a=u.url,u.hasContent||(u.data&&(a=u.url+=($n.test(a)?"&":"?")+u.data,delete u.data),u.cache===!1&&(u.url=Ln.test(a)?a.replace(Ln,"$1_="+Dn++):a+($n.test(a)?"&":"?")+"_="+Dn++)),u.ifModified&&(se.lastModified[a]&&x.setRequestHeader("If-Modified-Since",se.lastModified[a]),se.etag[a]&&x.setRequestHeader("If-None-Match",se.etag[a])),(u.data&&u.hasContent&&u.contentType!==!1||e.contentType)&&x.setRequestHeader("Content-Type",u.contentType),x.setRequestHeader("Accept",u.dataTypes[0]&&u.accepts[u.dataTypes[0]]?u.accepts[u.dataTypes[0]]+("*"!==u.dataTypes[0]?", "+Xn+"; q=0.01":""):u.accepts["*"]);for(s in u.headers)x.setRequestHeader(s,u.headers[s]);if(u.beforeSend&&(u.beforeSend.call(h,x,u)===!1||2===y))return x.abort();b="abort";for(s in{success:1,error:1,complete:1})x[s](u[s]);if(c=O(In,u,e,x)){x.readyState=1,l&&f.trigger("ajaxSend",[x,u]),u.async&&u.timeout>0&&(o=setTimeout(function(){x.abort("timeout")},u.timeout));try{y=1,c.send(v,n)}catch(w){if(!(2>y))throw w;n(-1,w)}}else n(-1,"No Transport");return x},getJSON:function(t,e,n){return se.get(t,e,n,"json")},getScript:function(t,e){return se.get(t,void 0,e,"script")}}),se.each(["get","post"],function(t,e){se[e]=function(t,n,i,s){return se.isFunction(n)&&(s=s||i,i=n,n=void 0),se.ajax({url:t,type:e,dataType:s,data:n,success:i})}}),se._evalUrl=function(t){return se.ajax({url:t,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0})},se.fn.extend({wrapAll:function(t){if(se.isFunction(t))return this.each(function(e){se(this).wrapAll(t.call(this,e))});if(this[0]){var e=se(t,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&e.insertBefore(this[0]),e.map(function(){for(var t=this;t.firstChild&&1===t.firstChild.nodeType;)t=t.firstChild;return t}).append(this)}return this},wrapInner:function(t){return this.each(se.isFunction(t)?function(e){se(this).wrapInner(t.call(this,e))}:function(){var e=se(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)})},wrap:function(t){var e=se.isFunction(t);return this.each(function(n){se(this).wrapAll(e?t.call(this,n):t)})},unwrap:function(){return this.parent().each(function(){se.nodeName(this,"body")||se(this).replaceWith(this.childNodes)}).end()}}),se.expr.filters.hidden=function(t){return t.offsetWidth<=0&&t.offsetHeight<=0||!ne.reliableHiddenOffsets()&&"none"===(t.style&&t.style.display||se.css(t,"display"))},se.expr.filters.visible=function(t){return!se.expr.filters.hidden(t)};var Yn=/%20/g,Un=/\[\]$/,Vn=/\r?\n/g,Zn=/^(?:submit|button|image|reset|file)$/i,Gn=/^(?:input|select|textarea|keygen)/i;se.param=function(t,e){var n,i=[],s=function(t,e){e=se.isFunction(e)?e():null==e?"":e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)};if(void 0===e&&(e=se.ajaxSettings&&se.ajaxSettings.traditional),se.isArray(t)||t.jquery&&!se.isPlainObject(t))se.each(t,function(){s(this.name,this.value)});else for(n in t)I(n,t[n],e,s);return i.join("&").replace(Yn,"+")},se.fn.extend({serialize:function(){return se.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var t=se.prop(this,"elements");return t?se.makeArray(t):this}).filter(function(){var t=this.type;return this.name&&!se(this).is(":disabled")&&Gn.test(this.nodeName)&&!Zn.test(t)&&(this.checked||!Ne.test(t))}).map(function(t,e){var n=se(this).val();return null==n?null:se.isArray(n)?se.map(n,function(t){return{name:e.name,value:t.replace(Vn,"\r\n")}}):{name:e.name,value:n.replace(Vn,"\r\n")}}).get()}}),se.ajaxSettings.xhr=void 0!==t.ActiveXObject?function(){return!this.isLocal&&/^(get|post|head|put|delete|options)$/i.test(this.type)&&X()||Q()}:X;var Jn=0,Kn={},ti=se.ajaxSettings.xhr();t.attachEvent&&t.attachEvent("onunload",function(){for(var t in Kn)Kn[t](void 0,!0)}),ne.cors=!!ti&&"withCredentials"in ti,ti=ne.ajax=!!ti,ti&&se.ajaxTransport(function(t){if(!t.crossDomain||ne.cors){var e;return{send:function(n,i){var s,a=t.xhr(),r=++Jn;if(a.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(s in t.xhrFields)a[s]=t.xhrFields[s];t.mimeType&&a.overrideMimeType&&a.overrideMimeType(t.mimeType),t.crossDomain||n["X-Requested-With"]||(n["X-Requested-With"]="XMLHttpRequest");for(s in n)void 0!==n[s]&&a.setRequestHeader(s,n[s]+"");a.send(t.hasContent&&t.data||null),e=function(n,s){var o,l,c;if(e&&(s||4===a.readyState))if(delete Kn[r],e=void 0,a.onreadystatechange=se.noop,s)4!==a.readyState&&a.abort();else{c={},o=a.status,"string"==typeof a.responseText&&(c.text=a.responseText);try{l=a.statusText}catch(d){l=""}o||!t.isLocal||t.crossDomain?1223===o&&(o=204):o=c.text?200:404}c&&i(o,l,c,a.getAllResponseHeaders())},t.async?4===a.readyState?setTimeout(e):a.onreadystatechange=Kn[r]=e:e()},abort:function(){e&&e(void 0,!0)}}}}),se.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/(?:java|ecma)script/},converters:{"text script":function(t){return se.globalEval(t),t}}}),se.ajaxPrefilter("script",function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET",t.global=!1)}),se.ajaxTransport("script",function(t){if(t.crossDomain){var e,n=pe.head||se("head")[0]||pe.documentElement;return{send:function(i,s){e=pe.createElement("script"),e.async=!0,t.scriptCharset&&(e.charset=t.scriptCharset),e.src=t.url,e.onload=e.onreadystatechange=function(t,n){(n||!e.readyState||/loaded|complete/.test(e.readyState))&&(e.onload=e.onreadystatechange=null,e.parentNode&&e.parentNode.removeChild(e),e=null,n||s(200,"success"))},n.insertBefore(e,n.firstChild)},abort:function(){e&&e.onload(void 0,!0)}}}});var ei=[],ni=/(=)\?(?=&|$)|\?\?/;se.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=ei.pop()||se.expando+"_"+Dn++;return this[t]=!0,t}}),se.ajaxPrefilter("json jsonp",function(e,n,i){var s,a,r,o=e.jsonp!==!1&&(ni.test(e.url)?"url":"string"==typeof e.data&&!(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&ni.test(e.data)&&"data");return o||"jsonp"===e.dataTypes[0]?(s=e.jsonpCallback=se.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,o?e[o]=e[o].replace(ni,"$1"+s):e.jsonp!==!1&&(e.url+=($n.test(e.url)?"&":"?")+e.jsonp+"="+s),e.converters["script json"]=function(){return r||se.error(s+" was not called"),r[0]},e.dataTypes[0]="json",a=t[s],t[s]=function(){r=arguments},i.always(function(){t[s]=a,e[s]&&(e.jsonpCallback=n.jsonpCallback,ei.push(s)),r&&se.isFunction(a)&&a(r[0]),r=a=void 0}),"script"):void 0}),se.parseHTML=function(t,e,n){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(n=e,e=!1),e=e||pe;var i=ue.exec(t),s=!n&&[];return i?[e.createElement(i[1])]:(i=se.buildFragment([t],e,s),s&&s.length&&se(s).remove(),se.merge([],i.childNodes))};var ii=se.fn.load;se.fn.load=function(t,e,n){if("string"!=typeof t&&ii)return ii.apply(this,arguments);var i,s,a,r=this,o=t.indexOf(" ");return o>=0&&(i=se.trim(t.slice(o,t.length)),t=t.slice(0,o)),se.isFunction(e)?(n=e,e=void 0):e&&"object"==typeof e&&(a="POST"),r.length>0&&se.ajax({url:t,type:a,dataType:"html",data:e}).done(function(t){s=arguments,r.html(i?se("<div>").append(se.parseHTML(t)).find(i):t)}).complete(n&&function(t,e){r.each(n,s||[t.responseText,e,t])}),this},se.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(t,e){se.fn[e]=function(t){return this.on(e,t)}}),se.expr.filters.animated=function(t){return se.grep(se.timers,function(e){return t===e.elem}).length};var si=t.document.documentElement;se.offset={setOffset:function(t,e,n){var i,s,a,r,o,l,c,d=se.css(t,"position"),u=se(t),h={};"static"===d&&(t.style.position="relative"),o=u.offset(),a=se.css(t,"top"),l=se.css(t,"left"),c=("absolute"===d||"fixed"===d)&&se.inArray("auto",[a,l])>-1,c?(i=u.position(),r=i.top,s=i.left):(r=parseFloat(a)||0,s=parseFloat(l)||0),se.isFunction(e)&&(e=e.call(t,n,o)),null!=e.top&&(h.top=e.top-o.top+r),null!=e.left&&(h.left=e.left-o.left+s),"using"in e?e.using.call(t,h):u.css(h)}},se.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){se.offset.setOffset(this,t,e)});var e,n,i={top:0,left:0},s=this[0],a=s&&s.ownerDocument;if(a)return e=a.documentElement,se.contains(e,s)?(typeof s.getBoundingClientRect!==ke&&(i=s.getBoundingClientRect()),n=Y(a),{top:i.top+(n.pageYOffset||e.scrollTop)-(e.clientTop||0),left:i.left+(n.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}):i},position:function(){if(this[0]){var t,e,n={top:0,left:0},i=this[0];return"fixed"===se.css(i,"position")?e=i.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),se.nodeName(t[0],"html")||(n=t.offset()),n.top+=se.css(t[0],"borderTopWidth",!0),n.left+=se.css(t[0],"borderLeftWidth",!0)),{top:e.top-n.top-se.css(i,"marginTop",!0),left:e.left-n.left-se.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var t=this.offsetParent||si;t&&!se.nodeName(t,"html")&&"static"===se.css(t,"position");)t=t.offsetParent;return t||si})}}),se.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,e){var n=/Y/.test(e);se.fn[t]=function(i){return Ee(this,function(t,i,s){var a=Y(t);return void 0===s?a?e in a?a[e]:a.document.documentElement[i]:t[i]:void(a?a.scrollTo(n?se(a).scrollLeft():s,n?s:se(a).scrollTop()):t[i]=s)},t,i,arguments.length,null)}}),se.each(["top","left"],function(t,e){se.cssHooks[e]=S(ne.pixelPosition,function(t,n){return n?(n=en(t,e),sn.test(n)?se(t).position()[e]+"px":n):void 0})}),se.each({Height:"height",Width:"width"},function(t,e){se.each({padding:"inner"+t,content:e,"":"outer"+t},function(n,i){se.fn[i]=function(i,s){var a=arguments.length&&(n||"boolean"!=typeof i),r=n||(i===!0||s===!0?"margin":"border");return Ee(this,function(e,n,i){var s;return se.isWindow(e)?e.document.documentElement["client"+t]:9===e.nodeType?(s=e.documentElement,Math.max(e.body["scroll"+t],s["scroll"+t],e.body["offset"+t],s["offset"+t],s["client"+t])):void 0===i?se.css(e,n,r):se.style(e,n,i,r)},e,a?i:void 0,a,null)}})}),se.fn.size=function(){return this.length},se.fn.andSelf=se.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],function(){return se});var ai=t.jQuery,ri=t.$;return se.noConflict=function(e){return t.$===se&&(t.$=ri),e&&t.jQuery===se&&(t.jQuery=ai),se},typeof e===ke&&(t.jQuery=t.$=se),se}),/*
 * Foundation Responsive Library
 * http://foundation.zurb.com
 * Copyright 2014, ZURB
 * Free to use under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
*/
function(t,e,n,i){"use strict";function s(t){return("string"==typeof t||t instanceof String)&&(t=t.replace(/^['\\\/"]+|(;\s?})+|['\\\/"]+$/g,"")),t}var a=function(e){for(var n=e.length,i=t("head");n--;)0===i.has("."+e[n]).length&&i.append('<meta class="'+e[n]+'" />')};a(["foundation-mq-small","foundation-mq-small-only","foundation-mq-medium","foundation-mq-medium-only","foundation-mq-large","foundation-mq-large-only","foundation-mq-xlarge","foundation-mq-xlarge-only","foundation-mq-xxlarge","foundation-data-attribute-namespace"]),t(function(){"undefined"!=typeof FastClick&&"undefined"!=typeof n.body&&FastClick.attach(n.body)});var r=function(e,i){if("string"==typeof e){if(i){var s;if(i.jquery){if(s=i[0],!s)return i}else s=i;return t(s.querySelectorAll(e))}return t(n.querySelectorAll(e))}return t(e,i)},o=function(t){var e=[];return t||e.push("data"),this.namespace.length>0&&e.push(this.namespace),e.push(this.name),e.join("-")},l=function(t){for(var e=t.split("-"),n=e.length,i=[];n--;)0!==n?i.push(e[n]):this.namespace.length>0?i.push(this.namespace,e[n]):i.push(e[n]);return i.reverse().join("-")},c=function(e,n){var i=this,s=function(){var s=r(this),a=!s.data(i.attr_name(!0)+"-init");s.data(i.attr_name(!0)+"-init",t.extend({},i.settings,n||e,i.data_options(s))),a&&i.events(this)};return r(this.scope).is("["+this.attr_name()+"]")?s.call(this.scope):r("["+this.attr_name()+"]",this.scope).each(s),"string"==typeof e?this[e].call(this,n):void 0},d=function(t,e){function n(){e(t[0])}function i(){if(this.one("load",n),/MSIE (\d+\.\d+);/.test(navigator.userAgent)){var t=this.attr("src"),e=t.match(/\?/)?"&":"?";e+="random="+(new Date).getTime(),this.attr("src",t+e)}}return t.attr("src")?void(t[0].complete||4===t[0].readyState?n():i.call(t)):void n()};/*! matchMedia() polyfill - Test a CSS media type/query in JS. Authors & copyright (c) 2012: Scott Jehl, Paul Irish, Nicholas Zakas, David Knight. Dual MIT/BSD license */
e.matchMedia||(e.matchMedia=function(){var t=e.styleMedia||e.media;if(!t){var i=n.createElement("style"),s=n.getElementsByTagName("script")[0],a=null;i.type="text/css",i.id="matchmediajs-test",s.parentNode.insertBefore(i,s),a="getComputedStyle"in e&&e.getComputedStyle(i,null)||i.currentStyle,t={matchMedium:function(t){var e="@media "+t+"{ #matchmediajs-test { width: 1px; } }";return i.styleSheet?i.styleSheet.cssText=e:i.textContent=e,"1px"===a.width}}}return function(e){return{matches:t.matchMedium(e||"all"),media:e||"all"}}}()),/*
   * jquery.requestAnimationFrame
   * https://github.com/gnarf37/jquery-requestAnimationFrame
   * Requires jQuery 1.8+
   *
   * Copyright (c) 2012 Corey Frang
   * Licensed under the MIT license.
   */
function(t){function n(){i&&(r(n),l&&t.fx.tick())}for(var i,s=0,a=["webkit","moz"],r=e.requestAnimationFrame,o=e.cancelAnimationFrame,l="undefined"!=typeof t.fx;s<a.length&&!r;s++)r=e[a[s]+"RequestAnimationFrame"],o=o||e[a[s]+"CancelAnimationFrame"]||e[a[s]+"CancelRequestAnimationFrame"];r?(e.requestAnimationFrame=r,e.cancelAnimationFrame=o,l&&(t.fx.timer=function(e){e()&&t.timers.push(e)&&!i&&(i=!0,n())},t.fx.stop=function(){i=!1})):(e.requestAnimationFrame=function(t){var n=(new Date).getTime(),i=Math.max(0,16-(n-s)),a=e.setTimeout(function(){t(n+i)},i);return s=n+i,a},e.cancelAnimationFrame=function(t){clearTimeout(t)})}(t),e.Foundation={name:"Foundation",version:"5.5.2",media_queries:{small:r(".foundation-mq-small").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"small-only":r(".foundation-mq-small-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),medium:r(".foundation-mq-medium").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"medium-only":r(".foundation-mq-medium-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),large:r(".foundation-mq-large").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"large-only":r(".foundation-mq-large-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),xlarge:r(".foundation-mq-xlarge").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),"xlarge-only":r(".foundation-mq-xlarge-only").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,""),xxlarge:r(".foundation-mq-xxlarge").css("font-family").replace(/^[\/\\'"]+|(;\s?})+|[\/\\'"]+$/g,"")},stylesheet:t("<style></style>").appendTo("head")[0].sheet,global:{namespace:i},init:function(t,n,i,s,a){var o=[t,i,s,a],l=[];if(this.rtl=/rtl/i.test(r("html").attr("dir")),this.scope=t||this.scope,this.set_namespace(),n&&"string"==typeof n&&!/reflow/i.test(n))this.libs.hasOwnProperty(n)&&l.push(this.init_lib(n,o));else for(var c in this.libs)l.push(this.init_lib(c,n));return r(e).load(function(){r(e).trigger("resize.fndtn.clearing").trigger("resize.fndtn.dropdown").trigger("resize.fndtn.equalizer").trigger("resize.fndtn.interchange").trigger("resize.fndtn.joyride").trigger("resize.fndtn.magellan").trigger("resize.fndtn.topbar").trigger("resize.fndtn.slider")}),t},init_lib:function(e,n){return this.libs.hasOwnProperty(e)?(this.patch(this.libs[e]),n&&n.hasOwnProperty(e)?("undefined"!=typeof this.libs[e].settings?t.extend(!0,this.libs[e].settings,n[e]):"undefined"!=typeof this.libs[e].defaults&&t.extend(!0,this.libs[e].defaults,n[e]),this.libs[e].init.apply(this.libs[e],[this.scope,n[e]])):(n=n instanceof Array?n:new Array(n),this.libs[e].init.apply(this.libs[e],n))):function(){}},patch:function(t){t.scope=this.scope,t.namespace=this.global.namespace,t.rtl=this.rtl,t.data_options=this.utils.data_options,t.attr_name=o,t.add_namespace=l,t.bindings=c,t.S=this.utils.S},inherit:function(t,e){for(var n=e.split(" "),i=n.length;i--;)this.utils.hasOwnProperty(n[i])&&(t[n[i]]=this.utils[n[i]])},set_namespace:function(){var e=this.global.namespace===i?t(".foundation-data-attribute-namespace").css("font-family"):this.global.namespace;this.global.namespace=e===i||/false/i.test(e)?"":e},libs:{},utils:{S:r,throttle:function(t,e){var n=null;return function(){var i=this,s=arguments;null==n&&(n=setTimeout(function(){t.apply(i,s),n=null},e))}},debounce:function(t,e,n){var i,s;return function(){var a=this,r=arguments,o=function(){i=null,n||(s=t.apply(a,r))},l=n&&!i;return clearTimeout(i),i=setTimeout(o,e),l&&(s=t.apply(a,r)),s}},data_options:function(e,n){function i(t){return!isNaN(t-0)&&null!==t&&""!==t&&t!==!1&&t!==!0}function s(e){return"string"==typeof e?t.trim(e):e}n=n||"options";var a,r,o,l={},c=function(t){var e=Foundation.global.namespace;return t.data(e.length>0?e+"-"+n:n)},d=c(e);if("object"==typeof d)return d;for(o=(d||":").split(";"),a=o.length;a--;)r=o[a].split(":"),r=[r[0],r.slice(1).join(":")],/true/i.test(r[1])&&(r[1]=!0),/false/i.test(r[1])&&(r[1]=!1),i(r[1])&&(r[1]=-1===r[1].indexOf(".")?parseInt(r[1],10):parseFloat(r[1])),2===r.length&&r[0].length>0&&(l[s(r[0])]=s(r[1]));return l},register_media:function(e,n){Foundation.media_queries[e]===i&&(t("head").append('<meta class="'+n+'"/>'),Foundation.media_queries[e]=s(t("."+n).css("font-family")))},add_custom_rule:function(t,e){if(e===i&&Foundation.stylesheet)Foundation.stylesheet.insertRule(t,Foundation.stylesheet.cssRules.length);else{var n=Foundation.media_queries[e];n!==i&&Foundation.stylesheet.insertRule("@media "+Foundation.media_queries[e]+"{ "+t+" }",Foundation.stylesheet.cssRules.length)}},image_loaded:function(t,e){function n(t){for(var e=t.length,n=e-1;n>=0;n--)if(t.attr("height")===i)return!1;return!0}var s=this,a=t.length;(0===a||n(t))&&e(t),t.each(function(){d(s.S(this),function(){a-=1,0===a&&e(t)})})},random_str:function(){return this.fidx||(this.fidx=0),this.prefix=this.prefix||[this.name||"F",(+new Date).toString(36)].join("-"),this.prefix+(this.fidx++).toString(36)},match:function(t){return e.matchMedia(t).matches},is_small_up:function(){return this.match(Foundation.media_queries.small)},is_medium_up:function(){return this.match(Foundation.media_queries.medium)},is_large_up:function(){return this.match(Foundation.media_queries.large)},is_xlarge_up:function(){return this.match(Foundation.media_queries.xlarge)},is_xxlarge_up:function(){return this.match(Foundation.media_queries.xxlarge)},is_small_only:function(){return!(this.is_medium_up()||this.is_large_up()||this.is_xlarge_up()||this.is_xxlarge_up())},is_medium_only:function(){return this.is_medium_up()&&!this.is_large_up()&&!this.is_xlarge_up()&&!this.is_xxlarge_up()},is_large_only:function(){return this.is_medium_up()&&this.is_large_up()&&!this.is_xlarge_up()&&!this.is_xxlarge_up()},is_xlarge_only:function(){return this.is_medium_up()&&this.is_large_up()&&this.is_xlarge_up()&&!this.is_xxlarge_up()},is_xxlarge_only:function(){return this.is_medium_up()&&this.is_large_up()&&this.is_xlarge_up()&&this.is_xxlarge_up()}}},t.fn.foundation=function(){var t=Array.prototype.slice.call(arguments,0);return this.each(function(){return Foundation.init.apply(Foundation,[this].concat(t)),this})}}(jQuery,window,window.document),function(t,e,n){"use strict";Foundation.libs.abide={name:"abide",version:"5.5.2",settings:{live_validate:!0,validate_on_blur:!0,focus_on_invalid:!0,error_labels:!0,error_class:"error",timeout:1e3,patterns:{alpha:/^[a-zA-Z]+$/,alpha_numeric:/^[a-zA-Z0-9]+$/,integer:/^[-+]?\d+$/,number:/^[-+]?\d*(?:[\.\,]\d+)?$/,card:/^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9][0-9])[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11})$/,cvv:/^([0-9]){3,4}$/,email:/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/,url:/^(https?|ftp|file|ssh):\/\/([-;:&=\+\$,\w]+@{1})?([-A-Za-z0-9\.]+)+:?(\d+)?((\/[-\+~%\/\.\w]+)?\??([-\+=&;%@\.\w]+)?#?([\w]+)?)?/,domain:/^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,8}$/,datetime:/^([0-2][0-9]{3})\-([0-1][0-9])\-([0-3][0-9])T([0-5][0-9])\:([0-5][0-9])\:([0-5][0-9])(Z|([\-\+]([0-1][0-9])\:00))$/,date:/(?:19|20)[0-9]{2}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-9])|(?:(?!02)(?:0[1-9]|1[0-2])-(?:30))|(?:(?:0[13578]|1[02])-31))$/,time:/^(0[0-9]|1[0-9]|2[0-3])(:[0-5][0-9]){2}$/,dateISO:/^\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2}$/,month_day_year:/^(0[1-9]|1[012])[- \/.](0[1-9]|[12][0-9]|3[01])[- \/.]\d{4}$/,day_month_year:/^(0[1-9]|[12][0-9]|3[01])[- \/.](0[1-9]|1[012])[- \/.]\d{4}$/,color:/^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/},validators:{equalTo:function(t){var e=n.getElementById(t.getAttribute(this.add_namespace("data-equalto"))).value,i=t.value,s=e===i;return s}}},timer:null,init:function(t,e,n){this.bindings(e,n)},events:function(e){function n(t,e){clearTimeout(i.timer),i.timer=setTimeout(function(){i.validate([t],e)}.bind(t),a.timeout)}var i=this,s=i.S(e).attr("novalidate","novalidate"),a=s.data(this.attr_name(!0)+"-init")||{};this.invalid_attr=this.add_namespace("data-invalid"),s.off(".abide").on("submit.fndtn.abide",function(t){var e=/ajax/i.test(i.S(this).attr(i.attr_name()));return i.validate(i.S(this).find("input, textarea, select").not(":hidden, [data-abide-ignore]").get(),t,e)}).on("validate.fndtn.abide",function(t){"manual"===a.validate_on&&i.validate([t.target],t)}).on("reset",function(e){return i.reset(t(this),e)}).find("input, textarea, select").not(":hidden, [data-abide-ignore]").off(".abide").on("blur.fndtn.abide change.fndtn.abide",function(t){a.validate_on_blur&&a.validate_on_blur===!0&&n(this,t),"change"===a.validate_on&&n(this,t)}).on("keydown.fndtn.abide",function(t){a.live_validate&&a.live_validate===!0&&9!=t.which&&n(this,t),"tab"===a.validate_on&&9===t.which?n(this,t):"change"===a.validate_on&&n(this,t)}).on("focus",function(e){navigator.userAgent.match(/iPad|iPhone|Android|BlackBerry|Windows Phone|webOS/i)&&t("html, body").animate({scrollTop:t(e.target).offset().top},100)})},reset:function(e){var n=this;e.removeAttr(n.invalid_attr),t("["+n.invalid_attr+"]",e).removeAttr(n.invalid_attr),t("."+n.settings.error_class,e).not("small").removeClass(n.settings.error_class),t(":input",e).not(":button, :submit, :reset, :hidden, [data-abide-ignore]").val("").removeAttr(n.invalid_attr)},validate:function(t,e,n){for(var i=this.parse_patterns(t),s=i.length,a=this.S(t[0]).closest("form"),r=/submit/.test(e.type),o=0;s>o;o++)if(!i[o]&&(r||n))return this.settings.focus_on_invalid&&t[o].focus(),a.trigger("invalid.fndtn.abide"),this.S(t[o]).closest("form").attr(this.invalid_attr,""),!1;return(r||n)&&a.trigger("valid.fndtn.abide"),a.removeAttr(this.invalid_attr),n?!1:!0},parse_patterns:function(t){for(var e=t.length,n=[];e--;)n.push(this.pattern(t[e]));return this.check_validation_and_apply_styles(n)},pattern:function(t){var e=t.getAttribute("type"),n="string"==typeof t.getAttribute("required"),i=t.getAttribute("pattern")||"";return this.settings.patterns.hasOwnProperty(i)&&i.length>0?[t,this.settings.patterns[i],n]:i.length>0?[t,new RegExp(i),n]:this.settings.patterns.hasOwnProperty(e)?[t,this.settings.patterns[e],n]:(i=/.*/,[t,i,n])},check_validation_and_apply_styles:function(e){var n=e.length,i=[],s=this.S(e[0][0]).closest("[data-"+this.attr_name(!0)+"]");for(s.data(this.attr_name(!0)+"-init")||{};n--;){var a,r,o=e[n][0],l=e[n][2],c=o.value.trim(),d=this.S(o).parent(),u=o.getAttribute(this.add_namespace("data-abide-validator")),h="radio"===o.type,f="checkbox"===o.type,p=this.S('label[for="'+o.getAttribute("id")+'"]'),g=l?o.value.length>0:!0,m=[];if(o.getAttribute(this.add_namespace("data-equalto"))&&(u="equalTo"),a=d.is("label")?d.parent():d,h&&l)m.push(this.valid_radio(o,l));else if(f&&l)m.push(this.valid_checkbox(o,l));else if(u){for(var v=u.split(" "),_=!0,y=!0,b=0;b<v.length;b++)r=this.settings.validators[v[b]].apply(this,[o,l,a]),m.push(r),y=r&&_,_=r;y?(this.S(o).removeAttr(this.invalid_attr),a.removeClass("error"),p.length>0&&this.settings.error_labels&&p.removeClass(this.settings.error_class).removeAttr("role"),t(o).triggerHandler("valid")):(this.S(o).attr(this.invalid_attr,""),a.addClass("error"),p.length>0&&this.settings.error_labels&&p.addClass(this.settings.error_class).attr("role","alert"),t(o).triggerHandler("invalid"))}else if(m.push(e[n][1].test(c)&&g||!l&&o.value.length<1||t(o).attr("disabled")?!0:!1),m=[m.every(function(t){return t})],m[0])this.S(o).removeAttr(this.invalid_attr),o.setAttribute("aria-invalid","false"),o.removeAttribute("aria-describedby"),a.removeClass(this.settings.error_class),p.length>0&&this.settings.error_labels&&p.removeClass(this.settings.error_class).removeAttr("role"),t(o).triggerHandler("valid");else{this.S(o).attr(this.invalid_attr,""),o.setAttribute("aria-invalid","true");var x=a.find("small."+this.settings.error_class,"span."+this.settings.error_class),w=x.length>0?x[0].id:"";w.length>0&&o.setAttribute("aria-describedby",w),a.addClass(this.settings.error_class),p.length>0&&this.settings.error_labels&&p.addClass(this.settings.error_class).attr("role","alert"),t(o).triggerHandler("invalid")}i=i.concat(m)}return i},valid_checkbox:function(e,n){var e=this.S(e),i=e.is(":checked")||!n||e.get(0).getAttribute("disabled");return i?(e.removeAttr(this.invalid_attr).parent().removeClass(this.settings.error_class),t(e).triggerHandler("valid")):(e.attr(this.invalid_attr,"").parent().addClass(this.settings.error_class),t(e).triggerHandler("invalid")),i},valid_radio:function(e){for(var n=e.getAttribute("name"),i=this.S(e).closest("[data-"+this.attr_name(!0)+"]").find("[name='"+n+"']"),s=i.length,a=!1,r=!1,o=0;s>o;o++)i[o].getAttribute("disabled")?(r=!0,a=!0):i[o].checked?a=!0:r&&(a=!1);for(var o=0;s>o;o++)a?(this.S(i[o]).removeAttr(this.invalid_attr).parent().removeClass(this.settings.error_class),t(i[o]).triggerHandler("valid")):(this.S(i[o]).attr(this.invalid_attr,"").parent().addClass(this.settings.error_class),t(i[o]).triggerHandler("invalid"));return a},valid_equal:function(t,e,i){var s=n.getElementById(t.getAttribute(this.add_namespace("data-equalto"))).value,a=t.value,r=s===a;return r?(this.S(t).removeAttr(this.invalid_attr),i.removeClass(this.settings.error_class),label.length>0&&settings.error_labels&&label.removeClass(this.settings.error_class)):(this.S(t).attr(this.invalid_attr,""),i.addClass(this.settings.error_class),label.length>0&&settings.error_labels&&label.addClass(this.settings.error_class)),r},valid_oneof:function(t,e,n,i){var t=this.S(t),s=this.S("["+this.add_namespace("data-oneof")+"]"),a=s.filter(":checked").length>0;if(a?t.removeAttr(this.invalid_attr).parent().removeClass(this.settings.error_class):t.attr(this.invalid_attr,"").parent().addClass(this.settings.error_class),!i){var r=this;s.each(function(){r.valid_oneof.call(r,this,null,null,!0)})}return a},reflow:function(){var t=this,e=t.S("["+this.attr_name()+"]").attr("novalidate","novalidate");t.S(e).each(function(e,n){t.events(n)})}}}(jQuery,window,window.document),function(t){"use strict";Foundation.libs.accordion={name:"accordion",version:"5.5.2",settings:{content_class:"content",active_class:"active",multi_expand:!1,toggleable:!0,callback:function(){}},init:function(t,e,n){this.bindings(e,n)},events:function(e){var n=this,i=this.S;n.create(this.S(e)),i(this.scope).off(".fndtn.accordion").on("click.fndtn.accordion","["+this.attr_name()+"] > dd > a, ["+this.attr_name()+"] > li > a",function(e){var s=i(this).closest("["+n.attr_name()+"]"),a=n.attr_name()+"="+s.attr(n.attr_name()),r=s.data(n.attr_name(!0)+"-init")||n.settings,o=i("#"+this.href.split("#")[1]),l=t("> dd, > li",s),c=l.children("."+r.content_class),d=c.filter("."+r.active_class);return e.preventDefault(),s.attr(n.attr_name())&&(c=c.add("["+a+"] dd > ."+r.content_class+", ["+a+"] li > ."+r.content_class),l=l.add("["+a+"] dd, ["+a+"] li")),r.toggleable&&o.is(d)?(o.parent("dd, li").toggleClass(r.active_class,!1),o.toggleClass(r.active_class,!1),i(this).attr("aria-expanded",function(t,e){return"true"===e?"false":"true"}),r.callback(o),o.triggerHandler("toggled",[s]),void s.triggerHandler("toggled",[o])):(r.multi_expand||(c.removeClass(r.active_class),l.removeClass(r.active_class),l.children("a").attr("aria-expanded","false")),o.addClass(r.active_class).parent().addClass(r.active_class),r.callback(o),o.triggerHandler("toggled",[s]),s.triggerHandler("toggled",[o]),void i(this).attr("aria-expanded","true"))})},create:function(e){var n=this,i=e,s=t("> .accordion-navigation",i),a=i.data(n.attr_name(!0)+"-init")||n.settings;s.children("a").attr("aria-expanded","false"),s.has("."+a.content_class+"."+a.active_class).children("a").attr("aria-expanded","true"),a.multi_expand&&e.attr("aria-multiselectable","true")},off:function(){},reflow:function(){}}}(jQuery,window,window.document),function(t){"use strict";Foundation.libs.alert={name:"alert",version:"5.5.2",settings:{callback:function(){}},init:function(t,e,n){this.bindings(e,n)},events:function(){var e=this,n=this.S;t(this.scope).off(".alert").on("click.fndtn.alert","["+this.attr_name()+"] .close",function(t){var i=n(this).closest("["+e.attr_name()+"]"),s=i.data(e.attr_name(!0)+"-init")||e.settings;t.preventDefault(),Modernizr.csstransitions?(i.addClass("alert-close"),i.on("transitionend webkitTransitionEnd oTransitionEnd",function(){n(this).trigger("close.fndtn.alert").remove(),s.callback()})):i.fadeOut(300,function(){n(this).trigger("close.fndtn.alert").remove(),s.callback()})})},reflow:function(){}}}(jQuery,window,window.document),function(t,e,n,i){"use strict";Foundation.libs.clearing={name:"clearing",version:"5.5.2",settings:{templates:{viewing:'<a href="#" class="clearing-close">&times;</a><div class="visible-img" style="display: none"><div class="clearing-touch-label"></div><img src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D" alt="" /><p class="clearing-caption"></p><a href="#" class="clearing-main-prev"><span></span></a><a href="#" class="clearing-main-next"><span></span></a></div><img class="clearing-preload-next" style="display: none" src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D" alt="" /><img class="clearing-preload-prev" style="display: none" src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs%3D" alt="" />'},close_selectors:".clearing-close, div.clearing-blackout",open_selectors:"",skip_selector:"",touch_label:"",init:!1,locked:!1},init:function(t,e,n){var i=this;Foundation.inherit(this,"throttle image_loaded"),this.bindings(e,n),i.S(this.scope).is("["+this.attr_name()+"]")?this.assemble(i.S("li",this.scope)):i.S("["+this.attr_name()+"]",this.scope).each(function(){i.assemble(i.S("li",this))})},events:function(i){var s=this,a=s.S,r=t(".scroll-container");r.length>0&&(this.scope=r),a(this.scope).off(".clearing").on("click.fndtn.clearing","ul["+this.attr_name()+"] li "+this.settings.open_selectors,function(t,e,n){var e=e||a(this),n=n||e,i=e.next("li"),r=e.closest("["+s.attr_name()+"]").data(s.attr_name(!0)+"-init"),o=a(t.target);t.preventDefault(),r||(s.init(),r=e.closest("["+s.attr_name()+"]").data(s.attr_name(!0)+"-init")),n.hasClass("visible")&&e[0]===n[0]&&i.length>0&&s.is_open(e)&&(n=i,o=a("img",n)),s.open(o,e,n),s.update_paddles(n)}).on("click.fndtn.clearing",".clearing-main-next",function(t){s.nav(t,"next")}).on("click.fndtn.clearing",".clearing-main-prev",function(t){s.nav(t,"prev")}).on("click.fndtn.clearing",this.settings.close_selectors,function(t){Foundation.libs.clearing.close(t,this)}),t(n).on("keydown.fndtn.clearing",function(t){s.keydown(t)}),a(e).off(".clearing").on("resize.fndtn.clearing",function(){s.resize()}),this.swipe_events(i)},swipe_events:function(){var t=this,e=t.S;e(this.scope).on("touchstart.fndtn.clearing",".visible-img",function(t){t.touches||(t=t.originalEvent);var n={start_page_x:t.touches[0].pageX,start_page_y:t.touches[0].pageY,start_time:(new Date).getTime(),delta_x:0,is_scrolling:i};e(this).data("swipe-transition",n),t.stopPropagation()}).on("touchmove.fndtn.clearing",".visible-img",function(n){if(n.touches||(n=n.originalEvent),!(n.touches.length>1||n.scale&&1!==n.scale)){var i=e(this).data("swipe-transition");if("undefined"==typeof i&&(i={}),i.delta_x=n.touches[0].pageX-i.start_page_x,Foundation.rtl&&(i.delta_x=-i.delta_x),"undefined"==typeof i.is_scrolling&&(i.is_scrolling=!!(i.is_scrolling||Math.abs(i.delta_x)<Math.abs(n.touches[0].pageY-i.start_page_y))),!i.is_scrolling&&!i.active){n.preventDefault();var s=i.delta_x<0?"next":"prev";i.active=!0,t.nav(n,s)}}}).on("touchend.fndtn.clearing",".visible-img",function(t){e(this).data("swipe-transition",{}),t.stopPropagation()})},assemble:function(e){var n=e.parent();if(!n.parent().hasClass("carousel")){n.after('<div id="foundationClearingHolder"></div>');var i=n.detach(),s="";if(null!=i[0]){s=i[0].outerHTML;var a=this.S("#foundationClearingHolder"),r=n.data(this.attr_name(!0)+"-init"),o={grid:'<div class="carousel">'+s+"</div>",viewing:r.templates.viewing},l='<div class="clearing-assembled"><div>'+o.viewing+o.grid+"</div></div>",c=this.settings.touch_label;Modernizr.touch&&(l=t(l).find(".clearing-touch-label").html(c).end()),a.after(l).remove()}}},open:function(e,i,s){function a(){setTimeout(function(){this.image_loaded(h,function(){1!==h.outerWidth()||p?r.call(this,h):a.call(this)}.bind(this))}.bind(this),100)}function r(e){var n=t(e);n.css("visibility","visible"),n.trigger("imageVisible"),l.css("overflow","hidden"),c.addClass("clearing-blackout"),d.addClass("clearing-container"),u.show(),this.fix_height(s).caption(o.S(".clearing-caption",u),o.S("img",s)).center_and_label(e,f).shift(i,s,function(){s.closest("li").siblings().removeClass("visible"),s.closest("li").addClass("visible")}),u.trigger("opened.fndtn.clearing")}var o=this,l=t(n.body),c=s.closest(".clearing-assembled"),d=o.S("div",c).first(),u=o.S(".visible-img",d),h=o.S("img",u).not(e),f=o.S(".clearing-touch-label",d),p=!1,g={};t("body").on("touchmove",function(t){t.preventDefault()}),h.error(function(){p=!0}),this.locked()||(u.trigger("open.fndtn.clearing"),g=this.load(e),g.interchange?h.attr("data-interchange",g.interchange).foundation("interchange","reflow"):h.attr("src",g.src).attr("data-interchange",""),h.css("visibility","hidden"),a.call(this))},close:function(e,i){e.preventDefault();var s,a,r=function(t){return/blackout/.test(t.selector)?t:t.closest(".clearing-blackout")}(t(i)),o=t(n.body);return i===e.target&&r&&(o.css("overflow",""),s=t("div",r).first(),a=t(".visible-img",s),a.trigger("close.fndtn.clearing"),this.settings.prev_index=0,t("ul["+this.attr_name()+"]",r).attr("style","").closest(".clearing-blackout").removeClass("clearing-blackout"),s.removeClass("clearing-container"),a.hide(),a.trigger("closed.fndtn.clearing")),t("body").off("touchmove"),!1},is_open:function(t){return t.parent().prop("style").length>0},keydown:function(e){var n=t(".clearing-blackout ul["+this.attr_name()+"]"),i=this.rtl?37:39,s=this.rtl?39:37,a=27;e.which===i&&this.go(n,"next"),e.which===s&&this.go(n,"prev"),e.which===a&&this.S("a.clearing-close").trigger("click.fndtn.clearing")},nav:function(e,n){var i=t("ul["+this.attr_name()+"]",".clearing-blackout");e.preventDefault(),this.go(i,n)},resize:function(){var e=t("img",".clearing-blackout .visible-img"),n=t(".clearing-touch-label",".clearing-blackout");e.length&&(this.center_and_label(e,n),e.trigger("resized.fndtn.clearing"))},fix_height:function(t){var e=t.parent().children(),n=this;return e.each(function(){var t=n.S(this),e=t.find("img");t.height()>e.outerHeight()&&t.addClass("fix-height")}).closest("ul").width(100*e.length+"%"),this},update_paddles:function(t){t=t.closest("li");var e=t.closest(".carousel").siblings(".visible-img");t.next().length>0?this.S(".clearing-main-next",e).removeClass("disabled"):this.S(".clearing-main-next",e).addClass("disabled"),t.prev().length>0?this.S(".clearing-main-prev",e).removeClass("disabled"):this.S(".clearing-main-prev",e).addClass("disabled")},center_and_label:function(t,e){return e.css(!this.rtl&&e.length>0?{marginLeft:-(e.outerWidth()/2),marginTop:-(t.outerHeight()/2)-e.outerHeight()-10}:{marginRight:-(e.outerWidth()/2),marginTop:-(t.outerHeight()/2)-e.outerHeight()-10,left:"auto",right:"50%"}),this},load:function(t){var e,n,i;return"A"===t[0].nodeName?(e=t.attr("href"),n=t.data("clearing-interchange")):(i=t.closest("a"),e=i.attr("href"),n=i.data("clearing-interchange")),this.preload(t),{src:e?e:t.attr("src"),interchange:e?n:t.data("clearing-interchange")}},preload:function(t){this.img(t.closest("li").next(),"next").img(t.closest("li").prev(),"prev")},img:function(e,n){if(e.length){var i,s,a,r=t(".clearing-preload-"+n),o=this.S("a",e);o.length?(i=o.attr("href"),s=o.data("clearing-interchange")):(a=this.S("img",e),i=a.attr("src"),s=a.data("clearing-interchange")),s?r.attr("data-interchange",s):(r.attr("src",i),r.attr("data-interchange",""))}return this},caption:function(t,e){var n=e.attr("data-caption");return n?t.html(n).show():t.text("").hide(),this},go:function(t,e){var n=this.S(".visible",t),i=n[e]();this.settings.skip_selector&&0!=i.find(this.settings.skip_selector).length&&(i=i[e]()),i.length&&this.S("img",i).trigger("click.fndtn.clearing",[n,i]).trigger("change.fndtn.clearing")},shift:function(t,e,n){var i,s=e.parent(),a=this.settings.prev_index||e.index(),r=this.direction(s,t,e),o=this.rtl?"right":"left",l=parseInt(s.css("left"),10),c=e.outerWidth(),d={};e.index()===a||/skip/.test(r)?/skip/.test(r)&&(i=e.index()-this.settings.up_count,this.lock(),i>0?(d[o]=-(i*c),s.animate(d,300,this.unlock())):(d[o]=0,s.animate(d,300,this.unlock()))):/left/.test(r)?(this.lock(),d[o]=l+c,s.animate(d,300,this.unlock())):/right/.test(r)&&(this.lock(),d[o]=l-c,s.animate(d,300,this.unlock())),n()},direction:function(t,e,n){var i,s=this.S("li",t),a=s.outerWidth()+s.outerWidth()/4,r=Math.floor(this.S(".clearing-container").outerWidth()/a)-1,o=s.index(n);return this.settings.up_count=r,i=this.adjacent(this.settings.prev_index,o)?o>r&&o>this.settings.prev_index?"right":o>r-1&&o<=this.settings.prev_index?"left":!1:"skip",this.settings.prev_index=o,i},adjacent:function(t,e){for(var n=e+1;n>=e-1;n--)if(n===t)return!0;return!1},lock:function(){this.settings.locked=!0},unlock:function(){this.settings.locked=!1},locked:function(){return this.settings.locked},off:function(){this.S(this.scope).off(".fndtn.clearing"),this.S(e).off(".fndtn.clearing")},reflow:function(){this.init()}}}(jQuery,window,window.document),function(t,e,n){"use strict";Foundation.libs.dropdown={name:"dropdown",version:"5.5.2",settings:{active_class:"open",disabled_class:"disabled",mega_class:"mega",align:"bottom",is_hover:!1,hover_timeout:150,opened:function(){},closed:function(){}},init:function(e,n,i){Foundation.inherit(this,"throttle"),t.extend(!0,this.settings,n,i),this.bindings(n,i)},events:function(){var i=this,s=i.S;s(this.scope).off(".dropdown").on("click.fndtn.dropdown","["+this.attr_name()+"]",function(e){var n=s(this).data(i.attr_name(!0)+"-init")||i.settings;(!n.is_hover||Modernizr.touch)&&(e.preventDefault(),s(this).parent("[data-reveal-id]").length&&e.stopPropagation(),i.toggle(t(this)))}).on("mouseenter.fndtn.dropdown","["+this.attr_name()+"], ["+this.attr_name()+"-content]",function(t){var e,n,a=s(this);clearTimeout(i.timeout),a.data(i.data_attr())?(e=s("#"+a.data(i.data_attr())),n=a):(e=a,n=s("["+i.attr_name()+'="'+e.attr("id")+'"]'));var r=n.data(i.attr_name(!0)+"-init")||i.settings;s(t.currentTarget).data(i.data_attr())&&r.is_hover&&i.closeall.call(i),r.is_hover&&i.open.apply(i,[e,n])}).on("mouseleave.fndtn.dropdown","["+this.attr_name()+"], ["+this.attr_name()+"-content]",function(){var t,e=s(this);if(e.data(i.data_attr()))t=e.data(i.data_attr(!0)+"-init")||i.settings;else var n=s("["+i.attr_name()+'="'+s(this).attr("id")+'"]'),t=n.data(i.attr_name(!0)+"-init")||i.settings;i.timeout=setTimeout(function(){e.data(i.data_attr())?t.is_hover&&i.close.call(i,s("#"+e.data(i.data_attr()))):t.is_hover&&i.close.call(i,e)}.bind(this),t.hover_timeout)}).on("click.fndtn.dropdown",function(e){var a=s(e.target).closest("["+i.attr_name()+"-content]"),r=a.find("a");return r.length>0&&"false"!==a.attr("aria-autoclose")&&i.close.call(i,s("["+i.attr_name()+"-content]")),e.target!==n&&!t.contains(n.documentElement,e.target)||s(e.target).closest("["+i.attr_name()+"]").length>0?void 0:!s(e.target).data("revealId")&&a.length>0&&(s(e.target).is("["+i.attr_name()+"-content]")||t.contains(a.first()[0],e.target))?void e.stopPropagation():void i.close.call(i,s("["+i.attr_name()+"-content]"))}).on("opened.fndtn.dropdown","["+i.attr_name()+"-content]",function(){i.settings.opened.call(this)}).on("closed.fndtn.dropdown","["+i.attr_name()+"-content]",function(){i.settings.closed.call(this)}),s(e).off(".dropdown").on("resize.fndtn.dropdown",i.throttle(function(){i.resize.call(i)},50)),this.resize()},close:function(e){var n=this;e.each(function(i){var s=t("["+n.attr_name()+"="+e[i].id+"]")||t("aria-controls="+e[i].id+"]");s.attr("aria-expanded","false"),n.S(this).hasClass(n.settings.active_class)&&(n.S(this).css(Foundation.rtl?"right":"left","-99999px").attr("aria-hidden","true").removeClass(n.settings.active_class).prev("["+n.attr_name()+"]").removeClass(n.settings.active_class).removeData("target"),n.S(this).trigger("closed.fndtn.dropdown",[e]))}),e.removeClass("f-open-"+this.attr_name(!0))},closeall:function(){var e=this;t.each(e.S(".f-open-"+this.attr_name(!0)),function(){e.close.call(e,e.S(this))})},open:function(t,e){this.css(t.addClass(this.settings.active_class),e),t.prev("["+this.attr_name()+"]").addClass(this.settings.active_class),t.data("target",e.get(0)).trigger("opened.fndtn.dropdown",[t,e]),t.attr("aria-hidden","false"),e.attr("aria-expanded","true"),t.focus(),t.addClass("f-open-"+this.attr_name(!0))},data_attr:function(){return this.namespace.length>0?this.namespace+"-"+this.name:this.name},toggle:function(t){if(!t.hasClass(this.settings.disabled_class)){var e=this.S("#"+t.data(this.data_attr()));0!==e.length&&(this.close.call(this,this.S("["+this.attr_name()+"-content]").not(e)),e.hasClass(this.settings.active_class)?(this.close.call(this,e),e.data("target")!==t.get(0)&&this.open.call(this,e,t)):this.open.call(this,e,t))}},resize:function(){var e=this.S("["+this.attr_name()+"-content].open"),n=t(e.data("target"));e.length&&n.length&&this.css(e,n)},css:function(t,e){var n=Math.max((e.width()-t.width())/2,8),i=e.data(this.attr_name(!0)+"-init")||this.settings,s=t.parent().css("overflow-y")||t.parent().css("overflow");if(this.clear_idx(),this.small()){var a=this.dirs.bottom.call(t,e,i);t.attr("style","").removeClass("drop-left drop-right drop-top").css({position:"absolute",width:"95%","max-width":"none",top:a.top}),t.css(Foundation.rtl?"right":"left",n)}else if("visible"!==s){var r=e[0].offsetTop+e[0].offsetHeight;t.attr("style","").css({position:"absolute",top:r}),t.css(Foundation.rtl?"right":"left",n)}else this.style(t,e,i);return t},style:function(e,n,i){var s=t.extend({position:"absolute"},this.dirs[i.align].call(e,n,i));e.attr("style","").css(s)},dirs:{_base:function(t){var i=this.offsetParent(),s=i.offset(),a=t.offset();a.top-=s.top,a.left-=s.left,a.missRight=!1,a.missTop=!1,a.missLeft=!1,a.leftRightFlag=!1;var r;r=n.getElementsByClassName("row")[0]?n.getElementsByClassName("row")[0].clientWidth:e.innerWidth;var o=(e.innerWidth-r)/2,l=r;return this.hasClass("mega")||(t.offset().top<=this.outerHeight()&&(a.missTop=!0,l=e.innerWidth-o,a.leftRightFlag=!0),t.offset().left+this.outerWidth()>t.offset().left+o&&t.offset().left-o>this.outerWidth()&&(a.missRight=!0,a.missLeft=!1),t.offset().left-this.outerWidth()<=0&&(a.missLeft=!0,a.missRight=!1)),a},top:function(t,e){var n=Foundation.libs.dropdown,i=n.dirs._base.call(this,t);return this.addClass("drop-top"),1==i.missTop&&(i.top=i.top+t.outerHeight()+this.outerHeight(),this.removeClass("drop-top")),1==i.missRight&&(i.left=i.left-this.outerWidth()+t.outerWidth()),(t.outerWidth()<this.outerWidth()||n.small()||this.hasClass(e.mega_menu))&&n.adjust_pip(this,t,e,i),Foundation.rtl?{left:i.left-this.outerWidth()+t.outerWidth(),top:i.top-this.outerHeight()}:{left:i.left,top:i.top-this.outerHeight()}},bottom:function(t,e){var n=Foundation.libs.dropdown,i=n.dirs._base.call(this,t);return 1==i.missRight&&(i.left=i.left-this.outerWidth()+t.outerWidth()),(t.outerWidth()<this.outerWidth()||n.small()||this.hasClass(e.mega_menu))&&n.adjust_pip(this,t,e,i),n.rtl?{left:i.left-this.outerWidth()+t.outerWidth(),top:i.top+t.outerHeight()}:{left:i.left,top:i.top+t.outerHeight()}},left:function(t){var e=Foundation.libs.dropdown.dirs._base.call(this,t);return this.addClass("drop-left"),1==e.missLeft&&(e.left=e.left+this.outerWidth(),e.top=e.top+t.outerHeight(),this.removeClass("drop-left")),{left:e.left-this.outerWidth(),top:e.top}},right:function(t,e){var n=Foundation.libs.dropdown.dirs._base.call(this,t);this.addClass("drop-right"),1==n.missRight?(n.left=n.left-this.outerWidth(),n.top=n.top+t.outerHeight(),this.removeClass("drop-right")):n.triggeredRight=!0;var i=Foundation.libs.dropdown;return(t.outerWidth()<this.outerWidth()||i.small()||this.hasClass(e.mega_menu))&&i.adjust_pip(this,t,e,n),{left:n.left+t.outerWidth(),top:n.top}}},adjust_pip:function(t,e,n,i){var s=Foundation.stylesheet,a=8;t.hasClass(n.mega_class)?a=i.left+e.outerWidth()/2-8:this.small()&&(a+=i.left-8),this.rule_idx=s.cssRules.length;var r=".f-dropdown.open:before",o=".f-dropdown.open:after",l="left: "+a+"px;",c="left: "+(a-1)+"px;";1==i.missRight&&(a=t.outerWidth()-23,r=".f-dropdown.open:before",o=".f-dropdown.open:after",l="left: "+a+"px;",c="left: "+(a-1)+"px;"),1==i.triggeredRight&&(r=".f-dropdown.open:before",o=".f-dropdown.open:after",l="left:-12px;",c="left:-14px;"),s.insertRule?(s.insertRule([r,"{",l,"}"].join(" "),this.rule_idx),s.insertRule([o,"{",c,"}"].join(" "),this.rule_idx+1)):(s.addRule(r,l,this.rule_idx),s.addRule(o,c,this.rule_idx+1))
},clear_idx:function(){var t=Foundation.stylesheet;"undefined"!=typeof this.rule_idx&&(t.deleteRule(this.rule_idx),t.deleteRule(this.rule_idx),delete this.rule_idx)},small:function(){return matchMedia(Foundation.media_queries.small).matches&&!matchMedia(Foundation.media_queries.medium).matches},off:function(){this.S(this.scope).off(".fndtn.dropdown"),this.S("html, body").off(".fndtn.dropdown"),this.S(e).off(".fndtn.dropdown"),this.S("[data-dropdown-content]").off(".fndtn.dropdown")},reflow:function(){}}}(jQuery,window,window.document),function(t,e){"use strict";Foundation.libs.equalizer={name:"equalizer",version:"5.5.2",settings:{use_tallest:!0,before_height_change:t.noop,after_height_change:t.noop,equalize_on_stack:!1,act_on_hidden_el:!1},init:function(t,e,n){Foundation.inherit(this,"image_loaded"),this.bindings(e,n),this.reflow()},events:function(){this.S(e).off(".equalizer").on("resize.fndtn.equalizer",function(){this.reflow()}.bind(this))},equalize:function(e){var n,i,s=!1,a=e.data("equalizer"),r=e.data(this.attr_name(!0)+"-init")||this.settings;if(n=e.find(r.act_on_hidden_el?a?"["+this.attr_name()+'-watch="'+a+'"]':"["+this.attr_name()+"-watch]":a?"["+this.attr_name()+'-watch="'+a+'"]:visible':"["+this.attr_name()+"-watch]:visible"),0!==n.length&&(r.before_height_change(),e.trigger("before-height-change.fndth.equalizer"),n.height("inherit"),r.equalize_on_stack!==!1||(i=n.first().offset().top,n.each(function(){return t(this).offset().top!==i?(s=!0,!1):void 0}),!s))){var o=n.map(function(){return t(this).outerHeight(!1)}).get();if(r.use_tallest){var l=Math.max.apply(null,o);n.css("height",l)}else{var c=Math.min.apply(null,o);n.css("height",c)}r.after_height_change(),e.trigger("after-height-change.fndtn.equalizer")}},reflow:function(){var e=this;this.S("["+this.attr_name()+"]",this.scope).each(function(){var n=t(this),i=n.data("equalizer-mq"),s=!0;i&&(i="is_"+i.replace(/-/g,"_"),Foundation.utils.hasOwnProperty(i)&&(s=!1)),e.image_loaded(e.S("img",this),function(){if(s||Foundation.utils[i]())e.equalize(n);else{var t=n.find("["+e.attr_name()+"-watch]:visible");t.css("height","auto")}})})}}}(jQuery,window,window.document),function(t,e){"use strict";Foundation.libs.interchange={name:"interchange",version:"5.5.2",cache:{},images_loaded:!1,nodes_loaded:!1,settings:{load_attr:"interchange",named_queries:{"default":"only screen",small:Foundation.media_queries.small,"small-only":Foundation.media_queries["small-only"],medium:Foundation.media_queries.medium,"medium-only":Foundation.media_queries["medium-only"],large:Foundation.media_queries.large,"large-only":Foundation.media_queries["large-only"],xlarge:Foundation.media_queries.xlarge,"xlarge-only":Foundation.media_queries["xlarge-only"],xxlarge:Foundation.media_queries.xxlarge,landscape:"only screen and (orientation: landscape)",portrait:"only screen and (orientation: portrait)",retina:"only screen and (-webkit-min-device-pixel-ratio: 2),only screen and (min--moz-device-pixel-ratio: 2),only screen and (-o-min-device-pixel-ratio: 2/1),only screen and (min-device-pixel-ratio: 2),only screen and (min-resolution: 192dpi),only screen and (min-resolution: 2dppx)"},directives:{replace:function(e,n,i){if(null!==e&&/IMG/.test(e[0].nodeName)){var s=e[0].src;if(new RegExp(n,"i").test(s))return;return e.attr("src",n),i(e[0].src)}var a=e.data(this.data_attr+"-last-path"),r=this;if(a!=n)return/\.(gif|jpg|jpeg|tiff|png)([?#].*)?/i.test(n)?(t(e).css("background-image","url("+n+")"),e.data("interchange-last-path",n),i(n)):t.get(n,function(t){e.html(t),e.data(r.data_attr+"-last-path",n),i()})}}},init:function(e,n,i){Foundation.inherit(this,"throttle random_str"),this.data_attr=this.set_data_attr(),t.extend(!0,this.settings,n,i),this.bindings(n,i),this.reflow()},get_media_hash:function(){var t="";for(var e in this.settings.named_queries)t+=matchMedia(this.settings.named_queries[e]).matches.toString();return t},events:function(){var n,i=this;return t(e).off(".interchange").on("resize.fndtn.interchange",i.throttle(function(){var t=i.get_media_hash();t!==n&&i.resize(),n=t},50)),this},resize:function(){var e=this.cache;if(!this.images_loaded||!this.nodes_loaded)return void setTimeout(t.proxy(this.resize,this),50);for(var n in e)if(e.hasOwnProperty(n)){var i=this.results(n,e[n]);i&&this.settings.directives[i.scenario[1]].call(this,i.el,i.scenario[0],function(t){if(arguments[0]instanceof Array)var e=arguments[0];else var e=Array.prototype.slice.call(arguments,0);return function(){t.el.trigger(t.scenario[1],e)}}(i))}},results:function(t,e){var n=e.length;if(n>0)for(var i=this.S("["+this.add_namespace("data-uuid")+'="'+t+'"]');n--;){var s,a=e[n][2];if(s=matchMedia(this.settings.named_queries.hasOwnProperty(a)?this.settings.named_queries[a]:a),s.matches)return{el:i,scenario:e[n]}}return!1},load:function(t,e){return("undefined"==typeof this["cached_"+t]||e)&&this["update_"+t](),this["cached_"+t]},update_images:function(){var t=this.S("img["+this.data_attr+"]"),e=t.length,n=e,i=0,s=this.data_attr;for(this.cache={},this.cached_images=[],this.images_loaded=0===e;n--;){if(i++,t[n]){var a=t[n].getAttribute(s)||"";a.length>0&&this.cached_images.push(t[n])}i===e&&(this.images_loaded=!0,this.enhance("images"))}return this},update_nodes:function(){var t=this.S("["+this.data_attr+"]").not("img"),e=t.length,n=e,i=0,s=this.data_attr;for(this.cached_nodes=[],this.nodes_loaded=0===e;n--;){i++;var a=t[n].getAttribute(s)||"";a.length>0&&this.cached_nodes.push(t[n]),i===e&&(this.nodes_loaded=!0,this.enhance("nodes"))}return this},enhance:function(n){for(var i=this["cached_"+n].length;i--;)this.object(t(this["cached_"+n][i]));return t(e).trigger("resize.fndtn.interchange")},convert_directive:function(t){var e=this.trim(t);return e.length>0?e:"replace"},parse_scenario:function(t){var e=t[0].match(/(.+),\s*(\w+)\s*$/),n=t[1].match(/(.*)\)/);if(e)var i=e[1],s=e[2];else var a=t[0].split(/,\s*$/),i=a[0],s="";return[this.trim(i),this.convert_directive(s),this.trim(n[1])]},object:function(t){var e=this.parse_data_attr(t),n=[],i=e.length;if(i>0)for(;i--;){var s=e[i].split(/,\s?\(/);if(s.length>1){var a=this.parse_scenario(s);n.push(a)}}return this.store(t,n)},store:function(t,e){var n=this.random_str(),i=t.data(this.add_namespace("uuid",!0));return this.cache[i]?this.cache[i]:(t.attr(this.add_namespace("data-uuid"),n),this.cache[n]=e)},trim:function(e){return"string"==typeof e?t.trim(e):e},set_data_attr:function(t){return t?this.namespace.length>0?this.namespace+"-"+this.settings.load_attr:this.settings.load_attr:this.namespace.length>0?"data-"+this.namespace+"-"+this.settings.load_attr:"data-"+this.settings.load_attr},parse_data_attr:function(t){for(var e=t.attr(this.attr_name()).split(/\[(.*?)\]/),n=e.length,i=[];n--;)e[n].replace(/[\W\d]+/,"").length>4&&i.push(e[n]);return i},reflow:function(){this.load("images",!0),this.load("nodes",!0)}}}(jQuery,window,window.document),function(t,e,n,i){"use strict";Foundation.libs.joyride={name:"joyride",version:"5.5.2",defaults:{expose:!1,modal:!0,keyboard:!0,tip_location:"bottom",nub_position:"auto",scroll_speed:1500,scroll_animation:"linear",timer:0,start_timer_on_click:!0,start_offset:0,next_button:!0,prev_button:!0,tip_animation:"fade",pause_after:[],exposed:[],tip_animation_fade_speed:300,cookie_monster:!1,cookie_name:"joyride",cookie_domain:!1,cookie_expires:365,tip_container:"body",abort_on_close:!0,tip_location_patterns:{top:["bottom"],bottom:[],left:["right","top","bottom"],right:["left","top","bottom"]},post_ride_callback:function(){},post_step_callback:function(){},pre_step_callback:function(){},pre_ride_callback:function(){},post_expose_callback:function(){},template:{link:'<a href="#close" class="joyride-close-tip">&times;</a>',timer:'<div class="joyride-timer-indicator-wrap"><span class="joyride-timer-indicator"></span></div>',tip:'<div class="joyride-tip-guide"><span class="joyride-nub"></span></div>',wrapper:'<div class="joyride-content-wrapper"></div>',button:'<a href="#" class="small button joyride-next-tip"></a>',prev_button:'<a href="#" class="small button joyride-prev-tip"></a>',modal:'<div class="joyride-modal-bg"></div>',expose:'<div class="joyride-expose-wrapper"></div>',expose_cover:'<div class="joyride-expose-cover"></div>'},expose_add_class:""},init:function(e,n,i){Foundation.inherit(this,"throttle random_str"),this.settings=this.settings||t.extend({},this.defaults,i||n),this.bindings(n,i)},go_next:function(){this.settings.$li.next().length<1?this.end():this.settings.timer>0?(clearTimeout(this.settings.automate),this.hide(),this.show(),this.startTimer()):(this.hide(),this.show())},go_prev:function(){this.settings.$li.prev().length<1||(this.settings.timer>0?(clearTimeout(this.settings.automate),this.hide(),this.show(null,!0),this.startTimer()):(this.hide(),this.show(null,!0)))},events:function(){var n=this;t(this.scope).off(".joyride").on("click.fndtn.joyride",".joyride-next-tip, .joyride-modal-bg",function(t){t.preventDefault(),this.go_next()}.bind(this)).on("click.fndtn.joyride",".joyride-prev-tip",function(t){t.preventDefault(),this.go_prev()}.bind(this)).on("click.fndtn.joyride",".joyride-close-tip",function(t){t.preventDefault(),this.end(this.settings.abort_on_close)}.bind(this)).on("keyup.fndtn.joyride",function(t){if(this.settings.keyboard&&this.settings.riding)switch(t.which){case 39:t.preventDefault(),this.go_next();break;case 37:t.preventDefault(),this.go_prev();break;case 27:t.preventDefault(),this.end(this.settings.abort_on_close)}}.bind(this)),t(e).off(".joyride").on("resize.fndtn.joyride",n.throttle(function(){if(t("["+n.attr_name()+"]").length>0&&n.settings.$next_tip&&n.settings.riding){if(n.settings.exposed.length>0){var e=t(n.settings.exposed);e.each(function(){var e=t(this);n.un_expose(e),n.expose(e)})}n.is_phone()?n.pos_phone():n.pos_default(!1)}},100))},start:function(){var e=this,n=t("["+this.attr_name()+"]",this.scope),i=["timer","scrollSpeed","startOffset","tipAnimationFadeSpeed","cookieExpires"],s=i.length;!n.length>0||(this.settings.init||this.events(),this.settings=n.data(this.attr_name(!0)+"-init"),this.settings.$content_el=n,this.settings.$body=t(this.settings.tip_container),this.settings.body_offset=t(this.settings.tip_container).position(),this.settings.$tip_content=this.settings.$content_el.find("> li"),this.settings.paused=!1,this.settings.attempts=0,this.settings.riding=!0,"function"!=typeof t.cookie&&(this.settings.cookie_monster=!1),(!this.settings.cookie_monster||this.settings.cookie_monster&&!t.cookie(this.settings.cookie_name))&&(this.settings.$tip_content.each(function(n){var a=t(this);this.settings=t.extend({},e.defaults,e.data_options(a));for(var r=s;r--;)e.settings[i[r]]=parseInt(e.settings[i[r]],10);e.create({$li:a,index:n})}),!this.settings.start_timer_on_click&&this.settings.timer>0?(this.show("init"),this.startTimer()):this.show("init")))},resume:function(){this.set_li(),this.show()},tip_template:function(e){var n,i;return e.tip_class=e.tip_class||"",n=t(this.settings.template.tip).addClass(e.tip_class),i=t.trim(t(e.li).html())+this.prev_button_text(e.prev_button_text,e.index)+this.button_text(e.button_text)+this.settings.template.link+this.timer_instance(e.index),n.append(t(this.settings.template.wrapper)),n.first().attr(this.add_namespace("data-index"),e.index),t(".joyride-content-wrapper",n).append(i),n[0]},timer_instance:function(e){var n;return n=0===e&&this.settings.start_timer_on_click&&this.settings.timer>0||0===this.settings.timer?"":t(this.settings.template.timer)[0].outerHTML},button_text:function(e){return this.settings.tip_settings.next_button?(e=t.trim(e)||"Next",e=t(this.settings.template.button).append(e)[0].outerHTML):e="",e},prev_button_text:function(e,n){return this.settings.tip_settings.prev_button?(e=t.trim(e)||"Previous",e=0==n?t(this.settings.template.prev_button).append(e).addClass("disabled")[0].outerHTML:t(this.settings.template.prev_button).append(e)[0].outerHTML):e="",e},create:function(e){this.settings.tip_settings=t.extend({},this.settings,this.data_options(e.$li));var n=e.$li.attr(this.add_namespace("data-button"))||e.$li.attr(this.add_namespace("data-text")),i=e.$li.attr(this.add_namespace("data-button-prev"))||e.$li.attr(this.add_namespace("data-prev-text")),s=e.$li.attr("class"),a=t(this.tip_template({tip_class:s,index:e.index,button_text:n,prev_button_text:i,li:e.$li}));t(this.settings.tip_container).append(a)},show:function(e,n){var s=null;if(this.settings.$li===i||-1===t.inArray(this.settings.$li.index(),this.settings.pause_after))if(this.settings.paused?this.settings.paused=!1:this.set_li(e,n),this.settings.attempts=0,this.settings.$li.length&&this.settings.$target.length>0){if(e&&(this.settings.pre_ride_callback(this.settings.$li.index(),this.settings.$next_tip),this.settings.modal&&this.show_modal()),this.settings.pre_step_callback(this.settings.$li.index(),this.settings.$next_tip),this.settings.modal&&this.settings.expose&&this.expose(),this.settings.tip_settings=t.extend({},this.settings,this.data_options(this.settings.$li)),this.settings.timer=parseInt(this.settings.timer,10),this.settings.tip_settings.tip_location_pattern=this.settings.tip_location_patterns[this.settings.tip_settings.tip_location],!/body/i.test(this.settings.$target.selector)){var a=t(".joyride-modal-bg");/pop/i.test(this.settings.tipAnimation)?a.hide():a.fadeOut(this.settings.tipAnimationFadeSpeed),this.scroll_to()}this.is_phone()?this.pos_phone(!0):this.pos_default(!0),s=this.settings.$next_tip.find(".joyride-timer-indicator"),/pop/i.test(this.settings.tip_animation)?(s.width(0),this.settings.timer>0?(this.settings.$next_tip.show(),setTimeout(function(){s.animate({width:s.parent().width()},this.settings.timer,"linear")}.bind(this),this.settings.tip_animation_fade_speed)):this.settings.$next_tip.show()):/fade/i.test(this.settings.tip_animation)&&(s.width(0),this.settings.timer>0?(this.settings.$next_tip.fadeIn(this.settings.tip_animation_fade_speed).show(),setTimeout(function(){s.animate({width:s.parent().width()},this.settings.timer,"linear")}.bind(this),this.settings.tip_animation_fade_speed)):this.settings.$next_tip.fadeIn(this.settings.tip_animation_fade_speed)),this.settings.$current_tip=this.settings.$next_tip}else this.settings.$li&&this.settings.$target.length<1?this.show(e,n):this.end();else this.settings.paused=!0},is_phone:function(){return matchMedia(Foundation.media_queries.small).matches&&!matchMedia(Foundation.media_queries.medium).matches},hide:function(){this.settings.modal&&this.settings.expose&&this.un_expose(),this.settings.modal||t(".joyride-modal-bg").hide(),this.settings.$current_tip.css("visibility","hidden"),setTimeout(t.proxy(function(){this.hide(),this.css("visibility","visible")},this.settings.$current_tip),0),this.settings.post_step_callback(this.settings.$li.index(),this.settings.$current_tip)},set_li:function(t,e){t?(this.settings.$li=this.settings.$tip_content.eq(this.settings.start_offset),this.set_next_tip(),this.settings.$current_tip=this.settings.$next_tip):(this.settings.$li=e?this.settings.$li.prev():this.settings.$li.next(),this.set_next_tip()),this.set_target()},set_next_tip:function(){this.settings.$next_tip=t(".joyride-tip-guide").eq(this.settings.$li.index()),this.settings.$next_tip.data("closed","")},set_target:function(){var e=this.settings.$li.attr(this.add_namespace("data-class")),i=this.settings.$li.attr(this.add_namespace("data-id")),s=function(){return i?t(n.getElementById(i)):e?t("."+e).first():t("body")};this.settings.$target=s()},scroll_to:function(){var n,i;n=t(e).height()/2,i=Math.ceil(this.settings.$target.offset().top-n+this.settings.$next_tip.outerHeight()),0!=i&&t("html, body").stop().animate({scrollTop:i},this.settings.scroll_speed,"swing")},paused:function(){return-1===t.inArray(this.settings.$li.index()+1,this.settings.pause_after)},restart:function(){this.hide(),this.settings.$li=i,this.show("init")},pos_default:function(t){var e=this.settings.$next_tip.find(".joyride-nub"),n=Math.ceil(e.outerWidth()/2),i=Math.ceil(e.outerHeight()/2),s=t||!1;if(s&&(this.settings.$next_tip.css("visibility","hidden"),this.settings.$next_tip.show()),/body/i.test(this.settings.$target.selector))this.settings.$li.length&&this.pos_modal(e);else{var a=this.settings.tip_settings.tipAdjustmentY?parseInt(this.settings.tip_settings.tipAdjustmentY):0,r=this.settings.tip_settings.tipAdjustmentX?parseInt(this.settings.tip_settings.tipAdjustmentX):0;this.bottom()?(this.settings.$next_tip.css(this.rtl?{top:this.settings.$target.offset().top+i+this.settings.$target.outerHeight()+a,left:this.settings.$target.offset().left+this.settings.$target.outerWidth()-this.settings.$next_tip.outerWidth()+r}:{top:this.settings.$target.offset().top+i+this.settings.$target.outerHeight()+a,left:this.settings.$target.offset().left+r}),this.nub_position(e,this.settings.tip_settings.nub_position,"top")):this.top()?(this.settings.$next_tip.css(this.rtl?{top:this.settings.$target.offset().top-this.settings.$next_tip.outerHeight()-i+a,left:this.settings.$target.offset().left+this.settings.$target.outerWidth()-this.settings.$next_tip.outerWidth()}:{top:this.settings.$target.offset().top-this.settings.$next_tip.outerHeight()-i+a,left:this.settings.$target.offset().left+r}),this.nub_position(e,this.settings.tip_settings.nub_position,"bottom")):this.right()?(this.settings.$next_tip.css({top:this.settings.$target.offset().top+a,left:this.settings.$target.outerWidth()+this.settings.$target.offset().left+n+r}),this.nub_position(e,this.settings.tip_settings.nub_position,"left")):this.left()&&(this.settings.$next_tip.css({top:this.settings.$target.offset().top+a,left:this.settings.$target.offset().left-this.settings.$next_tip.outerWidth()-n+r}),this.nub_position(e,this.settings.tip_settings.nub_position,"right")),!this.visible(this.corners(this.settings.$next_tip))&&this.settings.attempts<this.settings.tip_settings.tip_location_pattern.length&&(e.removeClass("bottom").removeClass("top").removeClass("right").removeClass("left"),this.settings.tip_settings.tip_location=this.settings.tip_settings.tip_location_pattern[this.settings.attempts],this.settings.attempts++,this.pos_default())}s&&(this.settings.$next_tip.hide(),this.settings.$next_tip.css("visibility","visible"))},pos_phone:function(e){var n=this.settings.$next_tip.outerHeight(),i=(this.settings.$next_tip.offset(),this.settings.$target.outerHeight()),s=t(".joyride-nub",this.settings.$next_tip),a=Math.ceil(s.outerHeight()/2),r=e||!1;s.removeClass("bottom").removeClass("top").removeClass("right").removeClass("left"),r&&(this.settings.$next_tip.css("visibility","hidden"),this.settings.$next_tip.show()),/body/i.test(this.settings.$target.selector)?this.settings.$li.length&&this.pos_modal(s):this.top()?(this.settings.$next_tip.offset({top:this.settings.$target.offset().top-n-a}),s.addClass("bottom")):(this.settings.$next_tip.offset({top:this.settings.$target.offset().top+i+a}),s.addClass("top")),r&&(this.settings.$next_tip.hide(),this.settings.$next_tip.css("visibility","visible"))},pos_modal:function(t){this.center(),t.hide(),this.show_modal()},show_modal:function(){if(!this.settings.$next_tip.data("closed")){var e=t(".joyride-modal-bg");if(e.length<1){var e=t(this.settings.template.modal);e.appendTo("body")}/pop/i.test(this.settings.tip_animation)?e.show():e.fadeIn(this.settings.tip_animation_fade_speed)}},expose:function(){var n,i,s,a,r,o="expose-"+this.random_str(6);if(arguments.length>0&&arguments[0]instanceof t)s=arguments[0];else{if(!this.settings.$target||/body/i.test(this.settings.$target.selector))return!1;s=this.settings.$target}return s.length<1?(e.console&&console.error("element not valid",s),!1):(n=t(this.settings.template.expose),this.settings.$body.append(n),n.css({top:s.offset().top,left:s.offset().left,width:s.outerWidth(!0),height:s.outerHeight(!0)}),i=t(this.settings.template.expose_cover),a={zIndex:s.css("z-index"),position:s.css("position")},r=null==s.attr("class")?"":s.attr("class"),s.css("z-index",parseInt(n.css("z-index"))+1),"static"==a.position&&s.css("position","relative"),s.data("expose-css",a),s.data("orig-class",r),s.attr("class",r+" "+this.settings.expose_add_class),i.css({top:s.offset().top,left:s.offset().left,width:s.outerWidth(!0),height:s.outerHeight(!0)}),this.settings.modal&&this.show_modal(),this.settings.$body.append(i),n.addClass(o),i.addClass(o),s.data("expose",o),this.settings.post_expose_callback(this.settings.$li.index(),this.settings.$next_tip,s),void this.add_exposed(s))},un_expose:function(){var n,i,s,a,r,o=!1;if(arguments.length>0&&arguments[0]instanceof t)i=arguments[0];else{if(!this.settings.$target||/body/i.test(this.settings.$target.selector))return!1;i=this.settings.$target}return i.length<1?(e.console&&console.error("element not valid",i),!1):(n=i.data("expose"),s=t("."+n),arguments.length>1&&(o=arguments[1]),o===!0?t(".joyride-expose-wrapper,.joyride-expose-cover").remove():s.remove(),a=i.data("expose-css"),"auto"==a.zIndex?i.css("z-index",""):i.css("z-index",a.zIndex),a.position!=i.css("position")&&("static"==a.position?i.css("position",""):i.css("position",a.position)),r=i.data("orig-class"),i.attr("class",r),i.removeData("orig-classes"),i.removeData("expose"),i.removeData("expose-z-index"),void this.remove_exposed(i))},add_exposed:function(e){this.settings.exposed=this.settings.exposed||[],e instanceof t||"object"==typeof e?this.settings.exposed.push(e[0]):"string"==typeof e&&this.settings.exposed.push(e)},remove_exposed:function(e){var n,i;for(e instanceof t?n=e[0]:"string"==typeof e&&(n=e),this.settings.exposed=this.settings.exposed||[],i=this.settings.exposed.length;i--;)if(this.settings.exposed[i]==n)return void this.settings.exposed.splice(i,1)},center:function(){var n=t(e);return this.settings.$next_tip.css({top:(n.height()-this.settings.$next_tip.outerHeight())/2+n.scrollTop(),left:(n.width()-this.settings.$next_tip.outerWidth())/2+n.scrollLeft()}),!0},bottom:function(){return/bottom/i.test(this.settings.tip_settings.tip_location)},top:function(){return/top/i.test(this.settings.tip_settings.tip_location)},right:function(){return/right/i.test(this.settings.tip_settings.tip_location)},left:function(){return/left/i.test(this.settings.tip_settings.tip_location)},corners:function(n){var i=t(e),s=i.height()/2,a=Math.ceil(this.settings.$target.offset().top-s+this.settings.$next_tip.outerHeight()),r=i.width()+i.scrollLeft(),o=i.height()+a,l=i.height()+i.scrollTop(),c=i.scrollTop();return c>a&&(c=0>a?0:a),o>l&&(l=o),[n.offset().top<c,r<n.offset().left+n.outerWidth(),l<n.offset().top+n.outerHeight(),i.scrollLeft()>n.offset().left]},visible:function(t){for(var e=t.length;e--;)if(t[e])return!1;return!0},nub_position:function(t,e,n){t.addClass("auto"===e?n:e)},startTimer:function(){this.settings.$li.length?this.settings.automate=setTimeout(function(){this.hide(),this.show(),this.startTimer()}.bind(this),this.settings.timer):clearTimeout(this.settings.automate)},end:function(e){this.settings.cookie_monster&&t.cookie(this.settings.cookie_name,"ridden",{expires:this.settings.cookie_expires,domain:this.settings.cookie_domain}),this.settings.timer>0&&clearTimeout(this.settings.automate),this.settings.modal&&this.settings.expose&&this.un_expose(),t(this.scope).off("keyup.joyride"),this.settings.$next_tip.data("closed",!0),this.settings.riding=!1,t(".joyride-modal-bg").hide(),this.settings.$current_tip.hide(),("undefined"==typeof e||e===!1)&&(this.settings.post_step_callback(this.settings.$li.index(),this.settings.$current_tip),this.settings.post_ride_callback(this.settings.$li.index(),this.settings.$current_tip)),t(".joyride-tip-guide").remove()},off:function(){t(this.scope).off(".joyride"),t(e).off(".joyride"),t(".joyride-close-tip, .joyride-next-tip, .joyride-modal-bg").off(".joyride"),t(".joyride-tip-guide, .joyride-modal-bg").remove(),clearTimeout(this.settings.automate),this.settings={}},reflow:function(){}}}(jQuery,window,window.document),function(t,e){"use strict";Foundation.libs["magellan-expedition"]={name:"magellan-expedition",version:"5.5.2",settings:{active_class:"active",threshold:0,destination_threshold:20,throttle_delay:30,fixed_top:0,offset_by_height:!0,duration:700,easing:"swing"},init:function(t,e,n){Foundation.inherit(this,"throttle"),this.bindings(e,n)},events:function(){var e=this,n=e.S,i=e.settings;e.set_expedition_position(),n(e.scope).off(".magellan").on("click.fndtn.magellan","["+e.add_namespace("data-magellan-arrival")+"] a[href*=#]",function(n){var i=this.hostname===location.hostname||!this.hostname,s=e.filterPathname(location.pathname)===e.filterPathname(this.pathname),a=this.hash.replace(/(:|\.|\/)/g,"\\$1"),r=this;if(i&&s&&a){n.preventDefault();var o=t(this).closest("["+e.attr_name()+"]"),l=o.data("magellan-expedition-init"),c=this.hash.split("#").join(""),d=t('a[name="'+c+'"]');0===d.length&&(d=t("#"+c));var u=d.offset().top-l.destination_threshold+1;l.offset_by_height&&(u-=o.outerHeight()),t("html, body").stop().animate({scrollTop:u},l.duration,l.easing,function(){history.pushState?history.pushState(null,null,r.pathname+"#"+c):location.hash=r.pathname+"#"+c})}}).on("scroll.fndtn.magellan",e.throttle(this.check_for_arrivals.bind(this),i.throttle_delay))},check_for_arrivals:function(){var t=this;t.update_arrivals(),t.update_expedition_positions()},set_expedition_position:function(){var e=this;t("["+this.attr_name()+"=fixed]",e.scope).each(function(){var n,i,s=t(this),a=s.data("magellan-expedition-init"),r=s.attr("styles");s.attr("style",""),n=s.offset().top+a.threshold,i=parseInt(s.data("magellan-fixed-top")),isNaN(i)||(e.settings.fixed_top=i),s.data(e.data_attr("magellan-top-offset"),n),s.attr("style",r)})},update_expedition_positions:function(){var n=this,i=t(e).scrollTop();t("["+this.attr_name()+"=fixed]",n.scope).each(function(){var e=t(this),s=e.data("magellan-expedition-init"),a=e.attr("style"),r=e.data("magellan-top-offset");if(i+n.settings.fixed_top>=r){var o=e.prev("["+n.add_namespace("data-magellan-expedition-clone")+"]");0===o.length&&(o=e.clone(),o.removeAttr(n.attr_name()),o.attr(n.add_namespace("data-magellan-expedition-clone"),""),e.before(o)),e.css({position:"fixed",top:s.fixed_top}).addClass("fixed")}else e.prev("["+n.add_namespace("data-magellan-expedition-clone")+"]").remove(),e.attr("style",a).css("position","").css("top","").removeClass("fixed")})},update_arrivals:function(){var n=this,i=t(e).scrollTop();t("["+this.attr_name()+"]",n.scope).each(function(){var e=t(this),s=e.data(n.attr_name(!0)+"-init"),a=n.offsets(e,i),r=e.find("["+n.add_namespace("data-magellan-arrival")+"]"),o=!1;a.each(function(t,i){if(i.viewport_offset>=i.top_offset){var a=e.find("["+n.add_namespace("data-magellan-arrival")+"]");return a.not(i.arrival).removeClass(s.active_class),i.arrival.addClass(s.active_class),o=!0,!0}}),o||r.removeClass(s.active_class)})},offsets:function(e,n){var i=this,s=e.data(i.attr_name(!0)+"-init"),a=n;return e.find("["+i.add_namespace("data-magellan-arrival")+"]").map(function(){var n=t(this).data(i.data_attr("magellan-arrival")),r=t("["+i.add_namespace("data-magellan-destination")+"="+n+"]");if(r.length>0){var o=r.offset().top-s.destination_threshold;return s.offset_by_height&&(o-=e.outerHeight()),o=Math.floor(o),{destination:r,arrival:t(this),top_offset:o,viewport_offset:a}}}).sort(function(t,e){return t.top_offset<e.top_offset?-1:t.top_offset>e.top_offset?1:0})},data_attr:function(t){return this.namespace.length>0?this.namespace+"-"+t:t},off:function(){this.S(this.scope).off(".magellan"),this.S(e).off(".magellan")},filterPathname:function(t){return t=t||"",t.replace(/^\//,"").replace(/(?:index|default).[a-zA-Z]{3,4}$/,"").replace(/\/$/,"")},reflow:function(){var e=this;t("["+e.add_namespace("data-magellan-expedition-clone")+"]",e.scope).remove()}}}(jQuery,window,window.document),function(t){"use strict";Foundation.libs.offcanvas={name:"offcanvas",version:"5.5.2",settings:{open_method:"move",close_on_click:!1},init:function(t,e,n){this.bindings(e,n)},events:function(){var e=this,n=e.S,i="",s="",a="";"move"===this.settings.open_method?(i="move-",s="right",a="left"):"overlap_single"===this.settings.open_method?(i="offcanvas-overlap-",s="right",a="left"):"overlap"===this.settings.open_method&&(i="offcanvas-overlap"),n(this.scope).off(".offcanvas").on("click.fndtn.offcanvas",".left-off-canvas-toggle",function(a){e.click_toggle_class(a,i+s),"overlap"!==e.settings.open_method&&n(".left-submenu").removeClass(i+s),t(".left-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".left-off-canvas-menu a",function(a){var r=e.get_settings(a),o=n(this).parent();!r.close_on_click||o.hasClass("has-submenu")||o.hasClass("back")?n(this).parent().hasClass("has-submenu")?(a.preventDefault(),n(this).siblings(".left-submenu").toggleClass(i+s)):o.hasClass("back")&&(a.preventDefault(),o.parent().removeClass(i+s)):(e.hide.call(e,i+s,e.get_wrapper(a)),o.parent().removeClass(i+s)),t(".left-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".right-off-canvas-toggle",function(s){e.click_toggle_class(s,i+a),"overlap"!==e.settings.open_method&&n(".right-submenu").removeClass(i+a),t(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".right-off-canvas-menu a",function(s){var r=e.get_settings(s),o=n(this).parent();!r.close_on_click||o.hasClass("has-submenu")||o.hasClass("back")?n(this).parent().hasClass("has-submenu")?(s.preventDefault(),n(this).siblings(".right-submenu").toggleClass(i+a)):o.hasClass("back")&&(s.preventDefault(),o.parent().removeClass(i+a)):(e.hide.call(e,i+a,e.get_wrapper(s)),o.parent().removeClass(i+a)),t(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".exit-off-canvas",function(r){e.click_remove_class(r,i+a),n(".right-submenu").removeClass(i+a),s&&(e.click_remove_class(r,i+s),n(".left-submenu").removeClass(i+a)),t(".right-off-canvas-toggle").attr("aria-expanded","true")}).on("click.fndtn.offcanvas",".exit-off-canvas",function(n){e.click_remove_class(n,i+a),t(".left-off-canvas-toggle").attr("aria-expanded","false"),s&&(e.click_remove_class(n,i+s),t(".right-off-canvas-toggle").attr("aria-expanded","false"))})},toggle:function(t,e){e=e||this.get_wrapper(),e.is("."+t)?this.hide(t,e):this.show(t,e)},show:function(t,e){e=e||this.get_wrapper(),e.trigger("open.fndtn.offcanvas"),e.addClass(t)},hide:function(t,e){e=e||this.get_wrapper(),e.trigger("close.fndtn.offcanvas"),e.removeClass(t)},click_toggle_class:function(t,e){t.preventDefault();var n=this.get_wrapper(t);this.toggle(e,n)},click_remove_class:function(t,e){t.preventDefault();var n=this.get_wrapper(t);this.hide(e,n)},get_settings:function(t){var e=this.S(t.target).closest("["+this.attr_name()+"]");return e.data(this.attr_name(!0)+"-init")||this.settings},get_wrapper:function(t){var e=this.S(t?t.target:this.scope).closest(".off-canvas-wrap");return 0===e.length&&(e=this.S(".off-canvas-wrap")),e},reflow:function(){}}}(jQuery,window,window.document),function(t,e,n,i){"use strict";var s=function(){},a=function(s,a){if(s.hasClass(a.slides_container_class))return this;var c,d,u,h,f,p,g=this,m=s,v=0,_=!1;g.slides=function(){return m.children(a.slide_selector)},g.slides().first().addClass(a.active_slide_class),g.update_slide_number=function(e){a.slide_number&&(d.find("span:first").text(parseInt(e)+1),d.find("span:last").text(g.slides().length)),a.bullets&&(u.children().removeClass(a.bullets_active_class),t(u.children().get(e)).addClass(a.bullets_active_class))},g.update_active_link=function(e){var n=t('[data-orbit-link="'+g.slides().eq(e).attr("data-orbit-slide")+'"]');n.siblings().removeClass(a.bullets_active_class),n.addClass(a.bullets_active_class)},g.build_markup=function(){m.wrap('<div class="'+a.container_class+'"></div>'),c=m.parent(),m.addClass(a.slides_container_class),a.stack_on_small&&c.addClass(a.stack_on_small_class),a.navigation_arrows&&(c.append(t('<a href="#"><span></span></a>').addClass(a.prev_class)),c.append(t('<a href="#"><span></span></a>').addClass(a.next_class))),a.timer&&(h=t("<div>").addClass(a.timer_container_class),h.append("<span>"),h.append(t("<div>").addClass(a.timer_progress_class)),h.addClass(a.timer_paused_class),c.append(h)),a.slide_number&&(d=t("<div>").addClass(a.slide_number_class),d.append("<span></span> "+a.slide_number_text+" <span></span>"),c.append(d)),a.bullets&&(u=t("<ol>").addClass(a.bullets_container_class),c.append(u),u.wrap('<div class="orbit-bullets-container"></div>'),g.slides().each(function(e){var n=t("<li>").attr("data-orbit-slide",e).on("click",g.link_bullet);u.append(n)}))},g._goto=function(e,n){if(e===v)return!1;"object"==typeof p&&p.restart();var i=g.slides(),s="next";if(_=!0,v>e&&(s="prev"),e>=i.length){if(!a.circular)return!1;e=0}else if(0>e){if(!a.circular)return!1;e=i.length-1
}var r=t(i.get(v)),o=t(i.get(e));r.css("zIndex",2),r.removeClass(a.active_slide_class),o.css("zIndex",4).addClass(a.active_slide_class),m.trigger("before-slide-change.fndtn.orbit"),a.before_slide_change(),g.update_active_link(e);var l=function(){var t=function(){v=e,_=!1,n===!0&&(p=g.create_timer(),p.start()),g.update_slide_number(v),m.trigger("after-slide-change.fndtn.orbit",[{slide_number:v,total_slides:i.length}]),a.after_slide_change(v,i.length)};m.outerHeight()!=o.outerHeight()&&a.variable_height?m.animate({height:o.outerHeight()},250,"linear",t):t()};if(1===i.length)return l(),!1;var c=function(){"next"===s&&f.next(r,o,l),"prev"===s&&f.prev(r,o,l)};o.outerHeight()>m.outerHeight()&&a.variable_height?m.animate({height:o.outerHeight()},250,"linear",c):c()},g.next=function(t){t.stopImmediatePropagation(),t.preventDefault(),g._goto(v+1)},g.prev=function(t){t.stopImmediatePropagation(),t.preventDefault(),g._goto(v-1)},g.link_custom=function(e){e.preventDefault();var n=t(this).attr("data-orbit-link");if("string"==typeof n&&""!=(n=t.trim(n))){var i=c.find("[data-orbit-slide="+n+"]");-1!=i.index()&&g._goto(i.index())}},g.link_bullet=function(){var e=t(this).attr("data-orbit-slide");if("string"==typeof e&&""!=(e=t.trim(e)))if(isNaN(parseInt(e))){var n=c.find("[data-orbit-slide="+e+"]");-1!=n.index()&&g._goto(n.index()+1)}else g._goto(parseInt(e))},g.timer_callback=function(){g._goto(v+1,!0)},g.compute_dimensions=function(){var e=t(g.slides().get(v)),n=e.outerHeight();a.variable_height||g.slides().each(function(){t(this).outerHeight()>n&&(n=t(this).outerHeight())}),m.height(n)},g.create_timer=function(){var t=new r(c.find("."+a.timer_container_class),a,g.timer_callback);return t},g.stop_timer=function(){"object"==typeof p&&p.stop()},g.toggle_timer=function(){var t=c.find("."+a.timer_container_class);t.hasClass(a.timer_paused_class)?("undefined"==typeof p&&(p=g.create_timer()),p.start()):"object"==typeof p&&p.stop()},g.init=function(){g.build_markup(),a.timer&&(p=g.create_timer(),Foundation.utils.image_loaded(this.slides().children("img"),p.start)),f=new l(a,m),"slide"===a.animation&&(f=new o(a,m)),c.on("click","."+a.next_class,g.next),c.on("click","."+a.prev_class,g.prev),a.next_on_click&&c.on("click","."+a.slides_container_class+" [data-orbit-slide]",g.link_bullet),c.on("click",g.toggle_timer),a.swipe&&c.on("touchstart.fndtn.orbit",function(t){t.touches||(t=t.originalEvent);var e={start_page_x:t.touches[0].pageX,start_page_y:t.touches[0].pageY,start_time:(new Date).getTime(),delta_x:0,is_scrolling:i};c.data("swipe-transition",e),t.stopPropagation()}).on("touchmove.fndtn.orbit",function(t){if(t.touches||(t=t.originalEvent),!(t.touches.length>1||t.scale&&1!==t.scale)){var e=c.data("swipe-transition");if("undefined"==typeof e&&(e={}),e.delta_x=t.touches[0].pageX-e.start_page_x,"undefined"==typeof e.is_scrolling&&(e.is_scrolling=!!(e.is_scrolling||Math.abs(e.delta_x)<Math.abs(t.touches[0].pageY-e.start_page_y))),!e.is_scrolling&&!e.active){t.preventDefault();var n=e.delta_x<0?v+1:v-1;e.active=!0,g._goto(n)}}}).on("touchend.fndtn.orbit",function(t){c.data("swipe-transition",{}),t.stopPropagation()}),c.on("mouseenter.fndtn.orbit",function(){a.timer&&a.pause_on_hover&&g.stop_timer()}).on("mouseleave.fndtn.orbit",function(){a.timer&&a.resume_on_mouseout&&p.start()}),t(n).on("click","[data-orbit-link]",g.link_custom),t(e).on("load resize",g.compute_dimensions),Foundation.utils.image_loaded(this.slides().children("img"),g.compute_dimensions),Foundation.utils.image_loaded(this.slides().children("img"),function(){c.prev("."+a.preloader_class).css("display","none"),g.update_slide_number(0),g.update_active_link(0),m.trigger("ready.fndtn.orbit")})},g.init()},r=function(t,e,n){var i,s,a=this,r=e.timer_speed,o=t.find("."+e.timer_progress_class),l=-1;this.update_progress=function(t){var e=o.clone();e.attr("style",""),e.css("width",t+"%"),o.replaceWith(e),o=e},this.restart=function(){clearTimeout(s),t.addClass(e.timer_paused_class),l=-1,a.update_progress(0)},this.start=function(){return t.hasClass(e.timer_paused_class)?(l=-1===l?r:l,t.removeClass(e.timer_paused_class),i=(new Date).getTime(),o.animate({width:"100%"},l,"linear"),s=setTimeout(function(){a.restart(),n()},l),void t.trigger("timer-started.fndtn.orbit")):!0},this.stop=function(){if(t.hasClass(e.timer_paused_class))return!0;clearTimeout(s),t.addClass(e.timer_paused_class);var n=(new Date).getTime();l-=n-i;var o=100-l/r*100;a.update_progress(o),t.trigger("timer-stopped.fndtn.orbit")}},o=function(e){var n=e.animation_speed,i=1===t("html[dir=rtl]").length,s=i?"marginRight":"marginLeft",a={};a[s]="0%",this.next=function(t,e,i){t.animate({marginLeft:"-100%"},n),e.animate(a,n,function(){t.css(s,"100%"),i()})},this.prev=function(t,e,i){t.animate({marginLeft:"100%"},n),e.css(s,"-100%"),e.animate(a,n,function(){t.css(s,"100%"),i()})}},l=function(e){{var n=e.animation_speed;1===t("html[dir=rtl]").length}this.next=function(t,e,i){e.css({margin:"0%",opacity:"0.01"}),e.animate({opacity:"1"},n,"linear",function(){t.css("margin","100%"),i()})},this.prev=function(t,e,i){e.css({margin:"0%",opacity:"0.01"}),e.animate({opacity:"1"},n,"linear",function(){t.css("margin","100%"),i()})}};Foundation.libs=Foundation.libs||{},Foundation.libs.orbit={name:"orbit",version:"5.5.2",settings:{animation:"slide",timer_speed:1e4,pause_on_hover:!0,resume_on_mouseout:!1,next_on_click:!0,animation_speed:500,stack_on_small:!1,navigation_arrows:!0,slide_number:!0,slide_number_text:"of",container_class:"orbit-container",stack_on_small_class:"orbit-stack-on-small",next_class:"orbit-next",prev_class:"orbit-prev",timer_container_class:"orbit-timer",timer_paused_class:"paused",timer_progress_class:"orbit-progress",slides_container_class:"orbit-slides-container",preloader_class:"preloader",slide_selector:"*",bullets_container_class:"orbit-bullets",bullets_active_class:"active",slide_number_class:"orbit-slide-number",caption_class:"orbit-caption",active_slide_class:"active",orbit_transition_class:"orbit-transitioning",bullets:!0,circular:!0,timer:!0,variable_height:!1,swipe:!0,before_slide_change:s,after_slide_change:s},init:function(t,e,n){this.bindings(e,n)},events:function(t){var e=new a(this.S(t),this.S(t).data("orbit-init"));this.S(t).data(this.name+"-instance",e)},reflow:function(){var t=this;if(t.S(t.scope).is("[data-orbit]")){var e=t.S(t.scope),n=e.data(t.name+"-instance");n.compute_dimensions()}else t.S("[data-orbit]",t.scope).each(function(e,n){var i=t.S(n),s=(t.data_options(i),i.data(t.name+"-instance"));s.compute_dimensions()})}}}(jQuery,window,window.document),function(t,e,n,i){"use strict";function s(t){var e=/fade/i.test(t),n=/pop/i.test(t);return{animate:e||n,pop:n,fade:e}}Foundation.libs.reveal={name:"reveal",version:"5.5.2",locked:!1,settings:{animation:"fadeAndPop",animation_speed:250,close_on_background_click:!0,close_on_esc:!0,dismiss_modal_class:"close-reveal-modal",multiple_opened:!1,bg_class:"reveal-modal-bg",root_element:"body",open:function(){},opened:function(){},close:function(){},closed:function(){},on_ajax_error:t.noop,bg:t(".reveal-modal-bg"),css:{open:{opacity:0,visibility:"visible",display:"block"},close:{opacity:1,visibility:"hidden",display:"none"}}},init:function(e,n,i){t.extend(!0,this.settings,n,i),this.bindings(n,i)},events:function(){var t=this,e=t.S;return e(this.scope).off(".reveal").on("click.fndtn.reveal","["+this.add_namespace("data-reveal-id")+"]:not([disabled])",function(n){if(n.preventDefault(),!t.locked){var i=e(this),s=i.data(t.data_attr("reveal-ajax")),a=i.data(t.data_attr("reveal-replace-content"));if(t.locked=!0,"undefined"==typeof s)t.open.call(t,i);else{var r=s===!0?i.attr("href"):s;t.open.call(t,i,{url:r},{replaceContentSel:a})}}}),e(n).on("click.fndtn.reveal",this.close_targets(),function(n){if(n.preventDefault(),!t.locked){var i=e("["+t.attr_name()+"].open").data(t.attr_name(!0)+"-init")||t.settings,s=e(n.target)[0]===e("."+i.bg_class)[0];if(s){if(!i.close_on_background_click)return;n.stopPropagation()}t.locked=!0,t.close.call(t,s?e("["+t.attr_name()+"].open:not(.toback)"):e(this).closest("["+t.attr_name()+"]"))}}),e("["+t.attr_name()+"]",this.scope).length>0?e(this.scope).on("open.fndtn.reveal",this.settings.open).on("opened.fndtn.reveal",this.settings.opened).on("opened.fndtn.reveal",this.open_video).on("close.fndtn.reveal",this.settings.close).on("closed.fndtn.reveal",this.settings.closed).on("closed.fndtn.reveal",this.close_video):e(this.scope).on("open.fndtn.reveal","["+t.attr_name()+"]",this.settings.open).on("opened.fndtn.reveal","["+t.attr_name()+"]",this.settings.opened).on("opened.fndtn.reveal","["+t.attr_name()+"]",this.open_video).on("close.fndtn.reveal","["+t.attr_name()+"]",this.settings.close).on("closed.fndtn.reveal","["+t.attr_name()+"]",this.settings.closed).on("closed.fndtn.reveal","["+t.attr_name()+"]",this.close_video),!0},key_up_on:function(){var t=this;return t.S("body").off("keyup.fndtn.reveal").on("keyup.fndtn.reveal",function(e){var n=t.S("["+t.attr_name()+"].open"),i=n.data(t.attr_name(!0)+"-init")||t.settings;i&&27===e.which&&i.close_on_esc&&!t.locked&&t.close.call(t,n)}),!0},key_up_off:function(){return this.S("body").off("keyup.fndtn.reveal"),!0},open:function(n,i){var s,a=this;n?"undefined"!=typeof n.selector?s=a.S("#"+n.data(a.data_attr("reveal-id"))).first():(s=a.S(this.scope),i=n):s=a.S(this.scope);var r=s.data(a.attr_name(!0)+"-init");if(r=r||this.settings,s.hasClass("open")&&n.attr("data-reveal-id")==s.attr("id"))return a.close(s);if(!s.hasClass("open")){var o=a.S("["+a.attr_name()+"].open");if("undefined"==typeof s.data("css-top")&&s.data("css-top",parseInt(s.css("top"),10)).data("offset",this.cache_offset(s)),s.attr("tabindex","0").attr("aria-hidden","false"),this.key_up_on(s),s.on("open.fndtn.reveal",function(t){"fndtn.reveal"!==t.namespace}),s.on("open.fndtn.reveal").trigger("open.fndtn.reveal"),o.length<1&&this.toggle_bg(s,!0),"string"==typeof i&&(i={url:i}),"undefined"!=typeof i&&i.url){var l="undefined"!=typeof i.success?i.success:null;t.extend(i,{success:function(e,n,i){if(t.isFunction(l)){var c=l(e,n,i);"string"==typeof c&&(e=c)}"undefined"!=typeof options&&"undefined"!=typeof options.replaceContentSel?s.find(options.replaceContentSel).html(e):s.html(e),a.S(s).foundation("section","reflow"),a.S(s).children().foundation(),o.length>0&&(r.multiple_opened?a.to_back(o):a.hide(o,r.css.close)),a.show(s,r.css.open)}}),r.on_ajax_error!==t.noop&&t.extend(i,{error:r.on_ajax_error}),t.ajax(i)}else o.length>0&&(r.multiple_opened?a.to_back(o):a.hide(o,r.css.close)),this.show(s,r.css.open)}a.S(e).trigger("resize")},close:function(e){var e=e&&e.length?e:this.S(this.scope),n=this.S("["+this.attr_name()+"].open"),i=e.data(this.attr_name(!0)+"-init")||this.settings,s=this;n.length>0&&(e.removeAttr("tabindex","0").attr("aria-hidden","true"),this.locked=!0,this.key_up_off(e),e.trigger("close.fndtn.reveal"),(i.multiple_opened&&1===n.length||!i.multiple_opened||e.length>1)&&(s.toggle_bg(e,!1),s.to_front(e)),i.multiple_opened?(s.hide(e,i.css.close,i),s.to_front(t(t.makeArray(n).reverse()[1]))):s.hide(n,i.css.close,i))},close_targets:function(){var t="."+this.settings.dismiss_modal_class;return this.settings.close_on_background_click?t+", ."+this.settings.bg_class:t},toggle_bg:function(e,n){0===this.S("."+this.settings.bg_class).length&&(this.settings.bg=t("<div />",{"class":this.settings.bg_class}).appendTo("body").hide());var s=this.settings.bg.filter(":visible").length>0;n!=s&&((n==i?s:!n)?this.hide(this.settings.bg):this.show(this.settings.bg))},show:function(n,i){if(i){var a=n.data(this.attr_name(!0)+"-init")||this.settings,r=a.root_element,o=this;if(0===n.parent(r).length){var l=n.wrap('<div style="display: none;" />').parent();n.on("closed.fndtn.reveal.wrapped",function(){n.detach().appendTo(l),n.unwrap().unbind("closed.fndtn.reveal.wrapped")}),n.detach().appendTo(r)}var c=s(a.animation);if(c.animate||(this.locked=!1),c.pop){i.top=t(e).scrollTop()-n.data("offset")+"px";var d={top:t(e).scrollTop()+n.data("css-top")+"px",opacity:1};return setTimeout(function(){return n.css(i).animate(d,a.animation_speed,"linear",function(){o.locked=!1,n.trigger("opened.fndtn.reveal")}).addClass("open")},a.animation_speed/2)}if(c.fade){i.top=t(e).scrollTop()+n.data("css-top")+"px";var d={opacity:1};return setTimeout(function(){return n.css(i).animate(d,a.animation_speed,"linear",function(){o.locked=!1,n.trigger("opened.fndtn.reveal")}).addClass("open")},a.animation_speed/2)}return n.css(i).show().css({opacity:1}).addClass("open").trigger("opened.fndtn.reveal")}var a=this.settings;return s(a.animation).fade?n.fadeIn(a.animation_speed/2):(this.locked=!1,n.show())},to_back:function(t){t.addClass("toback")},to_front:function(t){t.removeClass("toback")},hide:function(n,i){if(i){var a=n.data(this.attr_name(!0)+"-init"),r=this;a=a||this.settings;var o=s(a.animation);if(o.animate||(this.locked=!1),o.pop){var l={top:-t(e).scrollTop()-n.data("offset")+"px",opacity:0};return setTimeout(function(){return n.animate(l,a.animation_speed,"linear",function(){r.locked=!1,n.css(i).trigger("closed.fndtn.reveal")}).removeClass("open")},a.animation_speed/2)}if(o.fade){var l={opacity:0};return setTimeout(function(){return n.animate(l,a.animation_speed,"linear",function(){r.locked=!1,n.css(i).trigger("closed.fndtn.reveal")}).removeClass("open")},a.animation_speed/2)}return n.hide().css(i).removeClass("open").trigger("closed.fndtn.reveal")}var a=this.settings;return s(a.animation).fade?n.fadeOut(a.animation_speed/2):n.hide()},close_video:function(e){var n=t(".flex-video",e.target),i=t("iframe",n);i.length>0&&(i.attr("data-src",i[0].src),i.attr("src",i.attr("src")),n.hide())},open_video:function(e){var n=t(".flex-video",e.target),s=n.find("iframe");if(s.length>0){var a=s.attr("data-src");if("string"==typeof a)s[0].src=s.attr("data-src");else{var r=s[0].src;s[0].src=i,s[0].src=r}n.show()}},data_attr:function(t){return this.namespace.length>0?this.namespace+"-"+t:t},cache_offset:function(t){var e=t.show().height()+parseInt(t.css("top"),10)+t.scrollY;return t.hide(),e},off:function(){t(this.scope).off(".fndtn.reveal")},reflow:function(){}}}(jQuery,window,window.document),function(t,e){"use strict";Foundation.libs.slider={name:"slider",version:"5.5.2",settings:{start:0,end:100,step:1,precision:null,initial:null,display_selector:"",vertical:!1,trigger_input_change:!1,on_change:function(){}},cache:{},init:function(t,e,n){Foundation.inherit(this,"throttle"),this.bindings(e,n),this.reflow()},events:function(){var n=this;t(this.scope).off(".slider").on("mousedown.fndtn.slider touchstart.fndtn.slider pointerdown.fndtn.slider","["+n.attr_name()+"]:not(.disabled, [disabled]) .range-slider-handle",function(e){n.cache.active||(e.preventDefault(),n.set_active_slider(t(e.target)))}).on("mousemove.fndtn.slider touchmove.fndtn.slider pointermove.fndtn.slider",function(i){if(n.cache.active)if(i.preventDefault(),t.data(n.cache.active[0],"settings").vertical){var s=0;i.pageY||(s=e.scrollY),n.calculate_position(n.cache.active,n.get_cursor_position(i,"y")+s)}else n.calculate_position(n.cache.active,n.get_cursor_position(i,"x"))}).on("mouseup.fndtn.slider touchend.fndtn.slider pointerup.fndtn.slider",function(){n.remove_active_slider()}).on("change.fndtn.slider",function(){n.settings.on_change()}),n.S(e).on("resize.fndtn.slider",n.throttle(function(){n.reflow()},300)),this.S("["+this.attr_name()+"]").each(function(){var e=t(this),i=e.children(".range-slider-handle")[0],s=n.initialize_settings(i);""!=s.display_selector&&t(s.display_selector).each(function(){this.hasOwnProperty("value")&&t(this).change(function(){e.foundation("slider","set_value",t(this).val())})})})},get_cursor_position:function(t,e){var n,i="page"+e.toUpperCase(),s="client"+e.toUpperCase();return"undefined"!=typeof t[i]?n=t[i]:"undefined"!=typeof t.originalEvent[s]?n=t.originalEvent[s]:t.originalEvent.touches&&t.originalEvent.touches[0]&&"undefined"!=typeof t.originalEvent.touches[0][s]?n=t.originalEvent.touches[0][s]:t.currentPoint&&"undefined"!=typeof t.currentPoint[e]&&(n=t.currentPoint[e]),n},set_active_slider:function(t){this.cache.active=t},remove_active_slider:function(){this.cache.active=null},calculate_position:function(e,n){var i=this,s=t.data(e[0],"settings"),a=(t.data(e[0],"handle_l"),t.data(e[0],"handle_o"),t.data(e[0],"bar_l")),r=t.data(e[0],"bar_o");requestAnimationFrame(function(){var t;t=Foundation.rtl&&!s.vertical?i.limit_to((r+a-n)/a,0,1):i.limit_to((n-r)/a,0,1),t=s.vertical?1-t:t;var o=i.normalized_value(t,s.start,s.end,s.step,s.precision);i.set_ui(e,o)})},set_ui:function(e,n){var i=t.data(e[0],"settings"),s=t.data(e[0],"handle_l"),a=t.data(e[0],"bar_l"),r=this.normalized_percentage(n,i.start,i.end),o=r*(a-s)-1,l=100*r,c=e.parent(),d=e.parent().children("input[type=hidden]");Foundation.rtl&&!i.vertical&&(o=-o),o=i.vertical?-o+a-s+1:o,this.set_translate(e,o,i.vertical),i.vertical?e.siblings(".range-slider-active-segment").css("height",l+"%"):e.siblings(".range-slider-active-segment").css("width",l+"%"),c.attr(this.attr_name(),n).trigger("change.fndtn.slider"),d.val(n),i.trigger_input_change&&d.trigger("change.fndtn.slider"),e[0].hasAttribute("aria-valuemin")||e.attr({"aria-valuemin":i.start,"aria-valuemax":i.end}),e.attr("aria-valuenow",n),""!=i.display_selector&&t(i.display_selector).each(function(){this.hasAttribute("value")?t(this).val(n):t(this).text(n)})},normalized_percentage:function(t,e,n){return Math.min(1,(t-e)/(n-e))},normalized_value:function(t,e,n,i,s){var a=n-e,r=t*a,o=(r-r%i)/i,l=r%i,c=l>=.5*i?i:0;return(o*i+c+e).toFixed(s)},set_translate:function(e,n,i){i?t(e).css("-webkit-transform","translateY("+n+"px)").css("-moz-transform","translateY("+n+"px)").css("-ms-transform","translateY("+n+"px)").css("-o-transform","translateY("+n+"px)").css("transform","translateY("+n+"px)"):t(e).css("-webkit-transform","translateX("+n+"px)").css("-moz-transform","translateX("+n+"px)").css("-ms-transform","translateX("+n+"px)").css("-o-transform","translateX("+n+"px)").css("transform","translateX("+n+"px)")},limit_to:function(t,e,n){return Math.min(Math.max(t,e),n)},initialize_settings:function(e){var n,i=t.extend({},this.settings,this.data_options(t(e).parent()));return null===i.precision&&(n=(""+i.step).match(/\.([\d]*)/),i.precision=n&&n[1]?n[1].length:0),i.vertical?(t.data(e,"bar_o",t(e).parent().offset().top),t.data(e,"bar_l",t(e).parent().outerHeight()),t.data(e,"handle_o",t(e).offset().top),t.data(e,"handle_l",t(e).outerHeight())):(t.data(e,"bar_o",t(e).parent().offset().left),t.data(e,"bar_l",t(e).parent().outerWidth()),t.data(e,"handle_o",t(e).offset().left),t.data(e,"handle_l",t(e).outerWidth())),t.data(e,"bar",t(e).parent()),t.data(e,"settings",i)},set_initial_position:function(e){var n=t.data(e.children(".range-slider-handle")[0],"settings"),i="number"!=typeof n.initial||isNaN(n.initial)?Math.floor(.5*(n.end-n.start)/n.step)*n.step+n.start:n.initial,s=e.children(".range-slider-handle");this.set_ui(s,i)},set_value:function(e){var n=this;t("["+n.attr_name()+"]",this.scope).each(function(){t(this).attr(n.attr_name(),e)}),t(this.scope).attr(n.attr_name())&&t(this.scope).attr(n.attr_name(),e),n.reflow()},reflow:function(){var e=this;e.S("["+this.attr_name()+"]").each(function(){var n=t(this).children(".range-slider-handle")[0],i=t(this).attr(e.attr_name());e.initialize_settings(n),i?e.set_ui(t(n),parseFloat(i)):e.set_initial_position(t(this))})}}}(jQuery,window,window.document),function(t,e,n,i){"use strict";Foundation.libs.tab={name:"tab",version:"5.5.2",settings:{active_class:"active",callback:function(){},deep_linking:!1,scroll_to_content:!0,is_hover:!1},default_tab_hashes:[],init:function(t,n,i){var s=this,a=this.S;a("["+this.attr_name()+"] > .active > a",this.scope).each(function(){s.default_tab_hashes.push(this.hash)}),s.entry_location=e.location.href,this.bindings(n,i),this.handle_location_hash_change()},events:function(){var t=this,n=this.S,i=function(e,i){var s=n(i).closest("["+t.attr_name()+"]").data(t.attr_name(!0)+"-init");(!s.is_hover||Modernizr.touch)&&(e.preventDefault(),e.stopPropagation(),t.toggle_active_tab(n(i).parent()))};n(this.scope).off(".tab").on("keydown.fndtn.tab","["+this.attr_name()+"] > * > a",function(t){var e=this,n=t.keyCode||t.which;9==n&&(t.preventDefault(),i(t,e))}).on("click.fndtn.tab","["+this.attr_name()+"] > * > a",function(t){var e=this;i(t,e)}).on("mouseenter.fndtn.tab","["+this.attr_name()+"] > * > a",function(){var e=n(this).closest("["+t.attr_name()+"]").data(t.attr_name(!0)+"-init");e.is_hover&&t.toggle_active_tab(n(this).parent())}),n(e).on("hashchange.fndtn.tab",function(e){e.preventDefault(),t.handle_location_hash_change()})},handle_location_hash_change:function(){var e=this,n=this.S;n("["+this.attr_name()+"]",this.scope).each(function(){var s=n(this).data(e.attr_name(!0)+"-init");if(s.deep_linking){var a;if(a=s.scroll_to_content?e.scope.location.hash:e.scope.location.hash.replace("fndtn-",""),""!=a){var r=n(a);if(r.hasClass("content")&&r.parent().hasClass("tabs-content"))e.toggle_active_tab(t("["+e.attr_name()+"] > * > a[href="+a+"]").parent());else{var o=r.closest(".content").attr("id");o!=i&&e.toggle_active_tab(t("["+e.attr_name()+"] > * > a[href=#"+o+"]").parent(),a)}}else for(var l=0;l<e.default_tab_hashes.length;l++)e.toggle_active_tab(t("["+e.attr_name()+"] > * > a[href="+e.default_tab_hashes[l]+"]").parent())}})},toggle_active_tab:function(s,a){var r=this,o=r.S,l=s.closest("["+this.attr_name()+"]"),c=s.find("a"),d=s.children("a").first(),u="#"+d.attr("href").split("#")[1],h=o(u),f=s.siblings(),p=l.data(this.attr_name(!0)+"-init"),g=function(e){var i,s=t(this),a=t(this).parents("li").prev().children('[role="tab"]'),r=t(this).parents("li").next().children('[role="tab"]');switch(e.keyCode){case 37:i=a;break;case 39:i=r;break;default:i=!1}i.length&&(s.attr({tabindex:"-1","aria-selected":null}),i.attr({tabindex:"0","aria-selected":!0}).focus()),t('[role="tabpanel"]').attr("aria-hidden","true"),t("#"+t(n.activeElement).attr("href").substring(1)).attr("aria-hidden",null)},m=function(t){var n=e.location.href===r.entry_location,i=p.scroll_to_content?r.default_tab_hashes[0]:n?e.location.hash:"fndtn-"+r.default_tab_hashes[0].replace("#","");n&&t===i||(e.location.hash=t)};d.data("tab-content")&&(u="#"+d.data("tab-content").split("#")[1],h=o(u)),p.deep_linking&&(p.scroll_to_content?(m(a||u),a==i||a==u?s.parent()[0].scrollIntoView():o(u)[0].scrollIntoView()):m(a!=i?"fndtn-"+a.replace("#",""):"fndtn-"+u.replace("#",""))),s.addClass(p.active_class).triggerHandler("opened"),c.attr({"aria-selected":"true",tabindex:0}),f.removeClass(p.active_class),f.find("a").attr({"aria-selected":"false",tabindex:-1}),h.siblings().removeClass(p.active_class).attr({"aria-hidden":"true",tabindex:-1}),h.addClass(p.active_class).attr("aria-hidden","false").removeAttr("tabindex"),p.callback(s),h.triggerHandler("toggled",[h]),l.triggerHandler("toggled",[s]),c.off("keydown").on("keydown",g)},data_attr:function(t){return this.namespace.length>0?this.namespace+"-"+t:t},off:function(){},reflow:function(){}}}(jQuery,window,window.document),function(t,e){"use strict";Foundation.libs.tooltip={name:"tooltip",version:"5.5.2",settings:{additional_inheritable_classes:[],tooltip_class:".tooltip",append_to:"body",touch_close_text:"Tap To Close",disable_for_touch:!1,hover_delay:200,show_on:"all",tip_template:function(t,e){return'<span data-selector="'+t+'" id="'+t+'" class="'+Foundation.libs.tooltip.settings.tooltip_class.substring(1)+'" role="tooltip">'+e+'<span class="nub"></span></span>'}},cache:{},init:function(t,e,n){Foundation.inherit(this,"random_str"),this.bindings(e,n)},should_show:function(e){var n=t.extend({},this.settings,this.data_options(e));return"all"===n.show_on?!0:this.small()&&"small"===n.show_on?!0:this.medium()&&"medium"===n.show_on?!0:this.large()&&"large"===n.show_on?!0:!1},medium:function(){return matchMedia(Foundation.media_queries.medium).matches},large:function(){return matchMedia(Foundation.media_queries.large).matches},events:function(e){function n(t,e,n){t.timer||(n?(t.timer=null,s.showTip(e)):t.timer=setTimeout(function(){t.timer=null,s.showTip(e)}.bind(t),s.settings.hover_delay))}function i(t,e){t.timer&&(clearTimeout(t.timer),t.timer=null),s.hide(e)}var s=this,a=s.S;s.create(this.S(e)),t(this.scope).off(".tooltip").on("mouseenter.fndtn.tooltip mouseleave.fndtn.tooltip touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip","["+this.attr_name()+"]",function(e){var r=a(this),o=t.extend({},s.settings,s.data_options(r)),l=!1;if(Modernizr.touch&&/touchstart|MSPointerDown/i.test(e.type)&&a(e.target).is("a"))return!1;if(/mouse/i.test(e.type)&&s.ie_touch(e))return!1;if(r.hasClass("open"))Modernizr.touch&&/touchstart|MSPointerDown/i.test(e.type)&&e.preventDefault(),s.hide(r);else{if(o.disable_for_touch&&Modernizr.touch&&/touchstart|MSPointerDown/i.test(e.type))return;if(!o.disable_for_touch&&Modernizr.touch&&/touchstart|MSPointerDown/i.test(e.type)&&(e.preventDefault(),a(o.tooltip_class+".open").hide(),l=!0,t(".open["+s.attr_name()+"]").length>0)){var c=a(t(".open["+s.attr_name()+"]")[0]);s.hide(c)}/enter|over/i.test(e.type)?n(this,r):"mouseout"===e.type||"mouseleave"===e.type?i(this,r):n(this,r,!0)}}).on("mouseleave.fndtn.tooltip touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip","["+this.attr_name()+"].open",function(e){return/mouse/i.test(e.type)&&s.ie_touch(e)?!1:void(("touch"!=t(this).data("tooltip-open-event-type")||"mouseleave"!=e.type)&&("mouse"==t(this).data("tooltip-open-event-type")&&/MSPointerDown|touchstart/i.test(e.type)?s.convert_to_touch(t(this)):i(this,t(this))))}).on("DOMNodeRemoved DOMAttrModified","["+this.attr_name()+"]:not(a)",function(){i(this,a(this))})},ie_touch:function(){return!1},showTip:function(t){var e=this.getTip(t);return this.should_show(t,e)?this.show(t):void 0},getTip:function(e){var n=this.selector(e),i=t.extend({},this.settings,this.data_options(e)),s=null;return n&&(s=this.S('span[data-selector="'+n+'"]'+i.tooltip_class)),"object"==typeof s?s:!1},selector:function(t){var e=t.attr(this.attr_name())||t.attr("data-selector");return"string"!=typeof e&&(e=this.random_str(6),t.attr("data-selector",e).attr("aria-describedby",e)),e},create:function(n){var i=this,s=t.extend({},this.settings,this.data_options(n)),a=this.settings.tip_template;"string"==typeof s.tip_template&&e.hasOwnProperty(s.tip_template)&&(a=e[s.tip_template]);var r=t(a(this.selector(n),t("<div></div>").html(n.attr("title")).html())),o=this.inheritable_classes(n);r.addClass(o).appendTo(s.append_to),Modernizr.touch&&(r.append('<span class="tap-to-close">'+s.touch_close_text+"</span>"),r.on("touchstart.fndtn.tooltip MSPointerDown.fndtn.tooltip",function(){i.hide(n)})),n.removeAttr("title").attr("title","")},reposition:function(e,n,i){var s,a,r,o,l;if(n.css("visibility","hidden").show(),s=e.data("width"),a=n.children(".nub"),r=a.outerHeight(),o=a.outerHeight(),n.css(this.small()?{width:"100%"}:{width:s?s:"auto"}),l=function(t,e,n,i,s){return t.css({top:e?e:"auto",bottom:i?i:"auto",left:s?s:"auto",right:n?n:"auto"}).end()},l(n,e.offset().top+e.outerHeight()+10,"auto","auto",e.offset().left),this.small())l(n,e.offset().top+e.outerHeight()+10,"auto","auto",12.5,t(this.scope).width()),n.addClass("tip-override"),l(a,-r,"auto","auto",e.offset().left);else{var c=e.offset().left;Foundation.rtl&&(a.addClass("rtl"),c=e.offset().left+e.outerWidth()-n.outerWidth()),l(n,e.offset().top+e.outerHeight()+10,"auto","auto",c),a.attr("style")&&a.removeAttr("style"),n.removeClass("tip-override"),i&&i.indexOf("tip-top")>-1?(Foundation.rtl&&a.addClass("rtl"),l(n,e.offset().top-n.outerHeight(),"auto","auto",c).removeClass("tip-override")):i&&i.indexOf("tip-left")>-1?(l(n,e.offset().top+e.outerHeight()/2-n.outerHeight()/2,"auto","auto",e.offset().left-n.outerWidth()-r).removeClass("tip-override"),a.removeClass("rtl")):i&&i.indexOf("tip-right")>-1&&(l(n,e.offset().top+e.outerHeight()/2-n.outerHeight()/2,"auto","auto",e.offset().left+e.outerWidth()+r).removeClass("tip-override"),a.removeClass("rtl"))}n.css("visibility","visible").hide()},small:function(){return matchMedia(Foundation.media_queries.small).matches&&!matchMedia(Foundation.media_queries.medium).matches},inheritable_classes:function(e){var n=t.extend({},this.settings,this.data_options(e)),i=["tip-top","tip-left","tip-bottom","tip-right","radius","round"].concat(n.additional_inheritable_classes),s=e.attr("class"),a=s?t.map(s.split(" "),function(e){return-1!==t.inArray(e,i)?e:void 0}).join(" "):"";return t.trim(a)},convert_to_touch:function(e){var n=this,i=n.getTip(e),s=t.extend({},n.settings,n.data_options(e));0===i.find(".tap-to-close").length&&(i.append('<span class="tap-to-close">'+s.touch_close_text+"</span>"),i.on("click.fndtn.tooltip.tapclose touchstart.fndtn.tooltip.tapclose MSPointerDown.fndtn.tooltip.tapclose",function(){n.hide(e)})),e.data("tooltip-open-event-type","touch")},show:function(t){var e=this.getTip(t);"touch"==t.data("tooltip-open-event-type")&&this.convert_to_touch(t),this.reposition(t,e,t.attr("class")),t.addClass("open"),e.fadeIn(150)},hide:function(t){var e=this.getTip(t);e.fadeOut(150,function(){e.find(".tap-to-close").remove(),e.off("click.fndtn.tooltip.tapclose MSPointerDown.fndtn.tapclose"),t.removeClass("open")})},off:function(){var e=this;this.S(this.scope).off(".fndtn.tooltip"),this.S(this.settings.tooltip_class).each(function(n){t("["+e.attr_name()+"]").eq(n).attr("title",t(this).text())}).remove()},reflow:function(){}}}(jQuery,window,window.document),function(t,e,n){"use strict";Foundation.libs.topbar={name:"topbar",version:"5.5.2",settings:{index:0,start_offset:0,sticky_class:"sticky",custom_back_text:!0,back_text:"Back",mobile_show_parent_link:!0,is_hover:!0,scrolltop:!0,sticky_on:"all",dropdown_autoclose:!0},init:function(e,n,i){Foundation.inherit(this,"add_custom_rule register_media throttle");var s=this;s.register_media("topbar","foundation-mq-topbar"),this.bindings(n,i),s.S("["+this.attr_name()+"]",this.scope).each(function(){{var e=t(this),n=e.data(s.attr_name(!0)+"-init");s.S("section, .top-bar-section",this)}e.data("index",0);var i=e.parent();i.hasClass("fixed")||s.is_sticky(e,i,n)?(s.settings.sticky_class=n.sticky_class,s.settings.sticky_topbar=e,e.data("height",i.outerHeight()),e.data("stickyoffset",i.offset().top)):e.data("height",e.outerHeight()),n.assembled||s.assemble(e),n.is_hover?s.S(".has-dropdown",e).addClass("not-click"):s.S(".has-dropdown",e).removeClass("not-click"),s.add_custom_rule(".f-topbar-fixed { padding-top: "+e.data("height")+"px }"),i.hasClass("fixed")&&s.S("body").addClass("f-topbar-fixed")})},is_sticky:function(t,e,n){var i=e.hasClass(n.sticky_class),s=matchMedia(Foundation.media_queries.small).matches,a=matchMedia(Foundation.media_queries.medium).matches,r=matchMedia(Foundation.media_queries.large).matches;return i&&"all"===n.sticky_on?!0:i&&this.small()&&-1!==n.sticky_on.indexOf("small")&&s&&!a&&!r?!0:i&&this.medium()&&-1!==n.sticky_on.indexOf("medium")&&s&&a&&!r?!0:i&&this.large()&&-1!==n.sticky_on.indexOf("large")&&s&&a&&r?!0:!1},toggle:function(n){var i,s=this;i=n?s.S(n).closest("["+this.attr_name()+"]"):s.S("["+this.attr_name()+"]");var a=i.data(this.attr_name(!0)+"-init"),r=s.S("section, .top-bar-section",i);s.breakpoint()&&(s.rtl?(r.css({right:"0%"}),t(">.name",r).css({right:"100%"})):(r.css({left:"0%"}),t(">.name",r).css({left:"100%"})),s.S("li.moved",r).removeClass("moved"),i.data("index",0),i.toggleClass("expanded").css("height","")),a.scrolltop?i.hasClass("expanded")?i.parent().hasClass("fixed")&&(a.scrolltop?(i.parent().removeClass("fixed"),i.addClass("fixed"),s.S("body").removeClass("f-topbar-fixed"),e.scrollTo(0,0)):i.parent().removeClass("expanded")):i.hasClass("fixed")&&(i.parent().addClass("fixed"),i.removeClass("fixed"),s.S("body").addClass("f-topbar-fixed")):(s.is_sticky(i,i.parent(),a)&&i.parent().addClass("fixed"),i.parent().hasClass("fixed")&&(i.hasClass("expanded")?(i.addClass("fixed"),i.parent().addClass("expanded"),s.S("body").addClass("f-topbar-fixed")):(i.removeClass("fixed"),i.parent().removeClass("expanded"),s.update_sticky_positioning())))},timer:null,events:function(){var n=this,i=this.S;i(this.scope).off(".topbar").on("click.fndtn.topbar","["+this.attr_name()+"] .toggle-topbar",function(t){t.preventDefault(),n.toggle(this)}).on("click.fndtn.topbar contextmenu.fndtn.topbar",'.top-bar .top-bar-section li a[href^="#"],['+this.attr_name()+'] .top-bar-section li a[href^="#"]',function(){var e=t(this).closest("li"),i=e.closest("["+n.attr_name()+"]"),s=i.data(n.attr_name(!0)+"-init");if(s.dropdown_autoclose&&s.is_hover){var a=t(this).closest(".hover");a.removeClass("hover")}!n.breakpoint()||e.hasClass("back")||e.hasClass("has-dropdown")||n.toggle()}).on("click.fndtn.topbar","["+this.attr_name()+"] li.has-dropdown",function(e){var s=i(this),a=i(e.target),r=s.closest("["+n.attr_name()+"]"),o=r.data(n.attr_name(!0)+"-init");
return a.data("revealId")?void n.toggle():void(n.breakpoint()||(!o.is_hover||Modernizr.touch)&&(e.stopImmediatePropagation(),s.hasClass("hover")?(s.removeClass("hover").find("li").removeClass("hover"),s.parents("li.hover").removeClass("hover")):(s.addClass("hover"),t(s).siblings().removeClass("hover"),"A"===a[0].nodeName&&a.parent().hasClass("has-dropdown")&&e.preventDefault())))}).on("click.fndtn.topbar","["+this.attr_name()+"] .has-dropdown>a",function(t){if(n.breakpoint()){t.preventDefault();var e=i(this),s=e.closest("["+n.attr_name()+"]"),a=s.find("section, .top-bar-section"),r=(e.next(".dropdown").outerHeight(),e.closest("li"));s.data("index",s.data("index")+1),r.addClass("moved"),n.rtl?(a.css({right:-(100*s.data("index"))+"%"}),a.find(">.name").css({right:100*s.data("index")+"%"})):(a.css({left:-(100*s.data("index"))+"%"}),a.find(">.name").css({left:100*s.data("index")+"%"})),s.css("height",e.siblings("ul").outerHeight(!0)+s.data("height"))}}),i(e).off(".topbar").on("resize.fndtn.topbar",n.throttle(function(){n.resize.call(n)},50)).trigger("resize.fndtn.topbar").load(function(){i(this).trigger("resize.fndtn.topbar")}),i("body").off(".topbar").on("click.fndtn.topbar",function(t){var e=i(t.target).closest("li").closest("li.hover");e.length>0||i("["+n.attr_name()+"] li.hover").removeClass("hover")}),i(this.scope).on("click.fndtn.topbar","["+this.attr_name()+"] .has-dropdown .back",function(t){t.preventDefault();var e=i(this),s=e.closest("["+n.attr_name()+"]"),a=s.find("section, .top-bar-section"),r=(s.data(n.attr_name(!0)+"-init"),e.closest("li.moved")),o=r.parent();s.data("index",s.data("index")-1),n.rtl?(a.css({right:-(100*s.data("index"))+"%"}),a.find(">.name").css({right:100*s.data("index")+"%"})):(a.css({left:-(100*s.data("index"))+"%"}),a.find(">.name").css({left:100*s.data("index")+"%"})),0===s.data("index")?s.css("height",""):s.css("height",o.outerHeight(!0)+s.data("height")),setTimeout(function(){r.removeClass("moved")},300)}),i(this.scope).find(".dropdown a").focus(function(){t(this).parents(".has-dropdown").addClass("hover")}).blur(function(){t(this).parents(".has-dropdown").removeClass("hover")})},resize:function(){var t=this;t.S("["+this.attr_name()+"]").each(function(){var e,i=t.S(this),s=i.data(t.attr_name(!0)+"-init"),a=i.parent("."+t.settings.sticky_class);if(!t.breakpoint()){var r=i.hasClass("expanded");i.css("height","").removeClass("expanded").find("li").removeClass("hover"),r&&t.toggle(i)}t.is_sticky(i,a,s)&&(a.hasClass("fixed")?(a.removeClass("fixed"),e=a.offset().top,t.S(n.body).hasClass("f-topbar-fixed")&&(e-=i.data("height")),i.data("stickyoffset",e),a.addClass("fixed")):(e=a.offset().top,i.data("stickyoffset",e)))})},breakpoint:function(){return!matchMedia(Foundation.media_queries.topbar).matches},small:function(){return matchMedia(Foundation.media_queries.small).matches},medium:function(){return matchMedia(Foundation.media_queries.medium).matches},large:function(){return matchMedia(Foundation.media_queries.large).matches},assemble:function(e){var n=this,i=e.data(this.attr_name(!0)+"-init"),s=n.S("section, .top-bar-section",e);s.detach(),n.S(".has-dropdown>a",s).each(function(){var e,s=n.S(this),a=s.siblings(".dropdown"),r=s.attr("href");a.find(".title.back").length||(e=t(1==i.mobile_show_parent_link&&r?'<li class="title back js-generated"><h5><a href="javascript:void(0)"></a></h5></li><li class="parent-link hide-for-medium-up"><a class="parent-link js-generated" href="'+r+'">'+s.html()+"</a></li>":'<li class="title back js-generated"><h5><a href="javascript:void(0)"></a></h5>'),t("h5>a",e).html(1==i.custom_back_text?i.back_text:"&laquo; "+s.html()),a.prepend(e))}),s.appendTo(e),this.sticky(),this.assembled(e)},assembled:function(e){e.data(this.attr_name(!0),t.extend({},e.data(this.attr_name(!0)),{assembled:!0}))},height:function(e){var n=0,i=this;return t("> li",e).each(function(){n+=i.S(this).outerHeight(!0)}),n},sticky:function(){var t=this;this.S(e).on("scroll",function(){t.update_sticky_positioning()})},update_sticky_positioning:function(){var t="."+this.settings.sticky_class,n=this.S(e),i=this;if(i.settings.sticky_topbar&&i.is_sticky(this.settings.sticky_topbar,this.settings.sticky_topbar.parent(),this.settings)){var s=this.settings.sticky_topbar.data("stickyoffset")+this.settings.start_offset;i.S(t).hasClass("expanded")||(n.scrollTop()>s?i.S(t).hasClass("fixed")||(i.S(t).addClass("fixed"),i.S("body").addClass("f-topbar-fixed")):n.scrollTop()<=s&&i.S(t).hasClass("fixed")&&(i.S(t).removeClass("fixed"),i.S("body").removeClass("f-topbar-fixed")))}},off:function(){this.S(this.scope).off(".fndtn.topbar"),this.S(e).off(".fndtn.topbar")},reflow:function(){}}}(jQuery,window,window.document),function(){$(function(){return setTimeout(function(){return $("#phonePopupModal").foundation("reveal","open")},2e3)})}.call(this);