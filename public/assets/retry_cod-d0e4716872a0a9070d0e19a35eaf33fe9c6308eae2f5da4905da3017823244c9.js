(function() {
  $(function() {
    return ga('send', 'event', {
      eventCategory: 'Checkout',
      eventAction: 'prepaid-popup-view',
      nonInteraction: true
    });
  });

  $(function() {
    document.getElementById('retry').addEventListener('submit', function(evt) {
      $('input[type=\'submit\']').attr('disabled', true);
      $('#retry_button').val('Please wait...');
      ga('send', 'event', {
        eventCategory: 'Checkout',
        eventAction: 'prepaid-popup-retry',
        nonInteraction: true
      });
      return true;
    });
    return document.getElementById('cod').addEventListener('submit', function(evt) {
      $('#cod_button').val('Please wait...');
      $('input[type=\'submit\']').attr('disabled', true);
      ga('send', 'event', {
        eventCategory: 'Checkout',
        eventAction: 'prepaid-popup-cod',
        nonInteraction: true
      });
      return true;
    });
  });

}).call(this);
