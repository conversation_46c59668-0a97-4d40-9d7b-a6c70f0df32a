(function(){"[object OperaMini]"===Object.prototype.toString.call(window.operamini)&&$(document).ready(function(){var t,e,a;return $("li.tabs > .tab-title").click(function(){var t,e;return e=$(".tabs-content > .content.active").attr("id"),$("#"+e).removeClass("active"),t=$($(this)[0].children[0]).attr("href"),$(t).addClass("active"),$("li.tabs > .tab-title.active").removeClass("active"),$(this).addClass("active")}),$(".addon_types").on("change",function(){var t;return t=$(this).attr("value"),$(this).find("option").each(function(){var e;return e=$(this).val(),e!==t&&$("#atv_"+e).hide(),$("#atv_"+e).show()})}),/cart/i.test(window.location.href)&&$(".item_block").length>0&&(a=$(".cart_checkout"),e=$(".add_place_order").attr("href"),a.hide(),t=$(".add_cont_shop"),t.css({background:"none","margin-top":"-7px","font-weight":"bold"}),t.before('<a class="opera_checkout_position_fix button small success" href="'+e+'">CHECKOUT</a>')),$(".sort_filter").length>0?($(".sort_filter").removeClass("fixed"),$(".sort_filter").addClass("opera_footer_fix")):void 0})}).call(this);