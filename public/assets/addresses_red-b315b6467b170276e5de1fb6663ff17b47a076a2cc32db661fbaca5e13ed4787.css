/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 7, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
#container {
  margin-top: 0.5em !important;
  padding-top: 1% !important;
}
/* line 10, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
#container #address_collect_submit {
  font-size: 0.875rem;
  font-weight: bold;
  background: #670B19 !important;
  box-shadow: 0 2px 4px 0 #828282;
  height: 45px;
  padding-top: 10px;
}

/* line 20, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.off-canvas-wrap .with-menu {
  margin-top: 4rem !important;
}

/* line 23, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.address_field_div {
  display: none;
}

/* line 26, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address, .shipping_address {
  padding: 0 8px;
}
/* line 28, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address label, .shipping_address label {
  color: #303030;
  display: none;
}
/* line 32, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address input[type="text"], .billing_address input[type="password"], .billing_address input[type="email"], .billing_address input[type="number"], .billing_address input[type="tel"], .billing_address input[type="time"], .billing_address textarea, .billing_address select, .shipping_address input[type="text"], .shipping_address input[type="password"], .shipping_address input[type="email"], .shipping_address input[type="number"], .shipping_address input[type="tel"], .shipping_address input[type="time"], .shipping_address textarea, .shipping_address select {
  margin: 0 0 0.5em 0;
  border-radius: 2px;
  background-color: #FFF;
  border: 1px solid #E0E0E0;
  outline: none;
}
/* line 38, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address input[type="text"]:focus, .billing_address input[type="text"]:hover, .billing_address input[type="password"]:focus, .billing_address input[type="password"]:hover, .billing_address input[type="email"]:focus, .billing_address input[type="email"]:hover, .billing_address input[type="number"]:focus, .billing_address input[type="number"]:hover, .billing_address input[type="tel"]:focus, .billing_address input[type="tel"]:hover, .billing_address input[type="time"]:focus, .billing_address input[type="time"]:hover, .billing_address textarea:focus, .billing_address textarea:hover, .billing_address select:focus, .billing_address select:hover, .shipping_address input[type="text"]:focus, .shipping_address input[type="text"]:hover, .shipping_address input[type="password"]:focus, .shipping_address input[type="password"]:hover, .shipping_address input[type="email"]:focus, .shipping_address input[type="email"]:hover, .shipping_address input[type="number"]:focus, .shipping_address input[type="number"]:hover, .shipping_address input[type="tel"]:focus, .shipping_address input[type="tel"]:hover, .shipping_address input[type="time"]:focus, .shipping_address input[type="time"]:hover, .shipping_address textarea:focus, .shipping_address textarea:hover, .shipping_address select:focus, .shipping_address select:hover {
  background-color: #FFF;
  border: 1px solid #000000;
  box-shadow: 0px 0px 0px 0px;
}
/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address input[type="text"]::-webkit-input-placeholder, .billing_address input[type="text"]::-moz-placeholder, .billing_address input[type="text"]:-ms-input-placeholder, .billing_address input[type="text"]:-moz-placeholder, .billing_address input[type="password"]::-webkit-input-placeholder, .billing_address input[type="password"]::-moz-placeholder, .billing_address input[type="password"]:-ms-input-placeholder, .billing_address input[type="password"]:-moz-placeholder, .billing_address input[type="email"]::-webkit-input-placeholder, .billing_address input[type="email"]::-moz-placeholder, .billing_address input[type="email"]:-ms-input-placeholder, .billing_address input[type="email"]:-moz-placeholder, .billing_address input[type="number"]::-webkit-input-placeholder, .billing_address input[type="number"]::-moz-placeholder, .billing_address input[type="number"]:-ms-input-placeholder, .billing_address input[type="number"]:-moz-placeholder, .billing_address input[type="tel"]::-webkit-input-placeholder, .billing_address input[type="tel"]::-moz-placeholder, .billing_address input[type="tel"]:-ms-input-placeholder, .billing_address input[type="tel"]:-moz-placeholder, .billing_address input[type="time"]::-webkit-input-placeholder, .billing_address input[type="time"]::-moz-placeholder, .billing_address input[type="time"]:-ms-input-placeholder, .billing_address input[type="time"]:-moz-placeholder, .billing_address textarea::-webkit-input-placeholder, .billing_address textarea::-moz-placeholder, .billing_address textarea:-ms-input-placeholder, .billing_address textarea:-moz-placeholder, .billing_address select::-webkit-input-placeholder, .billing_address select::-moz-placeholder, .billing_address select:-ms-input-placeholder, .billing_address select:-moz-placeholder, .shipping_address input[type="text"]::-webkit-input-placeholder, .shipping_address input[type="text"]::-moz-placeholder, .shipping_address input[type="text"]:-ms-input-placeholder, .shipping_address input[type="text"]:-moz-placeholder, .shipping_address input[type="password"]::-webkit-input-placeholder, .shipping_address input[type="password"]::-moz-placeholder, .shipping_address input[type="password"]:-ms-input-placeholder, .shipping_address input[type="password"]:-moz-placeholder, .shipping_address input[type="email"]::-webkit-input-placeholder, .shipping_address input[type="email"]::-moz-placeholder, .shipping_address input[type="email"]:-ms-input-placeholder, .shipping_address input[type="email"]:-moz-placeholder, .shipping_address input[type="number"]::-webkit-input-placeholder, .shipping_address input[type="number"]::-moz-placeholder, .shipping_address input[type="number"]:-ms-input-placeholder, .shipping_address input[type="number"]:-moz-placeholder, .shipping_address input[type="tel"]::-webkit-input-placeholder, .shipping_address input[type="tel"]::-moz-placeholder, .shipping_address input[type="tel"]:-ms-input-placeholder, .shipping_address input[type="tel"]:-moz-placeholder, .shipping_address input[type="time"]::-webkit-input-placeholder, .shipping_address input[type="time"]::-moz-placeholder, .shipping_address input[type="time"]:-ms-input-placeholder, .shipping_address input[type="time"]:-moz-placeholder, .shipping_address textarea::-webkit-input-placeholder, .shipping_address textarea::-moz-placeholder, .shipping_address textarea:-ms-input-placeholder, .shipping_address textarea:-moz-placeholder, .shipping_address select::-webkit-input-placeholder, .shipping_address select::-moz-placeholder, .shipping_address select:-ms-input-placeholder, .shipping_address select:-moz-placeholder {
  color: #828282;
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address select, .shipping_address select {
  background-image: url(/assets/down-52fb740af66b26a8b01106af7a8d8d928de02b6c5163d16c9c60e1eec1d05586.png) !important;
  background-size: 10px;
}
/* line 51, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address #dial_code, .shipping_address #dial_code {
  margin-top: 15%;
}
/* line 54, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address .save_btn, .shipping_address .save_btn {
  background-color: #0e9a7d;
}
/* line 57, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address table, .shipping_address table {
  background: inherit;
  border: none;
  margin-bottom: 0em;
}
/* line 62, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address table tr:nth-of-type(even), .shipping_address table tr:nth-of-type(even) {
  background: inherit;
}
/* line 65, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address table tr td, .shipping_address table tr td {
  color: inherit;
  padding: 0.1em;
}
/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address a, .shipping_address a {
  margin-bottom: 0em;
}
/* line 74, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.billing_address .shipping_confirm_checkbox, .shipping_address .shipping_confirm_checkbox {
  display: block;
  margin-top: 1%;
}

/* line 79, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
input[type='submit'] {
  margin-top: 1em;
}

/* line 83, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.payment_options label {
  color: inherit;
}

/* line 88, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.payment_btn .cont_payment {
  background-color: #0e9a7d;
}

/* line 92, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/addresses_red.scss */
.pincode_format {
  margin: 0px 0px 5px 0px;
  margin-left: inherit;
  font-size: 12px;
  color: #303030;
  padding-left: 0.7em;
}
