(function() {
  var a, addToCart, checkAddons, checkVariantSelection, div, form, h1, h5, img, li, option, ref, select, sendAddToCartGa, table, td, toCamelCase, tr, ul;

  ref = React.DOM, div = ref.div, h1 = ref.h1, ul = ref.ul, li = ref.li, a = ref.a, form = ref.form, select = ref.select, option = ref.option, table = ref.table, tr = ref.tr, td = ref.td, h5 = ref.h5, img = ref.img;

  this.DesignImages = React.createClass({
    componentDidMount: function() {
      return $('#design_images').slick({
        dots: true,
        infinite: true,
        speed: 300,
        slidesToShow: 1,
        adaptiveHeight: true
      });
    },
    render: function() {
      return div({
        id: 'design_images'
      }, this.props.images.length > 0 ? this.props.images.map(function(designImage, i) {
        return div({
          key: 'image_' + i,
          'data-lightbox': 'image-set',
          href: "//" + (designImage.sizes ? designImage.sizes.original : '/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg')
        }, img({
          className: 'design_image',
          src: "//" + (designImage.sizes ? designImage.sizes.small : '/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg')
        }));
      }) : div({
        id: 'design_images'
      }, div({
        'data-lightbox': 'image-set',
        href: "/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg"
      }, img({
        className: 'design_image',
        src: "/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg"
      }))));
    }
  });

  this.DesignTitle = React.createClass({
    render: function() {
      return h1({
        id: 'design_title'
      }, this.props.title);
    }
  });

  this.DesignPrice = React.createClass({
    render: function() {
      if (this.props.discount_percent > 0) {
        return div({
          className: 'product_design_price'
        }, div({
          className: 'row'
        }, div({
          className: 'small-4 columns percent_disc text-center'
        }, this.props.discount_percent + '% OFF'), div({
          className: 'small-8 columns line_through_text'
        }, this.props.price)), ul({
          className: 'no-bullet'
        }, li({
          className: 'product_discount_price'
        }, this.props.discount_price)));
      } else {
        return div({
          className: 'row product_design_price'
        }, ul({
          className: 'no-bullet columns'
        }, li({
          className: 'product_discount_price'
        }, this.props.price)));
      }
    }
  });

  this.DesignVariants = React.createClass({
    render: function() {
      return div({
        id: 'variants_block'
      }, this.props.variants.map(function(variant) {
        var variant_class;
        variant_class = 'variant';
        if (variant.quantity <= 0) {
          variant_class += ' line_through_text disabled';
        }
        return variant.option_type_values.map(function(option_type_value) {
          return React.createElement(DesignVariantList, {
            id: variant.id,
            class_name: variant_class,
            options: option_type_value.name
          });
        });
      }));
    }
  });

  this.DesignVariantList = React.createClass({
    on_click: function(e) {
      if ($(e.target).hasClass('disabled') === false) {
        $('.variant').removeClass('selected alert');
        return $(e.target).addClass('selected alert');
      }
    },
    render: function() {
      return a({
        id: this.props.id,
        onClick: this.on_click,
        className: this.props.class_name + ' button secondary round tiny'
      }, this.props.options);
    }
  });

  this.DesignAddons = React.createClass({
    componentDidMount: function() {
      return $('.addon_option_values').hide();
    },
    render: function() {
      var currency;
      currency = this.props.currency;
      return form({
        id: 'design_addon_form'
      }, this.props.addon_types.map(function(addon_type, index) {
        return div({
          className: 'row'
        }, React.createElement(DesignAddonTypeValues, {
          id: 'addon_type_' + index,
          title: addon_type.name,
          options: addon_type.addon_type_values,
          currency: currency
        }));
      }));
    }
  });

  this.DesignAddonTypeValues = React.createClass({
    render: function() {
      var currency;
      currency = this.props.currency;
      return div({
        className: 'columns'
      }, this.props.title, React.createElement(AddonSelectList, {
        id: this.props.id,
        options: this.props.options,
        type: 'atv',
        currency: currency,
        class_name: 'addon_types',
        key: 'atv_' + this.props.id
      }), this.props.options.map(function(at_option) {
        return div({
          className: 'row'
        }, div({
          className: 'small-10 small-offset-1 medium-10 medium-offset-1 columns addon_option_values',
          id: 'atv_' + at_option.id,
          key: 'atv_' + at_option.id
        }, at_option.addon_option_types.map(function(aot_key, index) {
          var key_name;
          key_name = aot_key.p_name;
          return div({
            className: 'row',
            key: key_name + '_main'
          }, div({
            className: 'columns',
            key: key_name
          }, key_name, React.createElement(AddonSelectList, {
            id: 'atov_' + index,
            options: aot_key.addon_option_values,
            currency: currency,
            key: 'atov_' + index,
            default_option: key_name
          })));
        })));
      }));
    }
  });

  this.AddonSelectList = React.createClass({
    getInitialState: function() {
      if (this.props.type === 'atv') {
        return {
          value: this.props.options[0].id
        };
      } else {
        return {
          value: 0
        };
      }
    },
    change: function(event) {
      var atv_block, prod_time, select_list, select_val, today;
      select_list = event.target;
      select_val = select_list.value;
      this.setState({
        value: select_val
      });
      if (select_list.id.match(/addon_type/)) {
        atv_block = '#atv_' + select_val;
        $('.addon_option_values').hide();
        $(atv_block).show();
        prod_time = $('#' + select_list.id + ' option:selected').data('prodtime');
        if (prod_time !== 'undefined') {
          today = new Date();
          today.setDate(today.getDate() + prod_time);
          return $('#delivery_date').html(today.toDateString());
        }
      }
    },
    render: function() {
      var currency, list_type;
      currency = this.props.currency;
      list_type = this.props.type;
      return select({
        id: this.props.id,
        value: this.state.value,
        className: this.props.class_name,
        onChange: this.change
      }, list_type !== 'atv' ? option({
        value: 0
      }, this.props.default_option) : void 0, this.props.options.map(function(at_option) {
        var option_value, prod_time;
        option_value = at_option.p_name;
        prod_time = '';
        if (at_option.price) {
          option_value += ' : ' + currency + ' ' + at_option.price;
        }
        if (at_option.prod_time) {
          prod_time = at_option.prod_time;
        } else if (list_type === 'atv') {
          prod_time = '0';
        }
        return option({
          value: at_option.id,
          'data-prodtime': prod_time
        }, option_value);
      }));
    }
  });

  this.DesignSpecification = React.createClass({
    render: function() {
      var designables, keys, properties, specifications;
      specifications = this.props.specifications;
      properties = specifications.properties;
      designables = specifications.designable;
      keys = ["product_id", "specification"];
      return table({
        className: 'specifications-table',
        id: 'two'
      }, keys.map(function(key, index) {
        return React.createElement(TableRows, {
          id: 'specification_' + index,
          value: specifications[key],
          index: index,
          key_name: key,
          key: 'specification_' + index
        });
      }), properties.map(function(key, index) {
        if (key.value !== []) {
          return React.createElement(TableRows, {
            id: 'properties_' + index,
            value: key.value,
            index: index,
            key_name: key.type,
            key: 'properties_' + index
          });
        }
      }), designables.map(function(key, index) {
        if (key.value !== '') {
          return React.createElement(TableRows, {
            id: 'designable_' + index,
            value: key.value,
            index: index,
            key_name: key.type,
            key: 'designable_' + index
          });
        }
      }));
    }
  });

  this.TableRows = React.createClass({
    render: function() {
      return tr({
        id: this.props.id
      }, td({}, toCamelCase(this.props.key_name)), td({}, this.props.value));
    }
  });

  this.DesignPolicies = React.createClass({
    render: function() {
      return div({
        className: 'policy_content'
      }, this.props.policies);
    }
  });

  this.ActionButtons = React.createClass({
    render: function() {
      return ul({
        className: 'small-block-grid-2 medium-block-grid-2'
      }, this.props.action_buttons.map(function(action_button, index) {
        return React.createElement(ActionButton, {
          key: 'ab_' + index,
          class_name: action_button.class_name,
          link: action_button.link,
          name: action_button.name
        });
      }));
    }
  });

  checkVariantSelection = function() {
    var status;
    status = true;
    if ($('.variant').length > 0) {
      status = false;
      if ($('.variant.selected').length === 1) {
        status = true;
      }
    }
    if (status === false) {
      alert('Please select options');
    }
    return status;
  };

  checkAddons = function() {
    var status;
    status = true;
    $('.addon_types').each(function() {
      var addon_option_id, option_value_select;
      addon_option_id = '#atv_' + this.value;
      if ($(addon_option_id).length > 0) {
        option_value_select = $('select', addon_option_id);
        option_value_select.each(function() {
          if (this.value === '0') {
            return status = false;
          }
        });
        if (status === false) {
          return option_value_select.css({
            'border-color': 'red',
            'border-width': '3px'
          });
        } else {
          return option_value_select.css({
            'border-color': '',
            'border-width': ''
          });
        }
      }
    });
    if (status === false) {
      alert('Please select highlighted options');
    }
    return status;
  };

  sendAddToCartGa = function(ga_hash) {
    ga('ec:addProduct', {
      'id': ga_hash.id,
      'name': ga_hash.name,
      'category': ga_hash.category,
      'brand': ga_hash.brand,
      'price': ga_hash.price,
      'quantity': ga_hash.quantity
    });
    ga('ec:setAction', 'add');
    ga('send', 'event', 'UX', 'click', 'add to cart');
    window._pq = window._pq || [];
    return _pq.push(['track', 'add_to_cart']);
  };

  addToCart = function(url) {
    var design_id, line_item_addons_attributes;
    design_id = $('#product_id').html().split(' ').pop();
    line_item_addons_attributes = [];
    $('.addon_types').each(function(index) {
      var addon_attributes, atv_block, atv_id, notes, option_value_select;
      notes = '';
      atv_id = $(this).val();
      atv_block = $('#atv_' + atv_id);
      addon_attributes = {};
      if (atv_block.length > 0) {
        option_value_select = $('select', atv_block);
        option_value_select.each(function() {
          return notes = notes + $('option:first', this).text() + ' : ' + $('option:selected', this).text() + ', ';
        });
      }
      addon_attributes['addon_type_value_id'] = atv_id;
      addon_attributes['notes'] = notes;
      return line_item_addons_attributes.push(addon_attributes);
    });
    return $.ajax({
      url: url,
      type: 'POST',
      dataType: 'json',
      data: {
        line_items: {
          design_id: design_id,
          quantity: 1,
          line_item_addons_attributes: line_item_addons_attributes,
          variant_id: $('.variant.selected').attr('id')
        },
        design_page: true
      },
      success: function(response) {
        sendAddToCartGa(response.ga_hash);
        return window.location.href = response.redirect_url;
      }
    });
  };

  this.ActionButton = React.createClass({
    logUnbxd: function(e) {
      return Unbxd.track("addToCart", {
        "pid": $('#product_id').html().split(' ').pop()
      });
    },
    on_click: function(e) {
      e.preventDefault();
      if (checkAddons() === true && checkVariantSelection() === true) {
        return addToCart(e.target.href);
      }
    },
    render: function() {
      return li({
        className: 'action_button_btn',
        onClick: this.on_click
      }, a({
        className: this.props.class_name,
        href: this.props.link,
        method: this.props.method,
        onClick: this.logUnbxd
      }, this.props.name));
    }
  });

  toCamelCase = function(str) {
    var arr, full_text, i;
    full_text = [];
    arr = str.replace("_", " ").split(" ");
    i = 0;
    while (i < arr.length) {
      full_text.push(arr[i].charAt(0).toUpperCase() + arr[i].substr(1).toLowerCase());
      ++i;
    }
    return full_text.join(" ");
  };

  this.SimilarDesigns = React.createClass({
    componentDidMount: function() {
      return $('#similar_designs').slick({
        dots: true,
        arrows: false,
        slidesToShow: 4,
        slidesToScroll: 4,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3
            }
          }, {
            breakpoint: 600,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2
            }
          }
        ]
      });
    },
    render: function() {
      var similar_design;
      return div({
        id: 'similar_designs'
      }, (function() {
        var j, len, ref1, results;
        if (this.props.data.designs) {
          ref1 = this.props.data.designs;
          results = [];
          for (j = 0, len = ref1.length; j < len; j++) {
            similar_design = ref1[j];
            results.push(a({
              href: similar_design.design_path
            }, img({
              className: '',
              src: "//" + (similar_design.sizes ? similar_design.sizes.small : '/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg'),
              alt: "Buy " + similar_design.title + " online"
            })));
          }
          return results;
        }
      }).call(this));
    }
  });

}).call(this);
