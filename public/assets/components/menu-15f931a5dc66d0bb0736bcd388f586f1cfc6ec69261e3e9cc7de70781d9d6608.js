var Menu = React.createClass({
  displayName: 'Menu',

  render: function () {
    if (typeof this.props.menus === 'undefined') {
      return true;
    }
    var list_items = [];
    if (!this.props.account_signed_in) {
      list_items.push(React.createElement(SignInLink, null));
    }
    for (menu in this.props.menus) {
      if (this.props.menus[menu].hasOwnProperty('menu_columns')) {
        var subMenuClass = 'has-submenu';
      }
      if (this.props.menus[menu].title.toLowerCase() === 'more') {
        this.props.menus[menu].preposition = 'Shop';
      } else {
        this.props.menus[menu].preposition = 'All';
      }
      list_items.push(React.createElement(
        'li',
        { className: subMenuClass },
        React.createElement(
          'a',
          { href: '#' },
          this.props.menus[menu].title
        ),
        React.createElement(
          'ul',
          { className: 'left-submenu' },
          React.createElement(
            'li',
            { className: 'back' },
            React.createElement(
              'a',
              { href: '#' },
              'Back'
            )
          ),
          React.createElement(
            'li',
            null,
            React.createElement(
              'a',
              { href: this.props.menus[menu].link },
              ' ',
              this.props.menus[menu].preposition,
              ' ',
              this.props.menus[menu].title
            )
          ),
          this.props.menus[menu].menu_columns.map(function (menu_column, i) {
            var subMenu;
            if (menu_column.title != '') {
              subMenu = React.createElement(
                MenuColumn,
                { key: i, title: menu_column.title },
                React.createElement(
                  'li',
                  null,
                  React.createElement(
                    'a',
                    { href: this.props.menus[menu].link },
                    ' ',
                    this.props.menus[menu].preposition,
                    ' ',
                    this.props.menus[menu].title
                  )
                ),
                menu_column.menu_items.map(function (menu_item, j) {
                  return React.createElement(
                    MenuItem,
                    { key: j },
                    menu_item
                  );
                })
              );
            } else {
              subMenu = [React.createElement(
                'li',
                null,
                React.createElement(
                  'a',
                  { href: this.props.menus[menu].link },
                  ' ',
                  this.props.menus[menu].preposition,
                  ' ',
                  this.props.menus[menu].title
                )
              )];
              subMenu.concat(menu_column.menu_items.map(function (menu_item, j) {
                return React.createElement(
                  MenuItem,
                  { key: j },
                  menu_item
                );
              }, this));
            }
            return { subMenu: subMenu };
          }, this)
        )
      ));
    }
    if (this.props.account_signed_in) {
      list_items.push(React.createElement(
        'li',
        null,
        React.createElement(
          'label',
          null,
          'Account'
        )
      ));
      for (var link in this.props.accountRelatedLinks) list_items.push(React.createElement(
        AccountRelatedLinks,
        null,
        this.props.accountRelatedLinks[link]
      ));
    }
    return React.createElement(
      'aside',
      { className: 'left-off-canvas-menu' },
      React.createElement(
        'ul',
        { className: 'off-canvas-list' },
        list_items
      )
    );
  }
});

var MenuColumn = React.createClass({
  displayName: 'MenuColumn',

  render: function () {
    return React.createElement(
      'li',
      { className: 'has-submenu' },
      React.createElement(
        'a',
        { href: '#' },
        this.props.title
      ),
      React.createElement(
        'ul',
        { className: 'left-submenu' },
        React.createElement(
          'li',
          { className: 'back' },
          React.createElement(
            'a',
            { href: '#' },
            'Back'
          )
        ),
        this.props.children
      )
    );
  }
});

var MenuItem = React.createClass({
  displayName: 'MenuItem',

  render: function () {
    return React.createElement(
      'li',
      null,
      React.createElement(
        'a',
        { href: this.props.children.link },
        this.props.children.title
      )
    );
  }
});

var SignInLink = React.createClass({
  displayName: 'SignInLink',

  render: function () {
    return React.createElement(
      'li',
      null,
      React.createElement(
        'a',
        { href: '/accounts/sign_in' },
        'Sign In'
      )
    );
  }
});

var AccountRelatedLinks = React.createClass({
  displayName: 'AccountRelatedLinks',

  render: function () {
    if (this.props.children.hasOwnProperty('data-method')) return React.createElement(
      'li',
      null,
      React.createElement(
        'a',
        { href: this.props.children.link, 'data-method': this.props.children['data-method'] },
        this.props.children.label
      )
    );else return React.createElement(
      'li',
      null,
      React.createElement(
        'a',
        { href: this.props.children.link },
        this.props.children.label
      )
    );
  }
});