(function() {
  var a, div, h5, img, li, ref, ul;

  ref = React.DOM, div = ref.div, img = ref.img, ul = ref.ul, li = ref.li, a = ref.a, h5 = ref.h5;

  this.BannerSlider = React.createClass({
    componentDidMount: function() {
      return $('#banner_slider').slick({
        dots: true,
        autoplay: true,
        autoplaySpeed: 3000,
        arrows: false
      });
    },
    render: function() {
      var slide;
      return div({
        key: 'bannerslider',
        id: 'banner_slider'
      }, (function() {
        var i, len, ref1, results;
        if (this.props.banner_slides) {
          ref1 = this.props.banner_slides;
          results = [];
          for (i = 0, len = ref1.length; i < len; i++) {
            slide = ref1[i];
            results.push(a({
              href: slide.link
            }, img({
              className: 'slick-slider-img',
              src: '//' + slide.photo,
              onError: defaultLoadImg
            })));
          }
          return results;
        }
      }).call(this));
    }
  });

  this.Frontpage = React.createClass({
    render: function() {
      var block;
      return ul({
        key: 'frontpage',
        className: 'home_page small-block-grid-2 medium-block-grid-3 large-block-grid-4'
      }, (function() {
        var i, len, ref1, results;
        if (this.props.blocks) {
          ref1 = this.props.blocks;
          results = [];
          for (i = 0, len = ref1.length; i < len; i++) {
            block = ref1[i];
            results.push(li({
              className: 'frontpage-caption'
            }, div({
              className: 'fr_page'
            }, a({
              href: block.link
            }, img({
              className: '',
              src: '//' + block.photo.sizes.main,
              alt: "Buy " + block.name + " online"
            }), div({
              className: 'panel text-center truncate'
            }, h5, block.name)))));
          }
          return results;
        }
      }).call(this));
    }
  });

  this.FeatureProducts = React.createClass({
    componentDidMount: function() {
      return $('#feature_products').slick({
        dots: true,
        arrows: false,
        slidesToShow: 4,
        slidesToScroll: 4,
        responsive: [
          {
            breakpoint: 1024,
            settings: {
              slidesToShow: 3,
              slidesToScroll: 3
            }
          }, {
            breakpoint: 600,
            settings: {
              slidesToShow: 2,
              slidesToScroll: 2
            }
          }
        ]
      });
    },
    render: function() {
      var feature_product;
      return div({
        key: 'featureproduct',
        id: 'feature_products'
      }, (function() {
        var i, len, ref1, results;
        if (this.props.feature_products) {
          ref1 = this.props.feature_products;
          results = [];
          for (i = 0, len = ref1.length; i < len; i++) {
            feature_product = ref1[i];
            results.push(a({
              href: feature_product.design_path
            }, img({
              className: '',
              src: '//' + feature_product.image_url,
              alt: "Buy " + feature_product.title + " online",
              onError: defaultLoadImg
            })));
          }
          return results;
        }
      }).call(this));
    }
  });

}).call(this);
