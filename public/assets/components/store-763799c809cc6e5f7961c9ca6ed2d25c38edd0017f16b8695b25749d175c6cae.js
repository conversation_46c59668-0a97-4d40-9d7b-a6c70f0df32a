(function() {
  var a, applySlick, div, form, h1, hr, img, input, label, li, p, ref, ul;

  applySlick = function() {
    return $('#specification_tabs.filters_tabs').slick({
      variableWidth: true,
      infinite: false,
      centerMode: false,
      slidesToShow: 4,
      slidesToScroll: 1,
      arrows: false,
      setPosition: true,
      responsive: [
        {
          breakpoint: 479
        }
      ]
    });
  };

  ref = React.DOM, div = ref.div, img = ref.img, ul = ref.ul, li = ref.li, a = ref.a, h1 = ref.h1, hr = ref.hr, p = ref.p, input = ref.input, label = ref.label, form = ref.form;

  this.Filters = React.createClass({
    getInitialState: function() {
      return {
        selectedFilters: '',
        tab_active: 'active',
        valueString: '',
        filters: this.props.data.filters
      };
    },
    updateSelectedFilters: function() {
      this.state.selectedFilters = $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').map(function() {
        return "#" + this.id;
      }).get().join(', ');
      return this.setValueString();
    },
    applyFilters: function() {
      var url;
      url = (window.location.href.replace(/\?.*/, '')) + "?" + this.state.valueString;
      return window.location.href = url;
    },
    clearFilters: function() {
      if (this.state.valueString !== '') {
        return window.location.replace('');
      } else {
        return $('#filterModal').foundation('reveal', 'close');
      }
    },
    setValueString: function() {
      $('.on-off-radiobox input:radio:checked').each(function() {
        $("#" + ($(this).data('min_input'))).val($(this).data('min'));
        return $("#" + ($(this).data('max_input'))).val($(this).data('max'));
      });
      this.state.valueString = $('.search-box-margin form, input:hidden.range_filter_input, .on-off-checkbox input:checkbox:checked').serialize();
      return this.getSearchResult();
    },
    getSearchResult: function() {
      var _this, url;
      url = (window.location.href.replace(/\?.*/, '')) + "?" + this.state.valueString;
      _this = this;
      return $.getJSON(url, function(response) {
        if (!(response && Object.keys(response).length)) {
          return true;
        }
        if ($('#specification_tabs.filters_tabs.slick-slider')) {
          $('#specification_tabs.filters_tabs').slick('unslick');
        }
        _this.setState({
          filters: response.filters
        });
        $("" + _this.state.selectedFilters).each(function() {
          return $(this).prop('checked', 'true');
        });
        $(document).foundation('tab', 'reflow');
        return applySlick();
      });
    },
    render: function() {
      var id_filter, index, list, range_filter, range_index;
      return div({
        className: 'row panel_block',
        id: 'designable_details'
      }, div({
        className: 'columns panel_heading'
      }, ul({
        className: 'filters_tabs',
        id: 'specification_tabs'
      }, (function() {
        var i, len, ref1, results;
        ref1 = this.state.filters.range_filters;
        results = [];
        for (range_index = i = 0, len = ref1.length; i < len; range_index = ++i) {
          range_filter = ref1[range_index];
          results.push(li({
            className: 'tabs',
            'data-tab': ''
          }, div({
            className: "tab-title " + (range_index === 0 ? 'active' : void 0)
          }, a({
            className: "button radius tiny",
            href: '#tab' + range_index
          }, range_filter.name))));
        }
        return results;
      }).call(this), (function() {
        var i, len, ref1, results;
        ref1 = this.state.filters.id_filters;
        results = [];
        for (index = i = 0, len = ref1.length; i < len; index = ++i) {
          id_filter = ref1[index];
          results.push(li({
            className: 'tabs',
            'data-tab': ''
          }, div({
            className: 'tab-title'
          }, a({
            className: 'button radius tiny',
            href: '#tab' + (range_index + index)
          }, id_filter.name))));
        }
        return results;
      }).call(this))), div({
        className: 'columns panel_content'
      }, div({
        className: 'tabs-content'
      }, (function() {
        var i, len, ref1, results;
        ref1 = this.state.filters.range_filters;
        results = [];
        for (range_index = i = 0, len = ref1.length; i < len; range_index = ++i) {
          range_filter = ref1[range_index];
          results.push(div({
            id: 'tab' + range_index,
            className: "content " + (range_index === 0 ? 'active' : void 0)
          }, input({
            className: 'range_filter_input',
            id: range_filter.keys.min,
            type: 'hidden',
            name: range_filter.keys.min
          }), input({
            className: 'range_filter_input',
            id: range_filter.keys.max,
            type: 'hidden',
            name: range_filter.keys.max
          }), (function() {
            var j, len1, ref2, results1;
            ref2 = range_filter.list;
            results1 = [];
            for (j = 0, len1 = ref2.length; j < len1; j++) {
              list = ref2[j];
              results1.push(div({
                className: 'on-off-radiobox'
              }, input({
                name: range_filter.name + "_range",
                value: '',
                type: 'radio',
                id: (range_filter.name.replace(/\s/g, '_')) + "_" + list.values.min + "-" + list.values.max,
                'data-min_input': range_filter.keys.min,
                'data-max_input': range_filter.keys.max,
                'data-min': list.values.min,
                'data-max': list.values.max,
                onChange: this.updateSelectedFilters
              }), label({
                htmlFor: (range_filter.name.replace(/\s/g, '_')) + "_" + list.values.min + "-" + list.values.max
              }, list.name + " [" + list.count + "]")));
            }
            return results1;
          }).call(this)));
        }
        return results;
      }).call(this), (function() {
        var i, len, ref1, results;
        ref1 = this.state.filters.id_filters;
        results = [];
        for (index = i = 0, len = ref1.length; i < len; index = ++i) {
          id_filter = ref1[index];
          results.push(div({
            id: 'tab' + (range_index + index),
            className: 'content'
          }, (function() {
            var j, len1, ref2, results1;
            ref2 = id_filter.list;
            results1 = [];
            for (j = 0, len1 = ref2.length; j < len1; j++) {
              list = ref2[j];
              results1.push(div({
                className: 'on-off-checkbox'
              }, input({
                key: list.value,
                "class": id_filter.name,
                name: id_filter.key + "[]",
                id: id_filter.key + "_" + list.value,
                type: 'checkbox',
                value: list.value,
                onChange: this.updateSelectedFilters
              }), label({
                htmlFor: id_filter.key + "_" + list.value
              }, list.name + " [" + list.count + "]")));
            }
            return results1;
          }).call(this)));
        }
        return results;
      }).call(this))), div({
        className: 'columns short_filter_btn'
      }, div({
        className: 'btn_apply button success small-12',
        onClick: this.applyFilters
      }, 'Apply'), div({
        className: 'btn_clear button secondary small-12',
        onClick: this.clearFilters
      }, 'Clear')));
    }
  });

  window.onpopstate = function(event) {
    window.back_click = true;
    return window.addEventListener('load', function() {
      if ($(".previous").length > 0) {
        return $('.previous')[0].click();
      } else if ($(".next").length > 0) {
        return $('.next')[0].click();
      }
    });
  };

  this.Store = React.createClass({
    componentDidMount: function() {
      this.addToCart();
      this.sortDesigns();
      return this.hideShowButton();
    },
    componentDidUpdate: function() {
      var new_path, node;
      node = $('.off-canvas-wrap #main-section, .main-section');
      node.push($(document));
      node.each(function() {
        return $(this).scrollTop(node.offset().top);
      });
      new_path = window.location.href.split('?')[0];
      if (typeof back_click === "undefined" || back_click === false) {
        window.history.pushState({
          path: new_path
        }, '', store_param);
        return window.back_click = false;
      }
    },
    addToCart: function() {
      var _this;
      _this = this;
      return $('.store_block').on('click', '.add_to_cart_link', function(e) {
        var design_id;
        design_id = $(this).attr('id');
        return _this.createLineItem(design_id);
      });
    },
    hideShowButton: function() {
      var lastScrollTop;
      lastScrollTop = 0;
      return $('body #main-section').scroll(function(event) {
        var st;
        st = $(this).scrollTop();
        if (st > lastScrollTop) {
          $('#action_buttons').fadeOut(100);
        } else {
          $('#action_buttons').fadeIn(100);
        }
        return lastScrollTop = st;
      });
    },
    createLineItem: function(design_id) {
      return $.ajax({
        type: 'POST',
        dataType: 'json',
        url: '/line_items',
        data: {
          line_items: {
            design_id: design_id
          }
        },
        success: function(response) {
          if (response.url === void 0) {
            $('#cart_count').html(response.cart_count);
            return window.location.href = '/cart';
          } else if (response.cart_count === void 0) {
            return window.location.href = response.url;
          }
        }
      });
    },
    getInitialState: function() {
      return {
        designs: this.props.data.designs,
        prevPage: this.props.data.previous_page,
        page: this.props.data.next_page,
        loadingFlag: false
      };
    },
    defaultLoadImg: function(img, event) {
      return img.target.src = "/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg";
    },
    handleScroll: function(e) {
      if ($('.page_view_port:in-viewport').length && !this.state.loadingFlag) {
        this.setState({
          loadingFlag: true
        });
        return this.getDesigns();
      }
    },
    sortDesigns: function() {
      var _this;
      _this = this;
      return $('#shortModal').on('change', function() {
        var selectedValue, url;
        selectedValue = $("#shortModal option:selected").val();
        $('#shortModal').foundation('reveal', 'close');
        if (window.location.href.indexOf('?') === -1) {
          url = window.location.href + '?sort=' + selectedValue;
        } else {
          url = window.location.href.replace(/&?sort=[^\&]+/, '') + '&sort=' + selectedValue;
        }
        return window.location.replace(url);
      });
    },
    getDesigns: function(e) {
      var _this, pageNumber, url;
      pageNumber = $(e.target).attr('id').split('_')[1];
      if (window.location.href.indexOf('?') === -1) {
        url = window.location.href + '?page=' + pageNumber;
      } else {
        url = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + pageNumber;
      }
      _this = this;
      window.store_param = '?' + url.split('?').pop();
      return $.getJSON(url, function(result) {
        if (result && result.designs.length > 27) {
          if (ga) {
            ga('send', 'pageview', url);
          }
          return _this.setState({
            designs: result.designs,
            loadingFlag: false,
            page: result.next_page,
            prevPage: result.previous_page
          });
        } else {
          return _this.setState({
            designs: [],
            loadingFlag: false,
            page: result.next_page,
            prevPage: result.previous_page
          });
        }
      });
    },
    logImpressionToUnbxd: function(e) {
      var elementData;
      elementData = $(e.target).data();
      return Unbxd.track('click', {
        'pid': elementData.unbxdparam_sku,
        'prank': elementData.unbxdparam_prank
      });
    },
    render: function() {
      var block, index;
      return div({
        className: 'store_block'
      }, div({
        "class": 'heading_title'
      }, h1({
        className: 'product-title'
      }, this.props.store_page.title), p({
        className: 'product_count'
      }, this.props.data.results + " Products")), hr({}), ul({
        className: 'store_page_design small-block-grid-2 medium-block-grid-3 large-block-grid-4'
      }, (function() {
        var i, len, ref1, results;
        ref1 = (this.state.designs.length > 0 ? this.state.designs : this.props.data.designs);
        results = [];
        for (index = i = 0, len = ref1.length; i < len; index = ++i) {
          block = ref1[index];
          results.push(li({
            key: 'storepage_' + index
          }, div({
            className: 'fr_page'
          }, a({
            key: 'link' + index,
            'data-unbxdattr': 'product',
            'data-unbxdparam_sku': block.id,
            'data-unbxdparam_prank': index + 1,
            onClick: this.logImpressionToUnbxd,
            href: block.design_path
          }, img({
            key: 'img_' + index,
            className: 'error_img',
            src: "//" + (block.sizes ? block.sizes.small : '/assets/default_image-e51fdf96b8cb41312e62aeef1566b4793087e1c405f6b6bce99b1facab2fc56f.jpg'),
            alt: "Buy " + block.title + " " + block.category_name + " online",
            onError: this.defaultLoadImg
          })), div({
            className: 'panel design_desc'
          }, div({
            key: 'text_trim' + index,
            className: 'truncate'
          }, a({
            href: block.design_path
          }, block.title)), div({
            id: 'design_details',
            className: 'details_block'
          }, div({
            className: 'design-col1'
          }, div({
            className: 'design_price'
          }, ("" + this.props.data.symbol) + " " + block.discount_price), div({
            className: 'discount_font line_through_text'
          }, ("" + this.props.data.symbol) + " " + block.price)), div({
            className: 'design-col2'
          }, div({
            className: 'discount_new_wrap'
          }, block.discount_percent + '% OFF'))), block.state === 'in_stock' ? div({
            id: block.id,
            className: 'add_to_cart_link button tiny add_new_pos'
          }, 'ADD TO CART') : div({
            className: 'sold_out_link button disabled tiny alert'
          }, 'SOLD OUT')))));
        }
        return results;
      }).call(this), hr({}), div({
        className: 'navigate_page text-center'
      }, this.state.prevPage ? a({
        className: 'button secondary nav-button tiny previous',
        id: "page_" + this.state.prevPage,
        onClick: this.getDesigns
      }, 'Previous') : void 0, div, this.state.page ? a({
        className: 'button nav-button tiny next',
        id: "page_" + this.state.page,
        onClick: this.getDesigns
      }, 'Next') : void 0)));
    }
  });

}).call(this);
