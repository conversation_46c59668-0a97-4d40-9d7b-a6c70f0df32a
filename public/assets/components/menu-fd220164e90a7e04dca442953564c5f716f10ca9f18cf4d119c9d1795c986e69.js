var Menu=React.createClass({displayName:"Menu",render:function(){if("undefined"==typeof this.props.menus)return!0;var e=[];this.props.account_signed_in||e.push(React.createElement(SignInLink,null));for(menu in this.props.menus){if(this.props.menus[menu].hasOwnProperty("menu_columns"))var t="has-submenu";this.props.menus[menu].preposition="more"===this.props.menus[menu].title.toLowerCase()?"Shop":"All",e.push(React.createElement("li",{className:t},React.createElement("a",{href:"#"},this.props.menus[menu].title),React.createElement("ul",{className:"left-submenu"},React.createElement("li",{className:"back"},React.createElement("a",{href:"#"},"Back")),React.createElement("li",null,React.createElement("a",{href:this.props.menus[menu].link}," ",this.props.menus[menu].preposition," ",this.props.menus[menu].title)),this.props.menus[menu].menu_columns.map(function(e,t){var n;return""!=e.title?n=React.createElement(MenuColumn,{key:t,title:e.title},React.createElement("li",null,React.createElement("a",{href:this.props.menus[menu].link}," ",this.props.menus[menu].preposition," ",this.props.menus[menu].title)),e.menu_items.map(function(e,t){return React.createElement(MenuItem,{key:t},e)})):(n=[React.createElement("li",null,React.createElement("a",{href:this.props.menus[menu].link}," ",this.props.menus[menu].preposition," ",this.props.menus[menu].title))],n.concat(e.menu_items.map(function(e,t){return React.createElement(MenuItem,{key:t},e)},this))),{subMenu:n}},this))))}if(this.props.account_signed_in){e.push(React.createElement("li",null,React.createElement("label",null,"Account")));for(var n in this.props.accountRelatedLinks)e.push(React.createElement(AccountRelatedLinks,null,this.props.accountRelatedLinks[n]))}return React.createElement("aside",{className:"left-off-canvas-menu"},React.createElement("ul",{className:"off-canvas-list"},e))}}),MenuColumn=React.createClass({displayName:"MenuColumn",render:function(){return React.createElement("li",{className:"has-submenu"},React.createElement("a",{href:"#"},this.props.title),React.createElement("ul",{className:"left-submenu"},React.createElement("li",{className:"back"},React.createElement("a",{href:"#"},"Back")),this.props.children))}}),MenuItem=React.createClass({displayName:"MenuItem",render:function(){return React.createElement("li",null,React.createElement("a",{href:this.props.children.link},this.props.children.title))}}),SignInLink=React.createClass({displayName:"SignInLink",render:function(){return React.createElement("li",null,React.createElement("a",{href:"/accounts/sign_in"},"Sign In"))}}),AccountRelatedLinks=React.createClass({displayName:"AccountRelatedLinks",render:function(){return this.props.children.hasOwnProperty("data-method")?React.createElement("li",null,React.createElement("a",{href:this.props.children.link,"data-method":this.props.children["data-method"]},this.props.children.label)):React.createElement("li",null,React.createElement("a",{href:this.props.children.link},this.props.children.label))}});