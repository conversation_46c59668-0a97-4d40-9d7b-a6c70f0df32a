(function(){var e,r,s,t,a,n,o;n=React.DOM,r=n.div,t=n.img,o=n.ul,a=n.li,e=n.a,s=n.h5,this.BannerSlider=React.createClass({componentDidMount:function(){return $("#banner_slider").slick({dots:!0,autoplay:!0,autoplaySpeed:3e3,arrows:!1})},render:function(){var s;return r({key:"bannerslider",id:"banner_slider"},function(){var r,a,n,o;if(this.props.banner_slides){for(n=this.props.banner_slides,o=[],r=0,a=n.length;a>r;r++)s=n[r],o.push(e({href:s.link},t({className:"slick-slider-img",src:"//"+s.photo,onError:defaultLoadImg})));return o}}.call(this))}}),this.Frontpage=React.createClass({render:function(){var n;return o({key:"frontpage",className:"home_page small-block-grid-2 medium-block-grid-3 large-block-grid-4"},function(){var o,i,l,c;if(this.props.blocks){for(l=this.props.blocks,c=[],o=0,i=l.length;i>o;o++)n=l[o],c.push(a({className:"frontpage-caption"},r({className:"fr_page"},e({href:n.link},t({className:"",src:"//"+n.photo.sizes.main,alt:"Buy "+n.name+" online"}),r({className:"panel text-center truncate"},s,n.name)))));return c}}.call(this))}}),this.FeatureProducts=React.createClass({componentDidMount:function(){return $("#feature_products").slick({dots:!0,arrows:!1,slidesToShow:4,slidesToScroll:4,responsive:[{breakpoint:1024,settings:{slidesToShow:3,slidesToScroll:3}},{breakpoint:600,settings:{slidesToShow:2,slidesToScroll:2}}]})},render:function(){var s;return r({key:"featureproduct",id:"feature_products"},function(){var r,a,n,o;if(this.props.feature_products){for(n=this.props.feature_products,o=[],r=0,a=n.length;a>r;r++)s=n[r],o.push(e({href:s.design_path},t({className:"",src:"//"+s.image_url,alt:"Buy "+s.title+" online",onError:defaultLoadImg})));return o}}.call(this))}})}).call(this);