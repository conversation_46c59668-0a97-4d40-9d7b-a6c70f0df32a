(function() {
  var MeasurementExperienceForm, StitchingForm;

  $(function() {
    var checkBoxCheckEvent, paramGetMeasurmentForm;
    if ($('#login_modal').length > 0) {
      $('#login_modal').foundation('reveal', 'open');
    }
    $('#close_modal').click(function() {
      return $('#login_modal').foundation('reveal', 'close');
    });
    $('#inches,#feet').change(function() {
      var feet, in_cms, in_inches, inches;
      inches = parseInt($('#inches').val());
      feet = parseInt($('#feet').val());
      if (!isNaN(inches)) {
        in_inches = feet * 12 + inches;
        in_cms = Math.round(in_inches * 2.54 * 100) / 100;
        $('#height').val(feet + "." + inches);
        return $('#conversion_height').html("In Inches: " + in_inches + " inches  In Cms: " + in_cms + " cms");
      } else {
        return $('#conversion_height').html('');
      }
    });
    $('#weight').keyup(function() {
      var in_pounds, weight;
      weight = parseInt($(this).val());
      if (weight < 0) {
        $(this).val(0);
      }
      if (weight > 200) {
        $(this).val(200);
      }
      weight = parseInt($(this).val());
      in_pounds = Math.round(weight * 2.20 * 100) / 100;
      if (!isNaN(weight)) {
        return $('#conversion_weight').html("In Pounds: " + in_pounds);
      }
    });
    $('#measurement_data_1').on('submit', function(e) {
      var form_data;
      e.preventDefault();
      if ($('#weight').val() === '') {
        return alert('Weight is required');
      } else if ($('#weight').val() < 20) {
        return alert('Weight Should be more than 20');
      } else {
        form_data = $(this).serialize();
        return $.ajax(paramGetMeasurmentForm(form_data));
      }
    });
    $('#measurement_select').change(function() {
      var feet, form_data, inches;
      inches = parseInt($('#inches').val());
      feet = parseInt($('#feet').val());
      $('#height').val(parseFloat(feet + "." + inches));
      form_data = $('#measurement_data_1').serialize();
      return $.ajax(paramGetMeasurmentForm(form_data));
    });
    paramGetMeasurmentForm = function(form_data) {
      return {
        url: 'stitching_measurement/measurement_data',
        type: 'GET',
        data: form_data,
        datatype: 'script',
        beforeSend: function() {
          return $('#loadingImage').show();
        },
        complete: function() {
          return $('#loadingImage').hide();
        },
        success: function(data, success, jqhxr) {
          $(document).foundation();
          return $('.example-orbit').css('height', '200px');
        }
      };
    };
    $(document).on('click', '.similar-img-check', function() {
      var id;
      id = $(this).data('id');
      return checkBoxCheckEvent(id);
    });
    return checkBoxCheckEvent = function(id) {
      var check_box;
      check_box = $("#item_" + id);
      if (check_box.is(':checked')) {
        check_box[0].checked = false;
        $("#check_" + id).removeClass('selected_style');
        return $('.item_checkbox:checked').each(function() {
          var item_id;
          item_id = $(this).val();
          return $("#check_" + item_id).addClass('selected_style');
        });
      } else {
        check_box[0].checked = true;
        return $('.item_checkbox:checked').each(function() {
          id = $(this).val();
          return $("#check_" + id).addClass('selected_style');
        });
      }
    };
  });

  MeasurementExperienceForm = (function() {
    var bindings, disable, enableSubmit, experienceIndex;

    bindings = function() {
      this.$form.on('submit', (function(event) {
        event.preventDefault();
        disable.call(this);
        return this.callback(experienceIndex.call(this));
      }).bind(this));
      return this.$form.find('.measurement-experience-options').on('change', 'input[type=radio]', (function() {
        return enableSubmit.call(this);
      }).bind(this));
    };

    experienceIndex = function() {
      return this.$form.find('.measurement-experience-options input[type=radio]:checked').val();
    };

    disable = function() {
      return this.$form.hide();
    };

    enableSubmit = function() {
      return this.$form.find('input[type=submit]').removeAttr('disabled');
    };

    function MeasurementExperienceForm($form) {
      this.$form = $form;
      bindings.call(this);
    }

    MeasurementExperienceForm.prototype.afterSubmit = function(callback) {
      return this.callback = callback;
    };

    return MeasurementExperienceForm;

  })();

  StitchingForm = (function() {
    var bindings, disable, enable, housekeeping, scrollToForm, setMeasurementExperience;

    bindings = function() {
      return this.measurementExperienceForm.afterSubmit((function(experienceIndex) {
        setMeasurementExperience.call(this, experienceIndex);
        return enable.call(this);
      }).bind(this));
    };

    enable = function() {
      this.$container.show();
      return scrollToForm.call(this);
    };

    disable = function() {
      return this.$container.hide();
    };

    scrollToForm = function() {
      return $('html, body').animate({
        scrollTop: 0
      }, 300);
    };

    setMeasurementExperience = function(experience) {
      return this.$form.find('input[type=hidden][name=measurement_experience]').val(experience);
    };

    housekeeping = function() {
      disable.call(this);
      return bindings.call(this);
    };

    function StitchingForm($container, measurementExperienceForm) {
      this.$container = $container;
      this.$form = this.$container.find('#measurement_data_1');
      this.measurementExperienceForm = measurementExperienceForm;
      housekeeping.call(this);
    }

    return StitchingForm;

  })();

  $(function() {
    return new StitchingForm($('ul.accordion'), new MeasurementExperienceForm($('.measurement-experience')));
  });

}).call(this);
