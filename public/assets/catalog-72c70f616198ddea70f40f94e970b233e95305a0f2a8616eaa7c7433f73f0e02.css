/*Catalog Specific CSS */
/* line 6, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 11, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body {
  overflow: scroll !important;
}
/* line 13, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .heading_underline {
  text-decoration: underline;
}
/* line 16, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .line_through_text {
  text-decoration: line-through;
}
/* line 19, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .truncate {
  width: 100%;
  color: #383737;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 26, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design {
  margin: 0.1em;
}
/* line 28, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
  padding: 0 0.15rem 0.35rem;
}
/* line 34, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li img {
  width: 100%;
}
/* line 37, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .label-ribbon:before {
  font-family: "foundation-icons";
  content: "\f12b";
  color: #f1f1f1 !important;
  font-size: 16px;
  padding-right: 5px;
}
/* line 44, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .label-ribbon {
  border-color: #7d1438 !important;
  position: absolute;
  font-weight: 500;
  font-size: 12px;
  padding: 4px 0.5em 4px 0.5em;
  background-color: #d43153 !important;
  color: #f1f1f1 !important;
  display: inline-block;
  line-height: 1;
  margin: -16px 0px 0px -10px;
  border: 0 solid transparent;
  z-index: 1;
}
/* line 58, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .label-ribbon:after {
  position: absolute;
  content: '';
  top: 100%;
  left: 0;
  background-color: transparent !important;
  border-style: solid;
  border-width: 0px 0.78em 1em 0px;
  border-color: transparent;
  border-right-color: inherit;
  width: 0;
  height: 0;
}
/* line 72, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .b1g1.label-ribbon:before {
  content: '';
}
/* line 76, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page {
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 78, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page .panel {
  position: relative;
  padding: 0.4em 0.3em 0.5em 0.3em;
  background: #fffafa;
  border: 1px solid #fffafa;
}
/* line 83, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page .panel.design_desc {
  margin-bottom: 0em;
}
/* line 85, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page .panel.design_desc a {
  color: #383737;
}
/* line 89, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page .panel .add_to_cart_link {
  margin-bottom: 0.7em;
  width: 89%;
  bottom: 0em;
  color: #f1f1f1;
  background: #0e9a7d;
  position: absolute;
  padding: 10px 2px;
  box-shadow: 0px 3px 1px -1px #22756d;
  font-weight: 700;
}
/* line 100, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design > li .fr_page .panel .sold_out_link {
  margin-bottom: 0.7em;
  width: 89%;
  bottom: 0em;
  color: #f1f1f1;
  font-weight: 700;
}
/* line 111, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design li.original_price {
  font-size: 0.8em;
  color: white;
}
/* line 115, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design li.discount_price {
  font-weight: bold;
  color: white;
}
/* line 119, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design li.percent_off {
  color: red;
  margin-top: -1.7em;
  font-size: 0.9em;
}
/* line 125, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design .design_desc {
  padding: 0.5em;
}
/* line 127, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_design .design_desc li {
  padding-bottom: 0em;
}
/* line 132, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .postfix {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem;
  overflow: visible;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 2;
}
/* line 148, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .postfix.button {
  border-color: true;
}
/* line 152, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .postfix.button.radius {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 160, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .postfix.button.round {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}
/* line 168, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body span.postfix, body label.postfix {
  background: #f2f2f2;
  color: #333333;
  border-color: #cccccc;
}
/* line 174, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block {
  margin-bottom: 2em;
}
/* line 177, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #top_content {
  font-size: 0.9rem;
}
/* line 178, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #top_content.read-more {
  height: 25px;
  overflow: hidden;
}
/* line 184, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #top_content #hidden_content > * {
  font-size: 0.9rem;
}
/* line 188, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #top_content > * {
  font-size: 0.9rem;
}
/* line 193, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block a#view-more-top-content, body .store_page_block a#view-more-seo-post {
  color: #008CBA;
  font-size: 0.9rem;
  text-align: center;
}
/* line 199, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title {
  padding: 0.2rem 0rem;
  max-width: 100%;
}
/* line 202, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title .columns {
  padding: 0px;
}
/* line 205, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title .product-title {
  font-size: 1.2rem;
}
/* line 207, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title .product-title h1 {
  margin: 0px;
}
/* line 210, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title .product-title .product_count {
  font-size: 0.8rem;
}
/* line 214, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title #toggle-design-box {
  float: right;
  text-align: right;
}
/* line 217, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title #toggle-design-box span {
  font-size: 0.9rem;
  vertical-align: super;
}
/* line 221, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .heading_title #toggle-design-box i {
  font-size: 2rem;
  vertical-align: top;
}
/* line 227, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #category_links {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  padding: 0.3rem 0rem;
  max-width: 100%;
}
/* line 233, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #category_links .custom {
  background-color: #0e9a7d;
  /*border-color: #009688;*/
  color: #FFFFFF;
  font-size: 0.8rem;
  padding: 0.8em;
  margin-bottom: 0.3rem;
  box-shadow: 0px 3px 1px -1px #22756d;
}
/* line 243, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons {
  /*padding: 0 0.625rem;*/
}
/* line 245, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons #short_btn {
  padding: 0;
}
/* line 248, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons #filter_btn {
  padding: 0 0.625rem 0 0;
}
/* line 251, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons .select_box {
  width: 100%;
  overflow: hidden;
  position: relative;
  height: 50px;
}
/* line 256, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons .select_box #custom_sort {
  border: 2px solid #383737;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 10px;
  top: 0px;
  text-transform: uppercase;
  font-size: 12px;
  color: #383737;
  padding-top: 18px;
}
/* line 268, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons .select_box .form_input_select {
  width: 100%;
  font-size: 14px;
  border: 0;
  overflow: hidden;
  text-transform: none;
  background: 0 0;
  -webkit-appearance: none;
  opacity: 0;
  filter: alpha(opacity=0);
  margin-bottom: 12px;
}
/* line 281, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #action_buttons #filter-button {
  width: 100%;
  height: 50px;
  background: #e7e7e7;
  color: #383737;
  border: 2px solid #383737;
  font-size: 12px;
}
/* line 291, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #more-designs-loader {
  display: none;
  border: 4px dotted #fff;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}
/* line 300, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #load-more-designs-btn {
  display: none;
  font-size: 14px;
  border-radius: 2px;
  box-shadow: 0px 4px 2px #1b524d;
  cursor: pointer;
  background-color: #0e9a7d;
  border-color: #237363;
}
/* line 310, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .float-Btn {
  position: fixed;
  bottom: 16px;
  right: 12px;
  z-index: 19;
}
/* line 315, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block .float-Btn i:before {
  background-color: #d44777;
  width: 2.3rem;
  height: 2.3rem;
  line-height: 2.3rem;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  font-size: 1.5rem;
}
/* line 326, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #back-top {
  display: none;
}
/* line 330, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .store_page_block #toggle-design-box #toggle-design-icon {
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  transition: all 0.5s ease;
}
/* line 339, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .reveal-modal {
  overflow: scroll;
  height: 100%;
  top: 0px;
  bottom: 0px;
  margin-top: 0px;
  position: fixed;
  -webkit-overflow-scrolling: touch;
}
/* line 347, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .reveal-modal #loader {
  display: none;
  border: 10px dotted #fff;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  position: absolute;
  left: 43%;
  z-index: 9999;
  top: 44%;
  animation: spin 2s ease-in-out infinite;
}
/* line 361, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .reveal-modal-bg {
  background: #333333;
}
/* line 365, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details {
  border-radius: 0px;
  height: 100%;
}
/* line 368, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .chips_maker {
  background: #000000;
  padding: 0px 4px;
  display: none;
  border-radius: 2px;
}
/* line 373, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .chips_maker .chip {
  display: inline-block;
  padding: 2px 10px;
  height: 25px;
  margin: 3px 2px;
  font-size: 15px;
  line-height: 20px;
  border-radius: 16px;
  background-color: #607D8B;
}
/* line 382, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .chips_maker .chip .closebtn {
  padding-left: 10px;
  color: #FF9800;
  font-weight: bold;
  float: right;
  font-size: 20px;
  cursor: pointer;
}
/* line 392, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .panel_heading {
  padding: 0px;
}
/* line 395, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .panel_content.small-4 {
  padding: 0px 0px 59px 0px;
  height: 100%;
  overflow-y: scroll;
  border-radius: 0px;
  background-color: #71706f;
}
/* line 402, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content {
  padding-bottom: 59px;
  width: 66%;
  background-color: #fff;
  height: 100%;
  margin-bottom: 0px;
  overflow-y: scroll;
  border-radius: 0px;
}
/* line 410, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .active {
  padding-bottom: 0px;
}
/* line 413, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-radiobox {
  display: inline-block;
  margin-bottom: 1rem;
}
/* line 416, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-radiobox label {
  color: white;
}
/* line 421, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox {
  display: inline-block;
  margin-bottom: 1rem;
}
/* line 424, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox label {
  color: white;
}
/* line 429, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch {
  margin-bottom: 0.5rem !important;
  width: 25%;
}
/* line 432, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .color-input {
  visibility: hidden;
  width: 0;
}
/* line 436, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label {
  position: relative;
  left: 20px;
  bottom: 20px;
}
/* line 441, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label-custom-color {
  width: 30px;
  height: 30px;
  box-sizing: border-box;
  margin: 0px;
  position: relative;
  bottom: 5px;
  box-shadow: 1px 1px 2px #808080;
  border-radius: 4px;
}
/* line 451, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .label-custom-color:after {
  content: '\2713';
  display: block;
  position: absolute;
  bottom: -5px;
  left: 4px;
  opacity: 0;
  color: whitesmoke;
  font-size: 22px;
}
/* line 461, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch input:checked + .label-custom-color:after {
  opacity: 1;
}
/* line 464, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .on-off-checkbox.color-switch .multicolor-value {
  background: -webkit-linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
  background: linear-gradient(45deg, #edf1ee 0%, #8BC34A 25%, #FFEB3B 25%, #FFC107 40%, #FF5722 40%, #E91E63 60%, #673AB7 60%, #3F51B5 75%, #03A9F4 75%, #040404 100%);
}
/* line 470, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .switch.tiny label {
  height: 1rem;
  width: 2rem;
  background: #9e9696;
}
/* line 475, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .switch.tiny label:after {
  height: 0.5rem;
  width: 0.7rem;
  background: #464a46;
}
/* line 480, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .switch.tiny input {
  left: 8px;
  top: 5px;
}
/* line 484, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .switch.tiny input:checked + label {
  background: #43ac6a;
}
/* line 487, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .switch.tiny input:checked + label:after {
  left: 0.9rem;
}
/* line 492, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .label-custom {
  vertical-align: top;
  display: inline-block;
  width: 75%;
  float: right;
}
/* line 497, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .label-custom .label {
  font-size: 13px;
}
/* line 501, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .tabs-content .label-desktop-fix {
  width: 90%;
}
/* line 506, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details header .tab-filter-fix {
  font-size: 13px;
  background-color: #71706f;
  width: 100%;
  padding: 10px 0px 10px 5px;
  text-align: left;
  margin: 0px;
  line-height: 16px;
  font-weight: 700;
  text-transform: capitalize;
}
/* line 516, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details header .tab-filter-fix .tiny-green {
  border-radius: 50%;
  background: #43ac6a;
  color: black;
  padding: 2px 5px;
  margin-left: 3px;
  font-size: 10px;
  font-weight: 700;
}
/* line 526, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details header .active-tab {
  background-color: #5d5d5d;
}
/* line 531, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .short_filter_btn {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  box-shadow: 0 2px 34px -4px rgba(0, 0, 0, 0.7);
  background: #c1bbae;
}
/* line 538, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .short_filter_btn div {
  background-color: #c1bbae !important;
  color: #1f1c1c;
  margin: 0px;
  font-size: 16px;
  border-right: 1px solid #0c0c0c;
  padding: 18px 0px;
}
/* line 548, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #designable_details .filter-desktop-fix {
  bottom: 10px;
  left: 172px;
  width: 74%;
}
/* line 556, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .navigate_page {
  vertical-align: middle;
  margin-bottom: 1em;
}
/* line 561, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .nav-button {
  margin: 1em 1em 0 0;
}
/* line 565, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #design_details {
  width: 100%;
}
/* line 569, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .details_block {
  color: #383737;
  float: left;
  font-size: 15px;
}
/* line 575, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .discount_new_block {
  font-size: 12px;
  background-color: #e0356f;
  border-radius: 5%;
  padding: 6px 6px;
  line-height: 12px;
  text-align: center;
  position: relative;
  margin-top: -23px;
  float: right;
  color: #f1f1f1;
}
/* line 588, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .discount_new_wrap {
  padding: 1px;
}
/* line 592, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .add_new_pos {
  position: relative !important;
  width: 100% !important;
}
/* line 597, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .discount_font {
  font-size: 0.7em;
  font-weight: normal;
  float: left;
  color: #ffffff;
}
/* line 603, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .margin-down-5 {
  margin-bottom: 5px;
}
/* line 606, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body #search_desktop {
  display: none;
}
/* line 610, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
body .search_margin {
  margin-top: -15px !important;
}

/* line 615, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs {
  margin: 0.1em;
}
/* line 618, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs > li img {
  width: 100%;
}
/* line 621, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs > li .fr_page {
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  position: relative;
}
/* line 624, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs > li .fr_page .panel {
  height: 6.6em;
  position: relative;
  padding: 0.6em;
  background: #fffafa;
  border: 1px solid #fffafa;
}
/* line 631, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs > li .fr_page .panel.design_desc {
  margin-bottom: 0em;
}
/* line 633, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs > li .fr_page .panel.design_desc a {
  color: #383737;
  font-size: 14px;
}
/* line 642, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs li.original_price {
  font-size: 0.8em;
  color: #383737;
}
/* line 646, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs li.discount_price {
  font-weight: bold;
  color: #383737;
}
/* line 650, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs li.percent_off {
  color: red;
  margin-top: -1.7em;
  font-size: 0.9em;
}
/* line 656, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .design_desc {
  padding: 0.5em;
}
/* line 658, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .design_desc li {
  padding-bottom: 0em;
}
/* line 663, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs #design_details {
  width: 100%;
}
/* line 667, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .details_block {
  color: #383737;
}
/* line 671, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .design-col1 {
  float: left;
  width: 100%;
  font-size: 15px !important;
  /*font-weight: bold;*/
}
/* line 678, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .design-col2 {
  float: right;
  border-radius: 5%;
  font-size: 12px;
  background-color: #e0356f;
  color: #f1f1f1;
  padding: 6px 6px;
  line-height: 12px;
  text-align: center;
  position: relative;
  margin-top: -24px;
}
/* line 691, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .add-to-cart-bst {
  width: 100%;
  color: white;
  background: #0e9a7d;
  padding: 10px 2px;
  box-shadow: 0px 3px 1px -1px #22756d;
}
/* line 699, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .discount_new_wrap {
  text-align: center;
  word-wrap: break-word;
  padding-left: 0.5px;
}
/* line 705, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .add_new_pos {
  position: relative !important;
  width: 100% !important;
}
/* line 710, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .discount_font {
  font-size: 0.8em;
}
/* line 714, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.home_page_designs .design_price {
  font-weight: bold;
}

/* line 719, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info {
  /*border:1px dotted grey;*/
  padding: 8px;
  background-color: #fffafa;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 728, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post .seo-list-anchor {
  font-size: 14px;
  padding: 5px;
  line-height: 17px;
}
/* line 733, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post .seo-list-table {
  width: 70%;
  border: 1px solid black;
}
/* line 737, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post .seo-list-line-height {
  line-height: 30px;
}
/* line 740, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post .seo-list-font {
  font-size: 14px;
}
/* line 743, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post h2 {
  font-size: 1.5em;
  color: #383737;
}
/* line 747, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post p {
  font-size: 1em;
  color: #383737;
  text-align: justify;
}
/* line 752, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.footer-info #seo_post.read-more {
  height: 7.8em;
  overflow: hidden;
}

/* line 758, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.catalog-cert {
  position: absolute;
  width: 40px !important;
  height: 40px;
}

/* line 763, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.catalog-rating {
  position: relative;
  float: left;
  width: 40px !important;
  height: 40px;
  margin-top: -25px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media only screen and (min-width: 64.063em) {
  /* line 777, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
  .search_margin {
    display: none;
  }

  /* line 780, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
  #search_desktop {
    display: block;
  }
}
@media screen and (min-width: 40em) {
  /* line 786, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
  #toggle-design-box {
    display: none !important;
  }
}
/* line 791, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.rating_div .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 5%;
  font-weight: bold;
}
/* line 799, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.rating_div .green-rating {
  background-color: #16be48;
}
/* line 802, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.rating_div .red-rating {
  background-color: #FF5722;
}
/* line 805, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/catalog.scss */
.rating_div .orange-rating {
  background-color: #FFA000;
}

/*.star-yellow:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #FFC315 100%, #e2e0e0 0%);
      background: -o-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: -moz-linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      background: linear-gradient(left, #FFC315 100%, #e2e0e0 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      font-size: 115%;
    }
.star-gray:before {
      content: "\2605";
      display: inline-block;
      background: -webkit-linear-gradient(left, #e2e0e0 100%, #FFC315 0%);
      background: -o-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: -moz-linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      background: linear-gradient(left, #e2e0e0 100%, #FFC315 0%); 
      -webkit-text-fill-color: transparent;
      color: transparent;
      -webkit-background-clip: text;
      background-clip: text;
      
      font-size: 115%;
    }
.star-half:before{
    content: "\2605";
    display: inline-block;
    background: -webkit-linear-gradient(left, #FFC315 49%, #e2e0e0 50%);
    background: -o-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: -moz-linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    background: linear-gradient(left, #FFC315 49%, #e2e0e0 50%); 
    -webkit-text-fill-color: transparent;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    font-size: 115%;
}

.star_align{
  padding: 0px;
  text-align: center;
}
*/
