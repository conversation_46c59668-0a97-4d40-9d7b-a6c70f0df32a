@charset "UTF-8";
/* line 9, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_eid #eid-container .design-block {
  margin: 4px 0px;
}
/* line 12, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_eid #eid-container .featured-products {
  border: 1px solid gray;
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_eid #eid-container .featured-products .bestseller-title, .pages_eid #eid-container .featured-products .view-more {
  text-align: center;
}
/* line 18, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_eid #eid-container img {
  width: 100%;
}

@media only screen and (max-width: 620px) {
  /* line 26, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
  .pages_eid #eid-container .columns {
    padding: 0px;
  }
  /* line 29, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
  .pages_eid #eid-container .pad-right {
    padding-right: 4px !important;
  }
  /* line 32, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
  .pages_eid #eid-container .pad-left {
    padding-left: 4px !important;
  }
  /* line 36, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
  .pages_eid #eid-container .row .row {
    margin: 0 auto;
  }
}
/* line 44, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information #container {
  margin-top: 1.8em;
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_steps {
  background-color: #fffafa;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 50, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_steps .title {
  font-size: 150%;
  text-align: center;
  color: #383737;
}
/* line 55, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_steps .step_circle {
  display: inline-block;
  width: 50px;
  height: 50px;
  border-radius: 100%;
  font-size: 3%;
  line-height: 50px;
  text-align: center;
  margin-left: 10%;
  background: #BD3A55;
}
/* line 67, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_steps .step_text {
  font-size: 15px;
  color: #383737;
  font: inherit;
}
/* line 74, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .size_image .button {
  color: #383737;
  background-color: #fffafa;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  font-size: 16px;
  padding: 8px 12px;
  margin-bottom: 0px;
}
/* line 84, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block {
  background: #fffafa;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  margin-bottom: 20px;
}
/* line 89, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block a:hover, .pages_stitching_information .listing_panel_block a:focus {
  color: #383737;
}
/* line 93, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block li, .pages_stitching_information .listing_panel_block label {
  cursor: auto;
}
/* line 97, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block table {
  border: none;
}
/* line 100, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block p {
  text-align: left;
  background: none repeat scroll 0% 0%;
  margin: -7px 0px 0px;
  border-radius: 2px 2px 0px 0px;
  padding: 6px;
}
/* line 108, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block ul {
  font-size: 14px;
}
/* line 112, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .listing_panel_block td {
  vertical-align: baseline;
}
/* line 118, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .accordion table {
  display: none;
}
/* line 122, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .accordion a {
  outline: none;
  color: inherit;
}
/* line 127, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .accordion .expand {
  display: none;
  float: right;
}
/* line 132, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .accordion .collapse {
  float: right;
}
/* line 138, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_details .panel_content {
  padding: 0px;
}
/* line 141, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_details .stitching_question {
  font-size: 17px;
  color: #BD3A55;
  font-weight: bold;
}
/* line 147, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_details .stitching_answer {
  font-size: 15px;
  text-align: center;
  color: black;
  font: inherit;
}
/* line 156, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .statement {
  border: 1px solid;
  text-align: center;
  color: #383737;
  font-size: 14px;
  height: 24px;
  padding: 7px;
  margin: 0 auto;
  width: 80%;
  font-style: inherit;
  font-weight: bold;
}
/* line 169, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .message {
  padding: 2px;
  text-align: center;
  background: #fffafa;
  color: #ca3366;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  font-weight: bold;
}
/* line 177, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_stitching_information .stitching_number {
  color: rgba(105, 48, 0, 0.91);
  font-weight: 400;
  text-align: left;
}

/* line 184, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#trackModal {
  background-color: #efeeee;
  padding-top: 50px;
  min-height: 33vh;
  top: 50px;
}
/* line 189, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#trackModal .close-reveal-modal {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}
/* line 193, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#trackModal #order_number_field {
  margin-bottom: 22px;
}

/* line 198, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#list_of_order_modal {
  background-color: #efeeee;
  top: 0px !important;
  width: 100%;
  overflow: auto;
  position: fixed;
  height: 100%;
}
/* line 205, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#list_of_order_modal .close-reveal-modal {
  padding: 1% 5% 1% 7%;
  right: 0.3rem;
}

/* line 212, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section {
  margin-top: 0px !important;
}
/* line 215, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div {
  font-size: 0.65em;
}
/* line 217, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .questions_ul {
  list-style: none;
  margin-left: 0px;
}
/* line 222, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .question_li_content h3 {
  font-size: 1em;
}
/* line 224, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .question_li_content h3:before {
  content: "[+]  ";
}
/* line 230, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .question_selected h3 {
  font-size: 1em;
}
/* line 232, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .question_selected h3:before {
  content: "[-]  ";
}
/* line 237, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div #search_ques {
  font-size: 1.5em;
}
/* line 241, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div #search_answer span {
  font-size: 0.9em !important;
}
/* line 245, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .search_res {
  padding: 12px 8px;
  background-color: #fffafa;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 250, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faq_head {
  font-size: 1.5em;
  padding: 5px;
  margin-bottom: 10px;
}
/* line 255, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .quick_links {
  text-align: center;
  background-color: #fffafa;
}
/* line 258, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .quick_links .quick_link {
  height: 110px;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 261, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .quick_links .quick_link h2 {
  font-size: 1em;
}
/* line 266, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row {
  font-size: 1.2em;
}
/* line 268, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row .faqs_all {
  margin-top: 30px;
}
/* line 271, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faqs_row .view_more {
  margin-left: -17px;
  background-color: #1D2A29;
  padding: 10px;
  border-radius: 12px;
}
/* line 279, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faq_link_selected {
  color: #383737;
  background-color: #c7c7c7;
}
/* line 283, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .faq_link_selected::before {
  content: "";
  position: absolute;
  right: 40%;
  top: 102px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-bottom: 10px solid #efeeee;
  border-right: 8px solid transparent;
}
/* line 294, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .answer_selected {
  padding: 12px;
  background-color: #fffafa;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
}
/* line 298, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .answer_selected span {
  font-size: 0.9em !important;
}
/* line 302, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .navigation {
  height: 300px;
  overflow: auto;
}
/* line 306, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div hr {
  margin: 0.7rem 0 0.5rem 0;
}
/* line 309, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
.pages_faq .main-section #container .faq_main_div .answer_nav {
  height: 200px;
  overflow: auto;
}

/* line 318, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .accordion_title {
  background-color: cadetblue;
  border: solid;
}
/* line 324, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .tab-title.active a {
  background-color: #6dc1b8;
  border-bottom: none;
}
/* line 330, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .tab-title {
  width: 50%;
}
/* line 332, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .tab-title a {
  border-radius: 4px 4px 0px 0px;
  background-color: ghostwhite;
  border: 1px solid #a2a2a2;
  padding: 0.5em;
  text-align: center;
}
/* line 340, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .tabs-content {
  border-bottom: 1px solid;
  padding: 5px;
  border-left: 1px solid;
  border-right: 1px solid;
  background-color: grey;
  border-color: gray;
  margin-top: -2px;
}
/* line 349, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .accordion {
  margin-left: -0.3em;
}
/* line 353, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center #issue_panel .columns {
  padding-left: 0px;
  padding-right: 0px;
}
/* line 358, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category-links {
  height: 110px;
  border: 1px solid #6e6e6e;
  text-align: center;
  color: black;
}
/* line 364, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category-links.active {
  background-color: #6dc1b8;
}
/* line 367, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category-links.active:before {
  content: "";
  position: absolute;
  right: 43%;
  top: 98px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-bottom: 10px solid #fffafa;
  border-right: 8px solid transparent;
}
/* line 378, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .content_replace {
  padding: 0.9375rem;
  display: block;
  color: black;
  background-color: #cacaca;
}
/* line 384, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category_query {
  list-style: none;
  color: black;
}
/* line 387, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category_query hr {
  margin: 0.1rem 0 1.1875rem;
}
/* line 391, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category_query:first-child {
  padding-top: 1em;
}
/* line 394, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .stage_passed {
  background-color: #009688;
}
/* line 397, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .stage_passed:before {
  content: "✓ ";
}
/* line 400, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category_query:before {
  content: "›";
}
/* line 403, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .category_list {
  margin-left: 10px;
}
/* line 406, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .blur {
  opacity: 0.6;
}
/* line 409, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .button.info {
  background-color: #a0d3e8 !important;
}
/* line 412, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center #loadingImage {
  position: fixed;
  left: 45%;
  top: 30%;
  font-size: 4em;
}
/* line 418, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center #error_order_not_found_email {
  color: #D84315;
  padding: 3px;
}
/* line 422, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .login_hr:after {
  content: "OR";
  /* section sign */
  color: #999;
  display: inline;
  /* for vertical centering and background knockout */
  background-color: #cacaca;
  /* same as background color */
  padding: 0 0.5em;
  /* size of background color knockout */
}
/* line 429, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .login_hr {
  font-family: Arial, sans-serif;
  /* choose the font you like */
  text-align: center;
  /* horizontal centering */
  line-height: 1px;
  /* vertical centering */
  height: 1px;
  /* gap between the lines */
  font-size: 1em;
  /* choose font size you like */
  border-width: 1px 0;
  /* top and bottom borders */
  border-style: solid;
  border-color: #676767;
  margin: 20px 10px;
  /* 20px space above/below, 10px left/right */
  /* ensure 1px gap between borders */
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
/* line 447, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#help_center .track_btn {
  background-color: white;
  height: 100px;
  border: 1px solid black;
  text-align: center;
  color: black;
  padding: 10px;
}

/* line 457, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/pages.scss */
#order_table_list li {
  background-color: #fffafa;
  margin-top: 30px;
  list-style: none;
  box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.18);
  padding: 5px;
  margin-left: -20px;
}
