(function() {
  $(function() {
    var GetQuestions;
    $('.rating-tab #next_nps_bifurcation').on('click', function(e) {
      var order_number, star_value;
      e.preventDefault();
      star_value = $('#survey_star_value').val();
      order_number = $('#survey_order_number').val();
      return $.ajax({
        type: 'POST',
        data: {
          star_value: star_value,
          survey: {
            order_number: order_number,
            email: $('#survey_email').val(),
            token: $('#survey_token').val()
          },
          sample_form: location.href.includes('sample_form')
        },
        url: '/surveys',
        datatype: 'JSON',
        success: function(data) {
          if (data.star_value === false) {
            $('#survey_alert').text(data.notice);
            return $('#survey_alert').slideDown(500, function() {
              return setTimeout((function() {
                return $('#survey_alert').slideUp(500);
              }), 3000);
            });
          } else {
            return $('.rating_div').fadeOut(500, function() {
              return setTimeout((function() {
                GetQuestions(star_value, order_number);
                return $('.text-message').fadeIn(500);
              }), 100);
            });
          }
        },
        error: function(data) {
          $('#survey_alert').text(data.Error);
          return $('#survey_alert').slideDown(500, function() {
            return setTimeout((function() {
              return $('#survey_alert').slideUp(500);
            }), 3000);
          });
        }
      });
    });
    GetQuestions = function(star, order_number) {
      var audience, audience_text;
      if (star < 7) {
        audience = 'detractors';
        audience_text = 'We would like to understand the reason for your rating.';
      } else {
        if (star < 9) {
          audience = 'neutral';
          audience_text = 'We would like to understand the reason for your rating.';
        } else {
          $('#last-box').data('type', 'promoters');
          audience = 'promoters';
          audience_text = 'Help us improve our services, your reviews are incredibly helpful to us!';
        }
      }
      return $.ajax({
        type: 'GET',
        data: {
          audience: audience,
          order_number: order_number,
          sample_form: location.href.includes('sample_form')
        },
        url: '/surveys/get_audience_questions',
        datatype: 'script',
        success: function(data) {
          if (data.status === 422) {
            $('.alert-danger').text(data.notice);
            return $('.alert-danger').slideDown(500, function() {
              return setTimeout((function() {
                return $('.alert-danger').slideUp(500);
              }), 3000);
            });
          } else {
            return $('#audience-text').text(audience_text);
          }
        },
        error: function(data) {
          $('.alert-danger').text('Something went wrong.');
          return $('.alert-danger').slideDown(500, function() {
            return setTimeout((function() {
              return $('.alert-danger').slideUp(500);
            }), 3000);
          });
        }
      });
    };
    return $(function() {
      return $('.text-message .response-button').on('click', function(e) {
        e.preventDefault();
        if ($.trim($("#survey_notes").val()) === '') {
          $('#survey_alert').text("Please fill in comments");
          return $('#survey_alert').slideDown(500, function() {
            return setTimeout((function() {
              return $('#survey_alert').slideUp(500);
            }), 3000);
          });
        } else {
          return $.ajax({
            type: 'POST',
            data: {
              survey: {
                notes: $('#survey_notes').val(),
                order_number: $('#survey_order_number').val()
              }
            },
            url: '/surveys/update_notes',
            datatype: 'JSON',
            success: function(data) {
              $('.survey').remove();
              return $('.survey-message').show().text(data.notice);
            },
            error: function(data) {
              $('#survey_alert').text(data.Error);
              return $('#survey_alert').slideDown(500, function() {
                return setTimeout((function() {
                  return $('#survey_alert').slideUp(500);
                }), 3000);
              });
            }
          });
        }
      });
    });
  });

  $(function() {
    return $('.rating-tab #next_nps_bifurcation').on('click', function() {
      var order_number, path;
      order_number = $('#survey_order_number').val();
      path = '/surveys/get_line_item_image?order_number=' + order_number + '&sample_form=' + location.href.includes('sample_form');
      $.ajax({
        url: path,
        type: 'GET',
        datatype: 'script',
        success: function(data) {},
        error: function(error) {}
      });
    });
  });

  $(function() {
    return $(".review_text input[type = 'radio']").prop('checked', false);
  });

  $(function() {
    $(document).on('ready', function() {
      $('#no_of_rating_nps').text($('#slider').attr('data-slider') + '.0');
    });
    return $(document).foundation({
      slider: {
        on_change: function() {
          if ($('#slider').attr('data-slider') === '10') {
            $('#no_of_rating_nps').text($('#slider').attr('data-slider'));
          } else {
            $('#no_of_rating_nps').text($('#slider').attr('data-slider') + '.0');
          }
        }
      }
    });
  });

  $(function() {
    var showBifurcation;
    $('.bifurcation').on('click', function(e) {
      var row_number;
      row_number = $(this).attr('id').split('_')[1];
      if ($('#sub-' + this.id + ' ul li').length > 0) {
        $('#review_issue_' + row_number + ' .issues .sub-bifurcation, #review_issue_' + row_number + ' .issues .bifurcation, #close_' + row_number).hide();
        $('#close_' + row_number).show();
        return $('#sub-' + this.id).show();
      } else {
        return showBifurcation(row_number);
      }
    });
    showBifurcation = function(id) {
      $('#review_issue_' + id + ' .issues .bifurcation').show();
      return $('#close_' + id).hide();
    };
    return $('.back').on('click', function(e) {
      var row_number;
      row_number = $(this).attr('id').split('_')[1];
      $('#review_issue_' + row_number + ' .issues .sub-bifurcation, #close_' + row_number).hide();
      $('#review_issue_' + row_number + ' .issues .bifurcation').show();
      return $('html, body').animate({
        scrollTop: $('#review_issue_' + row_number + ' .issues .bifurcation').offset().top - 250
      }, 100);
    });
  });

  $(function() {
    return $('.number_star').click(function() {
      var color;
      $('#next_nps_bifurcation').show();
      $('#survey_star_value').val(parseInt($(this).text()));
      $('.number_star').css({
        'background-color': '#ffffff',
        'color': '#000000'
      });
      if (parseInt($(this).text()) <= 6) {
        color = '#E91E63';
      } else if (parseInt($(this).text()) > 6 && parseInt($(this).text()) < 9) {
        color = '#F57C00';
      } else {
        color = '#009688';
      }
      return $(this).css({
        'background-color': color,
        'color': '#ffffff'
      });
    });
  });

}).call(this);
