/*! PhotoSwipe - v4.1.2 - 2017-04-05
* http://photoswipe.com
* Copyright (c) 2017 <PERSON>; */
!function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipe=t()}(this,function(){"use strict";var e=function(e,t,n,i){var o={features:null,bind:function(e,t,n,i){var o=(i?"remove":"add")+"EventListener";t=t.split(" ");for(var a=0;a<t.length;a++)t[a]&&e[o](t[a],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){o.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){o.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(o.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var i=e.length;i--;)if(e[i][n]===t)return i;return-1},extend:function(e,t,n){for(var i in t)if(t.hasOwnProperty(i)){if(n&&e.hasOwnProperty(i))continue;e[i]=t[i]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(o.features)return o.features;var e=o.createEl(),t=e.style,n="",i={};if(i.oldIE=document.all&&!document.addEventListener,i.touch="ontouchstart"in window,window.requestAnimationFrame&&(i.raf=window.requestAnimationFrame,i.caf=window.cancelAnimationFrame),i.pointerEvent=navigator.pointerEnabled||navigator.msPointerEnabled,!i.pointerEvent){var a=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var r=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);r&&r.length>0&&(r=parseInt(r[1],10),r>=1&&8>r&&(i.isOldIOSPhone=!0))}var s=a.match(/Android\s([0-9\.]*)/),l=s?s[1]:0;l=parseFloat(l),l>=1&&(4.4>l&&(i.isOldAndroid=!0),i.androidVersion=l),i.isMobileOpera=/opera mini|opera mobi/i.test(a)}for(var c,d,u=["transform","perspective","animationName"],p=["","webkit","Moz","ms","O"],m=0;4>m;m++){n=p[m];for(var h=0;3>h;h++)c=u[h],d=n+(n?c.charAt(0).toUpperCase()+c.slice(1):c),!i[c]&&d in t&&(i[c]=d);n&&!i.raf&&(n=n.toLowerCase(),i.raf=window[n+"RequestAnimationFrame"],i.raf&&(i.caf=window[n+"CancelAnimationFrame"]||window[n+"CancelRequestAnimationFrame"]))}if(!i.raf){var f=0;i.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-f)),i=window.setTimeout(function(){e(t+n)},n);return f=t+n,i},i.caf=function(e){clearTimeout(e)}}return i.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,o.features=i,i}};o.detectFeatures(),o.features.oldIE&&(o.bind=function(e,t,n,i){t=t.split(" ");for(var o,a=(i?"detach":"attach")+"Event",r=function(){n.handleEvent.call(n)},s=0;s<t.length;s++)if(o=t[s])if("object"==typeof n&&n.handleEvent){if(i){if(!n["oldIE"+o])return!1}else n["oldIE"+o]=r;e[a]("on"+o,n["oldIE"+o])}else e[a]("on"+o,n)});var a=this,r=25,s=3,l={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e?1:t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};o.extend(l,i);var c,d,u,p,m,h,f,v,_,g,y,w,x,b,$,C,I,k,T,z,E,S,D,M,O,F,A,R,P,L,Z,N,U,K,H,W,q,B,G,Y,X,V,j,J,Q,et,tt,nt,it,ot,at,rt,st,lt,ct,dt,ut,pt=function(){return{x:0,y:0}},mt=pt(),ht=pt(),ft=pt(),vt={},_t=0,gt={},yt=pt(),wt=0,xt=!0,bt=[],$t={},Ct=!1,It=function(e,t){o.extend(a,t.publicMethods),bt.push(e)},kt=function(e){var t=ti();return e>t-1?e-t:0>e?t+e:e},Tt={},zt=function(e,t){return Tt[e]||(Tt[e]=[]),Tt[e].push(t)},Et=function(e){var t=Tt[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var i=0;i<t.length;i++)t[i].apply(a,n)}},St=function(){return(new Date).getTime()},Dt=function(e){ct=e,a.bg.style.opacity=e*l.bgOpacity},Mt=function(e,t,n,i,o){(!Ct||o&&o!==a.currItem)&&(i/=o?o.fitRatio:a.currItem.fitRatio),e[S]=w+t+"px, "+n+"px"+x+" scale("+i+")"},Ot=function(e){ot&&(e&&(g>a.currItem.fitRatio?Ct||(mi(a.currItem,!1,!0),Ct=!0):Ct&&(mi(a.currItem),Ct=!1)),Mt(ot,ft.x,ft.y,g))},Ft=function(e){e.container&&Mt(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},At=function(e,t){t[S]=w+e+"px, 0px"+x},Rt=function(e,t){if(!l.loop&&t){var n=p+(yt.x*_t-e)/yt.x,i=Math.round(e-wn.x);(0>n&&i>0||n>=ti()-1&&0>i)&&(e=wn.x+i*l.mainScrollEndFriction)}wn.x=e,At(e,m)},Pt=function(e,t){var n=xn[e]-gt[e];return ht[e]+mt[e]+n-n*(t/y)},Lt=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Zt=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},Nt=null,Ut=function(){Nt&&(o.unbind(document,"mousemove",Ut),o.addClass(e,"pswp--has_mouse"),l.mouseUsed=!0,Et("mouseUsed")),Nt=setTimeout(function(){Nt=null},100)},Kt=function(){o.bind(document,"keydown",a),Z.transform&&o.bind(a.scrollWrap,"click",a),l.mouseUsed||o.bind(document,"mousemove",Ut),o.bind(window,"resize scroll orientationchange",a),Et("bindEvents")},Ht=function(){o.unbind(window,"resize scroll orientationchange",a),o.unbind(window,"scroll",_.scroll),o.unbind(document,"keydown",a),o.unbind(document,"mousemove",Ut),Z.transform&&o.unbind(a.scrollWrap,"click",a),G&&o.unbind(window,f,a),clearTimeout(N),Et("unbindEvents")},Wt=function(e,t){var n=ci(a.currItem,vt,e);return t&&(it=n),n},qt=function(e){return e||(e=a.currItem),e.initialZoomLevel},Bt=function(e){return e||(e=a.currItem),e.w>0?l.maxSpreadZoom:1},Gt=function(e,t,n,i){return i===a.currItem.initialZoomLevel?(n[e]=a.currItem.initialPosition[e],!0):(n[e]=Pt(e,i),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]?(n[e]=t.max[e],!0):!1)},Yt=function(){if(S){var t=Z.perspective&&!M;return w="translate"+(t?"3d(":"("),void(x=Z.perspective?", 0px)":")")}S="left",o.addClass(e,"pswp--ie"),At=function(e,t){t.left=e+"px"},Ft=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,i=t*e.w,o=t*e.h;n.width=i+"px",n.height=o+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},Ot=function(){if(ot){var e=ot,t=a.currItem,n=t.fitRatio>1?1:t.fitRatio,i=n*t.w,o=n*t.h;e.width=i+"px",e.height=o+"px",e.left=ft.x+"px",e.top=ft.y+"px"}}},Xt=function(e){var t="";l.escKey&&27===e.keyCode?t="close":l.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,a[t]()))},Vt=function(e){e&&(V||X||at||q)&&(e.preventDefault(),e.stopPropagation())},jt=function(){a.setScrollOffset(0,o.getScrollY())},Jt={},Qt=0,en=function(e){Jt[e]&&(Jt[e].raf&&F(Jt[e].raf),Qt--,delete Jt[e])},tn=function(e){Jt[e]&&en(e),Jt[e]||(Qt++,Jt[e]={})},nn=function(){for(var e in Jt)Jt.hasOwnProperty(e)&&en(e)},on=function(e,t,n,i,o,a,r){var s,l=St();tn(e);var c=function(){if(Jt[e]){if(s=St()-l,s>=i)return en(e),a(n),void(r&&r());a((n-t)*o(s/i)+t),Jt[e].raf=O(c)}};c()},an={shout:Et,listen:zt,viewportSize:vt,options:l,isMainScrollAnimating:function(){return at},getZoomLevel:function(){return g},getCurrentIndex:function(){return p},isDragging:function(){return G},isZooming:function(){return et},setScrollOffset:function(e,t){gt.x=e,L=gt.y=t,Et("updateScrollOffset",gt)},applyZoomPan:function(e,t,n,i){ft.x=t,ft.y=n,g=e,Ot(i)},init:function(){if(!c&&!d){var n;a.framework=o,a.template=e,a.bg=o.getChildByClass(e,"pswp__bg"),A=e.className,c=!0,Z=o.detectFeatures(),O=Z.raf,F=Z.caf,S=Z.transform,P=Z.oldIE,a.scrollWrap=o.getChildByClass(e,"pswp__scroll-wrap"),a.container=o.getChildByClass(a.scrollWrap,"pswp__container"),m=a.container.style,a.itemHolders=C=[{el:a.container.children[0],wrap:0,index:-1},{el:a.container.children[1],wrap:0,index:-1},{el:a.container.children[2],wrap:0,index:-1}],C[0].el.style.display=C[2].el.style.display="none",Yt(),_={resize:a.updateSize,orientationchange:function(){clearTimeout(N),N=setTimeout(function(){vt.x!==a.scrollWrap.clientWidth&&a.updateSize()},500)},scroll:jt,keydown:Xt,click:Vt};var i=Z.isOldIOSPhone||Z.isOldAndroid||Z.isMobileOpera;for(Z.animationName&&Z.transform&&!i||(l.showAnimationDuration=l.hideAnimationDuration=0),n=0;n<bt.length;n++)a["init"+bt[n]]();if(t){var r=a.ui=new t(a,o);r.init()}Et("firstUpdate"),p=p||l.index||0,(isNaN(p)||0>p||p>=ti())&&(p=0),a.currItem=ei(p),(Z.isOldIOSPhone||Z.isOldAndroid)&&(xt=!1),e.setAttribute("aria-hidden","false"),l.modal&&(xt?e.style.position="fixed":(e.style.position="absolute",e.style.top=o.getScrollY()+"px")),void 0===L&&(Et("initialLayout"),L=R=o.getScrollY());var u="pswp--open ";for(l.mainClass&&(u+=l.mainClass+" "),l.showHideOpacity&&(u+="pswp--animate_opacity "),u+=M?"pswp--touch":"pswp--notouch",u+=Z.animationName?" pswp--css_animation":"",u+=Z.svg?" pswp--svg":"",o.addClass(e,u),a.updateSize(),h=-1,wt=null,n=0;s>n;n++)At((n+h)*yt.x,C[n].el.style);P||o.bind(a.scrollWrap,v,a),zt("initialZoomInEnd",function(){a.setContent(C[0],p-1),a.setContent(C[2],p+1),C[0].el.style.display=C[2].el.style.display="block",l.focus&&e.focus(),Kt()}),a.setContent(C[1],p),a.updateCurrItem(),Et("afterInit"),xt||(b=setInterval(function(){Qt||G||et||g!==a.currItem.initialZoomLevel||a.updateSize()},1e3)),o.addClass(e,"pswp--visible")}},close:function(){c&&(c=!1,d=!0,Et("close"),Ht(),ii(a.currItem,null,!0,a.destroy))},destroy:function(){Et("destroy"),Vn&&clearTimeout(Vn),e.setAttribute("aria-hidden","true"),e.className=A,b&&clearInterval(b),o.unbind(a.scrollWrap,v,a),o.unbind(window,"scroll",a),kn(),nn(),Tt=null},panTo:function(e,t,n){n||(e>it.min.x?e=it.min.x:e<it.max.x&&(e=it.max.x),t>it.min.y?t=it.min.y:t<it.max.y&&(t=it.max.y)),ft.x=e,ft.y=t,Ot()},handleEvent:function(e){e=e||window.event,_[e.type]&&_[e.type](e)},goTo:function(e){e=kt(e);var t=e-p;wt=t,p=e,a.currItem=ei(p),_t-=t,Rt(yt.x*_t),nn(),at=!1,a.updateCurrItem()},next:function(){a.goTo(p+1)},prev:function(){a.goTo(p-1)},updateCurrZoomItem:function(e){if(e&&Et("beforeChange",0),C[1].el.children.length){var t=C[1].el.children[0];ot=o.hasClass(t,"pswp__zoom-wrap")?t.style:null}else ot=null;it=a.currItem.bounds,y=g=a.currItem.initialZoomLevel,ft.x=it.center.x,ft.y=it.center.y,e&&Et("afterChange")},invalidateCurrItems:function(){$=!0;for(var e=0;s>e;e++)C[e].item&&(C[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==wt){var t,n=Math.abs(wt);if(!(e&&2>n)){a.currItem=ei(p),Ct=!1,Et("beforeChange",wt),n>=s&&(h+=wt+(wt>0?-s:s),n=s);for(var i=0;n>i;i++)wt>0?(t=C.shift(),C[s-1]=t,h++,At((h+2)*yt.x,t.el.style),a.setContent(t,p-n+i+1+1)):(t=C.pop(),C.unshift(t),h--,At(h*yt.x,t.el.style),a.setContent(t,p+n-i-1-1));if(ot&&1===Math.abs(wt)){var o=ei(I);o.initialZoomLevel!==g&&(ci(o,vt),mi(o),Ft(o))}wt=0,a.updateCurrZoomItem(),I=p,Et("afterChange")}}},updateSize:function(t){if(!xt&&l.modal){var n=o.getScrollY();if(L!==n&&(e.style.top=n+"px",L=n),!t&&$t.x===window.innerWidth&&$t.y===window.innerHeight)return;$t.x=window.innerWidth,$t.y=window.innerHeight,e.style.height=$t.y+"px"}if(vt.x=a.scrollWrap.clientWidth,vt.y=a.scrollWrap.clientHeight,jt(),yt.x=vt.x+Math.round(vt.x*l.spacing),yt.y=vt.y,Rt(yt.x*_t),Et("beforeResize"),void 0!==h){for(var i,r,c,d=0;s>d;d++)i=C[d],At((d+h)*yt.x,i.el.style),c=p+d-1,l.loop&&ti()>2&&(c=kt(c)),r=ei(c),r&&($||r.needsUpdate||!r.bounds)?(a.cleanSlide(r),a.setContent(i,c),1===d&&(a.currItem=r,a.updateCurrZoomItem(!0)),r.needsUpdate=!1):-1===i.index&&c>=0&&a.setContent(i,c),r&&r.container&&(ci(r,vt),mi(r),Ft(r));$=!1}y=g=a.currItem.initialZoomLevel,it=a.currItem.bounds,it&&(ft.x=it.center.x,ft.y=it.center.y,Ot(!0)),Et("resize")},zoomTo:function(e,t,n,i,a){t&&(y=g,xn.x=Math.abs(t.x)-ft.x,xn.y=Math.abs(t.y)-ft.y,Lt(ht,ft));var r=Wt(e,!1),s={};Gt("x",r,s,e),Gt("y",r,s,e);var l=g,c={x:ft.x,y:ft.y};Zt(s);var d=function(t){1===t?(g=e,ft.x=s.x,ft.y=s.y):(g=(e-l)*t+l,ft.x=(s.x-c.x)*t+c.x,ft.y=(s.y-c.y)*t+c.y),a&&a(t),Ot(1===t)};n?on("customZoomTo",0,1,n,i||o.easing.sine.inOut,d):d(1)}},rn=30,sn=10,ln={},cn={},dn={},un={},pn={},mn=[],hn={},fn=[],vn={},_n=0,gn=pt(),yn=0,wn=pt(),xn=pt(),bn=pt(),$n=function(e,t){return e.x===t.x&&e.y===t.y},Cn=function(e,t){return Math.abs(e.x-t.x)<r&&Math.abs(e.y-t.y)<r},In=function(e,t){return vn.x=Math.abs(e.x-t.x),vn.y=Math.abs(e.y-t.y),Math.sqrt(vn.x*vn.x+vn.y*vn.y)},kn=function(){j&&(F(j),j=null)},Tn=function(){G&&(j=O(Tn),Hn())},zn=function(){return!("fit"===l.scaleMode&&g===a.currItem.initialZoomLevel)},En=function(e,t){return e&&e!==document?e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1?!1:t(e)?e:En(e.parentNode,t):!1},Sn={},Dn=function(e,t){return Sn.prevent=!En(e.target,l.isClickableElement),Et("preventDragEvent",e,t,Sn),Sn.prevent},Mn=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},On=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},Fn=function(e,t,n){if(e-K>50){var i=fn.length>2?fn.shift():{};i.x=t,i.y=n,fn.push(i),K=e}},An=function(){var e=ft.y-a.currItem.initialPosition.y;return 1-Math.abs(e/(vt.y/2))},Rn={},Pn={},Ln=[],Zn=function(e){for(;Ln.length>0;)Ln.pop();return D?(ut=0,mn.forEach(function(e){0===ut?Ln[0]=e:1===ut&&(Ln[1]=e),ut++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(Ln[0]=Mn(e.touches[0],Rn),e.touches.length>1&&(Ln[1]=Mn(e.touches[1],Pn))):(Rn.x=e.pageX,Rn.y=e.pageY,Rn.id="",Ln[0]=Rn),Ln},Nn=function(e,t){var n,i,o,r,s=0,c=ft[e]+t[e],d=t[e]>0,u=wn.x+t.x,p=wn.x-hn.x;return n=c>it.min[e]||c<it.max[e]?l.panEndFriction:1,c=ft[e]+t[e]*n,!l.allowPanToNext&&g!==a.currItem.initialZoomLevel||(ot?"h"!==rt||"x"!==e||X||(d?(c>it.min[e]&&(n=l.panEndFriction,s=it.min[e]-c,i=it.min[e]-ht[e]),(0>=i||0>p)&&ti()>1?(r=u,0>p&&u>hn.x&&(r=hn.x)):it.min.x!==it.max.x&&(o=c)):(c<it.max[e]&&(n=l.panEndFriction,s=c-it.max[e],i=ht[e]-it.max[e]),(0>=i||p>0)&&ti()>1?(r=u,p>0&&u<hn.x&&(r=hn.x)):it.min.x!==it.max.x&&(o=c))):r=u,"x"!==e)?void(at||J||g>a.currItem.fitRatio&&(ft[e]+=t[e]*n)):(void 0!==r&&(Rt(r,!0),J=r===hn.x?!1:!0),it.min.x!==it.max.x&&(void 0!==o?ft.x=o:J||(ft.x+=t.x*n)),void 0!==r)},Un=function(e){if(!("mousedown"===e.type&&e.button>0)){if(Qn)return void e.preventDefault();if(!B||"mousedown"!==e.type){if(Dn(e,!0)&&e.preventDefault(),Et("pointerDown"),D){var t=o.arraySearch(mn,e.pointerId,"id");0>t&&(t=mn.length),mn[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=Zn(e),i=n.length;Q=null,nn(),G&&1!==i||(G=st=!0,o.bind(window,f,a),W=dt=lt=q=J=V=Y=X=!1,rt=null,Et("firstTouchStart",n),Lt(ht,ft),mt.x=mt.y=0,Lt(un,n[0]),Lt(pn,un),hn.x=yt.x*_t,fn=[{x:un.x,y:un.y}],K=U=St(),Wt(g,!0),kn(),Tn()),!et&&i>1&&!at&&!J&&(y=g,X=!1,et=Y=!0,mt.y=mt.x=0,Lt(ht,ft),Lt(ln,n[0]),Lt(cn,n[1]),On(ln,cn,bn),xn.x=Math.abs(bn.x)-ft.x,xn.y=Math.abs(bn.y)-ft.y,tt=nt=In(ln,cn))}}},Kn=function(e){if(e.preventDefault(),D){var t=o.arraySearch(mn,e.pointerId,"id");if(t>-1){var n=mn[t];n.x=e.pageX,n.y=e.pageY}}if(G){var i=Zn(e);if(rt||V||et)Q=i;else if(wn.x!==yt.x*_t)rt="h";else{var a=Math.abs(i[0].x-un.x)-Math.abs(i[0].y-un.y);Math.abs(a)>=sn&&(rt=a>0?"h":"v",Q=i)}}},Hn=function(){if(Q){var e=Q.length;if(0!==e)if(Lt(ln,Q[0]),dn.x=ln.x-un.x,dn.y=ln.y-un.y,et&&e>1){if(un.x=ln.x,un.y=ln.y,!dn.x&&!dn.y&&$n(Q[1],cn))return;Lt(cn,Q[1]),X||(X=!0,Et("zoomGestureStarted"));var t=In(ln,cn),n=Yn(t);n>a.currItem.initialZoomLevel+a.currItem.initialZoomLevel/15&&(dt=!0);var i=1,o=qt(),r=Bt();if(o>n)if(l.pinchToClose&&!dt&&y<=a.currItem.initialZoomLevel){var s=o-n,c=1-s/(o/1.2);Dt(c),Et("onPinchClose",c),lt=!0}else i=(o-n)/o,i>1&&(i=1),n=o-i*(o/3);else n>r&&(i=(n-r)/(6*o),i>1&&(i=1),n=r+i*o);0>i&&(i=0),tt=t,On(ln,cn,gn),mt.x+=gn.x-bn.x,mt.y+=gn.y-bn.y,Lt(bn,gn),ft.x=Pt("x",n),ft.y=Pt("y",n),W=n>g,g=n,Ot()}else{if(!rt)return;if(st&&(st=!1,Math.abs(dn.x)>=sn&&(dn.x-=Q[0].x-pn.x),Math.abs(dn.y)>=sn&&(dn.y-=Q[0].y-pn.y)),un.x=ln.x,un.y=ln.y,0===dn.x&&0===dn.y)return;if("v"===rt&&l.closeOnVerticalDrag&&!zn()){mt.y+=dn.y,ft.y+=dn.y;var d=An();return q=!0,Et("onVerticalDrag",d),Dt(d),void Ot()}Fn(St(),ln.x,ln.y),V=!0,it=a.currItem.bounds;var u=Nn("x",dn);u||(Nn("y",dn),Zt(ft),Ot())}}},Wn=function(e){if(Z.isOldAndroid){if(B&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(B),B=setTimeout(function(){B=0},600))}Et("pointerUp"),Dn(e,!1)&&e.preventDefault();var t;if(D){var n=o.arraySearch(mn,e.pointerId,"id");if(n>-1)if(t=mn.splice(n,1)[0],navigator.pointerEnabled)t.type=e.pointerType||"mouse";else{var i={4:"mouse",2:"touch",3:"pen"};t.type=i[e.pointerType],t.type||(t.type=e.pointerType||"mouse")}}var r,s=Zn(e),c=s.length;if("mouseup"===e.type&&(c=0),2===c)return Q=null,!0;1===c&&Lt(pn,s[0]),0!==c||rt||at||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Et("touchRelease",e,t));var d=-1;if(0===c&&(G=!1,o.unbind(window,f,a),kn(),et?d=0:-1!==yn&&(d=St()-yn)),yn=1===c?St():-1,r=-1!==d&&150>d?"zoom":"swipe",et&&2>c&&(et=!1,1===c&&(r="zoomPointerUp"),Et("zoomGestureEnded")),Q=null,V||X||at||q)if(nn(),H||(H=qn()),H.calculateSwipeSpeed("x"),q){var u=An();if(u<l.verticalDragRange)a.close();else{var p=ft.y,m=ct;on("verticalDrag",0,1,300,o.easing.cubic.out,function(e){ft.y=(a.currItem.initialPosition.y-p)*e+p,Dt((1-m)*e+m),Ot()}),Et("onVerticalDrag",1)}}else{if((J||at)&&0===c){var h=Gn(r,H);if(h)return;r="zoomPointerUp"}if(!at)return"swipe"!==r?void Xn():void(!J&&g>a.currItem.fitRatio&&Bn(H))}},qn=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(i){fn.length>1?(e=St()-K+50,t=fn[fn.length-2][i]):(e=St()-U,t=pn[i]),n.lastFlickOffset[i]=un[i]-t,n.lastFlickDist[i]=Math.abs(n.lastFlickOffset[i]),n.lastFlickSpeed[i]=n.lastFlickDist[i]>20?n.lastFlickOffset[i]/e:0,Math.abs(n.lastFlickSpeed[i])<.1&&(n.lastFlickSpeed[i]=0),n.slowDownRatio[i]=.95,n.slowDownRatioReverse[i]=1-n.slowDownRatio[i],n.speedDecelerationRatio[i]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(ft[e]>it.min[e]?n.backAnimDestination[e]=it.min[e]:ft[e]<it.max[e]&&(n.backAnimDestination[e]=it.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,on("bounceZoomPan"+e,ft[e],n.backAnimDestination[e],t||300,o.easing.sine.out,function(t){ft[e]=t,Ot()}))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,ft[e]+=n.distanceOffset[e])},panAnimLoop:function(){return Jt.zoomPan&&(Jt.zoomPan.raf=O(n.panAnimLoop),n.now=St(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),Ot(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05)?(ft.x=Math.round(ft.x),ft.y=Math.round(ft.y),Ot(),void en("zoomPan")):void 0}};return n},Bn=function(e){return e.calculateSwipeSpeed("y"),it=a.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05?(e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0):(tn("zoomPan"),e.lastNow=St(),void e.panAnimLoop())},Gn=function(e,t){var n;at||(_n=p);var i;if("swipe"===e){var r=un.x-pn.x,s=t.lastFlickDist.x<10;r>rn&&(s||t.lastFlickOffset.x>20)?i=-1:-rn>r&&(s||t.lastFlickOffset.x<-20)&&(i=1)}var c;i&&(p+=i,0>p?(p=l.loop?ti()-1:0,c=!0):p>=ti()&&(p=l.loop?0:ti()-1,c=!0),(!c||l.loop)&&(wt+=i,_t-=i,n=!0));var d,u=yt.x*_t,m=Math.abs(u-wn.x);return n||u>wn.x==t.lastFlickSpeed.x>0?(d=Math.abs(t.lastFlickSpeed.x)>0?m/Math.abs(t.lastFlickSpeed.x):333,d=Math.min(d,400),d=Math.max(d,250)):d=333,_n===p&&(n=!1),at=!0,Et("mainScrollAnimStart"),on("mainScroll",wn.x,u,d,o.easing.cubic.out,Rt,function(){nn(),at=!1,_n=-1,(n||_n!==p)&&a.updateCurrItem(),Et("mainScrollAnimComplete")}),n&&a.updateCurrItem(!0),n},Yn=function(e){return 1/nt*e*y},Xn=function(){var e=g,t=qt(),n=Bt();t>g?e=t:g>n&&(e=n);var i,r=1,s=ct;return lt&&!W&&!dt&&t>g?(a.close(),!0):(lt&&(i=function(e){Dt((r-s)*e+s)}),a.zoomTo(e,0,200,o.easing.cubic.out,i),!0)};It("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,i,o){k=e+t,T=e+n,z=e+i,E=o?e+o:""};D=Z.pointerEvent,D&&Z.touch&&(Z.touch=!1),D?navigator.pointerEnabled?e("pointer","down","move","up","cancel"):e("MSPointer","Down","Move","Up","Cancel"):Z.touch?(e("touch","start","move","end","cancel"),M=!0):e("mouse","down","move","up"),f=T+" "+z+" "+E,v=k,D&&!M&&(M=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),a.likelyTouchDevice=M,_[k]=Un,_[T]=Kn,_[z]=Wn,E&&(_[E]=_[z]),Z.touch&&(v+=" mousedown",f+=" mousemove mouseup",_.mousedown=_[k],_.mousemove=_[T],_.mouseup=_[z]),M||(l.allowPanToNext=!1)}}});var Vn,jn,Jn,Qn,ei,ti,ni,ii=function(t,n,i,r){Vn&&clearTimeout(Vn),Qn=!0,Jn=!0;var s;t.initialLayout?(s=t.initialLayout,t.initialLayout=null):s=l.getThumbBoundsFn&&l.getThumbBoundsFn(p);var c=i?l.hideAnimationDuration:l.showAnimationDuration,d=function(){en("initialZoom"),i?(a.template.removeAttribute("style"),a.bg.removeAttribute("style")):(Dt(1),n&&(n.style.display="block"),o.addClass(e,"pswp--animated-in"),Et("initialZoom"+(i?"OutEnd":"InEnd"))),r&&r(),Qn=!1};if(!c||!s||void 0===s.x)return Et("initialZoom"+(i?"Out":"In")),g=t.initialZoomLevel,Lt(ft,t.initialPosition),Ot(),e.style.opacity=i?0:1,Dt(1),void(c?setTimeout(function(){d()},c):d());var m=function(){var n=u,r=!a.currItem.src||a.currItem.loadError||l.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),i||(g=s.w/t.w,ft.x=s.x,ft.y=s.y-R,a[r?"template":"bg"].style.opacity=.001,Ot()),tn("initialZoom"),i&&!n&&o.removeClass(e,"pswp--animated-in"),r&&(i?o[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout(function(){o.addClass(e,"pswp--animate_opacity")},30)),Vn=setTimeout(function(){if(Et("initialZoom"+(i?"Out":"In")),i){var a=s.w/t.w,l={x:ft.x,y:ft.y},u=g,p=ct,m=function(t){1===t?(g=a,ft.x=s.x,ft.y=s.y-L):(g=(a-u)*t+u,ft.x=(s.x-l.x)*t+l.x,ft.y=(s.y-L-l.y)*t+l.y),Ot(),r?e.style.opacity=1-t:Dt(p-t*p)};n?on("initialZoom",0,1,c,o.easing.cubic.out,m,d):(m(1),Vn=setTimeout(d,c+20))}else g=t.initialZoomLevel,Lt(ft,t.initialPosition),Ot(),Dt(1),r?e.style.opacity=1:Dt(1),Vn=setTimeout(d,c+20)},i?25:90)};m()},oi={},ai=[],ri={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return jn.length}},si=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},li=function(e,t,n){var i=e.bounds;i.center.x=Math.round((oi.x-t)/2),i.center.y=Math.round((oi.y-n)/2)+e.vGap.top,i.max.x=t>oi.x?Math.round(oi.x-t):i.center.x,i.max.y=n>oi.y?Math.round(oi.y-n)+e.vGap.top:i.center.y,i.min.x=t>oi.x?0:i.center.x,i.min.y=n>oi.y?e.vGap.top:i.center.y},ci=function(e,t,n){if(e.src&&!e.loadError){var i=!n;if(i&&(e.vGap||(e.vGap={top:0,bottom:0}),Et("parseVerticalMargin",e)),oi.x=t.x,oi.y=t.y-e.vGap.top-e.vGap.bottom,i){var o=oi.x/e.w,a=oi.y/e.h;e.fitRatio=a>o?o:a;var r=l.scaleMode;"orig"===r?n=1:"fit"===r&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds=si())}if(!n)return;return li(e,e.w*n,e.h*n),i&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=si(),e.initialPosition=e.bounds.center,e.bounds},di=function(e,t,n,i,o,r){t.loadError||i&&(t.imageAppended=!0,mi(t,i,t===a.currItem&&Ct),n.appendChild(i),r&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},ui=function(e){e.loading=!0,e.loaded=!1;var t=e.img=o.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},pi=function(e,t){return e.src&&e.loadError&&e.container?(t&&(e.container.innerHTML=""),e.container.innerHTML=l.errorMsg.replace("%url%",e.src),!0):void 0},mi=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var i=n?e.w:Math.round(e.w*e.fitRatio),o=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=i+"px",e.placeholder.style.height=o+"px"),t.style.width=i+"px",t.style.height=o+"px"}},hi=function(){if(ai.length){for(var e,t=0;t<ai.length;t++)e=ai[t],e.holder.index===e.index&&di(e.index,e.item,e.baseDiv,e.img,!1,e.clearPlaceholder);ai=[]}};It("Controller",{publicMethods:{lazyLoadItem:function(e){e=kt(e);var t=ei(e);t&&(!t.loaded&&!t.loading||$)&&(Et("gettingData",e,t),t.src&&ui(t))},initController:function(){o.extend(l,ri,!0),a.items=jn=n,ei=a.getItemAt,ti=l.getNumItemsFn,ni=l.loop,ti()<3&&(l.loop=!1),zt("beforeChange",function(e){var t,n=l.preload,i=null===e?!0:e>=0,o=Math.min(n[0],ti()),r=Math.min(n[1],ti());for(t=1;(i?r:o)>=t;t++)a.lazyLoadItem(p+t);for(t=1;(i?o:r)>=t;t++)a.lazyLoadItem(p-t)}),zt("initialLayout",function(){a.currItem.initialLayout=l.getThumbBoundsFn&&l.getThumbBoundsFn(p)}),zt("mainScrollAnimComplete",hi),zt("initialZoomInEnd",hi),zt("destroy",function(){for(var e,t=0;t<jn.length;t++)e=jn[t],e.container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);ai=null})},getItemAt:function(e){return e>=0&&void 0!==jn[e]?jn[e]:!1},allowProgressiveImg:function(){return l.forceProgressiveLoading||!M||l.mouseUsed||screen.width>1200},setContent:function(e,t){l.loop&&(t=kt(t));var n=a.getItemAt(e.index);n&&(n.container=null);var i,r=a.getItemAt(t);if(!r)return void(e.el.innerHTML="");Et("gettingData",t,r),e.index=t,e.item=r;var s=r.container=o.createEl("pswp__zoom-wrap");if(!r.src&&r.html&&(r.html.tagName?s.appendChild(r.html):s.innerHTML=r.html),pi(r),ci(r,vt),!r.src||r.loadError||r.loaded)r.src&&!r.loadError&&(i=o.createEl("pswp__img","img"),i.style.opacity=1,i.src=r.src,mi(r,i),di(t,r,s,i,!0));else{if(r.loadComplete=function(n){if(c){if(e&&e.index===t){if(pi(n,!0))return n.loadComplete=n.img=null,ci(n,vt),Ft(n),void(e.index===p&&a.updateCurrZoomItem());n.imageAppended?!Qn&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):Z.transform&&(at||Qn)?ai.push({item:n,baseDiv:s,img:n.img,index:t,holder:e,clearPlaceholder:!0}):di(t,n,s,n.img,at||Qn,!0)}n.loadComplete=null,n.img=null,Et("imageLoadComplete",t,n)}},o.features.transform){var d="pswp__img pswp__img--placeholder";d+=r.msrc?"":" pswp__img--placeholder--blank";var u=o.createEl(d,r.msrc?"img":"");r.msrc&&(u.src=r.msrc),mi(r,u),s.appendChild(u),r.placeholder=u}r.loading||ui(r),a.allowProgressiveImg()&&(!Jn&&Z.transform?ai.push({item:r,baseDiv:s,img:r.img,index:t,holder:e}):di(t,r,s,r.img,!0,!0))}Jn||t!==p?Ft(r):(ot=s.style,ii(r,i||r.img)),e.el.innerHTML="",e.el.appendChild(s)},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var fi,vi={},_i=function(e,t,n){var i=document.createEvent("CustomEvent"),o={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};i.initCustomEvent("pswpTap",!0,!0,o),e.target.dispatchEvent(i)};It("Tap",{publicMethods:{initTap:function(){zt("firstTouchStart",a.onTapStart),zt("touchRelease",a.onTapRelease),zt("destroy",function(){vi={},fi=null})},onTapStart:function(e){e.length>1&&(clearTimeout(fi),fi=null)},onTapRelease:function(e,t){if(t&&!V&&!Y&&!Qt){var n=t;if(fi&&(clearTimeout(fi),fi=null,Cn(n,vi)))return void Et("doubleTap",n);if("mouse"===t.type)return void _i(e,t,"mouse");var i=e.target.tagName.toUpperCase();if("BUTTON"===i||o.hasClass(e.target,"pswp__single-tap"))return void _i(e,t);Lt(vi,n),fi=setTimeout(function(){_i(e,t),fi=null},300)}}}});var gi;It("DesktopZoom",{publicMethods:{initDesktopZoom:function(){P||(M?zt("mouseUsed",function(){a.setupDesktopZoom()}):a.setupDesktopZoom(!0))},setupDesktopZoom:function(t){gi={};var n="wheel mousewheel DOMMouseScroll";zt("bindEvents",function(){o.bind(e,n,a.handleMouseWheel)}),zt("unbindEvents",function(){gi&&o.unbind(e,n,a.handleMouseWheel)}),a.mouseZoomedIn=!1;var i,r=function(){a.mouseZoomedIn&&(o.removeClass(e,"pswp--zoomed-in"),a.mouseZoomedIn=!1),1>g?o.addClass(e,"pswp--zoom-allowed"):o.removeClass(e,"pswp--zoom-allowed"),s()},s=function(){i&&(o.removeClass(e,"pswp--dragging"),i=!1)};zt("resize",r),zt("afterChange",r),zt("pointerDown",function(){a.mouseZoomedIn&&(i=!0,o.addClass(e,"pswp--dragging"))}),zt("pointerUp",s),t||r()},handleMouseWheel:function(e){if(g<=a.currItem.fitRatio)return l.modal&&(!l.closeOnScroll||Qt||G?e.preventDefault():S&&Math.abs(e.deltaY)>2&&(u=!0,a.close())),!0;if(e.stopPropagation(),gi.x=0,"deltaX"in e)1===e.deltaMode?(gi.x=18*e.deltaX,gi.y=18*e.deltaY):(gi.x=e.deltaX,gi.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(gi.x=-.16*e.wheelDeltaX),gi.y=e.wheelDeltaY?-.16*e.wheelDeltaY:-.16*e.wheelDelta;else{if(!("detail"in e))return;gi.y=e.detail}Wt(g,!0);var t=ft.x-gi.x,n=ft.y-gi.y;(l.modal||t<=it.min.x&&t>=it.max.x&&n<=it.min.y&&n>=it.max.y)&&e.preventDefault(),a.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:vt.x/2+gt.x,y:vt.y/2+gt.y};var n=l.getDoubleTapZoom(!0,a.currItem),i=g===n;a.mouseZoomedIn=!i,a.zoomTo(i?a.currItem.initialZoomLevel:n,t,333),o[(i?"remove":"add")+"Class"](e,"pswp--zoomed-in")}}});var yi,wi,xi,bi,$i,Ci,Ii,ki,Ti,zi,Ei,Si,Di={history:!0,galleryUID:1},Mi=function(){return Ei.hash.substring(1)},Oi=function(){yi&&clearTimeout(yi),xi&&clearTimeout(xi)},Fi=function(){var e=Mi(),t={};if(e.length<5)return t;var n,i=e.split("&");for(n=0;n<i.length;n++)if(i[n]){var o=i[n].split("=");o.length<2||(t[o[0]]=o[1])}if(l.galleryPIDs){var a=t.pid;for(t.pid=0,n=0;n<jn.length;n++)if(jn[n].pid===a){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},Ai=function(){if(xi&&clearTimeout(xi),Qt||G)return void(xi=setTimeout(Ai,500));bi?clearTimeout(wi):bi=!0;var e=p+1,t=ei(p);t.hasOwnProperty("pid")&&(e=t.pid);var n=Ii+"&gid="+l.galleryUID+"&pid="+e;ki||-1===Ei.hash.indexOf(n)&&(zi=!0);var i=Ei.href.split("#")[0]+"#"+n;Si?"#"+n!==window.location.hash&&history[ki?"replaceState":"pushState"]("",document.title,i):ki?Ei.replace(i):Ei.hash=n,ki=!0,wi=setTimeout(function(){bi=!1},60)};It("History",{publicMethods:{initHistory:function(){if(o.extend(l,Di,!0),l.history){Ei=window.location,zi=!1,Ti=!1,ki=!1,Ii=Mi(),Si="pushState"in history,Ii.indexOf("gid=")>-1&&(Ii=Ii.split("&gid=")[0],Ii=Ii.split("?gid=")[0]),zt("afterChange",a.updateURL),zt("unbindEvents",function(){o.unbind(window,"hashchange",a.onHashChange)});var e=function(){Ci=!0,Ti||(zi?history.back():Ii?Ei.hash=Ii:Si?history.pushState("",document.title,Ei.pathname+Ei.search):Ei.hash=""),Oi()};zt("unbindEvents",function(){u&&e()}),zt("destroy",function(){Ci||e()}),zt("firstUpdate",function(){p=Fi().pid});var t=Ii.indexOf("pid=");t>-1&&(Ii=Ii.substring(0,t),"&"===Ii.slice(-1)&&(Ii=Ii.slice(0,-1))),setTimeout(function(){c&&o.bind(window,"hashchange",a.onHashChange)},40)}},onHashChange:function(){return Mi()===Ii?(Ti=!0,void a.close()):void(bi||($i=!0,a.goTo(Fi().pid),$i=!1))},updateURL:function(){Oi(),$i||(ki?yi=setTimeout(Ai,800):Ai())}}}),o.extend(a,an)};return e}),/*! PhotoSwipe Default UI - 4.1.2 - 2017-04-05
* http://photoswipe.com
* Copyright (c) 2017 Dmitry Semenov; */
function(e,t){"function"==typeof define&&define.amd?define(t):"object"==typeof exports?module.exports=t():e.PhotoSwipeUI_Default=t()}(this,function(){"use strict";var e=function(e,t){var n,i,o,a,r,s,l,c,d,u,p,m,h,f,v,_,g,y,w,x=this,b=!1,$=!0,C=!0,I={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"Whatsapp",label:"Share via Whatsapp",url:"whatsapp://send?text=Check this out! {{url}}"},{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href.split("?")[0]},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},k=function(e){if(_)return!0;e=e||window.event,v.timeToIdle&&v.mouseUsed&&!d&&R();for(var n,i,o=e.target||e.srcElement,a=o.getAttribute("class")||"",r=0;r<W.length;r++)n=W[r],n.onTap&&a.indexOf("pswp__"+n.name)>-1&&(n.onTap(),i=!0);if(i){e.stopPropagation&&e.stopPropagation(),_=!0;var s=t.features.isOldAndroid?600:30;g=setTimeout(function(){_=!1},s)}},T=function(){return!e.likelyTouchDevice||v.mouseUsed||screen.width>v.fitControlsWidth},z=function(e,n,i){t[(i?"add":"remove")+"Class"](e,"pswp__"+n)},E=function(){var e=1===v.getNumItemsFn();e!==f&&(z(i,"ui--one-slide",e),f=e)},S=function(){z(l,"share-modal--hidden",C)},D=function(){return C=!C,C?(t.removeClass(l,"pswp__share-modal--fade-in"),setTimeout(function(){C&&S()},300)):(S(),setTimeout(function(){C||t.addClass(l,"pswp__share-modal--fade-in")},30)),C||O(),!1},M=function(t){t=t||window.event;var n=t.target||t.srcElement;return e.shout("shareLinkClick",t,n),n.href?n.hasAttribute("download")?!0:(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),C||D(),!1):!1},O=function(){for(var e,t,n,i,o,a="",r=0;r<v.shareButtons.length;r++)e=v.shareButtons[r],n=v.getImageURLForShare(e),i=v.getPageURLForShare(e),o=v.getTextForShare(e),t=e.url.replace("{{url}}",encodeURIComponent(i)).replace("{{image_url}}",encodeURIComponent(n)).replace("{{raw_image_url}}",n).replace("{{text}}",encodeURIComponent(o)),a+='<a href="'+t+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",v.parseShareButtonOut&&(a=v.parseShareButtonOut(e,a));l.children[0].innerHTML=a,l.children[0].onclick=M},F=function(e){for(var n=0;n<v.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+v.closeElClasses[n]))return!0},A=0,R=function(){clearTimeout(w),A=0,d&&x.setIdle(!1)},P=function(e){e=e?e:window.event;var t=e.relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(w),w=setTimeout(function(){x.setIdle(!0)},v.timeToIdleOutside))},L=function(){v.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=x.getFullscreenAPI()),n?(t.bind(document,n.eventK,x.updateFullscreen),x.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs"))},Z=function(){v.preloaderEl&&(N(!0),u("beforeChange",function(){clearTimeout(h),h=setTimeout(function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&N(!1):N(!0)},v.loadingIndicatorDelay)}),u("imageLoadComplete",function(t,n){e.currItem===n&&N(!0)}))},N=function(e){m!==e&&(z(p,"preloader--active",!e),m=e)},U=function(e){var n=e.vGap;if(T()){var r=v.barsSize;if(v.captionEl&&"auto"===r.bottom)if(a||(a=t.createEl("pswp__caption pswp__caption--fake"),a.appendChild(t.createEl("pswp__caption__center")),i.insertBefore(a,o),t.addClass(i,"pswp__ui--fit")),v.addCaptionHTMLFn(e,a,!0)){var s=a.clientHeight;n.bottom=parseInt(s,10)||44}else n.bottom=r.top;else n.bottom="auto"===r.bottom?0:r.bottom;n.top=r.top}else n.top=n.bottom=0},K=function(){v.timeToIdle&&u("mouseUsed",function(){t.bind(document,"mousemove",R),t.bind(document,"mouseout",P),y=setInterval(function(){A++,2===A&&x.setIdle(!0)},v.timeToIdle/2)})},H=function(){u("onVerticalDrag",function(e){$&&.95>e?x.hideControls():!$&&e>=.95&&x.showControls()});var e;u("onPinchClose",function(t){$&&.9>t?(x.hideControls(),e=!0):e&&!$&&t>.9&&x.showControls()}),u("zoomGestureEnded",function(){e=!1,e&&!$&&x.showControls()})},W=[{name:"caption",option:"captionEl",onInit:function(e){o=e}},{name:"share-modal",option:"shareEl",onInit:function(e){l=e},onTap:function(){D()}},{name:"button--share",option:"shareEl",onInit:function(e){s=e},onTap:function(){D()}},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){r=e}},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e}}],q=function(){var e,n,o,a=function(i){if(i)for(var a=i.length,r=0;a>r;r++){e=i[r],n=e.className;for(var s=0;s<W.length;s++)o=W[s],n.indexOf("pswp__"+o.name)>-1&&(v[o.option]?(t.removeClass(e,"pswp__element--disabled"),o.onInit&&o.onInit(e)):t.addClass(e,"pswp__element--disabled"))}};a(i.children);var r=t.getChildByClass(i,"pswp__top-bar");r&&a(r.children)};x.init=function(){t.extend(e.options,I,!0),v=e.options,i=t.getChildByClass(e.scrollWrap,"pswp__ui"),u=e.listen,H(),u("beforeChange",x.update),u("doubleTap",function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(v.getDoubleTapZoom(!1,e.currItem),t,333)}),u("preventDragEvent",function(e,t,n){var i=e.target||e.srcElement;i&&i.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(i.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(i.tagName))&&(n.prevent=!1)}),u("bindEvents",function(){t.bind(i,"pswpTap click",k),t.bind(e.scrollWrap,"pswpTap",x.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",x.onMouseOver)}),u("unbindEvents",function(){C||D(),y&&clearInterval(y),t.unbind(document,"mouseout",P),t.unbind(document,"mousemove",R),t.unbind(i,"pswpTap click",k),t.unbind(e.scrollWrap,"pswpTap",x.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",x.onMouseOver),n&&(t.unbind(document,n.eventK,x.updateFullscreen),n.isFullscreen()&&(v.hideAnimationDuration=0,n.exit()),n=null)}),u("destroy",function(){v.captionEl&&(a&&i.removeChild(a),t.removeClass(o,"pswp__caption--empty")),l&&(l.children[0].onclick=null),t.removeClass(i,"pswp__ui--over-close"),t.addClass(i,"pswp__ui--hidden"),x.setIdle(!1)}),v.showAnimationDuration||t.removeClass(i,"pswp__ui--hidden"),u("initialZoomIn",function(){v.showAnimationDuration&&t.removeClass(i,"pswp__ui--hidden")}),u("initialZoomOut",function(){t.addClass(i,"pswp__ui--hidden")}),u("parseVerticalMargin",U),q(),v.shareEl&&s&&l&&(C=!0),E(),K(),L(),Z()},x.setIdle=function(e){d=e,z(i,"ui--idle",e)},x.update=function(){$&&e.currItem?(x.updateIndexIndicator(),v.captionEl&&(v.addCaptionHTMLFn(e.currItem,o),z(o,"caption--empty",!e.currItem.title)),b=!0):b=!1,C||D(),E()},x.updateFullscreen=function(i){i&&setTimeout(function(){e.setScrollOffset(0,t.getScrollY())},50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},x.updateIndexIndicator=function(){v.counterEl&&(r.innerHTML=e.getCurrentIndex()+1+v.indexIndicatorSep+v.getNumItemsFn())},x.onGlobalTap=function(n){n=n||window.event;var i=n.target||n.srcElement;if(!_)if(n.detail&&"mouse"===n.detail.pointerType){if(F(i))return void e.close();t.hasClass(i,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?v.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(v.tapToToggleControls&&($?x.hideControls():x.showControls()),v.tapToClose&&(t.hasClass(i,"pswp__img")||F(i)))return void e.close()},x.onMouseOver=function(e){e=e||window.event;var t=e.target||e.srcElement;z(i,"ui--over-close",F(t))},x.hideControls=function(){t.addClass(i,"pswp__ui--hidden"),$=!1},x.showControls=function(){$=!0,b||x.update(),t.removeClass(i,"pswp__ui--hidden")},x.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},x.getFullscreenAPI=function(){var t,n=document.documentElement,i="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:i}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+i}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+i}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return c=v.closeOnScroll,v.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?e.template[this.enterK]():void e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return v.closeOnScroll=c,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}};return e}),function(){var e,t,n,i,o,a,r,s,l,c,d,u,p,m,h,f;$(document).on("click",".design-info-blocks a.main",function(e){var t;e.preventDefault(),t=$(this).attr("href"),$(this).find("span").toggle(),$(t).slideToggle("fast")}),$(document).on("click",".design-info-sub-blocks a",function(e){var t;e.preventDefault(),t=$($(this).attr("href")).is(":visible"),$(".sub-specs-table").hide(),$(".sub-grp span.button-icon").removeClass("button-toggle"),t||($(this).find("span.button-icon").addClass("button-toggle"),$($(this).attr("href")).show())}),c=function(e,t,n){$.ajax({url:"/save_review",type:"Post",data:{design_id:e,rating:t,review:n},datatype:"JSON",success:function(e){200===e.status?"false"===n?($("#save-alert-message").show().css("color","#21a95a").text("Rating Saved Successfully"),$("#save-alert-message").data("rating-given",t)):($("#save-alert-message").show().css("color","#21a95a").text(e.message),window.location.reload()):$("#save-alert-message").show().text(e.message)},error:function(){}})},$(document).on("click","#review-submit-btn",function(){var e,t,n;t=$("#form-rating-star input[name='score']").val(),n=$("textarea#review-text").val(),e=$(this).data("design-id"),$("#save-alert-message").hide(),n.trim().length>30?($("textarea#review-text").removeClass("alert-border"),$("#rating-alert-message, #review-alert-message").hide(),c(e,t,n)):0===t.length?$("#rating-alert-message").show():0!==$("#save-alert-message").data("rating-given")?($("#rating-alert-message").hide(),$("textarea#review-text").addClass("alert-border"),$("#review-alert-message").show()):($("#rating-alert-message").hide(),$("textarea#review-text").addClass("alert-border"),$("#review-alert-message").show(),c(e,t,"false"))}),$(document).on("change","#pre-order-check",function(){var e;return e=this.checked?$(this).val():$(".delivery_day").data("date"),$(".delivery_day").html(""+e)}),$(document).on("change",".addon_types",function(){var e,t,n,o,a,r,s,l;a=[],l=$(this).find(":selected"),s=l.val(),o="#"+l.text().split("-")[0].trim().toLowerCase().replace(/ /g,"_"),$(".info_message").hide(),$(o).css("display","block"),$("#unstitch").val()===s&&$("#stitchingModal").length>0&&h(),$(".custom").val()===s||$(".petticoat_stitching").val()===s||$("#unstitch").val()||$(".pre_stitch").val()===s?$(this).next().addClass("hide"):$(this).next().removeClass("hide"),$(".custom").val()===s&&$(".plus_size_blouse_custom").length>0?$(".plus_size_blouse_custom").show():$(".plus_size_blouse_custom").hide(),l.hasClass("standard-stitch-variant")?i($(".standard-stitch-variant").attr("selected-variant-oldprice"),$(".standard-stitch-variant").attr("selected-variant-price")):l.hasClass("custom")&&void 0!==l.attr("data-variant-id")&&i(l.attr("data-old-price"),l.attr("data-price")),n="delivery-date",$(".addon_types").each(function(){var e,t,i;return t=$(this).find(":selected"),t.hasClass("standard")&&$("a.button.size.selected").length>0?(i=$("a.button.size.selected"),0===parseInt(i.data("prodtime"))&&(n="rts-date")):i=t,e=void 0!==i.data(n)&&i.data(n).length>0?i.data(n):$("#pre-order-check").length>0&&$("#pre-order-check").is(":checked")?$("#pre-order-check").val():i.attr("data-delivery-date"),a.push([parseInt(i.data("prodtime")),e,i.data("ready-to-ship")])}),r=a.sort().pop(),$(".delivery_day").html(r[1]),"true"===r[2]?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),t="#atv_"+s,e=$(t).find(".columns").children().length,(e>0||$(t).find(".size-chart-div").length>0)&&(l.hasClass("standard")||l.hasClass("custom")?$(".addon_option_types").hide():(l.hasClass("petticoat_stitching")||l.hasClass("pre_stitch"))&&$(".addon_option_type_without_size").hide(),$(t).show(),$(this).next().removeClass("hide"))}),$(document).on("change",".addon_types",function(){var e;return(e=$(".addon_types").find(":selected").hasClass("custom"))&&!$(".stitching_note").is(":visible")?$(".stitching_note").removeClass("hide"):e?void 0:$(".stitching_note").addClass("hide")}),i=function(e,t,n,i,o){var a,r;return null==n&&(n=void 0),null==i&&(i=!1),null==o&&(o=void 0),void 0!==o?(a=$(".product_discount_price_"+o),r=$(".product_price_wo_discount_"+o)):(a=$(".product_discount_price"),r=$(".product_price_wo_discount")),void 0!==t&&a[0].innerText!==t&&(a.text(t),a.addClass("shake-effect"),$(".changeInPriceNote").fadeIn().css("display","inline-flex"),setTimeout(function(){return a.removeClass("shake-effect"),$(".changeInPriceNote").fadeOut()},750),r.text(e)),void 0!==n?("true"===i?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),$(".delivery_day").html(n)):void 0},h=function(){return"true"===$("#unstitch").attr("popup")&&$(".addon_types").first().next().addClass("hide"),$("#unstitch").val()===$(".addon_types").find(":selected").val()&&"false"===$("#unstitch").attr("popup")?($(".addon_types").first().next().addClass("hide"),$("#main-section").css({position:"fixed",overflow:"hidden"}),$(".fixed").css("z-index","0"),$("#stitchingModal").show(),$("#unstitch").attr("popup","true")):void 0},r=function(){var e;return e=!1,$("#pre-order-check").length>0&&(e=$("#pre-order-check").is(":checked")?!0:!1),e},$(document).on("click",".plus_size_fabric_option",function(){return $(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),$(this).addClass("plus_size_fabric_selected")}),o=function(){var e,t,n;return n=!0,t=!0,$(".regular_plus_options").length>0?(e=$("select",".regular_plus_options"),e.each(function(){return"0"===this.value?t=!1:void 0})):t=!1,$(".addon_types").each(function(){var e,i;i=void 0,$(this).val().trim()||(n=!1,$(this).css({"border-color":"#d50909","border-width":"2px"})),e="#atv_"+this.value,$(e).length>0&&(i=$("select",e),i.css({"border-color":"","border-width":""}),i.each(function(){$(this).hasClass("plus_size_regular_select")||"0"!==this.value||$(this).hasClass("plus_size_custom_select")&&$(".plus_size_custom_regular").hasClass("selected_custom_plus_size")||(n=!1)}),n===!1?($(e).show(),i.each(function(){"0"===this.value&&$(this).css({"border-color":"#d50909","border-width":"2px"})})):i.css({"border-color":"","border-width":""}),$(".size").length>0&&$(".size-chart").is(":visible")&&($(".regular_plus_options").length>0?1===$(".size.selected").length||t||(alert("Please select a Size"),n=!1):1!==$(".size.selected").length&&(alert("Please select a Size"),n=!1))),($(".plus_size_regular_select",e).length>0&&1!==$(".size.selected").length&&!$(".plus_size_fabric_option",".regular_plus_options").hasClass("plus_size_fabric_selected")||$(".plus_size_custom_select",e).length>0&&$(".plus_size_custom").hasClass("selected_custom_plus_size")&&!$(".plus_size_fabric_option",".custom_hide_option_type").hasClass("plus_size_fabric_selected"))&&(alert("Please select fabric color"),n=!1)}),n===!1&&alert("Please select highlighted options"),n},s=function(){var e,t,n;return n=!0,$(".select-size").removeClass("shake-effect"),$(".variant, .variant_stitch").is(":visible")&&(n=!1,(1===$(".variant.selected, .variant_stitch.selected").length||$(".variant.selected").length>1)&&(n=!0)),$(".variant").length<1&&$(".variant_modal").is(":visible")&&$(".variant_modal.selected").length>1&&(n=!0),n===!1&&(e=$(".add_to_buy_bow").attr("design_id"),t=$("#variant_select_"+e),t.length>0&&d(t),$(".select-size").addClass("shake-effect")),n},u=function(e,t){var n,i,o;n=[];for(o in e)i=e[o],n.push(i.id),ga("ec:addProduct",i),ga("ec:setAction","add"),ga("send","event","UX","click","add to cart"),t&&ga("send","event","UX","click","add to cart with ajax"),window._pq=window._pq||[],_pq.push(["track","add_to_cart"]),"undefined"!=typeof fbq&&fbq("track","AddToCart",{value:i.price,currency:"INR",content_ids:i.id,content_type:"product",content_category:i.category});n.length>1&&ga("send","event","complete_the_look","add_to_cart",n.join("-"))},e=function(e,t,n){var i,o,a,s,l;o=[],i={quantity:1},i.design_id=$("#product_id").html().split(" ").pop(),i.rakhi_note=r(),a=[],l=$(".addon_types option.custom").is(":selected")&&$(".variant_stitch").length>0?$(".addon_types option.custom").attr("data-variant-id"):$(".variant_stitch").length>0?$(".variant_stitch.selected").attr("id"):$(".variant.selected").attr("id"),i.variant_id=l,$(".variant_stitch").is(":visible")||$(".addon_types").each(function(){var e,t,n,o,r,s,l,c;s="",c="",o=$(this).val(),n=$("#atv_"+o),e={},t=[],c=$(".size.selected").attr("id"),n.length>0&&(l=$("select",n),l.length>0&&l.each(function(){$("option:first",this).text()!==$("option:selected",this).text()&&(s=s+$("option:first",this).text()+" : "+$("option:selected",this).text()+", ")}),$("input:checked",n).each(function(){s=s+this.name+", ",t.push(this.value)}),$(".plus_size_fabric_option",n).hasClass("plus_size_fabric_selected")&&(r=$(".plus_size_fabric_selected"),s=s+r.data("optionType")+" : "+r.data("color")+", "),$(".common_addons",n).size()>0&&$(".size.selected").length>0&&$(".size-chart").is(":visible")&&(s=s+" Standard Stitching Size : size-"+c),e.addon_option_type_id=t),e.addon_type_value_id=o,e.notes=s.replace(RegExp("  ","g"),""),a.push(e),i.line_item_addons_attributes=a}),o.push(i),$(".addon_product:checked").each(function(){return i={quantity:1},i.design_id=this.value,i.pair_product=!0,$("#variant_select_"+this.value).length>0&&(i.variant_id=$("#variant_select_"+this.value+" .variant_modal.selected").attr("id")),o.push(i)}),s={line_items:o,design_page:!0},$.ajax({type:"POST",data:s,url:e,dataType:"JSON",success:function(e){u(e.ga_hash,t),t?($(".cart_count").html(e.cart_count),$(".buy_now").hide(),$(".go_to_cart").show(),$("#add_to_cart_message").fadeIn("slow"),setTimeout(function(){$("#add_to_cart_message").fadeOut("slow")},2e3)):window.location.assign(e.redirect_url)},beforeSend:function(){n||($(".progress_img").show(),$("#design_image_block").css("opacity","0.4"))},complete:function(){n||($(".progress_img").hide(),$("#design_image_block").css("opacity","1"))}})},m=function(){return $("#main-section").css({position:"inherit",overflow:"auto"}),$(".fixed").css("z-index","99")},$(document).on("click",".variant, .variant_stitch",function(){var e,t;e=$(this),$(".variant-price-text").html("Price shown is for the size selected"),e.hasClass("disabled")===!1&&($(".variant, .variant_stitch").removeClass("selected alert"),e.addClass("selected alert"),e[0].className.indexOf("variant")>0&&(t=e.attr("id"),$("#"+t).addClass("selected alert")),i(e.attr("data-old-price"),e.attr("data-price"),e.attr("data-delivery-date"),e.attr("data-ready-to-ship")),$(".variant_stitch").length>0&&($(".standard-stitch-variant").attr("selected-variant-price",$(".product_discount_price").text()),$(".standard-stitch-variant").attr("selected-variant-oldprice",$(".product_price_wo_discount").text())))}),d=function(e){var t,n;return e.addClass("animate"),n=window.innerHeight-e.children(".content").height(),t="translate3d(0px,"+(n-20)+"px, 0px)",e.css("transform",t),e.siblings(".variant-bg").css({"pointer-events":"auto",opacity:"1"})},l=function(){return $(".addon_product:checked").each(function(){var e;return e=$("#variant_select_"+this.value),e.length>0&&!e.find(".variant_modal").hasClass("selected")||e.find(".variant_modal").hasClass("variant-selected")?($(this).siblings("label").show(),$(this).prop("checked",!1)):void 0}),$(".variant_selection").css("transform","translate3d(0px, 110%, 0px)"),$(".variant-bg").css({"pointer-events":"none",opacity:"0"})},$(document).on("click",".plus_size_blouse_regular",function(){return $(".standard_option_values, .regular_plus_options").show(),$(".plus_size_blouse_regular").addClass("plus_size_regular_btn"),$(".size").removeClass("selected")}),$(document).on("click",".plus_size_custom",function(){return $(".custom_hide_option_type").show(),$(".plus_size_custom").addClass("selected_custom_plus_size"),$(".plus_size_custom_regular").removeClass("selected_custom_plus_size"),$(".plus_size_custom_regular").addClass("other_custom_plus_size"),$(".plus_size_custom").removeClass("other_custom_plus_size")}),$(document).on("click",".plus_size_custom_regular",function(){var e;return $(".custom_hide_option_type").hide(),e=$("select",".custom_hide_option_type"),$(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),e.length>0&&e.each(function(){return $(this).find("option:eq(0)").prop("selected",!0)}),$(".plus_size_custom_regular").addClass("selected_custom_plus_size"),$(".plus_size_custom").addClass("other_custom_plus_size"),$(".plus_size_custom").removeClass("selected_custom_plus_size"),$(".plus_size_custom_regular").removeClass("other_custom_plus_size")}),$(document).on("click",".addon_product",function(){var e,t;return e=$(this),e.siblings("label").toggle(),t=$("#variant_select_"+this.value),t.length>0?e.is(":checked")?d(t):void 0:ga("send","event","complete_the_look","addon_product_view",this.value)}),$(document).on("click",".variant_modal",function(){var e,t,n;return n=$(this),e=n.attr("value"),t=n.parents("#variant_select_"+e),t.find(this).hasClass("disabled")===!1&&(t.find(".variant_modal").removeClass("variant-selected alert"),t.find(this).addClass("variant-selected alert")),i(n.attr("data-old-price"),n.attr("data-price"),n.attr("data-delivery-date"),n.attr("data-ready-to-ship"),e)}),$(document).on("click",".select-addon-variant",function(){var e,t,n;return t=$(this),n=t.attr("value"),e=t.parents("#variant_select_"+n),e.find(".variant_modal").each(function(){return $(this).hasClass("variant-selected")?(e.find(".variant_modal").removeClass("selected alert variant-selected"),$(this).addClass("selected alert"),ga("send","event","complete_the_look","addon_product_view",n)):void 0}),l()}),$(document).on("click",".variant-bg, .close-variant-select",function(){return l()}),$(document).on("click",".size",function(){var e,t,n,i,o,a,r;$(".regular_plus_options").hide(),n=$("select",".regular_plus_options"),$(".plus_size_blouse_regular").removeClass("plus_size_regular_btn"),$(".plus_size_fabric_option").removeClass("plus_size_fabric_selected"),n.length>0&&n.each(function(){return $(this).find("option:eq(0)").prop("selected",!0)}),(o=$("#salwar_kameez_specific"))&&(r=parseInt($(this).text(),10),o.find("var:first").text(r+2),o.find("var:last").text(r+3),o.show(),$("#salwar_kameez_default").hide()),$(this).data("delivery-date").length>0&&(t=[],a=parseInt($(this).data("prodtime")),e=0===a?"rts-date":"delivery-date",t.push([a,$(this).data("delivery-date"),$(this).data("ready-to-ship")]),$(".addon_types").each(function(){return $(this).is(":checked")&&!$(this).hasClass("standard")?t.push([parseInt($(this).data("prodtime")),$(this).data(e),$(this).data("ready-to-ship")]):void 0}),i=t.sort().pop(),i[2]===!0?$("#ready_to_ship, del.strike_old_date").show():$("#ready_to_ship, del.strike_old_date").hide(),$(".delivery_day").html(i[1])),$(this).hasClass("disabled")===!1&&($(".size").removeClass("selected alert"),$(this).addClass("selected alert"),$(".standard_option_values").show(),$(".std_size_chart tr").removeClass("highlight"),$(".std_size_chart td:first-child:contains('"+$(this).text()+"')").parent("tr").addClass("highlight"))}),$(document).on("change","#show_rts_products",function(){return $(this).is(":checked")?$.each($(this).data("rts-sizes"),function(e,t){return $("#"+t).css("border","2px solid #7b0e1d")}):$("a.button.size").css("border","1px solid #aba9a4")}),$(document).on("click",".add_to_buy_bow, #buy_now, #add_to_cart",function(){m(),$("#stitchingModal").hide(),s()&&o()&&("undefined"!=typeof Unbxd&&unbxdTrack("addToCart",{pid:$("#product_id").html().split(" ").pop()}),e($(this).attr("data-targeturl"),!1,$(this).hasClass("add_to_buy_bow")))}),$(document).on("click",".stitch",function(){var e;return m(),$("#stitchingModal").hide(),$(".standard").prop("selected",!0),e=$(".addon_types").find(":selected").val(),$(".standard").val()===e?$(".addon_types").first().next().removeClass("hide"):void 0}),$(document).on("click",".closeStitchingModal",function(){return m(),$("#stitchingModal").hide()}),$(document).on("click","#size-chart-btn, #dynamic-size-chart-btn, #add_review, .view-more-btn",function(){return $("body").addClass("modal-open").css("margin-top","0px")}),$(document).on("click",".close-review-more",function(){return $("body").removeClass("modal-open")}),$(document).foundation("reveal",{animation:!1}),f=function(){return $("#variants_block").length>0?$(".variant").each(function(){return $(".product_discount_price").text().trim()===$(this).attr("data-price")?($(".variant-price-text").html("The price shown is for size : "+$(this).text()),!1):void 0}):void 0},f(),$(document).on("turbolinks:render",function(){return t(),f()}),$(function(){return $(".aov_select").on("change",function(){var e,t,n,i,o,a;return $("#standard_height_notice").length>0?null!==(i=$("#"+this.id+" option:selected").text().match(/\d+/g))?(e=i.map(Number).slice(0,2),t=12*e[0],e.length>1&&(t+=e[1]),n=$("#standard_height_notice").data("default"),$.each($("#standard_height_notice").data(),function(e,i){return t>=e?n=i:void 0}),$(".std_height_img").hide(),$("#std-"+n).show(),a=$($("#standard_info_icon span").length>0?"#standard_info_icon span":"<span style= 'font-size:11px;'></span>"),o=a.text("For the selected size, kameez will fall "+n.replace(/_/g," ")+" area"),$("#standard_info_icon").prepend(o),$("#standard_height_notice").show(),setTimeout(function(){return $("#standard_height_notice").fadeOut()},3e3),$("#standard_info_icon").show()):($("#standard_height_notice").hide(),$("#standard_info_icon").hide()):void 0})}),$(document).on("click","#standard_info_icon a",function(){return $("#standard_height_notice").toggle(),setTimeout(function(){return $("#standard_height_notice").fadeOut()},3e3)}),$(document).on("tap",function(){return $("#standard_height_notice").is(":visible")?$("#standard_height_notice").hide():void 0}),t=function(){($(".design_image").length>1||1===$(".design_image").length&&1===$(".design_video").length)&&($("#design_images").attr({"data-orbit":"","data-timer":"false"}),$("#design_images").css("text-align","initial"),$(".orbit-timer").hide(),$(document).foundation({orbit:{animation_speed:100,autoplay:!1,navigation_arrows:!0,bullets:!0,variable_height:!0,timer:!1,circular:!1}}),$(".label_for_image_box").length>0&&$(".label_for_image_box, .label_text").css("display","inline-block"))},t(),n=function(e){var t,n,i,o;$(window).scrollTop(100),$(".fixed").css("z-index","0"),$("#branch-banner-iframe").css("display","none"),o=document.querySelectorAll(".pswp")[0],n=[],$(".design_image").each(function(){var e,t,i,o,a,r;return e=$(this),i=e[0].id,o=e[0].width,t=e[0].height,r=o>2?1.5*o:400*o,a=t>2?1.5*t:400*t,n.push("product_video_src"===i?{html:'<video class="design_video" id="product_video_zoom" controls muted playsinline loop user-scalable="no"><source src="'+e.data("src")+'" type="video/mp4"></video>'}:{src:e.data("src"),w:r,h:a,title:"Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution."})}),i={index:e.data("index")},t=new PhotoSwipe(o,PhotoSwipeUI_Default,n,i),t.listen("gettingData",function(e,t){var n;n=new Image,n.src=t.src,0!==n.width&&(t.w=n.width,t.h=n.height)}),t.listen("afterChange",function(){var e;e=t.currItem.html,void 0!==e?$("#product_video_zoom")[0].play():$("#product_video_zoom")[0]&&($("#product_video_zoom")[0].pause(),$("#product_video_zoom")[0].muted=!0)}),t.listen("destroy",function(){return $(".fixed").css("z-index","99"),$("#branch-banner-iframe").fadeIn()}),t.init()},$(document).on("click",".design_image",function(){return n($(this))}),$(document).on("click","#share-now-button",function(){return p($(this)),ga("send","event","UX","click","share and earn")}),p=function(e){var t,n,i,o,a;return t=e,signedIn()?(i="check this out",o=t.attr("data-d-link")+"?utm_medium=mirraw_mobile_web&referral_campaign=share_and_earn_user_"+t.attr("data-u-id"),n=encodeURIComponent(i)+" - "+encodeURIComponent(o),a="whatsapp://send?text="+n,window.location.href=a):window.location="/accounts/sign_in"},$(function(){return $(document).scroll(function(){var e,t,n;return $(".buy-now").length>0?(e=$("#action_buttons"),t=$(this).scrollTop(),n=$(".buy-now").offset().top-window.innerHeight+15,""===$(".line_items_count_text").text()?$("#line_items_count").hide():$("#line_items_count").show(),t>n?e.removeClass("fixed_button"):e.addClass("fixed_button")):void 0})}),$(function(){return $(document).on("click","#check_for_pdd",function(){var e,t;return t=$("#pdd_product_id").val(),e=$("#pin_code").val(),""!==e?a(t,e):void 0})}),a=function(e,t){var n;return n=/(^\d{6}$)/,null!=e&&null!=t?$.ajax({type:"GET",data:{pincode:t,id:e},url:"/designs/pdd_design",success:function(e){var i;return e.error===!1?(i=e.eta,$(".delivery_block").show(),$(".delivery_day").html(i).css("color","#303030"),$(".pdd_error_message").hide()):n.test(t)?($(".delivery_block").hide(),$(".pdd_error_message").show(),$(".pdd_error_message").html("Unable to calculate, Please try another pincode").css("color","red")):($(".delivery_block").hide(),$(".pdd_error_message").show(),$(".pdd_error_message").html("<strong>Invalid pincode.</strong> Please enter a valid pincode").css("color","red"))}}):void 0}}.call(this);