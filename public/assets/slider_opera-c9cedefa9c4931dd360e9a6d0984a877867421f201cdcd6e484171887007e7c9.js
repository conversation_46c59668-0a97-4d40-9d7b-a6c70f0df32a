$(document).ready(function() {

  var currentIndex = 0,
  items = $('#design_images_opera div'),
  itemAmt = items.length;

  items.eq(0).css('display', 'inline-block');

  function cycleItems() {
    var item = $('#design_images_opera div').eq(currentIndex);
    items.hide();
    item.css('display','inline-block');
  }

  $('.opera_next').click(function() {
    currentIndex += 1;
    if (currentIndex > itemAmt - 1) {
      currentIndex = 0;
    }
    cycleItems();
  });

  $('.opera_prev').click(function() {
    currentIndex -= 1;
    if (currentIndex < 0) {
      currentIndex = itemAmt - 1;
    }
    cycleItems();
  });  
});
