(function(){$(function(){var e,t,a,s,i,n,r,l,_,o,m,u,c,h,d,v,p,g;return $(document).foundation(),$(".other_number_field").on("keydown keyup",function(e){return $(this).val()<0&&$(this).val(0),$(this).val()>100&&$(this).val(100),13===e.which?e.preventDefault():void 0}),$(".help_link").click(function(){return $("body").css("overflow","hidden")}),$("#top_form").hasClass("active")||$("#top_form").addClass("active"),$(document).on("close.fndtn.reveal",".reveal-modal",function(){return $("body").css("overflow","scroll"),$(".example-orbit").css("height","200px")}),$(".other_button").click(function(){var e,t,a,s,i;return t=$(this).data("key"),s=$(this).data("min"),a=$(this).data("max"),i=$("#other_field_"+t).val(),e="accuracy_info"===t||"source_info"===t,!e&&isNaN(i)||""===i.trim()||(!isNaN(a)&&parseFloat(i)>a||!isNaN(s)&&parseFloat(i)<s?($("#mapping_error_"+t).removeClass("hide"),setTimeout(function(){$("#mapping_error_"+t).addClass("hide")},2e3)):($("#measurement_other_"+t).show(),$("#measurement_text_field_"+t).hide(),$("#other_field_"+t).val(""),$("#stitching_measurement_"+t).val(i),e||(i+=" Inch "),$("#measurement_other_"+t).html(i))),v(t,parseFloat(i))}),$(".measurement_li").click(function(){var e;return e=$(this).data("key"),$("#measurement_list_"+e).children().removeClass("active_li"),$(this).addClass("active_li"),$(this).data("dependent-mapping")===!0?s($(this).data("value")):void 0}),s=function(e){var t,a,s,i,n,r,l;for(n=$("#product_mapping").data($("#stitching_measurement_design_id").val()+"-"+e),a=1,r=["length","sleeves_length"],l=[],t=0,i=r.length;i>t;t++)s=r[t],$("."+s+"_size_li").each(function(){return $(this).show(),!isNaN(parseInt($(this).data("value")))&&(parseInt($(this).data("value"))<n[a][0]||parseInt($(this).data("value"))>n[a][1])||isNaN(parseInt($(this).data("value")))&&0!==n[a][0]?$(this).hide().removeClass("active_li"):void 0}),0===$("."+s+"_size_li.active_li").length&&$("#stitching_measurement_"+s).val(""),l.push(a++);return l},v=function(e,t){var a,s;return a=h(e).replace("Size","").replace("Length","").replace("Chest","Bust"),a=""===a?"Blouse":a,isNaN(t)?$("#fitting_notice_"+e).html(""):(s=$("#measurement_type_garments_body_measurement").is(":checked")&&$("#measurement_type_garments_garment_measurement").is(":visible")&&-1!==jQuery.inArray(e,["chest_size","hip_size","waist_size"])?"Final garment that will be made will have "+a+" length of "+(t+2)+" to "+(t+3)+" inches":"chest_size"!==e||$("#mapping_error_"+e).hasClass("hide")?"Final garment that will be made will have "+a+" length of "+t+" inches":"",$("#fitting_notice_"+e).html(s))},$(".measurement_type_radio").change(function(){var e,t,a,s,i;for(s=["chest_size","waist_size","hip_size"],i=[],e=0,a=s.length;a>e;e++)t=s[e],i.push(v(t,parseFloat($("#stitching_measurement_"+t).val())));return i}),$(".other_field").click(function(){var e,t;return e=$(this).data("key"),$("#stitching_measurement_"+e).val(""),$("#measurement_text_field_"+e).show(),$("#measurement_other_"+e).hide(),t=$("#measurement_other_"+e).text(),"other"!==t.toLowerCase()?$("#other_field_"+e).val(t):void 0}),$(".measurement_options").click(function(){var e,t,a;return e=$(this).data("count"),t=$(this).data("key"),a=$(this).data("value"),$("#stitching_measurement_"+t).val(a),$("#measurement_text_field_"+t).hide(),$("#measurement_other_"+t).show(),0===$(".data_entry_"+e).length&&("top"===e.split("_")[1]?$("#bottom_form").length>0?$("#bottom_form").addClass("active"):$("#addition_question_form").addClass("active"):"bottom"===e.split("_")[1]&&$("#addition_question_form").addClass("active")),v(t,a),l(".data_entry_"+e)}),l=function(e){return $(e).length>0&&$(e).is(":visible")?$("html, body").animate({scrollTop:$(e).offset().top-200},300):void 0},l("#update_form_stitching"),$(".product_type_btn").click(function(){var e;return $(".product_type_btn").removeClass("selected_type"),e=$(this),e.addClass("selected_type"),"anarkali"===e.data("product")?($("#chudidar_bottom").show(),$("#salwar_bottom").hide(),$("#as_per_image_bottom").parent().removeClass("small-6").addClass("small-12")):($("#chudidar_bottom").show(),$("#salwar_bottom").show(),$("#as_per_image_bottom").parent().removeClass("small-12").addClass("small-6")),$("#product_type").val($(this).data("product")),$(".product_type_btn_bottom").removeClass("selected_type"),$("#product_bottom_type").val("")}),$(".product_type_btn_bottom").click(function(){var e;return $(".product_type_btn_bottom").removeClass("selected_type"),e=$(this),e.addClass("selected_type"),$("#product_bottom_type").val(e.data("product")),$("#stitching_measurement_product_designable_type").val("anarkali"===$("#product_type").val()?"chudidar"===$("#product_bottom_type").val()?"anarkali":"as_per_image"===$("#product_bottom_type").val()?"anarkali_with_bottom_as_per_image":"anarkali_pant":"as_per_image"!==$("#product_bottom_type").val()?"kameez_"+$("#product_bottom_type").val():"kameez_with_bottom_as_per_image")}),$(".img-check").click(function(){var e,t;return t=$(this).data("id"),e=""+t!==$("#stitching_measurement_style_no").val(),e?($("#stitching_measurement_style_no").val(t),$("#style_no_value").html(t),$(".img-check").removeClass("selected_style"),$(this).addClass("selected_style"),c(t),$("#stitching_measurement_back_style_no").val(""),$.ajax(r(t,$("#styles_mapping").data(""+t)[0]))):void 0}),r=function(e,t){return{url:"stitching_measurement/get_back_styles",type:"GET",data:{front_style_no:e,back_styles:t},datatype:"script",beforeSend:function(){return $("#loadingImage").show()},complete:function(){return $("#loadingImage").hide()},success:function(){return $(document).foundation(),$(".example-orbit").css("height","200px")}}},c=function(a){return 6===a||13===a?($("#measurement_list_sleeves_length ,#title_sleeves_length ,#measurement_list_shoulder_size ,#title_shoulder_size").hide(),$("#stitching_measurement_sleeves_length").val("NA"),$("#stitching_measurement_shoulder_size").val("NA")):($("#measurement_list_sleeves_length").show(),$("#title_sleeves_length ,#measurement_list_shoulder_size ,#title_shoulder_size").show(),"NA"===$("#stitching_measurement_sleeves_length").val()&&$("#stitching_measurement_sleeves_length").val(""),"NA"===$("#stitching_measurement_shoulder_size").val()&&$("#stitching_measurement_shoulder_size").val("")),12===a||16===a||14===a?($("#measurement_list_front_neck").hide(),$("#title_front_neck").hide(),$("#stitching_measurement_front_neck").val(14===a?0:3)):($("#title_front_neck ,#measurement_list_front_neck").show(),"NA"===$("#stitching_measurement_front_neck").val()&&$("#stitching_measurement_front_neck").val("")),e(a),t(a)},e=function(e){var t,a,s,i,n,r,l;if(l=Array.from(new Set($("#styles_mapping").data(""+e)[1].split(","))),$(".hook_size_li").removeClass("active_li").hide(),$("#stitching_measurement_hook").val(""),1===l.length&&""!==l[0]&&"all"!==l[0].toLowerCase().trim())t=$("#"+l[0].toLowerCase().trim()+"_link"),t.show().addClass("active_li"),$("#stitching_measurement_hook").val(t.data("value"));else if(""!==l[0])for(s=0,i=l.length;i>s;s++)a=l[s],n=a.toLowerCase().trim(),"all"===n?$(".hook_size_li").show():$("#"+n+"_link").show();return r=$("#previous_measurements").data("hook"),$("li[data-value='"+r+"']").is(":visible")?($("#stitching_measurement_hook").val(r),$("li[data-value='"+r+"']").addClass("active_li")):void 0},t=function(e){var t,a,s;return s=Array.from(new Set($("#styles_mapping").data(""+e)[2].split(","))),t=s[0].toLowerCase().trim(),$(".sleeves_length_size_li").hide().removeClass("active_li"),$("#stitching_measurement_sleeves_length").val(""),s.length>1?$(".sleeves_length_size_li").show():1===s.length&&""!==t&&("all"!==t?($(".sleeves_length_size_li").hide().removeClass("active_li"),"sleeveless"===t?($("li[data-value='Sleeveless']").show().addClass("active_li"),$("#stitching_measurement_sleeves_length").val("Sleeveless")):$("#measurement_list_sleeves_length li[data-value!='Sleeveless']").show()):$(".sleeves_length_size_li").show()),a=$("#previous_measurements").attr("data-sleeves-length"),$("#measurement_list_sleeves_length li[data-value='"+a+"']").is(":visible")?($("#stitching_measurement_sleeves_length").val(a),$("#measurement_list_sleeves_length li[data-value='"+a+"']").addClass("active_li")):void 0},""!==$("#stitching_measurement_style_no").val()&&void 0!==$("#stitching_measurement_style_no").val()&&c(parseInt($("#stitching_measurement_style_no").val())),15===$("#previous_measurements").data("back-style-no")&&($(".hook_size_li").removeClass("active_li").hide(),$("#stitching_measurement_hook").val("String At The Back"),$("#string_link").show().addClass("active_li")),p=function(){var e,t,a,s,i,n,r,l,_,o,m;for(e=[],_=$("#top_keys").data("keys"),t=0,n=_.length;n>t;t++)i=_[t],""===$("#"+i).val()&&e.push(i);for(o=$("#bottom_keys").data("keys"),a=0,r=o.length;r>a;a++)i=o[a],""===$("#"+i).val()&&e.push(i);for(m=$("#additional_keys").data("keys"),s=0,l=m.length;l>s;s++)i=m[s],""===$("#"+i).val()&&e.push(i);return e},$("#validation_button").click(function(){var e,t,a,n,r,_;for(""!==$("#stitching_measurement_chest_size").val()&&void 0!==$("#stitching_measurement_chest_size").val()&&"true"===$("#stitching_measurement_product_mapping_present").val()&&-1!==jQuery.inArray($("#stitching_measurement_product_designable_type").val(),["lehenga_choli","blouse"])&&s($("#stitching_measurement_chest_size").val()),a=p(),_=[],$(".field_label").removeClass("unfilled_measurements"),n=0,r=a.length;r>n;n++)e=a[n],t=e.replace("stitching_measurement_",""),$("#title_"+t).addClass("unfilled_measurements"),_.push(h(t)),$("#top_form ,#bottom_form").addClass("active");return a.length>0?(l("#measurement_list_"+a[0].replace("stitching_measurement_","")),new PNotify({title:"Oh No!",text:"Some Fields Are Empty:\n"+h(a.join("\n").replace(/stitching_measurement_/g," ")),type:"error",delay:4e3})):(new PNotify({title:"Review Measurements",text:"Please Review Your Measurements Before Submitting",type:"success"}),$(".hide_on_review").hide(),i())}),i=function(){var e,t,a,s,i,n,r,_,o,m,u;for(o='<table class="columns small-12">',m=$("#form_names").data("top"),e=$("#form_names").data("bottom"),o+="<tr><td colspan=2 class='table_head'> "+m+" Measurements </td></tr>",r=$("#top_keys").data("keys"),t=0,i=r.length;i>t;t++)s=r[t],"stitching_measurement_style_no"===s||"stitching_measurement_back_style_no"===s?(u=$("#"+s).val(),o+="<tr><td>"+h(s.replace("stitching_measurement_",""))+"</td><td><div class='blouse_image blouse_types_"+u+"'></div></td></tr>"):o+="product_type"===s||"stitching_measurement_hook"===s||"stitching_measurement_padded"===s?"<tr><td>"+h(s.replace("stitching_measurement_",""))+"</td><td>"+h($("#"+s).val())+"</td></tr>":"<tr><td>"+h(s.replace("stitching_measurement_",""))+"</td><td>"+h($("#"+s).val())+" Inch</td></tr>";if($("#bottom_keys").data("keys").length>0)for(o+="<tr><td colspan=2 class='table_head'> "+e+" Measurements</td></tr>",_=$("#bottom_keys").data("keys"),a=0,n=_.length;n>a;a++)s=_[a],o+="product_bottom_type"===s?"<tr><td>"+h(s.replace("stitching_measurement_",""))+"</td><td>"+h($("#"+s).val())+"</td></tr>":"<tr><td>"+h(s.replace("stitching_measurement_",""))+"</td><td>"+h($("#"+s).val())+" Inch</td></tr>";return o+="</table>",$("#review_table ,.show_on_review").show(),$("#review_table").empty(),$("#review_table").html(o),window.history&&window.history.pushState&&window.history.pushState(null,"measurement collect",""),l("#final_submit")},$(window).on("popstate",function(){return $(".show_on_review").hide(),$(".hide_on_review").show()}),$("#main_stitching_form").on("submit",function(e){var t,a;return e.preventDefault(),$("#final_submit").prop("disabled",!0),$("#final_submit").val("Wait.."),m()?(a=[],$(".item_checkbox:checked").each(function(){return a.push($(this).val())}),t=$(this).add("input[type=hidden][name=measurement_experience]").serialize()+("&similar_products="+a),$.ajax(u(t))):($("#final_submit").prop("disabled",!1),$("#final_submit").val("Submit"))}),m=function(){var e,t,a,s;if(a=$("#stitching_measurement_measurement_name").val(),""===a)return alert("Name Cannot be Blank"),!1;if("Create New"===a)return alert('Name Cannot be "Create New"'),!1;for(s=e=0,t=$("#measurement_select>option").length;t>=0?t>e:e>t;s=t>=0?++e:--e)if($("#measurement_select>option")[s].innerHTML===a)return $("#stitching_measurement_measurement_name").addClass("border-red"),$("#error_measurement_name").show(),!1;return!0},u=function(e){return{url:"stitching_measurement/create",type:"POST",data:e,datatype:"json",beforeSend:function(){return $("#loadingImage").show()},complete:function(){return $("#loadingImage").hide()},success:function(){var e,t,a,s,i;return new PNotify({title:"Success!",text:"Measurements Have Been Saved Successfully",type:"success"}),$(".show_on_review").hide(),$("#review_table").show(),a=$("#order_number").val(),t=$("#item_id").val(),i=$("#user_id").val(),s=$("#source").val(),e=$("#app_source").val(),ga("send","event","StitchingForm","Submit","order_"+a+"/item_"+t+"/user_"+i+"/source_"+s+"/app_source_"+e),setTimeout(function(){location.reload(!0)},600)},error:function(){return new PNotify({title:"Error!",text:"Some Error Occured",type:"error"}),$("#final_submit").prop("disabled",!1),$("#final_submit").val("Submit")}}},$(".example-orbit").css("height","200px"),$(".repeat_radio_btn").click(function(){return"Yes"===$(this).val()?$(".similar_products").show():($(".item_checkbox:checked").removeAttr("checked"),$(".similar-img-check").removeClass("selected_style"),$(".similar_products").hide())}),$("#go_back_button").click(function(){return $(".hide_on_review").show(),$(".show_on_review").hide()}),$("#selected_categories").length>0&&(d=$("#selected_categories").data("top"),a=$("#selected_categories").data("bottom"),$("#"+d).click(),$("#"+a+"_bottom").click()),""!==$("#stitching_measurement_height").val()&&(_=$("#stitching_measurement_height").val(),g=$("#stitching_measurement_weight").val(),o=_.split(".")[1]||"0",n=parseInt(_),$("#inches").val(o),$("#feet").val(n),$("#height").val(n+"."+o),$("#weight").val(g),$("#age").val($("#stitching_measurement_age").val()),$("#addition_question_form").addClass("active")),h=function(e){var t,a;return void 0===e?"":e.split?(t=function(e){return e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()},a=[],e.split("_").forEach(function(e){a.push(t(e))}),a.join(" ")):e}})}).call(this);