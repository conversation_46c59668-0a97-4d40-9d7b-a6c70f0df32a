(function() {
  $(function() {
    var toggleCodCharges;
    $('.payment_options input:radio:first').prop('checked', 'checked');
    toggleCodCharges = function() {
      if ($('input[name="order[pay_type]"]:checked').attr('id') === 'order_pay_type_cash_on_delivery') {
        $('.grand_total').hide();
        $('.grand_total_with_cod.hide').show();
        return $('.cod_charges').show();
      } else {
        $('.grand_total').show();
        $('.grand_total_with_cod.hide').hide();
        return $('.cod_charges').hide();
      }
    };
    $('input[name="order[pay_type]"]').on('click', function(e) {
      return toggleCodCharges();
    });
    return toggleCodCharges();
  });

}).call(this);
