/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 4, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-icon {
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 30px 30px;
  display: inline-block;
  height: 30px;
  width: 30px;
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-twitter {
  background-image: url(/assets/social-share-button/twitter-7b64ce0117a85c5d52ed45c27707af61d9b0e0d590284baa287cfe87867b9ae1.svg);
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-facebook {
  background-image: url(/assets/social-share-button/facebook-03210e1663ee772e93ed5d344cdb36657b68342821aaebe982f2f984915990b3.svg);
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-google_plus {
  background-image: url(/assets/social-share-button/google_plus-50c049fbb29cd3346f1bf9349017b644bcd00a53b56bd156728850f57bd85c0a.svg);
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-linkedin {
  background-image: url(/assets/social-share-button/linkedin-b7fd42895b291003c444a9c2acf867ea813671e0b725a5d7c05dbb13f5c0fdd3.svg);
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-pinterest {
  background-image: url(/assets/social-share-button/pinterest-86203d156197cce4087fee058b920d275c535df5fd59d8caa83da0ef41d1ec7f.svg);
}
/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/social-share.scss */
.social-share-button .ssb-email {
  background-image: url(/assets/social-share-button/email-7067143cbe69d9d8cf25e67fda1a315d133c2b79074a09088bd712cc5c81135d.svg);
}

/*! lightslider - v1.1.3 - 2015-04-14
* https://github.com/sachinchoolur/lightslider
* Copyright (c) 2015 Sachin N; Licensed MIT */
/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper, .lSSlideWrapper .lSFade {
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide, .lSSlideWrapper.usingCss .lSFade > * {
  -webkit-transition-timing-function: inherit !important;
  transition-timing-function: inherit !important;
  -webkit-transition-duration: inherit !important;
  transition-duration: inherit !important;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter, .lSSlideOuter .lSPager.lSGallery {
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideWrapper > .lightSlider:after {
  clear: both;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter {
  overflow: hidden;
  user-select: none;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider:after, .lightSlider:before {
  content: " ";
  display: table;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider {
  overflow: hidden;
  margin: 0;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper {
  max-width: 100%;
  overflow: hidden;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSSlide {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-transition: all 1s;
  -webkit-transition-property: -webkit-transform,height;
  -moz-transition-property: -moz-transform,height;
  transition-property: transform,height;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > * {
  position: absolute !important;
  top: 0;
  left: 0;
  z-index: 9;
  margin-right: 0;
  width: 100%;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > * {
  opacity: 0;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
  -webkit-transition-property: opacity;
  transition-property: opacity;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper .lSFade > .active {
  z-index: 10;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideWrapper.usingCss .lSFade > .active {
  opacity: 1;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg {
  margin: 10px 0 0;
  padding: 0;
  text-align: center;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li {
  cursor: pointer;
  display: inline-block;
  padding: 0 5px;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li a {
  background-color: #222;
  border-radius: 30px;
  display: inline-block;
  height: 8px;
  overflow: hidden;
  text-indent: -999em;
  width: 8px;
  position: relative;
  z-index: 99;
  -webkit-transition: all .5s linear 0s;
  transition: all .5s linear 0s;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSpg > li.active a, .lSSlideOuter .lSPager.lSpg > li:hover a {
  background-color: #428bca;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media {
  opacity: .8;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .media.active {
  opacity: 1;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery {
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -webkit-transition-property: -webkit-transform;
  -moz-transition-property: -moz-transform;
  user-select: none;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li {
  overflow: hidden;
  -webkit-transition: border-radius .12s linear 0s .35s linear 0s;
  transition: border-radius .12s linear 0s .35s linear 0s;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery li.active, .lSSlideOuter .lSPager.lSGallery li:hover {
  border-radius: 5px;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery img {
  display: block;
  height: auto;
  max-width: 100%;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager.lSGallery:after, .lSSlideOuter .lSPager.lSGallery:before {
  content: " ";
  display: table;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a {
  width: 32px;
  display: block;
  top: 50%;
  height: 32px;
  background-image: url(/assets/controls-f4c3996ad997182376dddc345b138b599bae61a44ac41dadb19bc18710908226.png);
  cursor: pointer;
  position: absolute;
  z-index: 98;
  margin-top: -16px;
  opacity: .5;
  -webkit-transition: opacity .35s linear 0s;
  transition: opacity .35s linear 0s;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a:hover {
  opacity: 1;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSPrev {
  background-position: 0 0;
  left: 10px;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > .lSNext {
  background-position: -32px 0;
  right: 10px;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSAction > a.disabled {
  pointer-events: none;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.cS-hidden {
  /*height:1px;opacity:0;*/
  filter: alpha(opacity=0);
  overflow: hidden;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical {
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical.noPager {
  padding-right: 0 !important;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSGallery {
  position: absolute !important;
  right: 0;
  top: 0;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lightSlider > * {
  width: 100% !important;
  max-width: none !important;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > a {
  left: 50%;
  margin-left: -14px;
  margin-top: 0;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSNext {
  background-position: 31px -31px;
  bottom: 10px;
  top: auto;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .lSAction > .lSPrev {
  background-position: 0 -31px;
  bottom: auto;
  top: 10px;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl {
  direction: rtl;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSPager, .lSSlideOuter .lightSlider {
  padding-left: 0;
  list-style: none;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSPager, .lSSlideOuter.lSrtl .lightSlider {
  padding-right: 0;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .lSGallery li, .lSSlideOuter .lightSlider > * {
  float: left;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .lSGallery li, .lSSlideOuter.lSrtl .lightSlider > * {
  float: right !important;
}

@-webkit-keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@keyframes rightEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: -15px;
  }
}
@-webkit-keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@keyframes topEnd {
  0%,100% {
    top: 0;
  }
  50% {
    top: -15px;
  }
}
@-webkit-keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@keyframes leftEnd {
  0%,100% {
    left: 0;
  }
  50% {
    left: 15px;
  }
}
@-webkit-keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
@keyframes bottomEnd {
  0%,100% {
    bottom: 0;
  }
  50% {
    bottom: -15px;
  }
}
/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .rightEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter .leftEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .rightEnd {
  -webkit-animation: topEnd .3s;
  animation: topEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.vertical .leftEnd {
  -webkit-animation: bottomEnd .3s;
  animation: bottomEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .rightEnd {
  -webkit-animation: leftEnd .3s;
  animation: leftEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lSSlideOuter.lSrtl .leftEnd {
  -webkit-animation: rightEnd .3s;
  animation: rightEnd .3s;
  position: relative;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrab > * {
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: -o-grab;
  cursor: -ms-grab;
  cursor: grab;
}

/* line 3, /home/<USER>/mirraw_docker/mirraw-mobile/vendor/assets/stylesheets/lightslider.css.scss */
.lightSlider.lsGrabbing > * {
  cursor: move;
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: -o-grabbing;
  cursor: -ms-grabbing;
  cursor: grabbing;
}

/*! PhotoSwipe Default UI CSS by Dmitry Semenov | photoswipe.com | MIT license */
/*

  Contents:

  1. Buttons
  2. Share modal and links
  3. Index indicator ("1 of X" counter)
  4. Caption
  5. Loading indicator
  6. Additional styles (root element, top bar, idle state, hidden state, etc.)

*/
/*
  
  1. Buttons

 */
/* <button> css reset */
/* line 37, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button {
  width: 44px;
  height: 44px;
  position: relative;
  background: none;
  cursor: pointer;
  overflow: visible;
  -webkit-appearance: none;
  display: block;
  border: 0;
  padding: 0;
  margin: 0;
  float: right;
  opacity: 0.75;
  transition: opacity 0.2s;
  box-shadow: none;
}
/* line 54, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button:focus, .pswp__button:hover {
  opacity: 1;
}
/* line 59, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button:active {
  outline: none;
  opacity: 0.9;
}
/* line 64, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

/* pswp__ui--over-close class it added when mouse is over element that should close gallery */
/* line 71, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--over-close .pswp__button--close {
  opacity: 1;
}

/* line 75, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button,
.pswp__button--arrow--left:before,
.pswp__button--arrow--right:before {
  background: url(/images/default-skin-red.png) 0 0 no-repeat;
  background-size: 264px 88px;
  width: 44px;
  height: 44px;
}

@media (-webkit-min-device-pixel-ratio: 1.1), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {
  /* Serve SVG sprite if browser supports SVG and resolution is more than 105dpi */
  /* line 87, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
  .pswp--svg .pswp__button,
  .pswp--svg .pswp__button--arrow--left:before,
  .pswp--svg .pswp__button--arrow--right:before {
    background-image: url(/assets/default-skin-red-c3fc555956e6a51901c6102d6975bee08c6ff000b2df09869579e43196b222c7.svg);
  }

  /* line 93, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
  .pswp--svg .pswp__button--arrow--left,
  .pswp--svg .pswp__button--arrow--right {
    background: none;
  }
}
/* line 99, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--close {
  background-position: 0 -44px;
}

/* line 103, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--share {
  background-position: -44px -44px;
}

/* line 107, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--fs {
  display: none;
}

/* line 111, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--supports-fs .pswp__button--fs {
  display: block;
}

/* line 115, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--fs .pswp__button--fs {
  background-position: -44px 0;
}

/* line 119, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--zoom {
  display: block;
  background-position: -88px 0;
}

/* line 124, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--zoom-allowed .pswp__button--zoom {
  display: block;
}

/* line 128, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--zoomed-in .pswp__button--zoom {
  background-position: -132px 0;
}

/* no arrows on touch screens */
/* line 134, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--touch .pswp__button--arrow--left,
.pswp--touch .pswp__button--arrow--right {
  visibility: hidden;
}

/*
  Arrow buttons hit area
  (icon is added to :before pseudo-element)
*/
/* line 144, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--left,
.pswp__button--arrow--right {
  background: none;
  top: 50%;
  margin-top: -50px;
  width: 70px;
  height: 100px;
  position: absolute;
}

/* line 154, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--left {
  left: 0;
}

/* line 158, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--right {
  right: 0;
}

/* line 162, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--left:before,
.pswp__button--arrow--right:before {
  content: '';
  top: 35px;
  background-color: #eeeeee;
  height: 30px;
  width: 32px;
  position: absolute;
}

/* line 172, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--left:before {
  left: 6px;
  background-position: -138px -44px;
}

/* line 177, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__button--arrow--right:before {
  right: 6px;
  background-position: -94px -44px;
}

/*

  2. Share modal/popup and links

 */
/* line 189, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__counter,
.pswp__share-modal {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* line 196, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-modal {
  display: block;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  padding: 10px;
  position: absolute;
  z-index: 1600;
  opacity: 0;
  transition: opacity 0.25s ease-out;
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

/* line 212, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-modal--hidden {
  display: none;
}

/* line 216, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-tooltip {
  z-index: 1620;
  position: absolute;
  background: #FFF;
  top: 56px;
  border-radius: 2px;
  display: block;
  width: auto;
  right: 44px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
  transform: translateY(6px);
  transition: transform 0.25s;
  -webkit-backface-visibility: hidden;
  will-change: transform;
}
/* line 231, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-tooltip a {
  display: block;
  padding: 8px 12px;
  color: #000;
  text-decoration: none;
  font-size: 14px;
  line-height: 18px;
}
/* line 239, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-tooltip a:hover {
  text-decoration: none;
  color: #000;
}
/* line 245, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-tooltip a:first-child {
  /* round corners on the first/last list item */
  border-radius: 2px 2px 0 0;
}
/* line 250, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-tooltip a:last-child {
  border-radius: 0 0 2px 2px;
}

/* line 256, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-modal--fade-in {
  opacity: 1;
}
/* line 259, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__share-modal--fade-in .pswp__share-tooltip {
  transform: translateY(0);
}

/* increase size of share links on touch devices */
/* line 265, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--touch .pswp__share-tooltip a {
  padding: 16px 12px;
}

/* line 270, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--facebook:before {
  content: '';
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  top: -12px;
  right: 15px;
  border: 6px solid transparent;
  border-bottom-color: #FFF;
  -webkit-pointer-events: none;
  -moz-pointer-events: none;
  pointer-events: none;
}
/* line 285, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--facebook:hover {
  background: #3E5C9A;
  color: #FFF;
}
/* line 289, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--facebook:hover:before {
  border-bottom-color: #3E5C9A;
}

/* line 296, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--twitter:hover {
  background: #55ACEE;
  color: #FFF;
}

/* line 303, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--pinterest:hover {
  background: #CCC;
  color: #CE272D;
}

/* line 310, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
a.pswp__share--download:hover {
  background: #DDD;
}

/*

  3. Index indicator ("1 of X" counter)

 */
/* line 322, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__counter {
  position: absolute;
  left: 0;
  top: 0;
  height: 44px;
  font-size: 13px;
  line-height: 44px;
  color: #670b19;
  opacity: 0.75;
  padding: 0 10px;
  font-weight: bold;
}

/*
  
  4. Caption

 */
/* line 342, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  min-height: 44px;
}
/* line 349, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption small {
  font-size: 11px;
  color: #BBB;
}

/* line 355, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption__center {
  text-align: center;
  max-width: 420px;
  margin: 0 auto;
  font-size: 13px;
  padding: 10px;
  line-height: 20px;
  color: #303030;
  background: rgba(238, 238, 238, 0.79);
}

/* line 366, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption--empty {
  display: none;
}

/* Fake caption element, used to calculate height of next/prev image */
/* line 371, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption--fake {
  visibility: hidden;
}

/*

  5. Loading indicator (preloader)

  You can play with it here - http://codepen.io/dimsemenov/pen/yyBWoR

 */
/* line 384, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__preloader {
  width: 44px;
  height: 44px;
  position: absolute;
  top: 0;
  left: 50%;
  margin-left: -22px;
  opacity: 0;
  transition: opacity 0.25s ease-out;
  will-change: opacity;
  direction: ltr;
}

/* line 397, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__preloader__icn {
  width: 20px;
  height: 20px;
  margin: 12px;
}

/* line 403, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__preloader--active {
  opacity: 1;
}
/* line 406, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__preloader--active .pswp__preloader__icn {
  /* We use .gif in browsers that don't support CSS animation */
  background: url(/assets/preloader-80d7ed3f3f4b50628f219778db814955e7d2007c05be88556778f90ee290715c.gif) 0 0 no-repeat;
}

/* line 413, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader--active {
  opacity: 1;
}
/* line 416, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {
  animation: clockwise 500ms linear infinite;
}
/* line 420, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {
  animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;
}
/* line 425, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader__icn {
  background: none;
  opacity: 0.75;
  width: 14px;
  height: 14px;
  position: absolute;
  left: 15px;
  top: 15px;
  margin: 0;
}
/* line 437, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader__cut {
  /* 
    The idea of animating inner circle is based on Polymer ("material") loading indicator 
     by Keanu Lee https://blog.keanulee.com/2014/10/20/the-tale-of-three-spinners.html
  */
  position: relative;
  width: 7px;
  height: 14px;
  overflow: hidden;
}
/* line 448, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--css_animation .pswp__preloader__donut {
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  border: 2px solid #FFF;
  border-radius: 50%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: 0;
  left: 0;
  background: none;
  margin: 0;
}

@media screen and (max-width: 1024px) {
  /* line 465, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
  .pswp__preloader {
    position: relative;
    left: auto;
    top: auto;
    margin: 0;
    float: right;
  }
}
@keyframes clockwise {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes donut-rotate {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(-140deg);
  }
  100% {
    transform: rotate(0);
  }
}
/*
  
  6. Additional styles

 */
/* root element of UI */
/* line 493, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui {
  -webkit-font-smoothing: auto;
  visibility: visible;
  opacity: 1;
  z-index: 1550;
}

/* top black bar with buttons and "1 of X" indicator */
/* line 501, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__top-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 44px;
  width: 100%;
}

/* line 509, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__caption,
.pswp__top-bar,
.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
  -webkit-backface-visibility: hidden;
  will-change: opacity;
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* pswp--has_mouse class is added only when two subsequent mousemove events occur */
/* line 520, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--has_mouse .pswp__button--arrow--left,
.pswp--has_mouse .pswp__button--arrow--right {
  visibility: visible;
}

/* chnaged background colour according to theme */
/* line 526, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__top-bar {
  background-color: white;
}

/* pswp__ui--fit class is added when main image "fits" between top bar and bottom bar (caption) */
/* line 532, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--fit .pswp__top-bar,
.pswp__ui--fit .pswp__caption {
  background-color: rgba(0, 0, 0, 0.3);
}

/* pswp__ui--idle class is added when mouse isn't moving for several seconds (JS option timeToIdle) */
/* line 541, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--idle .pswp__top-bar {
  opacity: 0;
}
/* line 545, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--idle .pswp__button--arrow--left,
.pswp__ui--idle .pswp__button--arrow--right {
  opacity: 0;
}

/*
  pswp__ui--hidden class is added when controls are hidden
  e.g. when user taps to toggle visibility of controls
*/
/* line 556, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--hidden .pswp__top-bar,
.pswp__ui--hidden .pswp__caption,
.pswp__ui--hidden .pswp__button--arrow--left,
.pswp__ui--hidden .pswp__button--arrow--right {
  /* Force paint & create composition layer for controls. */
  opacity: 0.001;
}

/* pswp__ui--one-slide class is added when there is just one item in gallery */
/* line 567, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__ui--one-slide .pswp__button--arrow--left,
.pswp__ui--one-slide .pswp__button--arrow--right,
.pswp__ui--one-slide .pswp__counter {
  display: none;
}

/* line 574, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp__element--disabled {
  display: none !important;
}

/* line 580, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/default-skin.scss */
.pswp--minimal--dark .pswp__top-bar {
  background: none;
}

/*! PhotoSwipe main CSS by Dmitry Semenov | photoswipe.com | MIT license */
/*
	Styles for basic PhotoSwipe functionality (sliding area, open/close transitions)
*/
/* pswp = photoswipe */
/* line 20, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp {
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  overflow: hidden;
  -ms-touch-action: none;
  touch-action: none;
  z-index: 1500;
  -webkit-text-size-adjust: 100%;
  /* create separate layer, to avoid paint on window.onscroll in webkit/blink */
  -webkit-backface-visibility: hidden;
  outline: none;
}
/* line 37, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp * {
  box-sizing: border-box;
}
/* line 42, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp img {
  max-width: none;
}

/* style is added when JS option showHideOpacity is set to true */
/* line 48, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--animate_opacity {
  /* 0.001, because opacity:0 doesn't trigger Paint action, which causes lag at start of transition */
  opacity: 0.001;
  will-change: opacity;
  /* for open/close transition */
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 56, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--open {
  display: block;
}

/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--zoom-allowed .pswp__img {
  /* autoprefixer: off */
  cursor: -webkit-zoom-in;
  cursor: -moz-zoom-in;
  cursor: zoom-in;
}

/* line 67, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--zoomed-in .pswp__img {
  /* autoprefixer: off */
  cursor: -webkit-grab;
  cursor: -moz-grab;
  cursor: grab;
}

/* line 74, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--dragging .pswp__img {
  /* autoprefixer: off */
  cursor: -webkit-grabbing;
  cursor: -moz-grabbing;
  cursor: grabbing;
}

/*
	Background is added as a separate element.
	As animating opacity is much faster than animating rgba() background-color.
*/
/* line 85, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  opacity: 0;
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  will-change: opacity;
}

/* line 98, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__scroll-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* line 107, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__container,
.pswp__zoom-wrap {
  -ms-touch-action: none;
  touch-action: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

/* Prevent selection and tap highlights */
/* line 119, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__container,
.pswp__img {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* line 128, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__zoom-wrap {
  position: absolute;
  width: 100%;
  -webkit-transform-origin: left top;
  -moz-transform-origin: left top;
  -ms-transform-origin: left top;
  transform-origin: left top;
  /* for open/close transition */
  transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 139, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__bg {
  will-change: opacity;
  /* for open/close transition */
  transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);
}

/* line 146, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--animated-in .pswp__bg,
.pswp--animated-in .pswp__zoom-wrap {
  -webkit-transition: none;
  transition: none;
}

/* line 153, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__container,
.pswp__zoom-wrap {
  -webkit-backface-visibility: hidden;
}

/* line 158, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__item {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: hidden;
}

/* line 167, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__img {
  position: absolute;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
}

/*
	stretched thumbnail or div placeholder element (see below)
	style is added to avoid flickering in webkit/blink when layers overlap
*/
/* line 179, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__img--placeholder {
  -webkit-backface-visibility: hidden;
}

/*
	div element that matches size of large image
	large image loads on top of it
*/
/* line 187, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__img--placeholder--blank {
  background: #b3b3b3;
}

/* line 191, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp--ie .pswp__img {
  width: 100% !important;
  height: auto !important;
  left: 0;
  top: 0;
}

/*
	Error message appears when image is not loaded
	(JS option errorMsg controls markup)
*/
/* line 202, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__error-msg {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100%;
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  margin-top: -8px;
  color: #CCC;
}

/* line 214, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/photoswipe.scss */
.pswp__error-msg a {
  color: #CCC;
  text-decoration: underline;
}

/* line 9, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 14, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.lSAction a {
  opacity: 1 !important;
}

/* line 18, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox + label {
  padding-left: 26px !important;
  cursor: pointer;
  display: inline-block;
  line-height: 20px;
  -khtml-user-select: none;
  position: relative;
  height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* line 30, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox:checked,
.Mcustom-checkbox:not(:checked) {
  position: absolute;
  left: -9999px;
  opacity: 0;
}

/* line 36, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox:not(.filled-in) + label:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 12px;
  height: 12px;
  z-index: 0;
  border: 2px solid #696969;
  border-radius: 1px;
  margin-top: 2px;
  transition: .2s;
  transform: scale(0);
}

/* line 50, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in + label:after {
  border-radius: 2px;
}

/* line 53, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in + label:after,
.Mcustom-checkbox.filled-in + label:before {
  content: '';
  left: 0;
  position: absolute;
  transition: border .25s, background-color .25s, width .2s .1s, height .2s .1s, top .2s .1s, left .2s .1s;
  z-index: 1;
}

/* line 61, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in:not(:checked) + label:before {
  width: 0;
  height: 0;
  border: 3px solid transparent;
  left: 6px;
  top: 10px;
  -webkit-transform: rotateZ(37deg);
  transform: rotateZ(37deg);
  -webkit-transform-origin: 20% 40%;
  transform-origin: 100% 100%;
}

/* line 72, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in:not(:checked) + label:after {
  height: 14px;
  width: 14px;
  background-color: transparent;
  border: 1px solid #cecbcb;
  top: 3px;
  z-index: 0;
}

/* line 80, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in:checked + label:before {
  top: 2px;
  left: -1px;
  width: 7px;
  height: 11px;
  border-top: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
  -webkit-transform: rotateZ(37deg);
  transform: rotateZ(37deg);
  -webkit-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
}

/* line 94, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.Mcustom-checkbox.filled-in:checked + label:after {
  top: 3px;
  left: -1px;
  width: 14px;
  height: 14px;
  border: 2px solid #7b0e1d;
  background-color: #7b0e1d;
  z-index: 0;
}

/* line 103, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-prev {
  background-image: none !important;
  left: 5%;
  top: 45%;
  position: absolute;
  width: 30px;
  height: 30px;
}
/* line 110, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-prev span {
  content: '';
  border-left: 2px solid #303030;
  border-bottom: 2px solid #303030;
  width: 20px;
  height: 20px;
  position: absolute;
  transform: rotate(45deg);
  top: 15%;
}

/* line 121, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-next {
  background-image: none !important;
  left: 90%;
  position: absolute;
  top: 45%;
  width: 30px;
  height: 30px;
}
/* line 128, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-next span {
  content: '';
  border-right: 2px solid #303030;
  border-top: 2px solid #303030;
  width: 20px;
  height: 20px;
  position: absolute;
  transform: rotate(45deg);
  top: 15%;
}

/* line 139, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-bullets-container {
  text-align: center;
}
/* line 141, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-bullets-container .orbit-bullets {
  float: none;
  margin: 0 auto 6px auto;
  overflow: hidden;
  position: relative;
  text-align: center;
  top: 5px;
}
/* line 148, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-bullets-container .orbit-bullets li {
  background: #CCCCCC;
  cursor: pointer;
  display: inline-block;
  float: none;
  height: 0.4rem;
  margin-right: 6px;
  width: 0.4rem;
  border-radius: 1000px;
}
/* line 158, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-bullets-container .orbit-bullets .active {
  background: #670b19;
}

/* line 163, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.orbit-timer, .orbit-next, .orbit-prev, .orbit-bullets {
  display: block;
}

/* line 166, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.pswp--touch .pswp__button--arrow--left, .pswp--touch .pswp__button--arrow--right {
  visibility: visible;
}

/* line 169, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body {
  overflow: scroll;
}
/* line 173, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .off-canvas-wrap #container {
  width: 96vw;
  overflow-y: hidden;
  padding: 0em;
  margin: 2%;
  overflow-x: hidden;
}
/* line 181, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .hidden {
  display: none;
}
/* line 184, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .action_button_btn {
  padding-bottom: 0px;
  text-align: center;
}
/* line 188, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .overflow_messages {
  background: #008cba;
  text-align: center;
  padding: 3%;
  z-index: 100 !important;
}
/* line 194, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #add_to_cart_message {
  padding: 10px;
}
/* line 197, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons {
  transform: translate3d(0px, 0px, 0px);
}
/* line 199, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons .cart_button {
  margin-bottom: 0;
  background-color: #0d997c;
  width: 100%;
  font-weight: 700;
}
/* line 205, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons .right-button {
  margin-bottom: 0;
  background-color: #0d997c;
  width: 100%;
  padding: 1.2em 0;
  font-weight: 700;
}
/* line 212, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons .action_button_btn {
  padding-bottom: 0px;
}
/* line 215, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons .left-button {
  background-color: #101010;
  color: #0d997c;
  margin-bottom: 0;
  width: 100%;
  padding-left: 0%;
  font-weight: 700;
  padding-right: 0%;
  padding: 1.2em 0;
}
/* line 226, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .heading_underline {
  text-decoration: underline;
}
/* line 230, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons.fixed, body .designs_show.page #secondary_action_buttons.fixed {
  bottom: 0;
  top: auto;
  margin-bottom: 0;
}
/* line 233, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons.fixed .add_place_order, body .designs_show.page #secondary_action_buttons.fixed .add_place_order {
  background-color: #0e9a7d;
}
/* line 236, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons.fixed .add_to_cart, body .designs_show.page #secondary_action_buttons.fixed .add_to_cart {
  background-color: black;
  color: #0e9a7d;
}
/* line 241, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons.fixed a, body .designs_show.page #action_buttons.fixed input[type='submit'], body .designs_show.page #secondary_action_buttons.fixed a, body .designs_show.page #secondary_action_buttons.fixed input[type='submit'] {
  margin-bottom: 0em;
  width: 100%;
  font-weight: bold;
  line-height: inherit;
}
/* line 247, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #action_buttons.fixed li, body .designs_show.page #secondary_action_buttons.fixed li {
  padding-bottom: 0em;
  padding: 0em;
}
/* line 253, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page #secondary_action_buttons {
  margin: 3.7em 0;
}
/* line 256, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .panel_block {
  background-color: #010710;
  border: 0.1em solid #010710;
  margin-top: 1em;
  border-radius: .5em;
}
/* line 261, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .panel_block .panel_content {
  background-color: #4c4b4b;
  border: 1px solid black;
  border-radius: 6px;
}
/* line 267, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .panel_block .panel_heading {
  padding: 0em .2em;
}
/* line 269, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .panel_block .panel_heading ul {
  margin: .2em auto;
}
/* line 274, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .line_through_text {
  text-decoration: line-through;
}
/* line 277, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .truncate {
  width: 100%;
  color: #303030;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 285, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .no-bullet.shipping_desc li {
  color: white;
}
/* line 289, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .close-icon {
  background-image: url(/assets/close-32-4b75407a3032b7b5d98bd2162d3607306997e2fd591ff6cad0cebce6ad3a300c.png);
  background-repeat: no-repeat;
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  background-size: 1em;
  background-color: black;
  border-radius: 5em;
}
/* line 300, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .bordered_block {
  padding: 1em;
  margin-bottom: 1em;
  box-shadow: 0 0 2em black;
  border: 0.1em solid #4d4d4d;
}
/* line 307, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .opera_checkout_position_fix {
  width: 100%;
  margin-top: 18px;
  background-color: #0E9A7D !important;
}
/* line 313, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .mirraw_cert_logo {
  display: inline;
  opacity: 0.85;
}
/* line 318, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .small_msg {
  font-size: 12px;
}
/* line 322, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .wrap_total {
  white-space: nowrap;
}
/* line 326, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body .designs_show.page .sign_in_button a {
  color: white;
  font-size: 0.825rem;
  text-transform: uppercase;
}
/* line 333, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
body #disclaimer-box {
  font-size: 13px;
  text-align: center;
  margint-top: 8px;
  font-style: italic;
}

/* line 343, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.fixed_button {
  position: fixed;
  bottom: 0;
  z-index: 10;
  right: 0;
  left: 0;
  top: auto;
  animation: smoothScroll 0.3s forwards;
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translateY(63px);
  transform: translateY(63px);
  background: #ffffff;
}
/* line 361, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.fixed_button .action_button_btn {
  margin: 0.375rem;
}

@keyframes smoothScroll {
  0% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0px);
  }
}
/* line 374, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_image_div li {
  list-style: none;
  padding: 0;
}
/* line 377, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_image_div li .small-centered {
  padding: 0 10px;
}
/* line 381, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #stitching_testimonials {
  width: 100%;
  border-top: 4px solid #8f1b1d;
}
/* line 384, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #stitching_testimonials .testimonial-header {
  text-align: center;
  letter-spacing: 1px;
  font-size: 0.875rem;
}
/* line 389, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #stitching_testimonials ul {
  margin-top: 1em;
  text-align: center;
  height: 3.65em !important;
}
/* line 393, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #stitching_testimonials ul li {
  color: #8f1b1d;
  text-align: cneter;
  font-family: "Lato Black";
}
/* line 400, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .wishlist-forms {
  position: relative;
}
/* line 402, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .wishlist-forms .wishlist-heart-button {
  font-size: 1.9rem;
  margin: 0;
  padding: 0;
  float: right;
  background: transparent;
  color: #8f1b1d;
  position: absolute;
  right: 0px;
  top: 0px;
}
/* line 412, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .wishlist-forms .wishlist-heart-button:focus {
  outline: none;
}
/* line 416, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .wishlist-forms .wishlist-heart-button.empty-heart {
  color: gray;
}
/* line 420, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel {
  text-align: center;
  margin-bottom: 14px;
}
/* line 425, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .rating-box {
  padding: 0.5rem 0 0 1.5rem;
}
/* line 427, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .rating-box .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  font-weight: bold;
}
/* line 433, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .rating-box .small_rating.green-rating {
  background-color: #16be48;
}
/* line 436, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .rating-box .small_rating.red-rating {
  background-color: #FF5722;
}
/* line 439, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .rating-box .small_rating.orange-rating {
  background-color: #FFA000;
}
/* line 445, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-name a {
  text-decoration: underline;
  font-size: 0.875rem;
  color: #303030;
}
/* line 450, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-name .small_rating {
  font-size: 12px;
  background-color: #8f1c1e;
  color: white;
  padding: 2px;
  font-weight: bold;
  margin-left: 5px;
}
/* line 457, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-name .small_rating.green-rating {
  background-color: #16be48;
}
/* line 460, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-name .small_rating.red-rating {
  background-color: #FF5722;
}
/* line 463, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-name .small_rating.orange-rating {
  background-color: #FFA000;
}
/* line 468, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .designer-panel .designer-sub-panel {
  margin-bottom: 10px;
  font-size: 14px;
  color: #303030;
  padding: 0.5rem 0 0 1.8rem;
}
/* line 475, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .tap_text {
  text-align: center;
}
/* line 477, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .tap_text small {
  color: #303030;
}
/* line 481, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_images {
  margin: 0px;
  text-align: center;
}
/* line 484, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_images li {
  text-align: center;
}
/* line 487, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_images img {
  margin: auto;
}
/* line 491, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .th {
  border: none !important;
}
/* line 494, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .lSGallery {
  margin-left: auto;
  margin-right: auto;
  border-radius: 2px;
}
/* line 500, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block ul.lSPager li {
  margin: 0 2px;
}
/* line 502, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block ul.lSPager li a {
  background-color: #969696;
  height: 6px;
  width: 6px;
  z-index: 9;
}
/* line 509, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block ul.lSPager li.active a {
  background-color: #670b19;
}
/* line 515, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons {
  background-color: #f4f4f4;
  padding: 6px 3px 6px 8px;
  margin-bottom: 16px;
}
/* line 519, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons .hsw-block {
  margin-left: -3px;
}
/* line 521, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons .hsw-block a {
  border-radius: 50%;
  background-color: #670b19;
  padding: 1px 6px;
  font-size: 14px;
  font-style: italic;
  font-weight: 700;
  font-family: 'Times New Roman';
  cursor: pointer;
  color: #ffffff;
}
/* line 533, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons select {
  padding: 0.5em;
  color: #303030;
}
/* line 537, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons .addon_option_values {
  border: none;
  margin: 0px 0px 10px 10px;
}
/* line 540, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons .addon_option_values .atov-name {
  font-weight: 700;
  font-size: 14px;
}
/* line 545, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons .stitching_note {
  font-size: 13px;
  color: #303030;
}
/* line 549, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_addons #salwar_kameez_default, #design_image_block #design_addons #salwar_kameez_specific {
  text-align: justify;
  line-height: 1.2em;
  font-size: 13px;
  font-style: italic;
  display: block;
  margin-bottom: 10px;
  padding-left: 10px;
  padding-right: 10px;
  color: #b11f2c;
  font-family: Times new roman;
}
/* line 562, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .container_addons {
  display: block;
  position: relative;
}
/* line 566, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .container_addons input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}
/* line 571, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .checkmark {
  display: inline-block;
  text-align: center;
  height: 25px;
  width: 25px;
  border-radius: 50%;
}
/* line 578, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}
/* line 583, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .container_addons input:checked ~ .checkmark:after {
  display: block;
}
/* line 586, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_fabric_selected {
  box-shadow: 0 0 0 4px #fff, 0 0 0 6px #8F1B1D;
}
/* line 589, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .fabric_color_table {
  padding-top: 10px;
  justify-content: left;
  margin-bottom: 0px;
  background: none;
  border: none;
}
/* line 593, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .fabric_color_table tr {
  background: none;
}
/* line 598, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .fabric_color_table label {
  justify-content: center;
}
/* line 601, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .fabric_color_table td {
  width: 60px;
  text-align: center;
  padding: 0px !important;
}
/* line 608, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .regular_plus_options, #design_image_block .custom_hide_option_type, #design_image_block .plus_size_blouse_custom {
  display: none;
}
/* line 611, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_custom_regular, #design_image_block .plus_size_custom {
  padding: 8px 5px 8px 5px;
  margin-left: 20px;
  font-size: 13px;
}
/* line 616, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_blouse_regular {
  border: 1px solid #d8d8d8;
  color: #303030;
  background-color: white;
  font-weight: 700;
  margin-left: 6px;
  margin-top: 2px;
  float: left;
  border-radius: 1%;
  padding: 5px;
  cursor: pointer;
  display: inline-table;
  line-height: normal;
}
/* line 630, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .discount_details {
  margin-top: 3px;
}
/* line 633, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .selected_custom_plus_size {
  background-color: #D6D6D6;
  color: black;
}
/* line 637, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .other_custom_plus_size {
  background-color: white;
  border-style: solid;
  border-width: 1px;
  color: black;
}
/* line 643, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .custom_select_size {
  text-align: center;
}
/* line 646, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .standard_option_values {
  padding-left: 10px;
  padding-right: 10px;
}
/* line 650, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_fabric_color {
  height: 15px;
  width: 15px;
  background-color: red;
  border-radius: 50%;
  display: inline-block;
}
/* line 657, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_regular_btn {
  background-color: #9a9999;
}
/* line 660, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_info {
  padding-top: 5px;
}
/* line 663, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_info_i {
  margin-left: 10px;
}
/* line 666, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_btn_info {
  display: flex;
}
/* line 669, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_custom_regular_align {
  padding-right: 0px;
}
/* line 672, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_additional_price {
  font-size: 14px;
}
/* line 675, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .plus_size_buttons {
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 679, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .product_price_wo_discount, #design_image_block .actual-price {
  text-decoration: line-through;
  padding: 0 2% 0 0;
  color: #303030;
  font-size: 0.8em;
}
/* line 685, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .available-offers-on-pdp {
  padding-left: 0px;
}
/* line 688, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .offer-message {
  padding-bottom: 2%;
}
/* line 691, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .product_discount_price, #design_image_block .discounted-price {
  font-size: 1em;
  color: #303030;
  font-weight: bolder;
  padding: 0;
  padding-right: 2%;
}
/* line 698, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .changeInPriceNote {
  display: none;
  position: absolute;
}
/* line 701, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .changeInPriceNote span {
  background: #3ebe49;
  font-size: 12px;
  margin-left: -10px;
  padding: 0px 7px 0px 7px;
  border-radius: 4px;
  font-weight: bold;
  color: white;
  z-index: 10;
}
/* line 711, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .changeInPriceNote .arrow {
  background-color: #3ebe49 !important;
  width: 10px;
  height: 10px;
  left: 45%;
  top: -5px;
  transform: rotate(45deg);
  position: relative;
  display: inline-flex;
}
/* line 722, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .product_discount_percent {
  font-size: 0.9em;
  border-radius: 1em;
  width: 21%;
  padding: 0.7em;
  text-align: center;
}
/* line 730, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .line_through_text {
  text-decoration: line-through;
  margin-top: 0.1em;
  font-size: 18px;
}
/* line 735, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_price_block {
  border-top: 2px solid black;
  border-bottom: 2px solid black;
  margin-top: 1em;
}
/* line 739, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #design_price_block li {
  padding-bottom: 0em;
}
/* line 743, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block {
  background: #f4f4f4;
  padding: 4px 3px;
  margin-bottom: 16px;
}
/* line 747, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block .var-title {
  padding-left: 4px;
  font-weight: 700;
}
/* line 751, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block .size-chart-text {
  font-size: 13px;
  margin-left: 12px;
}
/* line 755, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block a {
  padding: 0.625em 1.25em;
  margin: 0.3em 0.3em 1.6em 0.3em;
  box-shadow: none;
  font-weight: 700;
}
/* line 760, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block a.selected, #design_image_block #variants_block a:focus, #design_image_block #variants_block a:hover {
  background-color: #9a9999;
}
/* line 762, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block a.selected .variant-qty-left-msg, #design_image_block #variants_block a:focus .variant-qty-left-msg, #design_image_block #variants_block a:hover .variant-qty-left-msg {
  display: block;
  visibility: visible;
  opacity: 1;
}
/* line 769, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #variants_block .variant {
  font-size: 0.75rem;
}
/* line 773, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .variant-qty-left-msg {
  visibility: hidden;
  transition: visibility 0s, opacity 0.5s linear;
  opacity: 0;
  position: absolute;
  color: #670b19;
  top: 32px;
  left: 0;
  right: 0;
  font-size: 0.75rem;
}
/* line 784, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .deal_ends {
  background: linear-gradient(to bottom, #d07889, #FFF);
  margin-bottom: 20px;
  font-size: 18px;
  border-radius: 0px;
  padding: 4px 2px;
}
/* line 790, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .deal_ends .countdown {
  text-align: center;
}
/* line 792, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .deal_ends .countdown .deal_text {
  display: inline-block;
  font-family: inherit;
  color: #670b19;
  vertical-align: bottom;
  font-weight: bold;
  text-align: left;
}
/* line 800, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .deal_ends .countdown .clock {
  text-align: right;
  color: #670b19;
  font-weight: bold;
}
/* line 807, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #line_items_count {
  display: none;
  background-color: #d3b9be;
  color: #000000;
  text-align: center;
  padding: 2px;
  font-size: 14px;
  margin: 0.375rem 0;
}
/* line 827, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #line_items_count .line_items_count_text span {
  color: #670b19;
  text-transform: uppercase;
  font-weight: 700;
}
/* line 835, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block #add_review_button button#add_review {
  margin-bottom: 0;
}
/* line 839, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products {
  padding: 0 0.4rem;
}
/* line 841, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product-header {
  padding: 0.4rem 0;
  font-family: 'Lato Black';
}
/* line 845, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product {
  padding: 10px 0;
  border-top: 1px solid #d8d8d8;
}
/* line 849, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .addon-product-detail img {
  border: 1px solid #c1bbbb;
}
/* line 854, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns label {
  margin-left: 0;
}
/* line 856, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns label span {
  font-size: 0.75rem;
  margin: 0.3em;
}
/* line 861, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns .addon_product {
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  width: 22px;
  height: 22px;
  border: 2px solid grey;
  border-radius: 2px;
  margin: 0.3em;
}
/* line 871, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns .addon_product:not(:checked):after {
  content: "+";
  color: grey;
  padding: 0 0.25rem;
  font-size: 1rem;
  font-family: 'Lato Black';
}
/* line 879, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns .addon_product:checked {
  border: 2px solid #670b19;
  background: #670b19;
}
/* line 882, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .columns .addon_product:checked:after {
  content: "";
  top: 0.6em;
  left: 0.6em;
  width: 7px;
  height: 11px;
  border-top: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
  -webkit-transform: rotateZ(37deg);
  transform: rotateZ(37deg);
  -webkit-transform-origin: 100% 100%;
  transform-origin: 100% 100%;
  position: absolute;
}
/* line 901, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .addon-details {
  padding-left: 0.9375rem;
}
/* line 903, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .addon-details .product-title {
  color: #303030;
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 910, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_image_block .design-addon-products .addon-product .addon-details .product_design_price {
  margin: 0;
}

/* line 918, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.plus_size_info_modal.reveal-modal {
  height: 50%;
  min-height: 50vh;
  width: 80%;
  left: 10%;
  top: 25%;
}

/* line 926, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.animate {
  -webkit-transition: -webkit-transform .5s ease-out;
  transition: -webkit-transform .5s ease-out;
  transition: transform .5s ease-out;
  transition: transform .5s ease-out,-webkit-transform .5s ease-out;
}

/* line 933, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#write_review #rating-alert-message, #write_review #review-alert-message, #write_review #save-alert-message {
  display: none;
  color: #f54e1a;
  font-size: 14px;
}
/* line 938, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#write_review .alert-border {
  border: 2px solid #f54e1a;
}
/* line 942, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#write_review #form-rating-star img {
  width: 20px;
}
/* line 946, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#write_review #review-text {
  background-color: #565656;
}

/* line 950, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.percent_disc {
  background: #b11f2d;
  font-size: 0.675rem;
  color: #fff;
  padding: 0 1%;
  margin-bottom: 0;
}

/* line 959, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details .tabs .tab-title > a {
  padding: 0.5em 1em;
  margin: .03em;
  background-color: inherit;
  color: inherit;
  border: .1em solid;
}
/* line 966, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details .tabs .tab-title.active > a {
  background-color: #FFFFFF;
  color: #222222;
}
/* line 972, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details ul.no-bullet label b {
  text-decoration: underline;
}
/* line 975, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details ul.no-bullet label {
  color: white;
}
/* line 979, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details table {
  background: inherit;
  border: none;
}
/* line 983, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details table tr:nth-of-type(even) {
  background: inherit;
}
/* line 986, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details table tr td {
  color: #303030;
  vertical-align: baseline;
}

/* line 994, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.padding-6 {
  padding: 6px;
}

/* line 997, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block {
  background: #f4f4f4;
  margin-bottom: 8px;
  color: #303030;
}
/* line 1001, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block li, .listing_panel_block label {
  cursor: auto;
}
/* line 1004, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .variant-price-text {
  font-size: 12px;
}
/* line 1008, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block p {
  text-align: left;
  /*background: rgba(96, 96, 96, 0.9) none repeat scroll 0% 0%;*/
  color: #303030;
  border-radius: 2px 2px 0px 0px;
  padding: 6px;
  margin-bottom: 0;
}
/* line 1017, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block ul {
  font-size: 0.8125rem;
  padding: 4px;
  list-style-type: circle;
  color: #303030;
  margin-left: 1.5rem;
  margin-bottom: 0;
}
/* line 1026, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block td {
  vertical-align: baseline;
}
/* line 1029, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block #cod_available {
  font-size: 0.875rem;
}
/* line 1032, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .delivery_block {
  font-size: 0.875rem;
}
/* line 1034, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .delivery_block .notice {
  color: #670b19;
  font-size: 12px;
  line-height: 15px;
  font-style: italic;
}
/* line 1040, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .delivery_block del.strike_old_date {
  color: gray;
}
/* line 1044, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .sahre-earn {
  font-family: 'Lato Black';
  font-size: 0.875rem;
  text-decoration: underline;
}
/* line 1049, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.listing_panel_block .whatsapp-icon {
  width: 2.5em;
  border-left: 2px solid;
  padding-left: 0.75rem;
}

/* line 1057, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table, .accordion ul.sub-specs-table {
  display: none;
}
/* line 1061, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table.sub-specs-line {
  display: table;
}
/* line 1065, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion a, .accordion .sub-grp {
  outline: none;
  color: inherit;
}
/* line 1070, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .expand {
  display: none;
  float: right;
}
/* line 1075, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .collapse {
  float: right;
}
/* line 1079, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .spec-collapse {
  display: block;
}
/* line 1084, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .sub-grp p {
  font-size: 0.9rem;
  margin-left: 0.6rem;
}
/* line 1087, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .sub-grp p .button-icon {
  transition: all .5s ease-in-out;
  display: inline-block;
  font-weight: 700;
  font-size: 1.1rem;
  line-height: 1.8;
}
/* line 1094, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion .sub-grp p .button-toggle {
  transform: rotate(90deg);
}
/* line 1099, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table.sub-specs-table, .accordion .sub-specs-line {
  margin: 0 0 0.5rem 0.6rem;
}
/* line 1101, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table.sub-specs-table td.spec, .accordion .sub-specs-line td.spec {
  padding: 0.2rem 0.4rem;
}
/* line 1104, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table.sub-specs-table td.spec.first, .accordion .sub-specs-line td.spec.first {
  padding-right: 0px;
}
/* line 1108, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.accordion table.sub-specs-table {
  padding-left: 0.7rem;
}

/* line 1113, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#designable_details ul {
  margin-left: 1rem;
}

/* line 1117, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.pre-order {
  padding: 7px 20px;
}
/* line 1119, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.pre-order #pre-order-check {
  vertical-align: middle;
  margin: 0px -2px 0px 0px;
}
/* line 1123, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.pre-order label {
  color: #ffffff;
}

/* line 1130, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myImg {
  cursor: pointer;
  transition: 0.3s;
}

/* The Modal (background) */
/* line 1136, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.modal {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: #ffffff;
}

/* Modal Content (Image) */
/* line 1150, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
}

/* line 1157, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#loadingImage {
  position: fixed;
  left: 45%;
  top: 30%;
  border: 4px dotted #670b19;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}

/* Add Animation - Zoom in the Modal */
/* line 1170, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.modal-content {
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
/* shake effect */
/* line 1187, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.shake-effect {
  -webkit-animation-duration: 0.75s;
  animation-duration: 0.75s;
  -webkit-animation-fill-mode: none;
  animation-fill-mode: none;
  -webkit-animation-name: shake;
  animation-name: shake;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    transform: translateX(5px);
    color: #ed8f03;
  }
}
@-webkit-keyframes shake {
  0%, 100% {
    -webkit-transform: translateX(0);
    color: #ed8f03;
  }
  20%, 60% {
    -webkit-transform: translateX(-5px);
    color: #ed8f03;
  }
  40%, 80% {
    -webkit-transform: translateX(5px);
    color: #ed8f03;
  }
}
/* The Close Button */
/* line 1207, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.close {
  font-size: 2em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
  display: block;
  text-align: right;
}

/* line 1215, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}

/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px) {
  /* line 1225, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
  .modal-content {
    width: 100%;
  }
}
@media only screen and (min-width: 1200px) {
  /* line 1231, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
  .modal-content {
    width: auto;
    height: auto;
  }
}
/* line 1237, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#branch-banner-iframe {
  z-index: 1 !important;
}

/* line 1241, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.notice_class {
  color: orange;
  font-size: 14px;
  line-height: 15px;
}

/* line 1247, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.product_design_price {
  margin: 0;
}
/* line 1249, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.product_design_price .flash-icon {
  background-image: url(/assets/flash_icon-f2098f14ee8a5124c88abf6b4a184f10b51ca911c025e3399474534623439265.png);
  background-repeat: no-repeat;
  height: 1.2rem;
  background-size: 1.2em;
  width: 1.2rem;
  float: left;
  margin-top: 1px;
}

/* line 1260, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.font_greyed {
  color: #303030;
}
/* line 1262, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.font_greyed.delivery_day {
  font-size: 0.875rem;
}

/* line 1266, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.rating_message {
  font-size: 14px;
  padding-left: 8px;
}

/* line 1270, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings {
  display: inline;
}
/* line 1272, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 7px;
  font-weight: bold;
  display: inline-block;
  margin: 16px 0;
}
/* line 1282, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .green-rating {
  background-color: #16be48;
}
/* line 1285, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .red-rating {
  background-color: #FF5722;
}
/* line 1288, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .orange-rating {
  background-color: #FFA000;
}
/* line 1292, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .mirraw-recommended {
  display: inline-block;
  border-left: 2px solid #8f8f8f;
  margin-left: 10px;
  padding-left: 8px;
  position: relative;
}
/* line 1301, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .mirraw-recommended::before, .reviews_ratings .mirraw-recommended::after {
  content: '';
  position: absolute;
  left: -2px;
  height: 18px;
  width: 2px;
  background-color: #f4f4f4;
}
/* line 1312, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .mirraw-recommended::before {
  top: 0;
}
/* line 1316, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .mirraw-recommended::after {
  bottom: 0;
}
/* line 1320, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.reviews_ratings .mirraw-recommended > img {
  height: 54px;
}

/* line 1325, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.review-opinion {
  font-size: 12px;
  font-weight: 600;
  margin-top: 5px;
}

/* line 1330, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-text {
  margin: 8px 0px 8px 16px;
  color: #303030;
  font-weight: 700;
  font-size: 14px;
}

/* line 1336, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
button.btn-view-size {
  background: transparent;
  color: #b11f2d;
  padding: 4px 2px;
  font-size: 13px;
  margin: 0px;
}

/* line 1343, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
button.btn-view-size:hover {
  background: #009688;
  border: 1px solid #088074;
  color: #1c1b1c;
}

/* line 1348, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-chart-div {
  margin-bottom: 30px;
  margin: 0px 0px 10px 10px;
  padding: 0px;
  height: auto;
}
/* line 1354, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-chart-div .size-chart {
  margin: 8px 0px 8px 16px;
}
/* line 1356, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-chart-div .size-chart a.size {
  background-color: white;
  border: 1px solid #d8d8d8;
  color: #303030;
  font-weight: 700;
  margin-left: 6px;
  margin-top: 2px;
  float: left;
  border-radius: 1%;
  padding: 5px;
  cursor: pointer;
  display: inline-table;
}
/* line 1369, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-chart-div .size-chart a.size.selected, .size-chart-div .size-chart a.size:hover {
  background-color: #9a9999;
  color: #2b2a2a;
  border-color: #a2a0a1;
}
/* line 1375, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.size-chart-div .show_rts_sizes {
  margin: -10px 0px 8px 12px;
}

/* line 1379, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
span.size-left, span.size-right {
  position: absolute;
  padding: 4px 8px;
  font-size: 18px;
  font-weight: 700;
  background-color: #d2d2d2;
  top: 38px;
  color: #2d2b2b;
  z-index: 2;
  cursor: pointer;
  border-radius: 50%;
}

/* line 1391, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
span.size-left {
  left: -2px;
  display: none;
}

/* line 1395, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
span.size-right {
  right: -28px;
  display: block;
}

/* line 1399, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
span.size-left:hover, span.size-right:hover {
  background-color: #afafaf;
}

/* line 1403, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart {
  -webkit-overflow-scrolling: touch;
  height: 100%;
}
/* line 1406, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .btn_close_top {
  float: right;
  padding: 0px 7px;
  margin: 10px 4px 0px 0px;
  font-size: 24px;
  border-radius: 50%;
}
/* line 1413, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .btn_close {
  padding: 8px 30px;
  margin: 20px 0px 80px;
  font-size: 20px;
  border-radius: 5px;
}
/* line 1420, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .modal-sm-size .modal-content {
  width: 900px;
}
/* line 1423, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-header h4 {
  color: #303030;
}
/* line 1428, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-body .radio-buttons {
  margin: 10px 0px;
}
/* line 1430, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .modal-sm-size .modal-content .modal-body .radio-buttons .size-label {
  color: #303030;
  text-align: center;
}
/* line 1438, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#modal-size-chart .head_style {
  text-align: center;
  padding: 3px;
  font-size: 12px;
}

/* line 1444, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.stitching_offer {
  color: #EE9209;
  font-size: 15px;
  margin-top: 10px;
}

/* line 1450, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design_offer_label, .loyalty_label, .fd_message {
  display: flex;
  color: #fff;
  vertical-align: middle;
  padding: 0;
  width: 100%;
  font-size: 14px;
}
/* line 1457, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design_offer_label .b1g1_tnc, .design_offer_label .loyalty_tnc, .design_offer_label .qpm_tnc, .design_offer_label .fd_tnc, .loyalty_label .b1g1_tnc, .loyalty_label .loyalty_tnc, .loyalty_label .qpm_tnc, .loyalty_label .fd_tnc, .fd_message .b1g1_tnc, .fd_message .loyalty_tnc, .fd_message .qpm_tnc, .fd_message .fd_tnc {
  font-size: 0.875rem;
}

/* line 1462, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal {
  background-color: rgba(146, 140, 140, 0.84);
  height: 100%;
  overflow: auto;
}
/* line 1466, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-dialog {
  margin-top: 85px;
}
/* line 1469, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-header {
  font-size: 13px;
  display: inline-flex;
  background-color: #8f1b1d;
  width: 100%;
}
/* line 1474, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-header #stitching-confirmation-message {
  padding: 11px;
  color: #ffffff;
}
/* line 1478, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-header a {
  padding-left: 45%;
}
/* line 1482, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-content {
  width: 288px;
  height: 100%;
  background-color: #191919;
  color: #ffffff;
  font-size: 1.25em;
}
/* line 1489, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .closeStitchingModal {
  color: white;
  font-size: 1.8em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
}
/* line 1495, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-footer {
  text-align: center;
  padding: 2% 0 2%;
}
/* line 1498, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-footer .btn {
  padding: 7px !important;
  width: 46%;
  font-size: 12px !important;
  border-radius: 2px;
}
/* line 1504, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-footer .stitch {
  background: #0e9a7d;
}
/* line 1507, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-footer #add-cart {
  background: #ffffff;
  color: #8f1b1d !important;
}
/* line 1512, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-body {
  text-align: center;
  padding: 20px;
  font-size: 14px;
}
/* line 1516, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#stitchingModal .modal-body .modal-message {
  padding-top: 20px;
}

/* line 1522, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail {
  display: none;
  background-color: transparent;
  height: 90%;
  overflow: hidden;
  width: 90%;
  margin: 10% 5%;
  outline: none;
}
/* line 1531, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content {
  height: 100%;
  position: absolute;
}
/* line 1534, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content .modal-header {
  float: right;
  position: inherit;
  right: 5px;
}
/* line 1539, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content .modal-body {
  padding: 16px;
  font-size: 12px;
  overflow: auto;
  position: inherit;
  height: 100%;
  background: white;
  padding-bottom: 40px;
}
/* line 1548, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content .modal-body .modal-text .qsn {
  font-weight: bold;
}
/* line 1551, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content .modal-body .modal-text .ans {
  margin-bottom: 20px;
}
/* line 1554, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-content .modal-body .modal-text ul {
  font-size: 12px;
}
/* line 1560, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.tnc-in-detail .modal-dialog .modal-footer {
  text-align: center;
  background: #8f1b1d;
  padding: 15px 0px;
  position: inherit;
  width: 100%;
  bottom: 0;
  box-shadow: 2px -4px 20px 0px grey;
  color: #ffffff !important;
  font-weight: bold;
}

/* line 1574, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal {
  display: none;
  background-color: transparent;
  height: 65%;
  overflow: hidden;
  width: 90%;
  margin: 35% 5%;
  outline: none;
}
/* line 1583, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content {
  height: 100%;
  position: absolute;
}
/* line 1586, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-header {
  float: right;
  position: inherit;
  right: 5px;
}
/* line 1591, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-body {
  padding: 16px;
  font-size: 12px;
  overflow: auto;
  position: inherit;
  height: 100%;
  background: white;
  padding-bottom: 40px;
}
/* line 1600, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-body .modal-text ol {
  counter-reset: item;
  margin: 0;
  padding-left: 0;
}
/* line 1605, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-body .modal-text ol > li {
  counter-increment: item;
  list-style: none inside;
  margin: 20px 0;
  overflow: hidden;
  font-size: 13px !important;
  font-weight: bold;
  line-height: 1.3;
}
/* line 1614, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-body .modal-text ol > li:before {
  content: counter(item);
  margin-right: 20px;
  padding: 7px;
  display: block;
  border-radius: 50%;
  width: 26px;
  height: 26px;
  background: #8f1b1d;
  color: #fff;
  text-align: center;
  font: 13px 'Lato', Helvetica, Arial, sans-serif;
  font-weight: 80;
  float: left;
}
/* line 1630, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-content .modal-body .share-n-earn-tnc {
  text-align: center;
  text-decoration: underline;
  margin-bottom: 15px;
}
/* line 1637, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-modal .modal-dialog .modal-footer {
  text-align: center;
  background: #8f1b1d;
  padding: 15px 0px;
  position: inherit;
  width: 100%;
  bottom: 0;
  box-shadow: 2px -4px 20px 0px grey;
  color: #ffffff !important;
  font-weight: bold;
}

/* line 1651, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block {
  padding-top: 4px;
  margin-bottom: 8px;
  color: black;
}
/* line 1655, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-title {
  font-size: 12px;
  font-weight: bold;
}
/* line 1659, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-image {
  width: 22%;
}
/* line 1662, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-body {
  height: 100%;
  width: 40%;
  font-weight: normal;
  margin-top: 4px;
  padding-top: 0px;
  padding-left: 3px;
  padding-right: 6px;
  line-height: 1.3;
  font-size: 11px;
}
/* line 1673, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-button {
  width: 120px;
  height: 35px;
  padding: 0px;
  font-size: 11px;
  font-weight: bold;
  border-radius: 4px;
  border: 2px solid;
  margin-top: 4px;
  margin-right: 3px;
}
/* line 1683, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-button .share-btn-text > img {
  width: 26px;
  padding-left: 2px;
}
/* line 1687, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.share-n-earn-block .share-n-earn-button .share-btn-text {
  text-align: center;
  padding: 4px 0;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 1699, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#ready_to_ship {
  background: url(/assets/rts_logo-ad8b4d716c9c1cf163c952c75bb0938b9803476bae6b8e9dd23d7a33e299eb39.png) no-repeat left top;
  display: inline-block;
  width: 107px;
  height: 20px;
  margin-left: 16px;
  background-size: 105px;
}

/* line 1709, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.price_match_guarantee .price_match_description {
  display: none;
  background: white;
  color: black;
  padding: 10px;
  z-index: 1;
  position: absolute;
  margin-top: 10px;
}

/* line 1720, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#design_images_zoom {
  height: 100% !important;
}

/* line 1724, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myModal {
  height: 100%;
}
/* line 1742, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myModal .lSAction > .lSNext {
  background-color: white;
  border-radius: 25px;
  width: 40px;
  height: 40px;
  opacity: 0.6;
  background-image: none;
}
/* line 1744, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myModal .lSAction > .lSNext:before {
  content: '';
  border-right: 5px solid #a99b9b;
  border-top: 5px solid #a99b9b;
  width: 15px;
  height: 15px;
  position: absolute;
  transform: rotate(45deg);
  bottom: 13px;
  left: 12px;
}
/* line 1751, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myModal .lSAction > .lSPrev {
  background-color: white;
  border-radius: 25px;
  width: 40px;
  height: 40px;
  opacity: 0.6;
  background-image: none;
}
/* line 1753, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#myModal .lSAction > .lSPrev:before {
  content: '';
  border-right: 5px solid #a99b9b;
  border-top: 5px solid #a99b9b;
  width: 15px;
  height: 15px;
  position: absolute;
  transform: rotate(-135deg);
  bottom: 13px;
  left: 14px;
}

/* line 1762, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design_wrap {
  position: relative;
  display: inline-block;
}

/* line 1767, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.label_for_image_box {
  position: absolute;
  display: none;
  top: -4px;
  right: 0px;
  height: 125px;
  overflow: hidden;
  z-index: 1;
  width: 128px;
}

/* line 1778, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.label_text {
  display: none;
  top: 35px;
  width: 170px;
  padding: 5px 29px 5px;
  font-size: 15px;
  text-align: center;
  margin-left: -24px;
  color: #fff;
  z-index: 1;
  background-color: #e92d4c;
  position: absolute;
  left: 19px;
  -webkit-transform: rotate(45deg) translate3d(0, 0, 0);
  -moz-transform: rotate(45deg) translate3d(0, 0, 0);
  -ms-transform: rotate(45deg) translate3d(0, 0, 0);
  transform: rotate(45deg);
}

/* line 1797, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#standard_height_notice {
  background-color: #ffffff;
  position: absolute;
  border-radius: 5px;
  padding: 3px;
  z-index: 1;
  right: 0px;
  top: 111%;
}

/* line 1807, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
#standard_height_notice:after {
  content: '';
  width: 0px;
  height: 0px;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-top: 10px solid transparent;
  border-bottom: 10px solid #ffffff;
  position: absolute;
  top: 0%;
  right: 7%;
  margin-top: -20px;
}

/* line 1821, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.info_message {
  display: none;
  padding: 10px;
  font-style: italic;
  font-size: 13px;
  color: #8e1b1c;
  text-align: justify;
}

/* line 1829, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.highlight {
  background: #b7b7b7 !important;
}

/* line 1834, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant-bg {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 99;
  opacity: 0;
  -webkit-transition: opacity 0.3s cubic-bezier(0, 0, 0.3, 1);
  transition: opacity 0.3s cubic-bezier(0, 0, 0.3, 1);
  pointer-events: none;
}
/* line 1847, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  z-index: 99;
  -webkit-transform: translate3d(0, 110%, 0);
  transform: translate3d(0, 110%, 0);
  will-change: transform;
}
/* line 1858, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content {
  margin: 0 1em;
}
/* line 1860, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-header {
  padding: 1em 0;
  border-bottom: 1px solid #d8d8d8;
  margin-bottom: 1em;
}
/* line 1864, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-header .close-variant-select {
  font-size: 22px;
  float: right;
  line-height: 1;
}
/* line 1871, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-body .columns {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
}
/* line 1876, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-body .addon-details .product-title {
  color: #303030;
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* line 1883, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-body .addon-details .product_design_price {
  margin: 0;
}
/* line 1887, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-body .addon-details .discount_details .left {
  margin-left: 3px;
}
/* line 1893, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .variant-body .text .alert {
  background: #8f1b1d;
}
/* line 1898, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.select-variant .variant_selection .content .select-addon-variant {
  text-align: center;
  border: 1px solid #d4d4d4;
  padding: 0.6em;
  text-transform: uppercase;
}

/* line 1907, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb {
  display: flex;
  padding-left: 10px;
}
@media only screen and (min-width: 500px) and (max-width: 1040px) {
  /* line 1907, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
  .design-breadcrumb {
    margin-top: 15px;
  }
}
/* line 1915, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb ul {
  margin-bottom: 0px;
  margin-left: 0px;
}
/* line 1919, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb li:not(:first-child):before {
  content: '/';
  margin-left: 0px;
  margin-right: 0px;
}
/* line 1924, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb a {
  font-size: 12px !important;
  color: #303030;
}
/* line 1928, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb li {
  font-size: 12px !important;
  display: inline-block;
}
/* line 1932, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/designs_red.scss */
.design-breadcrumb .final {
  font-weight: bold;
}

/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* line 10, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend, .recommended-design-box {
  border-top: 1px solid #636060;
  border-bottom: 1px solid #636060;
  margin: 0 auto;
  background-color: transparent;
  box-shadow: none;
  max-width: 65.5rem;
  padding: 8px 0px;
}
/* line 19, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .title-block h1, .recommended-design-box .title-block h1 {
  color: #303030;
  font-size: 18px;
  text-transform: capitalize;
}

/* line 27, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader {
  display: inline-block;
  width: 49%;
  margin-bottom: 10px;
}
/* line 31, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader .image-container {
  text-align: center;
  background-color: #ffffff;
  padding: 15px 0px;
}
/* line 35, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .unbxd-img-loader .image-container img {
  animation: spin 8s ease-in-out infinite;
}
/* line 40, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .product-1 {
  padding-right: 4px;
}
/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.ebox-recommend .product-2 {
  padding-left: 4px;
}

/* line 48, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.no-design-box {
  text-align: center;
  font-style: italic;
}

/* line 53, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;
}
/* line 58, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products {
  margin: 0px;
}
/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list {
  display: inline-block;
  margin-right: 8px;
  width: 153px;
}
/* line 64, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box {
  position: relative;
  /*border: 1px solid #171616;*/
  margin: 0;
  width: 153px;
  border-radius: 2px;
}
/* line 70, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box {
  width: 151px;
  border-radius: 2px 2px 0px 0px;
}
/* line 73, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box img {
  width: 100%;
  border-radius: 2px 2px 0px 0px;
}
/* line 77, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating {
  position: absolute;
  width: 40px !important;
  height: 40px;
  margin-top: -25px;
}
/* line 82, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .small_rating {
  font-size: 12px;
  background-color: #16be48;
  color: white;
  padding: 5px;
  border-radius: 5%;
  font-weight: bold;
}
/* line 90, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .green-rating {
  background-color: #16be48;
}
/* line 93, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .red-rating {
  background-color: #FF5722;
}
/* line 96, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .image-box .catalog-rating .orange-rating {
  background-color: #FFA000;
}
/* line 101, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel {
  height: 3.8em;
  position: relative;
  padding: 0.6em;
  border: none;
  margin-bottom: 0em;
  border-radius: 0px 0px 2px 2px;
  text-align: center;
}
/* line 110, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel.design_desc a {
  color: white;
}
/* line 114, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .recommended-products .product-list .product-box .panel .price-block {
  font-size: 14px;
  color: #303030;
}
/* line 122, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn {
  position: relative;
  font-size: 26px;
  line-height: 32px;
  background-color: #4c4b4b;
  color: #eaeaea;
  text-align: center;
  bottom: 160px;
  width: 5%;
  box-shadow: -1px 0px 2px 0px #383737;
  display: none;
}
/* line 134, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn-left {
  left: 1px;
  float: right;
}
/* line 138, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
.recommended-design-box .scroll-btn-right {
  right: 1px;
  float: left;
}

@media only screen and (max-width: 989px) {
  /* line 146, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    overflow-x: scroll;
  }
  /* line 151, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 2px;
  }
}
@media only screen and (min-width: 980px) {
  /* line 160, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .row {
    text-align: center;
  }
  /* line 163, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products {
    overflow-x: visible;
    width: auto;
  }
  /* line 166, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .recommended-design-box .recommended-products .product-list {
    margin-bottom: 10px;
  }

  /* line 171, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/unbxd_recommendations_red.scss */
  .scroll-btn {
    display: none;
  }
}
/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */
/* line 11, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.page.designs_reviews #container, .page.reviews_site_review #container {
  margin-top: 1.8em;
}

/* line 15, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
#site_review_top_content {
  float: left;
  /*border: 1px dotted grey;*/
  padding: 20px;
  background-color: #ffffff;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  box-shadow: 0 0 0.5em #2d2d2d;
  width: 100%;
}
/* line 25, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
#site_review_top_content #top_content {
  font-size: 0.9rem;
}
/* line 26, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
#site_review_top_content #top_content.read-more {
  height: 125px;
  overflow: hidden;
}

/* line 33, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
#review_of_design {
  background: #f4f4f4;
  margin-bottom: 16px;
  color: #303030;
}

/* line 38, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container {
  width: 100%;
  height: 100%;
  color: #303030;
  padding: 10px;
}
/* line 43, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .heading {
  font-size: 18px;
  font-weight: bold;
}
/* line 47, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .heading_review {
  padding-bottom: 8px;
  margin-left: 7px;
}
/* line 50, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .heading_review .view_all {
  font-size: 15px;
  float: right;
  margin-right: 10px;
}
/* line 56, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .heading-content {
  font-size: 15px;
  font-weight: bold;
}
/* line 60, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-btn-row {
  width: 100%;
  margin-top: 2%;
}
/* line 63, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-btn-row .tabbed-btn {
  width: 50%;
  text-align: center;
  vertical-align: middle;
  padding: 5px 2px;
  font-size: 16px;
  float: left;
  cursor: pointer;
  color: #303030;
  font-weight: bold;
}
/* line 74, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-btn-row .active-btn {
  background-color: #d5a1ab;
}
/* line 77, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-btn-row .passive-btn {
  background-color: transparent;
  border: none;
}
/* line 82, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view {
  float: left;
  width: 100%;
  color: #303030;
  padding-bottom: 5px;
  margin-top: 25px;
}
/* line 88, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row:first-child {
  border-bottom: none;
}
/* line 91, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view #hidden_site_review {
  display: none;
}
/* line 94, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row {
  padding: 2%;
  width: 100%;
  clear: both;
  margin-bottom: -10px;
}
/* line 99, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .add-new-review {
  float: right;
}
/* line 103, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block {
  background-color: #ffffff;
  display: inline-block;
  width: 96%;
  padding: 15px 15px 0px 15px;
  margin-left: 2%;
  border-bottom: 1px solid #b9b6b6;
}
/* line 110, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .margin-left-top {
  margin-top: 1%;
  margin-left: 8%;
}
/* line 114, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .view-more-btn {
  position: relative;
  width: 100px;
  padding: 5px 3px;
  float: right;
  height: 25px;
  font-size: 12px;
  color: #7EDFFF;
  text-align: right;
}
/* line 125, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .user-sign {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #000000;
  text-align: center;
  float: left;
  margin: 5px 0px 0px 0px;
}
/* line 133, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .user-sign .user-name {
  position: relative;
  top: 25%;
  font-size: 1rem;
}
/* line 137, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .user-sign .user-name .user_image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  top: -12.5px;
  position: relative;
}
/* line 146, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .design-img {
  float: left;
  margin-top: 7px;
  overflow: hidden;
}
/* line 150, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .design-img .image_block_hidden {
  width: 50px;
  height: 50px;
}
/* line 155, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .stars {
  width: 55%;
  float: left;
  font-size: 12px;
}
/* line 161, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .stars .star img {
  background: none;
}
/* line 164, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .stars .user-name-full {
  overflow: hidden;
}
/* line 167, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .stars .rated-ago {
  margin-top: 0px;
}
/* line 171, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container .tabbed-view .review-row .block .review-text {
  float: left;
  width: 100%;
  height: 56px;
  overflow: hidden;
  font-size: 12px;
  text-align: justify;
  padding-right: 3%;
  margin-left: 0px;
}
/* line 184, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container #product_reviews {
  display: block;
}
/* line 187, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.review-container #site_reviews {
  display: none;
}

/* line 191, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.reveal-modal {
  color: #303030;
}

/* line 194, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.black-content {
  position: absolute;
  top: 0px;
  height: 100%;
  overflow: scroll;
}
/* line 195, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.black-content #modalTitle {
  color: #303030 !important;
}
/* line 198, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.black-content #modal_star {
  border-bottom: 1px solid white;
  padding-bottom: 15px;
}
@media (max-height: 600px) {
  /* line 203, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
  .black-content .modal-review-text {
    height: 100%;
    overflow-y: scroll;
  }
}
@media only screen and (min-device-width: 500px) and (orientation: landscape) {
  /* line 210, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
  .black-content .modal-review-text {
    height: 100%;
    overflow: scroll;
  }
}

/* line 220, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-green {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #2ecc71;
  border-color: #2ecc71;
}

/* line 226, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-blue {
  padding: 3px 12px !important;
  color: #670b19;
  background-color: #d5a1ab;
  font-size: 14px;
}

/* line 232, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-red {
  padding: 3px 12px !important;
  color: #fff;
  background-color: #EC407A;
  border-color: #EC407A;
}

/* line 238, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-green:hover {
  background-color: #27ae60;
  border-color: #27ae60;
}

/* line 242, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-blue:hover {
  border-color: #670b19;
}

/* line 245, /home/<USER>/mirraw_docker/mirraw-mobile/app/assets/stylesheets/reviews_red.scss */
.btn-custom-red:hover {
  background-color: #E91E63;
  border-color: #E91E63;
}
