(function(){var t,e,o,n,a,r,d,i,p;$(function(){return ga("send","event",{eventCategory:"Checkout",eventAction:"order-new",nonInteraction:!0})}),$(function(){var p,_,l,c,s,h;return $(".payment_options input:radio:first").prop("checked","checked"),i(),s=function(){return"order_pay_type_paypal"===$('input[name="order[pay_type]"]:checked').attr("id")||"order_pay_type_creditdebit_cardnet_banking"===$('input[name="order[pay_type]"]:checked').attr("id")?$("#paypal_message").show():$("#paypal_message").hide()},$(".domestic_offers_toggle").click(function(){return $(".domestic_offers_toggle .show_toggle_text").hasClass("hidden_offer_class")?($(".domestic_offers_toggle .show_toggle_text").removeClass("hidden_offer_class"),$(".offers_tab .hidden_offers").show(),$(".domestic_offers_toggle .show_toggle_text").text("Show Less"),$(".domestic_offers_toggle #down_arrow_toggle").hide(),$(".domestic_offers_toggle .hide_up").show(),$(".domestic_offers_toggle #up_arrow_toggle").show()):($(".domestic_offers_toggle .show_toggle_text").addClass("hidden_offer_class"),$(".offers_tab .hidden_offers").hide(),$(".domestic_offers_toggle .show_toggle_text").text($("#show_more_text").val()),$(".domestic_offers_toggle #up_arrow_toggle").hide(),$(".domestic_offers_toggle #down_arrow_toggle").show())}),c=function(){return"order_pay_type_cash_on_delivery"===$('input[name="order[pay_type]"]:checked').attr("id")?($(".grand_total").hide(),$(".grand_total_with_cod.hide").show(),$(".cod_charges").show()):($(".grand_total").show(),$(".grand_total_with_cod.hide").hide(),$(".cod_charges").hide())},$('input[name="order[pay_type]"]').on("click",function(){return c(),i(),s()}),c(),s(),l=function(t,e,o){var n,a;n=new Date,n.setTime(n.getTime()+24*o*60*60*1e3),a="expires="+n.toUTCString(),document.cookie=t+"="+e+";"+a+";path=/"},_=function(t){var e,o;return o="; "+document.cookie,e=o.split("; "+t+"="),2===e.length?e.pop().split(";").shift():void 0},$(".payment_options").length>0&&(window.onload=function(){var t;t=_("selected_payment_option"),$("#auto_select_retry_cod").val()?$("#order_pay_type_cash_on_delivery").attr("checked",!0):void 0===t?$(".payment_options input:radio:first").prop("checked","checked"):($(".payment_options #"+t).prop("checked","checked"),i(),c()),$("#wallet_payment_option").length>0&&p(),"PayPal"===$(".payment_options input:radio:checked").val()&&a()&&d()},$('input[name="order[pay_type]"]').on("click",function(){l("selected_payment_option",$('input[name="order[pay_type]"]:checked').attr("id"),7)}),p=function(){var t,e;"order_pay_type_cash_on_delivery"===$('input[name="order[pay_type]"]:checked').attr("id")?($(".grand_total, #wallet_discount_order_page, #wallet_payment_option").hide(),$(".cod_charges, .grand_total_with_cod.hide, #wallet_cant_be_used_for_cod").show(),$("#wallet_payment_option").length>0&&(t=$("#wallet_payment_option").data(),$("#you_pay_grand_total_with_cod").text(parseFloat(t.total)+parseFloat(t.referralAmount)+parseFloat(t.codCharges)))):($("#delivery_type_express").length>0&&$("#delivery_type_express").is(":checked")?($("#wallet_payment_option").hide(),t.total+=data("extraa_shipping"),e=!0):(e&&(t.total-=data("extraa_shipping"),e=!1),$("#wallet_payment_option").show()),$("#wallet_discount_order_page, .grand_total").show(),$("#wallet_cant_be_used_for_cod, .cod_charges, .grand_total_with_cod.hide").hide(),$("#wallet_payment_option").length>0&&$("#wallet_return").prop("checked")!==!0&&(t=$("#wallet_payment_option").data(),$("#grand_total_without_cod").text("Grand Total : "+t.total),$("#you_pay_grand_total").text(parseFloat(t.total))))},$('input[name="order[pay_type]"]').on("click",function(){var t;t=$(this).val(),r(),"PayPal"===t&&a()&&d(),p()}),h=function(t){t.removeProp("checked"),$("#wallet_error").text("*Wallet not applicable").show()},$("#wallet_return").click(function(){setWalletDiscount(),t()})),$("#otp-form").length||$("#codModal").css("height","auto").css("min-height",0),$("#new_order").submit(function(t,e){return($("#order_pay_type_cash_on_delivery").is(":checked")||$("#cashondelivery").is(":checked"))&&$("#codModal").length&&!e?(t.preventDefault(),o()):void 0}),$("#successButton").click(function(){return $("#new_order").trigger("submit",!0)}),$(document).on("closed.fndtn.reveal","[data-reveal]",function(){return $("#action_buttons input[type='submit']").removeAttr("disabled"),$("#action_buttons input[type='submit']").attr("value","PLACE ORDER"),$("#cod-otp").val(""),$("#otp-error-msg").hide(),$("html").removeClass("modal-open")}),$(document).on("open.fndtn.reveal","[data-reveal]",function(){return $("html").addClass("modal-open"),$("#otp-phone-change-form-and-content","#phone-change-error-msg").hide(),$("#otp-form-and-content").show()}),$(document).on("opened.fndtn.reveal","[data-reveal]",function(){return $("#cod-otp").focus()}),$("#otp-form").on("submit",function(t){return t.preventDefault(),$.ajax({type:"POST",data:{"cod-otp":$("#cod-otp").val()},url:$(this).attr("action"),dataType:"JSON",success:function(t){return t.verified===!0?$("#new_order").trigger("submit",!0):$("#otp-error-msg").show()}})}),$("#cod-otp").on("keydown",function(){return $("#otp-error-msg").hide()}),$("#resend-otp").on("click",function(){return $(this).attr("disabled",!0).css("opacity",.5),setTimeout(function(){return $("#resend-otp").attr("disabled",!1).css("opacity",1)},1e4),$.ajax(e($("#order_shipping_address").val(),$("#otp-phone").val(),!0))}),$("#cod-otp").on("input",function(){return $(this).val().match(/^[\d]{5}$/)?n($(this)):void 0}),$("#phone-change-button").on("click",function(){return $("#otp-form-and-content").hide(),$("#otp-phone-change-form-and-content").show(),$("#phone_for_otp").val($("#otp-phone").val())}),$("#otp-phone-change-form").on("submit",function(){return $("#phone_for_otp").val().length<8||$("#phone_for_otp").val().length>15?($("#phone-change-error-msg").show(),!1):void 0}),$("#otp-phone-change-form").on("submit",function(t){return t.preventDefault(),$.ajax({type:"POST",data:{phone:$("#phone_for_otp").val(),address_id:$("#address-phone-change-form").val()},url:$(this).attr("action"),dataType:"JSON",success:function(t){return t.otp_sent?($("#otp-phone-change-form-and-content").hide(),$("#otp-phone-text-value").html($("#phone_for_otp").val()),$("#otp-form-and-content").show()):$("#phone-change-error-msg").show()}})}),$("#otp-phone-change-form").on("ajax:success",function(t,e){return e.otp_sent?($("#otp-phone-change-form-and-content").hide(),$("#otp-phone-text-value").html($("#phone_for_otp").val()),$("#otp-form-and-content").show()):$("#phone-change-error-msg").show()}),$("#phone_for_otp").on("click",function(){return $("#phone-change-error-msg").hide()}),$("#otp-form-link").on("click",function(){return $("#otp-phone-change-form-and-content").hide(),$("#otp-form-and-content").show()})}),o=function(){return $("#codModal").foundation("reveal","open"),$("#otp-form").length?$.ajax(e($("#order_shipping_address").val(),$("#otp-phone").val(),!1)):void 0},e=function(t,e,o){return{type:"POST",data:{phone:e,address_id:t,resend:o},url:"/carts/generate_otp"}},n=function(t){t.attr("readonly","readonly"),t.attr("disabled","true"),setTimeout(function(){return t.blur(),t.removeAttr("readonly"),t.removeAttr("disabled")},100)},$(function(){return $(".add_checkout").on("click",function(){return"undefined"!=typeof fbq?fbq("track","AddPaymentInfo"):void 0})}),p=function(){var t,e,o;return t=$('input[name="delivery_type"]:checked'),o=t.data("shipping"),e=t.data("total"),void 0!==o&&void 0!==e&&($(".grand_total").html("Grand Total : "+e),$("#shipping_charge").html("Shipping : "+o),$(".grand_total.top").html(e),$("#estd_days").html("Estimated Delivery : "+t.data("delivery-days")+" days")),$("#delivery_type_express").is(":checked")?($("#wallet_return").prop("disabled",!0),$("#wallet_payment_option").hide(),$("#wallet_cant_be_used_for_express_delivery").show()):($("#wallet_return").prop("disabled",!1),$("#wallet_payment_option").show(),$("#wallet_cant_be_used_for_express_delivery").hide())},i=function(){var t,e,o,n;return n=$('input[name="order[pay_type]"]:checked'),n.data("domestic")?"order_pay_type_cash_on_delivery"!==$('input[name="order[pay_type]"]:checked').attr("id")?"available"===n.data("prepaidShippingPromo")?(o=n.data("grandtotalWithoutShipping"),$("#shipping_charge").html("Shipping : "+"FREE".fontcolor("green").bold()),$(".grand_total").html("Grand Total : "+o),$(".grand_total.top").html(o),$("#prepaid_discount").val(gon.prepaid_discount),$(".prepaid_discount").show()):($("#shipping_charge").html("Shipping : "+n.data("shipping")),n.data("prepaidPromotion")?(e=n.data("grandtotalWithPrepaidDiscount"),$(".grand_total").html("Grand Total : "+e),$(".grand_total.top").html(e),$("#prepaid_discount").val(gon.prepaid_discount),$(".prepaid_discount").show()):(t=n.data("grandtotal"),$(".grand_total").html("Grand Total : "+t),$(".grand_total.top").html(t),$("#prepaid_discount").val(0),$(".prepaid_discount").hide())):($("#shipping_charge").html("Shipping : "+n.data("shipping")),$("#prepaid_discount").val(0),$(".prepaid_discount").hide()):void 0},$(function(){return p(),t()}),$(function(){return $('input[name="delivery_type"]').on("click",function(){return p()}),$(window).scroll(function(){return stickyButton(action_buttons,totals_block,3.3),a()?stickyButton(paypal_button,totals_block,3.1):void 0})}),t=function(){return $("#wallet_return").length>0&&document.getElementById("wallet_return").checked?($("#express_delivery_division").hide(),$("#express_delievery_cannot_be_applied_for_wallet").show()):1===$("#delivery_type_express").length?(document.getElementById("express_delivery_division").style.display="block",$("#express_delievery_cannot_be_applied_for_wallet").hide()):void 0},a=function(){return $("#paypal_button").length>0},d=function(){return $("#action_buttons").hide(),$("#paypal_button").show()},r=function(){return $("#paypal_button").hide(),$("#action_buttons").show()}}).call(this);