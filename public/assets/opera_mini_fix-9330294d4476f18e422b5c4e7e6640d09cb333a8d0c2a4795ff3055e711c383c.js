(function() {
  if (Object.prototype.toString.call(window.operamini) === "[object OperaMini]") {
    $(document).ready(function() {
      var add_cont, href, order_parent;
      $("li.tabs > .tab-title").click(function() {
        var activate_content_selector, active_content_selector;
        active_content_selector = $(".tabs-content > .content.active").attr("id");
        $("#" + active_content_selector).removeClass("active");
        activate_content_selector = $($(this)[0].children[0]).attr("href");
        $(activate_content_selector).addClass("active");
        $("li.tabs > .tab-title.active").removeClass("active");
        return $(this).addClass("active");
      });
      $('.addon_types').on("change", function() {
        var addon_type_value_id;
        addon_type_value_id = $(this).attr('value');
        return $(this).find("option").each(function() {
          var current_val;
          current_val = $(this).val();
          if (current_val !== addon_type_value_id) {
            $('#atv_' + current_val).hide();
          }
          return $('#atv_' + current_val).show();
        });
      });
      if ((/cart/i.test(window.location.href)) && $(".item_block").length > 0) {
        order_parent = $('.cart_checkout');
        href = $('.add_place_order').attr('href');
        order_parent.hide();
        add_cont = $('.add_cont_shop');
        add_cont.css({
          'background': 'none',
          'margin-top': '-7px',
          'font-weight': 'bold'
        });
        add_cont.before('<a class="opera_checkout_position_fix button small success" href="' + href + '">CHECKOUT</a>');
      }
      if ($(".sort_filter").length > 0) {
        $(".sort_filter").removeClass("fixed");
        return $(".sort_filter").addClass("opera_footer_fix");
      }
    });
  }

}).call(this);
