@charset "UTF-8";
/* line 7, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.modal-open {
  overflow: hidden !important;
}

/* line 10, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#container {
  margin-top: 0.5em !important;
}

/* line 14, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal {
  overflow-y: scroll;
  background-color: #424242;
  top: 0 !important;
  position: fixed;
  width: 100%;
  height: 100%;
  border: 0;
}
/* line 22, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal #cod-confirmation-message {
  color: #f1f1f1;
}
/* line 25, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal #cod-confirmation-message {
  margin-top: 5%;
}
/* line 28, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .close-reveal-modal {
  font-size: 1.8em;
  padding: 1% 4% 3% 10%;
  right: 0.3em;
  color: white;
}
/* line 34, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form {
  font-size: 0.9em;
  text-align: center;
  padding-top: 10%;
}
/* line 38, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form .error-msg-for-otp {
  display: none;
  color: red;
}
/* line 42, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form .cod-otp-input {
  width: 50%;
  font-size: 1.2em;
  text-align: center;
  color: #f1f1f1;
  background-color: inherit;
  border-width: 0 0 3px;
  margin: 5% auto;
}
/* line 51, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form #otp-phone-change-form-and-content {
  display: none;
}
/* line 54, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form .cod-otp-modal-buttons {
  width: 50%;
}
/* line 57, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form #otp-phone-text-value {
  border: 0;
  padding: 0;
  background: 0;
  width: auto;
  color: white;
  font-size: 1.2em;
  font-weight: 800;
  clear: both;
  margin: 0;
  display: inline;
}
/* line 69, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form #phone-change-button {
  font-size: 0.9em;
  margin: 0;
}
/* line 73, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .cod-otp-verification-form .button-as-text[type=button] {
  background: 0;
  display: inline;
  padding: 0;
  color: #7EDFFF;
}
/* line 80, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .footer {
  margin-top: 40px;
  float: right;
}
/* line 84, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#codModal .footer #successButton {
  background: linear-gradient(to bottom, #0E9A7D, #267363);
  border-radius: 0.1rem;
  font-size: 1rem;
  padding: 18px 52px;
}

/* line 94, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal {
  -webkit-text-size-adjust: 100%;
  background: transparent;
  padding: 0px;
  border: 0px;
}
/* line 99, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal {
  background: #3e3d3d;
  width: 90%;
  margin-right: auto;
  margin-left: auto;
}
/* line 104, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body {
  padding: 15px;
}
/* line 107, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body #error-message .error-text {
  font-weight: bold;
  margin-bottom: 10px;
}
/* line 111, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body #error-message .paypal-success-reasons {
  margin-left: 10px;
}
/* line 115, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body .text-message {
  margin-bottom: 15px;
}
/* line 118, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body p {
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}
/* line 123, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-body .close-modal {
  float: right;
  line-height: 0px !important;
  font-size: 30px;
  color: #f1f1f1;
  margin-right: -10px;
}
/* line 131, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-footer {
  text-align: center;
}
/* line 133, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-footer .retry-payment {
  font-weight: bold;
}
/* line 136, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#paypalSuccessModal #paypalModal .modal-footer .cancel {
  color: white;
  background-color: transparent;
}

/* line 145, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link {
  display: none;
}
/* line 147, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel {
  background-color: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  padding-right: 8px;
}
/* line 151, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel h1 {
  color: #f1f1f1;
}
/* line 154, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel .close {
  float: right;
  border-radius: 50%;
  background-color: transparent;
  padding: 2px 6px;
  color: #7EDFFF;
  font-weight: bold;
}
/* line 162, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel p {
  padding-right: 2rem;
  text-align: justify;
  color: #f1f1f1;
}
/* line 167, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel .success {
  border-radius: 4px;
  background-color: #424242;
  padding: 12px;
  color: #f1f1f1;
  box-shadow: 0 0 0.5em #2d2d2d;
}
/* line 173, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel .success .fi-social-android {
  font-size: 30px;
  color: #a4c639;
  vertical-align: middle;
}
/* line 178, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#get_app_link .panel .success .fi-social-apple {
  font-size: 30px;
  color: #cdcdcd;
  vertical-align: middle;
}

/* line 188, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block label {
  color: inherit;
}
/* line 191, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion {
  padding-left: 0em;
  margin-left: 0em;
}
/* line 195, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion .accordion-navigation > a {
  background: inherit;
  color: inherit;
  padding: 0rem;
}
/* line 199, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion .accordion-navigation > a:before {
  content: '»';
  float: left;
  color: #7EDFFF;
  font-size: 14px;
  font-weight: 700;
  padding-right: 2px;
}
/* line 208, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion .accordion-navigation > .content {
  padding: 0rem;
}
/* line 210, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion .accordion-navigation > .content.active {
  background: inherit;
}
/* line 214, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .accordion .accordion-navigation.active > a:before {
  content: '«';
}
/* line 219, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .item_block {
  border-bottom: 0.1em solid #dcdcdc;
  margin-bottom: .5em;
  color: #f1f1f1;
  font-size: 14px;
}
/* line 224, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .item_block .truncate {
  color: #f1f1f1;
}
/* line 228, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block ul.addons-notes {
  font-size: 14px;
  padding-right: 0;
}
/* line 232, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block ul.addons-notes li.accordion-navigation a {
  font-size: 14px;
}
/* line 235, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block ul.addons-notes li.accordion-navigation .text-right {
  float: right;
}
/* line 240, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block ul.addons-notes li.accordion-navigation .content .row .left {
  margin-left: 5px;
}
/* line 247, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .order_total {
  padding: 0.2em 1em;
  margin-top: 1em;
}
/* line 251, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block #totals_block {
  color: #f1f1f1;
  font-size: 14px;
}
/* line 255, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block #totals_block .row .columns {
  padding-right: 0px;
}
/* line 260, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .grand_total, #create_orders_block .grand_total_with_cod {
  color: #f1f1f1;
  font-size: 14px;
  font-weight: 600;
}
/* line 265, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .shipping_address {
  margin-bottom: 2em;
  padding: 1em;
}
/* line 268, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .shipping_address .address-text {
  width: 100%;
  font-size: 14px;
  color: #f1f1f1;
}
/* line 273, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .shipping_address table {
  background: inherit;
  border: none;
  margin-bottom: 0em;
}
/* line 278, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .shipping_address table tr:nth-of-type(even) {
  background: inherit;
}
/* line 281, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#create_orders_block .shipping_address table tr td {
  color: inherit;
  padding: 0.1em;
}

/* line 291, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block .order-ack-title {
  color: #f1f1f1;
}
/* line 295, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block .panel_block .panel_content {
  padding: 1em;
}
/* line 299, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block hr {
  border: #4d4d4d solid;
  border-width: 0.1em 0 0;
}
/* line 303, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block table {
  width: 100%;
  border: 0em;
  background: inherit;
}
/* line 307, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block table tr {
  background: inherit;
}
/* line 309, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block table tr td, #order_show_block table tr th {
  color: inherit;
  line-height: 1em;
  padding: 0.3em;
}
/* line 316, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block .line_item_details {
  font-size: 14px;
  padding: 0.4em;
  opacity: 0.8;
  background-color: #4c4b4b;
  color: #f1f1f1;
  border: none;
}
/* line 324, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block .designer-order-block {
  background-color: #424242;
}
/* line 326, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_show_block .designer-order-block .panel_content {
  background-color: #424242;
}

/* line 332, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.notice_class {
  padding-left: 10px;
  color: orange;
  font-size: 14px;
  line-height: 15px;
}

/* line 339, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.quantity_total {
  margin-right: 0em;
}

/* line 343, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
table.customer_order {
  border: 1px solid #4d4d4d;
  border-collapse: collapse;
}

/* line 349, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
table.customer_order tr td {
  border: 1px solid #4d4d4d;
  color: white;
}
/* line 353, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
table.customer_order tr th {
  border: 1px solid #4d4d4d;
  color: white;
}
/* line 357, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
table.customer_order tr td.cost {
  text-align: right;
}

/* line 362, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
table.customer_order tr {
  background: #333333;
  color: white;
}

/* The Modal (background) */
/* line 369, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.modalForm {
  display: none;
  /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  padding-top: 80px;
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: black;
  /* Modal Content (Iframe) */
  /* The Close Button */
  /* 100% Image Width on Smaller Screens */
}
/* line 383, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.modalForm .modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}
@-webkit-keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
@keyframes zoom {
  from {
    transform: scale(0);
  }
  to {
    transform: scale(1);
  }
}
/* line 404, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.modalForm .close {
  position: absolute;
  top: 25px;
  right: 5px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  transition: 0.3s;
}
/* line 414, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.modalForm .close:hover,
.modalForm .close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}
@media only screen and (max-width: 700px) {
  /* line 422, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
  .modalForm .modal-content {
    width: 100%;
  }
}
@media only screen and (min-width: 1200px) {
  /* line 428, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
  .modalForm .modal-content {
    width: auto;
    height: auto;
  }
}

/* Order status on order show page */
/* line 436, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status {
  float: left;
  margin-left: 1%;
}
/* line 440, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div {
  position: relative;
  float: left;
  width: 16%;
}
/* line 445, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .circle_stage {
  display: inline-block;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  -o-border-radius: 50%;
  font-size: 12px;
  line-height: 50px;
  text-align: center;
  border: 2px solid;
}
/* line 456, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .circle_stage_domestic {
  display: inline-block;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  -o-border-radius: 50%;
  color: black;
  text-align: center;
  border: 2px solid white;
  font-size: 0.7em;
  vertical-align: middle;
}
/* line 468, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .square_stage {
  display: inline-block;
  font-size: 12px;
  line-height: 50px;
  text-align: center;
  border: 2px solid;
}
/* line 476, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .step_image {
  background: transparent;
  position: relative;
  top: -4px;
}
/* line 482, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .step_number {
  position: relative;
  top: -14px;
  color: black;
}
/* line 488, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #circle_div .vertical_line {
  width: 6px;
  height: 40px;
  margin-left: 52px;
  margin-bottom: -8px;
  display: inline-block;
  border: 1px solid;
}
/* line 498, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #notes_div {
  width: 82%;
  margin-left: 80px;
}
/* line 502, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#order_status #notes_div .status_note {
  margin-left: 18px;
  color: #f1f1f1;
  padding-top: 2px;
  font-size: small;
  margin-top: -3px;
  margin-bottom: 25px;
}

/* line 513, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.all_order_stages {
  display: inline-block;
}
/* line 515, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.all_order_stages .horizontal-line {
  width: 2em;
  height: 0.3em;
  background: yellowgreen;
  border: 1px solid white;
  border-radius: 1px;
  float: right;
}
/* line 523, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.all_order_stages .base {
  background: yellowgreen;
  display: inline-block;
  height: 1em;
  margin-left: 5%;
  position: relative;
  width: 2.5em;
  float: left;
  margin-right: 5%;
}
/* line 532, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.all_order_stages .base .pointer {
  border-left: 15px solid yellowgreen;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  content: "";
  height: 0;
  left: 40px;
  position: absolute;
  top: 0px;
  width: 0;
}
/* line 543, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.all_order_stages .base .uc_base {
  font-size: x-small;
  color: black;
  padding: 2px;
}

/* line 550, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.status_text {
  margin: 2% 10% 0% 10%;
  width: 80%;
  text-align: center;
  background-color: #E8C55B;
  border: 2px solid #D4A22E;
  color: black;
  border-radius: 2px;
  font-size: 0.8em;
}

/* line 561, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#return_panel .bordered_block {
  background-color: transparent;
  box-shadow: none;
}
/* line 564, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#return_panel .bordered_block .policy_button {
  background-color: #4c4b4b;
  box-shadow: 0 0 0.5em #2d2d2d;
  color: #f1f1f1;
}

/* Return Button */
/* line 572, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
body.modal-open {
  overflow: hidden;
  position: fixed;
}

/* line 577, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.reveal-modal {
  overflow-y: auto;
  height: 100%;
  position: fixed;
  top: 20px !important;
}
/* line 583, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.reveal-modal .close-reveal-modal {
  color: #4d4d4d;
}

/* line 588, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#bank_deposit_order_box {
  display: none;
  margin-right: 30px;
  background-color: #cecece;
  color: #000000;
  text-align: center;
  padding: 8px;
  position: fixed;
  z-index: 10;
  bottom: 1%;
  font-size: 16px;
  line-height: 21px;
  border: 2px solid #30924A;
}
/* line 601, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#bank_deposit_order_box .bank_deposit_order_text_close {
  float: right;
  text-align: right;
  position: relative;
  left: 25px;
  bottom: 25px;
  padding: 0px 3px;
  cursor: pointer;
  border-radius: 50%;
  background-color: #a9a9a9;
}
/* line 613, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
#bank_deposit_order_box .bank_deposit_order_text span {
  color: #30924A;
  text-transform: uppercase;
  font-weight: 500;
}

/* line 622, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.shipping_options .shipping_option {
  padding: 8px 8px 0px 8px;
}
/* line 624, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.shipping_options .shipping_option .shipping_radio_button {
  font-size: 14px;
}
/* line 627, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.shipping_options .shipping_option .shipping_option_price {
  float: right;
  font-size: 14px;
}
/* line 632, /home/<USER>/Mirraw/mirraw-mobile/app/assets/stylesheets/orders_black.scss */
.shipping_options .delivery_message {
  padding-left: 35px;
  font-size: 13px;
  color: #7EDFFF;
}
