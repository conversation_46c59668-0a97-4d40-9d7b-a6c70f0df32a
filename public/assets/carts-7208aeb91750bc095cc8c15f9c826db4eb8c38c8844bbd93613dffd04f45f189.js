(function(){$(function(){return $(".quantity_list").on("change",function(){var t,n;return t=this.id.split("_").pop(),n="/line_items/"+t,$.ajax({url:n,type:"PUT",dataType:"json",data:{line_items:{id:t,quantity:$(this).val()}},success:function(t){return window.location.href=t.redirect_url}})})}),$(function(){var t,n,i,e,r;return n=$("#secondary_action_buttons"),i=$("#main-section"),t=$("#container"),e=t.height(),r=function(){var t,r,o;return o=i.scrollTop(),r=i.height(),t=i.height()-n.height()/2,r>e-o?n.slideDown():n.slideUp()},$(window).ready(r).resize(r),i.scroll(r)})}).call(this);