/* line 2, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button {
  width: 100%;
}
/* line 5, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook {
  background: #3b5998;
  text-transform: capitalize;
}
/* line 9, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook span {
  background: #2d4373;
}
/* line 12, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.facebook span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1c4";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}
/* line 23, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter {
  background: #55acee;
  text-transform: capitalize;
}
/* line 27, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter span {
  background: #2795e9;
}
/* line 30, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.twitter span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1e4";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}
/* line 41, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google {
  background: #d50f25;
  text-transform: capitalize;
}
/* line 46, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google span {
  background: #a50c1d;
}
/* line 49, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.split.button.google span:after {
  border: none;
  font-family: "foundation-icons";
  content: "\f1ca";
  font-size: 2em;
  line-height: 0.03em;
  margin-left: -0.5em;
}

/* line 61, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.bordered_block label {
  color: white;
}
/* line 64, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
.bordered_block .login_btn {
  background-color: #0e9a7d;
}

/* line 68, /home/<USER>/project/mirraw-mobile/app/assets/stylesheets/sessions.css.scss */
hr {
  border: #4d4d4d solid;
  border-width: 0.1em 0 0;
  margin-top: 0.4em;
}
