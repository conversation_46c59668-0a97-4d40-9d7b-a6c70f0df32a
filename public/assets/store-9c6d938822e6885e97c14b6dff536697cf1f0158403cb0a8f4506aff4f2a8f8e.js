(function() {
  var EnableActiveTab, appendChips, appendFilters, checkForColourInUrl, getDesignsData, getMoreDesigns, keepSelectedPropertyActive, nextPageUrl, removeChips, searchTag, updateSelectedFilters;

  $(document).on('click', 'div#toggle-design-box', function() {
    if ($('#toggle-design-icon').hasClass('fi-list')) {
      $('#toggle-design-icon').removeClass('fi-list').addClass('fi-thumbnails');
      $('ul.store_page_design').removeClass('small-block-grid-2').addClass('small-block-grid-1');
      ga('send', 'event', 'DesignToggle', 'click', 'One-Design-View');
    } else {
      $('#toggle-design-icon').removeClass('fi-thumbnails').addClass('fi-list');
      $('ul.store_page_design').removeClass('small-block-grid-1').addClass('small-block-grid-2');
      ga('send', 'event', 'DesignToggle', 'click', 'Two-Design-View');
    }
  });

  nextPageUrl = function() {
    var nextPage, nextUrl;
    nextPage = $('.next').attr('id').split('_')[1];
    if (window.location.href.indexOf('?') === -1) {
      nextUrl = window.location.href + '?more_designs=true&page=' + nextPage;
    } else {
      nextUrl = window.location.href.replace(/&?((pid=)|(page=))[^\&]*/, '') + '&more_designs=true&page=' + nextPage;
    }
    return nextUrl;
  };

  $(document).on('click', 'div#load-more-designs-btn', function() {
    var nextUrl;
    nextUrl = nextPageUrl();
    getDesignsData(nextUrl);
  });

  getMoreDesigns = function() {
    $(window).on('scroll', function() {
      var buttonTop, nextUrl;
      static_header_scroll();
      if ($('.navigate_page').length > 0) {
        buttonTop = $('.navigate_page').position().top;
      }
      if ($('.next').length > 0 && ($('.catalog_product').length < 200) && (buttonTop > $(window).scrollTop())) {
        nextUrl = nextPageUrl();
        if (nextUrl && (buttonTop - $(window).scrollTop() < 1100)) {
          getDesignsData(nextUrl);
        }
      } else if ($('.next').length > 0 && $('.catalog_product').length >= 200) {
        $('.next, .previous').hide();
        $('#load-more-designs-btn').css('display', 'inline-block');
      }
      if ($(this).scrollTop() > 400) {
        $('#back-top').fadeIn();
      } else {
        $('#back-top').fadeOut();
      }
      getFooterPng();
    });
  };

  getDesignsData = function(nextUrl) {
    $.ajax({
      type: 'GET',
      url: nextUrl,
      dataType: 'script',
      beforeSend: function() {
        $('#load-more-designs-btn, .next, .previous').hide();
        $('#more-designs-loader').show();
        static_header_scroll();
        $(window).off('scroll');
      },
      complete: function() {
        if ($('.catalog_product').length >= 200) {
          $('#load-more-designs-btn').css('display', 'inline-block');
        } else {
          $('.next, .previous').show();
        }
        $('#more-designs-loader').hide();
        gaImpressions();
        gaPageview(false);
        getMoreDesigns();
        applyCTimer();
        lazyLoad();
        window.enableWishList();
      },
      success: function(response) {
        response;
      },
      error: function(xhr, status, error) {}
    });
  };

  $(function() {
    $(document).on('click', 'div#back-top', function() {
      static_header_scroll();
      $('body,html').animate({
        scrollTop: 0
      }, 500);
    });
    getMoreDesigns();
  });

  afterWindowOrTrubolinksLoad(function() {
    $(window).off('scroll');
    if ($('.pages_home').length) {
      return getMoreItems();
    } else {
      return getMoreDesigns();
    }
  });

  $(document).on('change', '.select_box', function() {
    var selectedValue, url;
    selectedValue = $('.form_input_select option:selected').val();
    if (window.location.href.indexOf('?') === -1) {
      url = window.location.href + '?sort=' + selectedValue;
    } else {
      url = window.location.href.replace(/&?sort=[^\&]+/, '') + '&sort=' + selectedValue;
    }
    if (Turbolinks.supported) {
      return Turbolinks.visit(url);
    } else {
      return window.location.assign(url);
    }
  });

  $(document).on('click', '#filter-button', function() {
    $('body').addClass('modal-open');
    $('.reveal-modal-bg').css('display', 'block');
  });

  $(document).foundation('reveal', {
    animation: false
  });

  $(document).on('click', '.tab-filter-fix', function(event) {
    var url;
    event.preventDefault();
    url = $(this).attr('href');
    $('nav > .tab-filter-fix').removeClass('active-tab');
    $(this).addClass('active-tab');
    $('.tabs-content > .content').removeClass('active');
    $(url).addClass('active');
  });

  searchTag = function(url) {
    if (window.location.href.indexOf('/tags?q=') !== -1) {
      url += '?q=' + $('#tag_list').val();
    } else if (window.location.href.indexOf('/search?utf8') !== -1) {
      url += '?' + $('.search_margin form').serialize();
    }
    return url;
  };

  checkForColourInUrl = function(url) {
    var index;
    if ((index = url.indexOf('/colour')) !== -1) {
      url = url.substr(0, index);
    }
    return url;
  };

  appendFilters = function(url) {
    var category_child_ids, category_name, colour_ids, colour_names, designer_ids, gender, i, imp_properties, len, link, max_odr, option_type_value_ids, preference, price_discount, property, property_value_ids, selectSort;
    category_child_ids = [];
    designer_ids = [];
    colour_names = [];
    colour_ids = [];
    gender = [];
    imp_properties = [];
    property_value_ids = [];
    option_type_value_ids = [];
    price_discount = '';
    category_name = $('#facet-data').data('category-name');
    link = window.location.href.slice(window.location.href.indexOf('?') + 1);
    $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').each(function() {
      var is_duplicate, name, priority, property;
      switch ($(this).data('key')) {
        case 'category_child_ids[]':
          category_child_ids.push($(this).val());
          break;
        case 'designer_ids[]':
          designer_ids.push($(this).val());
          break;
        case 'property_value_ids[]':
          if (category_name === void 0) {
            if ($(this).attr('class').match(/color-append/g)) {
              colour_names.push($(this).attr('placeholder'));
              colour_ids.push($(this).val());
            } else {
              property_value_ids.push($(this).val());
              break;
            }
          } else {
            priority = $(this).attr('data-priority');
            name = $(this).data('name');
            if (priority > 0) {
              is_duplicate = imp_properties.some(function(ele) {
                return ele.name === name;
              });
              if (!is_duplicate) {
                property = {
                  name: name,
                  priority: priority
                };
                imp_properties.push(property);
              }
            } else {
              property_value_ids.push($(this).val());
            }
          }
          break;
        case 'option_type_value_ids[]':
          option_type_value_ids.push($(this).val());
          break;
        case 'gender':
          gender.push($(this).val());
      }
    });
    $('.hidden-facet-data').each(function() {
      var is_duplicate, name, priority, property;
      priority = $(this).attr('data-priority');
      name = $(this).data('name');
      if (priority > 0) {
        is_duplicate = imp_properties.some(function(ele) {
          return ele.name === name;
        });
        if (!is_duplicate) {
          property = {
            name: $(this).data('name'),
            priority: $(this).data('priority')
          };
          return imp_properties.push(property);
        }
      }
    });
    $('input:hidden.range_filter_input').each(function() {
      if ($(this).val() !== '') {
        price_discount += '&' + $(this).attr('id') + '=' + $(this).val();
      }
    });
    url = searchTag(url);
    if (category_name === void 0) {
      if (colour_names.length !== 0) {
        if (url.indexOf('?') === -1) {
          url += '/colour-';
        } else {
          url += '&colour-';
          property_value_ids = property_value_ids.concat(colour_ids);
        }
      }
      url += colour_names.join('--');
    } else {
      imp_properties.sort(function(a, b) {
        var value;
        if (a['priority'] > b['priority']) {
          value = 1;
        } else if (a['priority'] === b['priority']) {
          if (a['name'] >= b['name']) {
            value = 1;
          } else {
            value = -1;
          }
        } else {
          value = -1;
        }
        return value;
      });
      if (url[url.length - 1] !== '/' && imp_properties.length > 0) {
        url += '/';
      }
      if (imp_properties.length > 0) {
        for (i = 0, len = imp_properties.length; i < len; i++) {
          property = imp_properties[i];
          url += property['name'] + '_';
        }
        url = url.slice(0, +(url.length - 2) + 1 || 9e9) + '-';
        url += category_name;
      }
    }
    if (price_discount !== '') {
      url += price_discount;
    }
    if (category_child_ids.length !== 0) {
      url += '&category_child_ids=' + category_child_ids.join(',');
    }
    if (designer_ids.length !== 0) {
      url += '&designer_ids=' + designer_ids.join(',');
    }
    if (property_value_ids.length > 0) {
      url += '&property_value_ids=' + property_value_ids;
    }
    if (option_type_value_ids.length !== 0) {
      url += '&option_type_value_ids=' + option_type_value_ids.join(',');
    }
    if (gender.length !== 0) {
      url += '&gender=' + gender;
    }
    if (window.location.href.indexOf('sort=') !== -1) {
      selectSort = $('.form_input_select option:selected').val();
      url += '&sort=' + selectSort;
    }
    if ((max_odr = $('#max-odr-parameter').data('max-odr')) !== void 0) {
      url += '&max_odr=' + max_odr;
    }
    if ((preference = getUrlParams(link).preference) !== void 0) {
      url += "&preference=" + preference;
    }
    return url;
  };

  appendChips = function() {
    $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').each(function() {
      var chip, id;
      id = $(this).attr('id');
      chip = '<div class=\'chip\'>' + $(this).data('chip') + '<span class=\'closebtn\' onclick=\'removeChips(this);\' data-remove=\'' + id + '\'>&times;</span></div>';
      $('#designable_details > .chips_maker').append(chip);
    });
  };

  keepSelectedPropertyActive = function() {
    var selected_property, selected_tab;
    selected_property = window.sessionStorage.getItem('last-filter');
    if (!($('#' + selected_property).length > 0)) {
      selected_property = 'price';
    }
    selected_tab = $('#' + selected_property).attr('href');
    $('nav > .tab-filter-fix').removeClass('active-tab');
    $('#' + selected_property).addClass('active-tab');
    $('.tabs-content > .content').removeClass('active').addClass('content');
    return $(selected_tab).addClass('content active');
  };

  updateSelectedFilters = function() {
    var correct_url, url, valueString;
    $('.on-off-radiobox input:radio:checked').each(function() {
      $('#' + $(this).data('minInput')).val($(this).data('min'));
      $('#' + $(this).data('maxInput')).val($(this).data('max'));
      $('#' + $(this).data('gender')).val($(this).data('gender'));
    });
    url = '';
    valueString = appendFilters(url);
    if (valueString !== '' && valueString.indexOf('?') === -1) {
      valueString = valueString.replace('&', '?');
    }
    correct_url = checkForColourInUrl($('#facet-data').attr('data-url'));
    correct_url += valueString;
    $.ajax({
      type: 'GET',
      url: correct_url,
      dataType: 'script',
      beforeSend: function() {
        $('#designable_details').css('opacity', '0.5');
        $('#loader').show();
      },
      complete: function() {
        $('#loader').hide();
        $('#designable_details').css('opacity', '1');
        appendChips();
      },
      success: function(response) {
        response;
        keepSelectedPropertyActive();
      },
      error: function(xhr, status, error) {
        alert('oops! Something went wrong');
      }
    });
  };

  $(document).on('click', '.facet-link, .facet-gen', function(e) {
    var current_value, selected_button, selected_property;
    e.preventDefault();
    selected_button = $(this).children()[0].firstElementChild;
    selected_property = $(selected_button).data('property');
    current_value = $(selected_button).prop('checked');
    $(selected_button).prop('checked', !current_value);
    window.sessionStorage.setItem('last-filter', selected_property.split(' ').join('-'));
    return updateSelectedFilters();
  });

  removeChips = function(id) {
    id = $(id).data('remove');
    $('#' + id).prop('checked', false);
    updateSelectedFilters();
  };

  $(document).on('click', '#filter-apply-btn', function() {
    var correct_url, url, valueString;
    url = '';
    valueString = appendFilters(url);
    if (valueString !== '' && valueString.indexOf('?') === -1) {
      valueString = valueString.replace('&', '?');
    }
    correct_url = checkForColourInUrl($('#facet-data').attr('data-url'));
    correct_url += valueString;
    if (Turbolinks.supported) {
      return Turbolinks.visit(correct_url);
    } else {
      return window.location.assign(correct_url);
    }
  });

  $(document).on('click', '#filter-clear-btn', function() {
    var url;
    $('.on-off-radiobox input:radio:checked, .on-off-checkbox input:checkbox:checked').each(function() {
      return $(this).prop('checked', false);
    });
    $('#max_price').removeAttr('value');
    $('#min_price').removeAttr('value');
    $('#max_discount').removeAttr('value');
    $('#max_discount').removeAttr('value');
    $('#gender').removeAttr('value');
    $('body').removeClass('modal-open');
    if (window.location.search !== '') {
      url = $('#facet-data').attr('data-url');
      url = searchTag(checkForColourInUrl(url));
      if (Turbolinks.supported) {
        return Turbolinks.visit(url);
      } else {
        return window.location.assign(url);
      }
    } else {
      updateSelectedFilters();
    }
  });

  $(document).on('click', '#filter-modal-close', function() {
    $('body').removeClass('modal-open');
    $('#filterModal').foundation('reveal', 'close');
    $('.reveal-modal-bg').css('display', 'none');
  });

  EnableActiveTab = function() {
    var active_tab;
    active_tab = true;
    appendChips();
    $('.tab-filter-fix').each(function() {
      if ($(this).hasClass('active-tab')) {
        active_tab = false;
      }
    });
    if (active_tab) {
      $('.tab-filter-fix:first').addClass('active-tab');
      $('#tab_1').addClass('active');
    }
  };

  EnableActiveTab();

  $(document).on('turbolinks:render', function() {
    EnableActiveTab();
    return keepSelectedPropertyActive();
  });

  $(function() {
    var nextPage, nextUrl, prevPage, prevUrl;
    if ($('.store_page_block .previous').length) {
      prevPage = $('.previous').attr('id').split('_')[1];
      prevUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + prevPage;
      $('.previous').attr('href', prevUrl);
    }
    if ($('.store_page_block .next').length) {
      nextPage = $('.next').attr('id').split('_')[1];
      if (window.location.href.indexOf('?') === -1) {
        nextUrl = window.location.href + '?page=' + nextPage;
      } else {
        nextUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + nextPage;
      }
      $('.next').attr('href', nextUrl);
    }
  });

  $(document).on('click', '.flash-deal-header .tab-fix', function() {
    var a;
    $(this).addClass('fd-active-tab').siblings().removeClass('fd-active-tab');
    if ($(this).hasClass('fd-ongoing-tab')) {
      $('.ongoing-fd-timer').fadeIn();
    } else {
      $('.ongoing-fd-timer').fadeOut();
    }
    a = parseInt($(this).attr('tab_id'));
    $('[fd_id=' + a + ']').fadeIn().siblings().fadeOut();
    if (isNaN(a)) {
      return $('.fd_active').fadeIn().siblings().fadeOut();
    }
  });

  $('#FAQs').foundation({
    accordion: {
      multi_expand: false
    }
  });

}).call(this);
