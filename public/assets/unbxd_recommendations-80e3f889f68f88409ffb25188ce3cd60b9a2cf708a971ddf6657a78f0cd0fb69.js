(function(){var e,n,o,t,a,d;for(d="undefined"!=typeof exports&&null!==exports?exports:this,d.loadRecommendation=function(e){var n;n=e.symbol||"INR",$.ajax({url:"/unbxd_recommendations/"+e.unbxdContainer,dataType:"script",cache:!0,data:{url_params:{currency_code:n,uid:e.user,pid:$("#unbxd-data-div").data("pid"),category:$("#unbxd-data-div").data("category")||e.category,brand:$("#unbxd-data-div").data("brand"),product_count:e.productCount,box_name:e.boxName}},success:function(n){return $("#box-"+e.unbxdContainer).fadeIn("slow"),gaImpressions(),ga("send","event","UX","load","Unbxd-Widget-Loaded"),$("#no-"+e.boxName.toLowerCase()).length>0?setTimeout(function(){return $("#"+e.boxName.toLowerCase()).fadeOut("slow")},3e3):void 0},error:function(){return $("div[data-unbxd-container ='"+e.unbxdContainer+"']").replaceWith("<div class='container recommended-design-box' id='"+e.boxName+"'><div class='title-block'><h1>"+e.boxName.toLowerCase().replace(/_/g," ")+("</h1></div><div class='row'><div class='no-design-box' id='no-"+e.boxName+"'>Sorry Not Able to Find Recommendations</div></div></div>")),setTimeout(function(){return $("#"+e.boxName).fadeOut("slow")},3e3)}})},(!d.bindView||d.bindView instanceof Array)&&(a=d.bindView||[]),o=function(e){var n,o,t,a;return t=e.offset().top,n=e.offset().top+e.outerHeight(),o=$(window).scrollTop()+window.innerHeight,a=$(window).scrollTop(),o>t&&n>a},d.bindView={push:function(e){var n;return n=$('div[data-unbxd-container="'+e+'"]'),n.length>0?o(n)?loadRecommendation(n.data()):$(document).one("scroll",function(){return loadRecommendation(n.data())}):void 0}},n=0,t=a.length;t>n;n++)e=a[n],bindView.push(e);a=[],$(function(){})}).call(this);