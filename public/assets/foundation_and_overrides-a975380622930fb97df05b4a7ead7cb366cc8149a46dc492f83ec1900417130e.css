@import url("https://fonts.googleapis.com/css?family=Roboto");
/* line 364, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-version {
  font-family: "/5.5.2/";
}

/* line 368, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-small {
  font-family: "/only screen/";
  width: 0;
}

/* line 373, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-small-only {
  font-family: "/only screen and (max-width: 29.9375em)/";
  width: 0;
}

/* line 378, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-medium {
  font-family: "/only screen and (min-width:30em)/";
  width: 30em;
}

/* line 383, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-medium-only {
  font-family: "/only screen and (min-width:30em) and (max-width:64em)/";
  width: 30em;
}

/* line 388, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-large {
  font-family: "/only screen and (min-width:64.0625em)/";
  width: 64.0625em;
}

/* line 393, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-large-only {
  font-family: "/only screen and (min-width:64.0625em) and (max-width:90em)/";
  width: 64.0625em;
}

/* line 398, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-xlarge {
  font-family: "/only screen and (min-width:90.0625em)/";
  width: 90.0625em;
}

/* line 403, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-xlarge-only {
  font-family: "/only screen and (min-width:90.0625em) and (max-width:120em)/";
  width: 90.0625em;
}

/* line 408, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-mq-xxlarge {
  font-family: "/only screen and (min-width:120.0625em)/";
  width: 120.0625em;
}

/* line 413, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
meta.foundation-data-attribute-namespace {
  font-family: false;
}

/* line 422, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
html, body {
  height: 100%;
}

/* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
html {
  box-sizing: border-box;
}

/* line 428, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
*,
*:before,
*:after {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}

/* line 434, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
html,
body {
  font-size: 100%;
}

/* line 438, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
body {
  background: #efeeee;
  color: #383737;
  cursor: auto;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  position: relative;
}

/* line 451, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
a:hover {
  cursor: pointer;
}

/* line 454, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
img {
  max-width: 100%;
  height: auto;
}

/* line 456, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
img {
  -ms-interpolation-mode: bicubic;
}

/* line 461, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
#map_canvas img,
#map_canvas embed,
#map_canvas object,
.map_canvas img,
.map_canvas embed,
.map_canvas object,
.mqa-display img,
.mqa-display embed,
.mqa-display object {
  max-width: none !important;
}

/* line 468, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.left {
  float: left !important;
}

/* line 469, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.right {
  float: right !important;
}

/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.clearfix:before, .clearfix:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.clearfix:after {
  clear: both;
}

/* line 473, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.hide {
  display: none;
}

/* line 478, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.invisible {
  visibility: hidden;
}

/* line 484, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* line 487, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
img {
  display: inline-block;
  vertical-align: middle;
}

/* line 497, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
textarea {
  height: auto;
  min-height: 50px;
}

/* line 500, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
select {
  width: 100%;
}

/* line 228, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.row {
  margin: 0 auto;
  max-width: 62.5rem;
  width: 100%;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row:before, .row:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row:after {
  clear: both;
}
/* line 232, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.row.collapse > .column,
.row.collapse > .columns {
  padding-left: 0;
  padding-right: 0;
}
/* line 235, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.row.collapse .row {
  margin-left: 0;
  margin-right: 0;
}
/* line 238, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.row .row {
  margin: 0 -0.9375rem;
  max-width: none;
  width: auto;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row .row:before, .row .row:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row .row:after {
  clear: both;
}
/* line 239, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.row .row.collapse {
  margin: 0;
  max-width: none;
  width: auto;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row .row.collapse:before, .row .row.collapse:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.row .row.collapse:after {
  clear: both;
}

/* line 243, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.column,
.columns {
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  width: 100%;
  float: left;
}

/* line 248, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.column + .column:last-child,
.columns + .column:last-child, .column +
.columns:last-child,
.columns +
.columns:last-child {
  float: right;
}
/* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
.column + .column.end,
.columns + .column.end, .column +
.columns.end,
.columns +
.columns.end {
  float: left;
}

@media only screen {
  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-0 {
    position: relative;
    left: 0;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-0 {
    position: relative;
    right: 0;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-1 {
    position: relative;
    left: 8.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-1 {
    position: relative;
    right: 8.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-2 {
    position: relative;
    left: 16.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-2 {
    position: relative;
    right: 16.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-3 {
    position: relative;
    left: 25%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-3 {
    position: relative;
    right: 25%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-4 {
    position: relative;
    left: 33.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-4 {
    position: relative;
    right: 33.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-5 {
    position: relative;
    left: 41.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-5 {
    position: relative;
    right: 41.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-6 {
    position: relative;
    left: 50%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-6 {
    position: relative;
    right: 50%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-7 {
    position: relative;
    left: 58.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-7 {
    position: relative;
    right: 58.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-8 {
    position: relative;
    left: 66.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-8 {
    position: relative;
    right: 66.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-9 {
    position: relative;
    left: 75%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-9 {
    position: relative;
    right: 75%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-10 {
    position: relative;
    left: 83.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-10 {
    position: relative;
    right: 83.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-push-11 {
    position: relative;
    left: 91.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-pull-11 {
    position: relative;
    right: 91.66667%;
    left: auto;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column,
  .columns {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-1 {
    width: 8.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-2 {
    width: 16.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-3 {
    width: 25%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-4 {
    width: 33.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-5 {
    width: 41.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-6 {
    width: 50%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-7 {
    width: 58.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-8 {
    width: 66.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-9 {
    width: 75%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-10 {
    width: 83.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-11 {
    width: 91.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-12 {
    width: 100%;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-0 {
    margin-left: 0 !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-1 {
    margin-left: 8.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-2 {
    margin-left: 16.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-3 {
    margin-left: 25% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-4 {
    margin-left: 33.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-5 {
    margin-left: 41.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-6 {
    margin-left: 50% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-7 {
    margin-left: 58.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-8 {
    margin-left: 66.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-9 {
    margin-left: 75% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-10 {
    margin-left: 83.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-offset-11 {
    margin-left: 91.66667% !important;
  }

  /* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .small-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
  }

  /* line 183, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.small-centered,
  .columns.small-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  /* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.small-uncentered,
  .columns.small-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  /* line 194, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.small-centered:last-child,
  .columns.small-centered:last-child {
    float: none;
  }

  /* line 200, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.small-uncentered:last-child,
  .columns.small-uncentered:last-child {
    float: left;
  }

  /* line 205, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.small-uncentered.opposite,
  .columns.small-uncentered.opposite {
    float: right;
  }

  /* line 212, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.small-collapse > .column,
  .row.small-collapse > .columns {
    padding-left: 0;
    padding-right: 0;
  }
  /* line 215, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.small-collapse .row {
    margin-left: 0;
    margin-right: 0;
  }
  /* line 218, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.small-uncollapse > .column,
  .row.small-uncollapse > .columns {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }
}
@media only screen and (min-width: 30em) {
  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-0 {
    position: relative;
    left: 0;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-0 {
    position: relative;
    right: 0;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-1 {
    position: relative;
    left: 8.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-1 {
    position: relative;
    right: 8.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-2 {
    position: relative;
    left: 16.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-2 {
    position: relative;
    right: 16.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-3 {
    position: relative;
    left: 25%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-3 {
    position: relative;
    right: 25%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-4 {
    position: relative;
    left: 33.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-4 {
    position: relative;
    right: 33.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-5 {
    position: relative;
    left: 41.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-5 {
    position: relative;
    right: 41.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-6 {
    position: relative;
    left: 50%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-6 {
    position: relative;
    right: 50%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-7 {
    position: relative;
    left: 58.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-7 {
    position: relative;
    right: 58.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-8 {
    position: relative;
    left: 66.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-8 {
    position: relative;
    right: 66.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-9 {
    position: relative;
    left: 75%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-9 {
    position: relative;
    right: 75%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-10 {
    position: relative;
    left: 83.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-10 {
    position: relative;
    right: 83.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-push-11 {
    position: relative;
    left: 91.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-pull-11 {
    position: relative;
    right: 91.66667%;
    left: auto;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column,
  .columns {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-1 {
    width: 8.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-2 {
    width: 16.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-3 {
    width: 25%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-4 {
    width: 33.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-5 {
    width: 41.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-6 {
    width: 50%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-7 {
    width: 58.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-8 {
    width: 66.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-9 {
    width: 75%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-10 {
    width: 83.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-11 {
    width: 91.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-12 {
    width: 100%;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-0 {
    margin-left: 0 !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-1 {
    margin-left: 8.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-2 {
    margin-left: 16.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-3 {
    margin-left: 25% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-4 {
    margin-left: 33.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-5 {
    margin-left: 41.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-6 {
    margin-left: 50% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-7 {
    margin-left: 58.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-8 {
    margin-left: 66.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-9 {
    margin-left: 75% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-10 {
    margin-left: 83.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-offset-11 {
    margin-left: 91.66667% !important;
  }

  /* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .medium-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
  }

  /* line 183, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.medium-centered,
  .columns.medium-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  /* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.medium-uncentered,
  .columns.medium-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  /* line 194, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.medium-centered:last-child,
  .columns.medium-centered:last-child {
    float: none;
  }

  /* line 200, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.medium-uncentered:last-child,
  .columns.medium-uncentered:last-child {
    float: left;
  }

  /* line 205, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.medium-uncentered.opposite,
  .columns.medium-uncentered.opposite {
    float: right;
  }

  /* line 212, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.medium-collapse > .column,
  .row.medium-collapse > .columns {
    padding-left: 0;
    padding-right: 0;
  }
  /* line 215, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.medium-collapse .row {
    margin-left: 0;
    margin-right: 0;
  }
  /* line 218, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.medium-uncollapse > .column,
  .row.medium-uncollapse > .columns {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-0 {
    position: relative;
    left: 0;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-0 {
    position: relative;
    right: 0;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-1 {
    position: relative;
    left: 8.33333%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-1 {
    position: relative;
    right: 8.33333%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-2 {
    position: relative;
    left: 16.66667%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-2 {
    position: relative;
    right: 16.66667%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-3 {
    position: relative;
    left: 25%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-3 {
    position: relative;
    right: 25%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-4 {
    position: relative;
    left: 33.33333%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-4 {
    position: relative;
    right: 33.33333%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-5 {
    position: relative;
    left: 41.66667%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-5 {
    position: relative;
    right: 41.66667%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-6 {
    position: relative;
    left: 50%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-6 {
    position: relative;
    right: 50%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-7 {
    position: relative;
    left: 58.33333%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-7 {
    position: relative;
    right: 58.33333%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-8 {
    position: relative;
    left: 66.66667%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-8 {
    position: relative;
    right: 66.66667%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-9 {
    position: relative;
    left: 75%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-9 {
    position: relative;
    right: 75%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-10 {
    position: relative;
    left: 83.33333%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-10 {
    position: relative;
    right: 83.33333%;
    left: auto;
  }

  /* line 264, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-11 {
    position: relative;
    left: 91.66667%;
    right: auto;
  }

  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-11 {
    position: relative;
    right: 91.66667%;
    left: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-0 {
    position: relative;
    left: 0;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-0 {
    position: relative;
    right: 0;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-1 {
    position: relative;
    left: 8.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-1 {
    position: relative;
    right: 8.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-2 {
    position: relative;
    left: 16.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-2 {
    position: relative;
    right: 16.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-3 {
    position: relative;
    left: 25%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-3 {
    position: relative;
    right: 25%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-4 {
    position: relative;
    left: 33.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-4 {
    position: relative;
    right: 33.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-5 {
    position: relative;
    left: 41.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-5 {
    position: relative;
    right: 41.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-6 {
    position: relative;
    left: 50%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-6 {
    position: relative;
    right: 50%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-7 {
    position: relative;
    left: 58.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-7 {
    position: relative;
    right: 58.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-8 {
    position: relative;
    left: 66.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-8 {
    position: relative;
    right: 66.66667%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-9 {
    position: relative;
    left: 75%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-9 {
    position: relative;
    right: 75%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-10 {
    position: relative;
    left: 83.33333%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-10 {
    position: relative;
    right: 83.33333%;
    left: auto;
  }

  /* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-push-11 {
    position: relative;
    left: 91.66667%;
    right: auto;
  }

  /* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-pull-11 {
    position: relative;
    right: 91.66667%;
    left: auto;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column,
  .columns {
    position: relative;
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-1 {
    width: 8.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-2 {
    width: 16.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-3 {
    width: 25%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-4 {
    width: 33.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-5 {
    width: 41.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-6 {
    width: 50%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-7 {
    width: 58.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-8 {
    width: 66.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-9 {
    width: 75%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-10 {
    width: 83.33333%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-11 {
    width: 91.66667%;
  }

  /* line 168, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-12 {
    width: 100%;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-0 {
    margin-left: 0 !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-1 {
    margin-left: 8.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-2 {
    margin-left: 16.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-3 {
    margin-left: 25% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-4 {
    margin-left: 33.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-5 {
    margin-left: 41.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-6 {
    margin-left: 50% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-7 {
    margin-left: 58.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-8 {
    margin-left: 66.66667% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-9 {
    margin-left: 75% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-10 {
    margin-left: 83.33333% !important;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-offset-11 {
    margin-left: 91.66667% !important;
  }

  /* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .large-reset-order {
    float: left;
    left: auto;
    margin-left: 0;
    margin-right: 0;
    right: auto;
  }

  /* line 183, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.large-centered,
  .columns.large-centered {
    margin-left: auto;
    margin-right: auto;
    float: none;
  }

  /* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.large-uncentered,
  .columns.large-uncentered {
    float: left;
    margin-left: 0;
    margin-right: 0;
  }

  /* line 194, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.large-centered:last-child,
  .columns.large-centered:last-child {
    float: none;
  }

  /* line 200, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.large-uncentered:last-child,
  .columns.large-uncentered:last-child {
    float: left;
  }

  /* line 205, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .column.large-uncentered.opposite,
  .columns.large-uncentered.opposite {
    float: right;
  }

  /* line 212, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.large-collapse > .column,
  .row.large-collapse > .columns {
    padding-left: 0;
    padding-right: 0;
  }
  /* line 215, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.large-collapse .row {
    margin-left: 0;
    margin-right: 0;
  }
  /* line 218, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .row.large-uncollapse > .column,
  .row.large-uncollapse > .columns {
    padding-left: 0.9375rem;
    padding-right: 0.9375rem;
    float: left;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-0 {
    position: relative;
    left: 0;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-0 {
    position: relative;
    right: 0;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-1 {
    position: relative;
    left: 8.33333%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-1 {
    position: relative;
    right: 8.33333%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-2 {
    position: relative;
    left: 16.66667%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-2 {
    position: relative;
    right: 16.66667%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-3 {
    position: relative;
    left: 25%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-3 {
    position: relative;
    right: 25%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-4 {
    position: relative;
    left: 33.33333%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-4 {
    position: relative;
    right: 33.33333%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-5 {
    position: relative;
    left: 41.66667%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-5 {
    position: relative;
    right: 41.66667%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-6 {
    position: relative;
    left: 50%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-6 {
    position: relative;
    right: 50%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-7 {
    position: relative;
    left: 58.33333%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-7 {
    position: relative;
    right: 58.33333%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-8 {
    position: relative;
    left: 66.66667%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-8 {
    position: relative;
    right: 66.66667%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-9 {
    position: relative;
    left: 75%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-9 {
    position: relative;
    right: 75%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-10 {
    position: relative;
    left: 83.33333%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-10 {
    position: relative;
    right: 83.33333%;
    left: auto;
  }

  /* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .push-11 {
    position: relative;
    left: 91.66667%;
    right: auto;
  }

  /* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_grid.scss */
  .pull-11 {
    position: relative;
    right: 91.66667%;
    left: auto;
  }
}
/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion {
  margin-bottom: 0;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.accordion:before, .accordion:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.accordion:after {
  clear: both;
}
/* line 132, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation, .accordion dd {
  display: block;
  margin-bottom: 0 !important;
}
/* line 135, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation.active > a, .accordion dd.active > a {
  background: #e8e8e8;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation > a, .accordion dd > a {
  background: #EFEFEF;
  color: #222222;
  display: block;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem;
}
/* line 143, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation > a:hover, .accordion dd > a:hover {
  background: #e3e3e3;
}
/* line 146, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation > .content, .accordion dd > .content {
  display: none;
  padding: 0.9375rem;
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_accordion.scss */
.accordion .accordion-navigation > .content.active, .accordion dd > .content.active {
  background: #FFFFFF;
  display: block;
}

/* line 112, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.8125rem;
  font-weight: normal;
  margin-bottom: 1.25rem;
  padding: 0.875rem 1.5rem 0.875rem 0.875rem;
  position: relative;
  transition: opacity 300ms ease-out;
  background-color: #008CBA;
  border-color: #0078a0;
  color: #FFFFFF;
}
/* line 115, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box .close {
  right: 0.25rem;
  background: inherit;
  color: #333333;
  font-size: 1.375rem;
  line-height: .9;
  margin-top: -0.6875rem;
  opacity: 0.3;
  padding: 0 6px 4px;
  position: absolute;
  top: 50%;
}
/* line 96, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box .close:hover, .alert-box .close:focus {
  opacity: 0.5;
}
/* line 117, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.radius {
  border-radius: 3px;
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.round {
  border-radius: 1000px;
}
/* line 120, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.success {
  background-color: #43AC6A;
  border-color: #3a945b;
  color: #FFFFFF;
}
/* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.alert {
  background-color: #f04124;
  border-color: #de2d0f;
  color: #FFFFFF;
}
/* line 122, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.secondary {
  background-color: #e7e7e7;
  border-color: #c7c7c7;
  color: #4f4f4f;
}
/* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.warning {
  background-color: #f08a24;
  border-color: #de770f;
  color: #FFFFFF;
}
/* line 124, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.info {
  background-color: #a0d3e8;
  border-color: #74bfdd;
  color: #4f4f4f;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_alert-boxes.scss */
.alert-box.alert-close {
  opacity: 0;
}

/* line 107, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
[class*="block-grid-"] {
  display: block;
  padding: 0;
  margin: 0 -0.625rem;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
[class*="block-grid-"]:before, [class*="block-grid-"]:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
[class*="block-grid-"]:after {
  clear: both;
}
/* line 51, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
[class*="block-grid-"] > li {
  display: block;
  float: left;
  height: auto;
  padding: 0 0.625rem 1.25rem;
}

@media only screen {
  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-1 > li {
    list-style: none;
    width: 100%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-1 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-1 > li:nth-of-type(1n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-2 > li {
    list-style: none;
    width: 50%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-2 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-5 > li {
    list-style: none;
    width: 20%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-5 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-6 > li {
    list-style: none;
    width: 16.66667%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-6 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-7 > li {
    list-style: none;
    width: 14.28571%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-7 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-8 > li {
    list-style: none;
    width: 12.5%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-8 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-9 > li {
    list-style: none;
    width: 11.11111%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-9 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-10 > li {
    list-style: none;
    width: 10%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-10 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-11 > li {
    list-style: none;
    width: 9.09091%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-11 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-12 > li {
    list-style: none;
    width: 8.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-12 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .small-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
  }
}
@media only screen and (min-width: 30em) {
  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-1 > li {
    list-style: none;
    width: 100%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-1 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-1 > li:nth-of-type(1n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-2 > li {
    list-style: none;
    width: 50%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-2 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-5 > li {
    list-style: none;
    width: 20%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-5 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-6 > li {
    list-style: none;
    width: 16.66667%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-6 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-7 > li {
    list-style: none;
    width: 14.28571%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-7 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-8 > li {
    list-style: none;
    width: 12.5%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-8 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-9 > li {
    list-style: none;
    width: 11.11111%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-9 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-10 > li {
    list-style: none;
    width: 10%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-10 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-11 > li {
    list-style: none;
    width: 9.09091%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-11 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-12 > li {
    list-style: none;
    width: 8.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-12 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .medium-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-1 > li {
    list-style: none;
    width: 100%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-1 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-1 > li:nth-of-type(1n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-2 > li {
    list-style: none;
    width: 50%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-2 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-2 > li:nth-of-type(2n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-3 > li {
    list-style: none;
    width: 33.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-3 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-3 > li:nth-of-type(3n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-4 > li {
    list-style: none;
    width: 25%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-4 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-4 > li:nth-of-type(4n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-5 > li {
    list-style: none;
    width: 20%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-5 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-5 > li:nth-of-type(5n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-6 > li {
    list-style: none;
    width: 16.66667%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-6 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-6 > li:nth-of-type(6n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-7 > li {
    list-style: none;
    width: 14.28571%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-7 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-7 > li:nth-of-type(7n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-8 > li {
    list-style: none;
    width: 12.5%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-8 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-8 > li:nth-of-type(8n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-9 > li {
    list-style: none;
    width: 11.11111%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-9 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-9 > li:nth-of-type(9n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-10 > li {
    list-style: none;
    width: 10%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-10 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-10 > li:nth-of-type(10n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-11 > li {
    list-style: none;
    width: 9.09091%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-11 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-11 > li:nth-of-type(11n+1) {
    clear: both;
  }

  /* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-12 > li {
    list-style: none;
    width: 8.33333%;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-12 > li:nth-of-type(1n) {
    clear: none;
  }
  /* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_block-grid.scss */
  .large-block-grid-12 > li:nth-of-type(12n+1) {
    clear: both;
  }
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs {
  border-style: solid;
  border-width: 1px;
  display: block;
  list-style: none;
  margin-left: 0;
  overflow: hidden;
  padding: 0.5625rem 0.875rem 0.5625rem;
  background-color: #f4f4f4;
  border-color: gainsboro;
  border-radius: 3px;
}
/* line 122, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > * {
  color: #008CBA;
  float: left;
  font-size: 0.6875rem;
  line-height: 0.6875rem;
  margin: 0;
  text-transform: uppercase;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *:hover a, .breadcrumbs > *:focus a {
  text-decoration: underline;
}
/* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > * a {
  color: #008CBA;
}
/* line 75, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.current {
  color: #333333;
  cursor: default;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.current a {
  color: #333333;
  cursor: default;
}
/* line 83, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.current:hover, .breadcrumbs > *.current:hover a, .breadcrumbs > *.current:focus, .breadcrumbs > *.current:focus a {
  text-decoration: none;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.unavailable {
  color: #999999;
}
/* line 90, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.unavailable a {
  color: #999999;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *.unavailable:hover, .breadcrumbs > *.unavailable:hover a, .breadcrumbs > *.unavailable:focus,
.breadcrumbs > *.unavailable a:focus {
  color: #999999;
  cursor: not-allowed;
  text-decoration: none;
}
/* line 102, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *:before {
  color: #AAAAAA;
  content: "/";
  margin: 0 0.75rem;
  position: relative;
  top: 1px;
}
/* line 110, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
.breadcrumbs > *:first-child:before {
  content: " ";
  margin: 0;
}

/* Accessibility - hides the forward slash */
/* line 127, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_breadcrumbs.scss */
[aria-label="breadcrumbs"] [aria-hidden="true"]:after {
  content: "/";
}

/* line 213, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button, .button {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  border-style: solid;
  border-width: 0;
  cursor: pointer;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
  line-height: normal;
  margin: 0 0 1.25rem;
  position: relative;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  padding: 1rem 2rem 1.0625rem 2rem;
  font-size: 1rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  transition: background-color 300ms ease-out;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button:hover, button:focus, .button:hover, .button:focus {
  background-color: #007095;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button:hover, button:focus, .button:hover, .button:focus {
  color: #FFFFFF;
}
/* line 220, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.secondary, .button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
  background-color: #b9b9b9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.secondary:hover, button.secondary:focus, .button.secondary:hover, .button.secondary:focus {
  color: #333333;
}
/* line 221, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.success, .button.success {
  background-color: #43AC6A;
  border-color: #368a55;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
  background-color: #368a55;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.success:hover, button.success:focus, .button.success:hover, .button.success:focus {
  color: #FFFFFF;
}
/* line 222, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.alert, .button.alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
  background-color: #cf2a0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.alert:hover, button.alert:focus, .button.alert:hover, .button.alert:focus {
  color: #FFFFFF;
}
/* line 223, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.warning, .button.warning {
  background-color: #f08a24;
  border-color: #cf6e0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.warning:hover, button.warning:focus, .button.warning:hover, .button.warning:focus {
  background-color: #cf6e0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.warning:hover, button.warning:focus, .button.warning:hover, .button.warning:focus {
  color: #FFFFFF;
}
/* line 224, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.info, .button.info {
  background-color: #a0d3e8;
  border-color: #61b6d9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.info:hover, button.info:focus, .button.info:hover, .button.info:focus {
  background-color: #61b6d9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.info:hover, button.info:focus, .button.info:hover, .button.info:focus {
  color: #FFFFFF;
}
/* line 226, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.large, .button.large {
  padding: 1.125rem 2.25rem 1.1875rem 2.25rem;
  font-size: 1.25rem;
}
/* line 227, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.small, .button.small {
  padding: 0.875rem 1.75rem 0.9375rem 1.75rem;
  font-size: 0.8125rem;
}
/* line 228, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.tiny, .button.tiny {
  padding: 0.625rem 1.25rem 0.6875rem 1.25rem;
  font-size: 0.6875rem;
}
/* line 229, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.expand, .button.expand {
  padding-left: 0;
  padding-right: 0;
  width: 100%;
}
/* line 231, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.left-align, .button.left-align {
  text-align: left;
  text-indent: 0.75rem;
}
/* line 232, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.right-align, .button.right-align {
  text-align: right;
  padding-right: 0.75rem;
}
/* line 234, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.radius, .button.radius {
  border-radius: 3px;
}
/* line 235, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.round, .button.round {
  border-radius: 1000px;
}
/* line 237, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled, button[disabled], .button.disabled, .button[disabled] {
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
  background-color: #007095;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
  color: #FFFFFF;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled:hover, button.disabled:focus, button[disabled]:hover, button[disabled]:focus, .button.disabled:hover, .button.disabled:focus, .button[disabled]:hover, .button[disabled]:focus {
  background-color: #008CBA;
}
/* line 238, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.secondary, button[disabled].secondary, .button.disabled.secondary, .button[disabled].secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
  background-color: #b9b9b9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
  color: #333333;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.secondary:hover, button.disabled.secondary:focus, button[disabled].secondary:hover, button[disabled].secondary:focus, .button.disabled.secondary:hover, .button.disabled.secondary:focus, .button[disabled].secondary:hover, .button[disabled].secondary:focus {
  background-color: #e7e7e7;
}
/* line 239, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.success, button[disabled].success, .button.disabled.success, .button[disabled].success {
  background-color: #43AC6A;
  border-color: #368a55;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
  background-color: #368a55;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
  color: #FFFFFF;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.success:hover, button.disabled.success:focus, button[disabled].success:hover, button[disabled].success:focus, .button.disabled.success:hover, .button.disabled.success:focus, .button[disabled].success:hover, .button[disabled].success:focus {
  background-color: #43AC6A;
}
/* line 240, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.alert, button[disabled].alert, .button.disabled.alert, .button[disabled].alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
  background-color: #cf2a0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
  color: #FFFFFF;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.alert:hover, button.disabled.alert:focus, button[disabled].alert:hover, button[disabled].alert:focus, .button.disabled.alert:hover, .button.disabled.alert:focus, .button[disabled].alert:hover, .button[disabled].alert:focus {
  background-color: #f04124;
}
/* line 241, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.warning, button[disabled].warning, .button.disabled.warning, .button[disabled].warning {
  background-color: #f08a24;
  border-color: #cf6e0e;
  color: #FFFFFF;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.warning:hover, button.disabled.warning:focus, button[disabled].warning:hover, button[disabled].warning:focus, .button.disabled.warning:hover, .button.disabled.warning:focus, .button[disabled].warning:hover, .button[disabled].warning:focus {
  background-color: #cf6e0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.warning:hover, button.disabled.warning:focus, button[disabled].warning:hover, button[disabled].warning:focus, .button.disabled.warning:hover, .button.disabled.warning:focus, .button[disabled].warning:hover, .button[disabled].warning:focus {
  color: #FFFFFF;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.warning:hover, button.disabled.warning:focus, button[disabled].warning:hover, button[disabled].warning:focus, .button.disabled.warning:hover, .button.disabled.warning:focus, .button[disabled].warning:hover, .button[disabled].warning:focus {
  background-color: #f08a24;
}
/* line 242, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.info, button[disabled].info, .button.disabled.info, .button[disabled].info {
  background-color: #a0d3e8;
  border-color: #61b6d9;
  color: #333333;
  box-shadow: none;
  cursor: default;
  opacity: 0.7;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.info:hover, button.disabled.info:focus, button[disabled].info:hover, button[disabled].info:focus, .button.disabled.info:hover, .button.disabled.info:focus, .button[disabled].info:hover, .button[disabled].info:focus {
  background-color: #61b6d9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.info:hover, button.disabled.info:focus, button[disabled].info:hover, button[disabled].info:focus, .button.disabled.info:hover, .button.disabled.info:focus, .button[disabled].info:hover, .button[disabled].info:focus {
  color: #FFFFFF;
}
/* line 175, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button.disabled.info:hover, button.disabled.info:focus, button[disabled].info:hover, button[disabled].info:focus, .button.disabled.info:hover, .button.disabled.info:focus, .button[disabled].info:hover, .button[disabled].info:focus {
  background-color: #a0d3e8;
}

/* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
button::-moz-focus-inner {
  border: 0;
  padding: 0;
}

@media only screen and (min-width: 30em) {
  /* line 250, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
  button, .button {
    display: inline-block;
  }
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group {
  list-style: none;
  margin: 0;
  left: 0;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.button-group:before, .button-group:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.button-group:after {
  clear: both;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-2 li {
  display: inline-block;
  margin: 0 -2px;
  width: 50%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-2 li > button, .button-group.even-2 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-2 li:first-child button, .button-group.even-2 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-2 li button, .button-group.even-2 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-3 li {
  display: inline-block;
  margin: 0 -2px;
  width: 33.33333%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-3 li > button, .button-group.even-3 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-3 li:first-child button, .button-group.even-3 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-3 li button, .button-group.even-3 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-4 li {
  display: inline-block;
  margin: 0 -2px;
  width: 25%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-4 li > button, .button-group.even-4 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-4 li:first-child button, .button-group.even-4 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-4 li button, .button-group.even-4 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-5 li {
  display: inline-block;
  margin: 0 -2px;
  width: 20%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-5 li > button, .button-group.even-5 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-5 li:first-child button, .button-group.even-5 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-5 li button, .button-group.even-5 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-6 li {
  display: inline-block;
  margin: 0 -2px;
  width: 16.66667%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-6 li > button, .button-group.even-6 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-6 li:first-child button, .button-group.even-6 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-6 li button, .button-group.even-6 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-7 li {
  display: inline-block;
  margin: 0 -2px;
  width: 14.28571%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-7 li > button, .button-group.even-7 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-7 li:first-child button, .button-group.even-7 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-7 li button, .button-group.even-7 li .button {
  width: 100%;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-8 li {
  display: inline-block;
  margin: 0 -2px;
  width: 12.5%;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-8 li > button, .button-group.even-8 li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-8 li:first-child button, .button-group.even-8 li:first-child .button {
  border-left: 0;
}
/* line 152, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.even-8 li button, .button-group.even-8 li .button {
  width: 100%;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group > li {
  display: inline-block;
  margin: 0 -2px;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group > li > button, .button-group > li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group > li:first-child button, .button-group > li:first-child .button {
  border-left: 0;
}
/* line 167, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li {
  display: block;
  margin: 0;
  float: none;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li > button, .button-group.stack > li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li:first-child button, .button-group.stack > li:first-child .button {
  border-left: 0;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li > button, .button-group.stack > li .button {
  border-color: rgba(255, 255, 255, 0.5);
  border-left-width: 0;
  border-top: 1px solid;
  display: block;
  margin: 0;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li > button {
  width: 100%;
}
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack > li:first-child button, .button-group.stack > li:first-child .button {
  border-top: 0;
}
/* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack-for-small > li {
  display: inline-block;
  margin: 0 -2px;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack-for-small > li > button, .button-group.stack-for-small > li .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.stack-for-small > li:first-child button, .button-group.stack-for-small > li:first-child .button {
  border-left: 0;
}
@media only screen and (max-width: 29.9375em) {
  /* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li {
    display: block;
    margin: 0;
  }
  /* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li > button, .button-group.stack-for-small > li .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5);
  }
  /* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li:first-child button, .button-group.stack-for-small > li:first-child .button {
    border-left: 0;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li > button, .button-group.stack-for-small > li .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left-width: 0;
    border-top: 1px solid;
    display: block;
    margin: 0;
  }
  /* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li > button {
    width: 100%;
  }
  /* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.stack-for-small > li:first-child button, .button-group.stack-for-small > li:first-child .button {
    border-top: 0;
  }
}
/* line 179, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > * {
  display: inline-block;
  margin: 0 -2px;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > * > button, .button-group.radius > * .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > *:first-child button, .button-group.radius > *:first-child .button {
  border-left: 0;
}
/* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > *,
.button-group.radius > * > a,
.button-group.radius > * > button,
.button-group.radius > * > .button {
  border-radius: 0;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > *:first-child, .button-group.radius > *:first-child > a, .button-group.radius > *:first-child > button, .button-group.radius > *:first-child > .button {
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius > *:last-child, .button-group.radius > *:last-child > a, .button-group.radius > *:last-child > button, .button-group.radius > *:last-child > .button {
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 180, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > * {
  display: block;
  margin: 0;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > * > button, .button-group.radius.stack > * .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > *:first-child button, .button-group.radius.stack > *:first-child .button {
  border-left: 0;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > * > button, .button-group.radius.stack > * .button {
  border-color: rgba(255, 255, 255, 0.5);
  border-left-width: 0;
  border-top: 1px solid;
  display: block;
  margin: 0;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > * > button {
  width: 100%;
}
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > *:first-child button, .button-group.radius.stack > *:first-child .button {
  border-top: 0;
}
/* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > *,
.button-group.radius.stack > * > a,
.button-group.radius.stack > * > button,
.button-group.radius.stack > * > .button {
  border-radius: 0;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > *:first-child, .button-group.radius.stack > *:first-child > a, .button-group.radius.stack > *:first-child > button, .button-group.radius.stack > *:first-child > .button {
  -webkit-top-left-radius: 3px;
  -webkit-top-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.radius.stack > *:last-child, .button-group.radius.stack > *:last-child > a, .button-group.radius.stack > *:last-child > button, .button-group.radius.stack > *:last-child > .button {
  -webkit-bottom-left-radius: 3px;
  -webkit-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
@media only screen and (min-width: 30em) {
  /* line 181, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * {
    display: inline-block;
    margin: 0 -2px;
  }
  /* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * > button, .button-group.radius.stack-for-small > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5);
  }
  /* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:first-child button, .button-group.radius.stack-for-small > *:first-child .button {
    border-left: 0;
  }
  /* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *,
  .button-group.radius.stack-for-small > * > a,
  .button-group.radius.stack-for-small > * > button,
  .button-group.radius.stack-for-small > * > .button {
    border-radius: 0;
  }
  /* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:first-child, .button-group.radius.stack-for-small > *:first-child > a, .button-group.radius.stack-for-small > *:first-child > button, .button-group.radius.stack-for-small > *:first-child > .button {
    -webkit-border-bottom-left-radius: 3px;
    -webkit-border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
  }
  /* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:last-child, .button-group.radius.stack-for-small > *:last-child > a, .button-group.radius.stack-for-small > *:last-child > button, .button-group.radius.stack-for-small > *:last-child > .button {
    -webkit-border-bottom-right-radius: 3px;
    -webkit-border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
  }
}
@media only screen and (max-width: 29.9375em) {
  /* line 181, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * {
    display: block;
    margin: 0;
  }
  /* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * > button, .button-group.radius.stack-for-small > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5);
  }
  /* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:first-child button, .button-group.radius.stack-for-small > *:first-child .button {
    border-left: 0;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * > button, .button-group.radius.stack-for-small > * .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left-width: 0;
    border-top: 1px solid;
    display: block;
    margin: 0;
  }
  /* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > * > button {
    width: 100%;
  }
  /* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:first-child button, .button-group.radius.stack-for-small > *:first-child .button {
    border-top: 0;
  }
  /* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *,
  .button-group.radius.stack-for-small > * > a,
  .button-group.radius.stack-for-small > * > button,
  .button-group.radius.stack-for-small > * > .button {
    border-radius: 0;
  }
  /* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:first-child, .button-group.radius.stack-for-small > *:first-child > a, .button-group.radius.stack-for-small > *:first-child > button, .button-group.radius.stack-for-small > *:first-child > .button {
    -webkit-top-left-radius: 3px;
    -webkit-top-right-radius: 3px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
  }
  /* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.radius.stack-for-small > *:last-child, .button-group.radius.stack-for-small > *:last-child > a, .button-group.radius.stack-for-small > *:last-child > button, .button-group.radius.stack-for-small > *:last-child > .button {
    -webkit-bottom-left-radius: 3px;
    -webkit-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
    border-bottom-right-radius: 3px;
  }
}
/* line 190, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > * {
  display: inline-block;
  margin: 0 -2px;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > * > button, .button-group.round > * .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > *:first-child button, .button-group.round > *:first-child .button {
  border-left: 0;
}
/* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > *,
.button-group.round > * > a,
.button-group.round > * > button,
.button-group.round > * > .button {
  border-radius: 0;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > *:first-child, .button-group.round > *:first-child > a, .button-group.round > *:first-child > button, .button-group.round > *:first-child > .button {
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round > *:last-child, .button-group.round > *:last-child > a, .button-group.round > *:last-child > button, .button-group.round > *:last-child > .button {
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}
/* line 191, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > * {
  display: block;
  margin: 0;
}
/* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > * > button, .button-group.round.stack > * .button {
  border-left: 1px solid;
  border-color: rgba(255, 255, 255, 0.5);
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > *:first-child button, .button-group.round.stack > *:first-child .button {
  border-left: 0;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > * > button, .button-group.round.stack > * .button {
  border-color: rgba(255, 255, 255, 0.5);
  border-left-width: 0;
  border-top: 1px solid;
  display: block;
  margin: 0;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > * > button {
  width: 100%;
}
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > *:first-child button, .button-group.round.stack > *:first-child .button {
  border-top: 0;
}
/* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > *,
.button-group.round.stack > * > a,
.button-group.round.stack > * > button,
.button-group.round.stack > * > .button {
  border-radius: 0;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > *:first-child, .button-group.round.stack > *:first-child > a, .button-group.round.stack > *:first-child > button, .button-group.round.stack > *:first-child > .button {
  -webkit-top-left-radius: 1rem;
  -webkit-top-right-radius: 1rem;
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-group.round.stack > *:last-child, .button-group.round.stack > *:last-child > a, .button-group.round.stack > *:last-child > button, .button-group.round.stack > *:last-child > .button {
  -webkit-bottom-left-radius: 1rem;
  -webkit-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
@media only screen and (min-width: 30em) {
  /* line 192, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * {
    display: inline-block;
    margin: 0 -2px;
  }
  /* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * > button, .button-group.round.stack-for-small > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5);
  }
  /* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:first-child button, .button-group.round.stack-for-small > *:first-child .button {
    border-left: 0;
  }
  /* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *,
  .button-group.round.stack-for-small > * > a,
  .button-group.round.stack-for-small > * > button,
  .button-group.round.stack-for-small > * > .button {
    border-radius: 0;
  }
  /* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:first-child, .button-group.round.stack-for-small > *:first-child > a, .button-group.round.stack-for-small > *:first-child > button, .button-group.round.stack-for-small > *:first-child > .button {
    -webkit-border-bottom-left-radius: 1000px;
    -webkit-border-top-left-radius: 1000px;
    border-bottom-left-radius: 1000px;
    border-top-left-radius: 1000px;
  }
  /* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:last-child, .button-group.round.stack-for-small > *:last-child > a, .button-group.round.stack-for-small > *:last-child > button, .button-group.round.stack-for-small > *:last-child > .button {
    -webkit-border-bottom-right-radius: 1000px;
    -webkit-border-top-right-radius: 1000px;
    border-bottom-right-radius: 1000px;
    border-top-right-radius: 1000px;
  }
}
@media only screen and (max-width: 29.9375em) {
  /* line 192, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * {
    display: block;
    margin: 0;
  }
  /* line 39, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * > button, .button-group.round.stack-for-small > * .button {
    border-left: 1px solid;
    border-color: rgba(255, 255, 255, 0.5);
  }
  /* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:first-child button, .button-group.round.stack-for-small > *:first-child .button {
    border-left: 0;
  }
  /* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * > button, .button-group.round.stack-for-small > * .button {
    border-color: rgba(255, 255, 255, 0.5);
    border-left-width: 0;
    border-top: 1px solid;
    display: block;
    margin: 0;
  }
  /* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > * > button {
    width: 100%;
  }
  /* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:first-child button, .button-group.round.stack-for-small > *:first-child .button {
    border-top: 0;
  }
  /* line 121, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *,
  .button-group.round.stack-for-small > * > a,
  .button-group.round.stack-for-small > * > button,
  .button-group.round.stack-for-small > * > .button {
    border-radius: 0;
  }
  /* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:first-child, .button-group.round.stack-for-small > *:first-child > a, .button-group.round.stack-for-small > *:first-child > button, .button-group.round.stack-for-small > *:first-child > .button {
    -webkit-top-left-radius: 1rem;
    -webkit-top-right-radius: 1rem;
    border-top-left-radius: 1rem;
    border-top-right-radius: 1rem;
  }
  /* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
  .button-group.round.stack-for-small > *:last-child, .button-group.round.stack-for-small > *:last-child > a, .button-group.round.stack-for-small > *:last-child > button, .button-group.round.stack-for-small > *:last-child > .button {
    -webkit-bottom-left-radius: 1rem;
    -webkit-bottom-right-radius: 1rem;
    border-bottom-left-radius: 1rem;
    border-bottom-right-radius: 1rem;
  }
}

/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.button-bar:before, .button-bar:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.button-bar:after {
  clear: both;
}
/* line 204, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-bar .button-group {
  float: left;
  margin-right: 0.625rem;
}
/* line 32, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_button-groups.scss */
.button-bar .button-group div {
  overflow: hidden;
}

/* Clearing Styles */
/* line 44, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-thumbs, [data-clearing] {
  list-style: none;
  margin-left: 0;
  margin-bottom: 0;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.clearing-thumbs:before, .clearing-thumbs:after, [data-clearing]:before, [data-clearing]:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.clearing-thumbs:after, [data-clearing]:after {
  clear: both;
}
/* line 50, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-thumbs li, [data-clearing] li {
  float: left;
  margin-right: 10px;
}
/* line 55, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-thumbs[class*="block-grid-"] li, [data-clearing][class*="block-grid-"] li {
  margin-right: 0;
}

/* line 60, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-blackout {
  background: #333333;
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 998;
  left: 0;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-blackout .clearing-close {
  display: block;
}

/* line 72, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-container {
  height: 100%;
  margin: 0;
  overflow: hidden;
  position: relative;
  z-index: 998;
}

/* line 80, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-touch-label {
  color: #AAAAAA;
  font-size: .6em;
  left: 50%;
  position: absolute;
  top: 50%;
}

/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.visible-img {
  height: 95%;
  position: relative;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.visible-img img {
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateY(-50%) translateX(-50%);
  -moz-transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -o-transform: translateY(-50%) translateX(-50%);
  transform: translateY(-50%) translateX(-50%);
  max-height: 100%;
  max-width: 100%;
}

/* line 115, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-caption {
  background: #333333;
  bottom: 0;
  color: #CCCCCC;
  font-size: 0.875em;
  line-height: 1.3;
  margin-bottom: 0;
  padding: 10px 30px 20px;
  position: absolute;
  text-align: center;
  width: 100%;
  left: 0;
}

/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-close {
  color: #CCCCCC;
  display: none;
  font-size: 30px;
  line-height: 1;
  padding-left: 20px;
  padding-top: 10px;
  z-index: 999;
}
/* line 138, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-close:hover, .clearing-close:focus {
  color: #CCCCCC;
}

/* line 142, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-assembled .clearing-container {
  height: 100%;
}
/* line 143, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-assembled .clearing-container .carousel > ul {
  display: none;
}

/* line 147, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-feature li {
  display: none;
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
.clearing-feature li.clearing-featured-img {
  display: block;
}

@media only screen and (min-width: 30em) {
  /* line 156, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev,
  .clearing-main-next {
    height: 100%;
    position: absolute;
    top: 0;
    width: 40px;
  }
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev > span,
  .clearing-main-next > span {
    border: solid 12px;
    display: block;
    height: 0;
    position: absolute;
    top: 50%;
    width: 0;
  }
  /* line 169, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev > span:hover,
  .clearing-main-next > span:hover {
    opacity: .8;
  }

  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev {
    left: 0;
  }
  /* line 174, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev > span {
    left: 5px;
    border-color: transparent;
    border-right-color: #CCCCCC;
  }

  /* line 180, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-next {
    right: 0;
  }
  /* line 182, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-next > span {
    border-color: transparent;
    border-left-color: #CCCCCC;
  }

  /* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-main-prev.disabled,
  .clearing-main-next.disabled {
    opacity: .3;
  }

  /* line 193, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel {
    background: rgba(51, 51, 51, 0.8);
    height: 120px;
    margin-top: 10px;
    text-align: center;
  }
  /* line 199, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul {
    display: inline-block;
    z-index: 999;
    height: 100%;
    position: relative;
    float: none;
  }
  /* line 206, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li {
    clear: none;
    cursor: pointer;
    display: block;
    float: left;
    margin-right: 0;
    min-height: inherit;
    opacity: .4;
    overflow: hidden;
    padding: 0;
    position: relative;
    width: 120px;
  }
  /* line 220, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li.fix-height img {
    height: 100%;
    max-width: none;
  }
  /* line 226, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li a.th {
    border: none;
    box-shadow: none;
    display: block;
  }
  /* line 232, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li img {
    cursor: pointer !important;
    width: 100% !important;
  }
  /* line 237, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li.visible {
    opacity: 1;
  }
  /* line 238, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .carousel > ul li:hover {
    opacity: .8;
  }
  /* line 243, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-assembled .clearing-container .visible-img {
    background: #333333;
    height: 85%;
    overflow: hidden;
  }

  /* line 250, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_clearing.scss */
  .clearing-close {
    padding-left: 0;
    padding-top: 0;
    position: absolute;
    top: 10px;
    right: 20px;
  }
}
/* Foundation Dropdowns */
/* line 231, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown {
  display: none;
  left: -9999px;
  list-style: none;
  margin-left: 0;
  position: absolute;
  background: #FFFFFF;
  border: solid 1px #cccccc;
  font-size: 0.875rem;
  height: auto;
  max-height: none;
  width: 100%;
  z-index: 89;
  margin-top: 2px;
  max-width: 200px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.open {
  display: block;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown > *:first-child {
  margin-top: 0;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown > *:last-child {
  margin-bottom: 0;
}
/* line 105, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown:before {
  border: inset 6px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent transparent #FFFFFF transparent;
  border-bottom-style: solid;
  position: absolute;
  top: -12px;
  left: 10px;
  z-index: 89;
}
/* line 112, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown:after {
  border: inset 7px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent transparent #cccccc transparent;
  border-bottom-style: solid;
  position: absolute;
  top: -14px;
  left: 9px;
  z-index: 88;
}
/* line 120, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.right:before {
  left: auto;
  right: 10px;
}
/* line 124, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.right:after {
  left: auto;
  right: 9px;
}
/* line 234, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right {
  display: none;
  left: -9999px;
  list-style: none;
  margin-left: 0;
  position: absolute;
  background: #FFFFFF;
  border: solid 1px #cccccc;
  font-size: 0.875rem;
  height: auto;
  max-height: none;
  width: 100%;
  z-index: 89;
  margin-top: 0;
  margin-left: 2px;
  max-width: 200px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right.open {
  display: block;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right > *:first-child {
  margin-top: 0;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right > *:last-child {
  margin-bottom: 0;
}
/* line 135, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right:before {
  border: inset 6px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent #FFFFFF transparent transparent;
  border-right-style: solid;
  position: absolute;
  top: 10px;
  left: -12px;
  z-index: 89;
}
/* line 142, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-right:after {
  border: inset 7px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent #cccccc transparent transparent;
  border-right-style: solid;
  position: absolute;
  top: 9px;
  left: -14px;
  z-index: 88;
}
/* line 238, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left {
  display: none;
  left: -9999px;
  list-style: none;
  margin-left: 0;
  position: absolute;
  background: #FFFFFF;
  border: solid 1px #cccccc;
  font-size: 0.875rem;
  height: auto;
  max-height: none;
  width: 100%;
  z-index: 89;
  margin-top: 0;
  margin-left: -2px;
  max-width: 200px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left.open {
  display: block;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left > *:first-child {
  margin-top: 0;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left > *:last-child {
  margin-bottom: 0;
}
/* line 156, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left:before {
  border: inset 6px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent transparent transparent #FFFFFF;
  border-left-style: solid;
  position: absolute;
  top: 10px;
  right: -12px;
  left: auto;
  z-index: 89;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-left:after {
  border: inset 7px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent transparent transparent #cccccc;
  border-left-style: solid;
  position: absolute;
  top: 9px;
  right: -14px;
  left: auto;
  z-index: 88;
}
/* line 242, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top {
  display: none;
  left: -9999px;
  list-style: none;
  margin-left: 0;
  position: absolute;
  background: #FFFFFF;
  border: solid 1px #cccccc;
  font-size: 0.875rem;
  height: auto;
  max-height: none;
  width: 100%;
  z-index: 89;
  margin-left: 0;
  margin-top: -2px;
  max-width: 200px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top.open {
  display: block;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top > *:first-child {
  margin-top: 0;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top > *:last-child {
  margin-bottom: 0;
}
/* line 179, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top:before {
  border: inset 6px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: #FFFFFF transparent transparent transparent;
  border-top-style: solid;
  bottom: -12px;
  position: absolute;
  top: auto;
  left: 10px;
  right: auto;
  z-index: 89;
}
/* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.drop-top:after {
  border: inset 7px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: #cccccc transparent transparent transparent;
  border-top-style: solid;
  bottom: -14px;
  position: absolute;
  top: auto;
  left: 9px;
  right: auto;
  z-index: 88;
}
/* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown li {
  cursor: pointer;
  font-size: 0.875rem;
  line-height: 1.125rem;
  margin: 0;
}
/* line 215, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown li:hover, .f-dropdown li:focus {
  background: #EEEEEE;
}
/* line 218, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown li.radius {
  border-radius: 3px;
}
/* line 220, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown li a {
  display: block;
  padding: 0.5rem;
  color: #555555;
}
/* line 250, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.content {
  display: none;
  left: -9999px;
  list-style: none;
  margin-left: 0;
  position: absolute;
  background: #FFFFFF;
  border: solid 1px #cccccc;
  font-size: 0.875rem;
  height: auto;
  max-height: none;
  padding: 1.25rem;
  width: 100%;
  z-index: 89;
  max-width: 200px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.content.open {
  display: block;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.content > *:first-child {
  margin-top: 0;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.content > *:last-child {
  margin-bottom: 0;
}
/* line 253, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.tiny {
  max-width: 200px;
}
/* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.small {
  max-width: 300px;
}
/* line 255, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.medium {
  max-width: 500px;
}
/* line 256, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.large {
  max-width: 800px;
}
/* line 257, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.mega {
  width: 100% !important;
  max-width: 100% !important;
}
/* line 261, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown.scss */
.f-dropdown.mega.open {
  left: 0 !important;
}

/* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button, button.dropdown {
  position: relative;
  padding-right: 3.5625rem;
}
/* line 63, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button::after, button.dropdown::after {
  border-color: #FFFFFF transparent transparent transparent;
  border-style: solid;
  content: "";
  display: block;
  height: 0;
  position: absolute;
  top: 50%;
  width: 0;
}
/* line 98, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button::after, button.dropdown::after {
  border-width: 0.375rem;
  right: 1.40625rem;
  margin-top: -0.15625rem;
}
/* line 117, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button::after, button.dropdown::after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 124, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.tiny, button.dropdown.tiny {
  padding-right: 2.625rem;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.tiny:after, button.dropdown.tiny:after {
  border-width: 0.375rem;
  right: 1.125rem;
  margin-top: -0.125rem;
}
/* line 117, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.tiny::after, button.dropdown.tiny::after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.small, button.dropdown.small {
  padding-right: 3.0625rem;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.small::after, button.dropdown.small::after {
  border-width: 0.4375rem;
  right: 1.3125rem;
  margin-top: -0.15625rem;
}
/* line 117, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.small::after, button.dropdown.small::after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.large, button.dropdown.large {
  padding-right: 3.625rem;
}
/* line 108, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.large::after, button.dropdown.large::after {
  border-width: 0.3125rem;
  right: 1.71875rem;
  margin-top: -0.15625rem;
}
/* line 117, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.large::after, button.dropdown.large::after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 127, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_dropdown-buttons.scss */
.dropdown.button.secondary:after, button.dropdown.secondary:after {
  border-color: #333333 transparent transparent transparent;
}

/* line 49, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_flex-video.scss */
.flex-video {
  height: 0;
  margin-bottom: 1rem;
  overflow: hidden;
  padding-bottom: 67.5%;
  padding-top: 1.5625rem;
  position: relative;
}
/* line 32, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_flex-video.scss */
.flex-video.widescreen {
  padding-bottom: 56.34%;
}
/* line 33, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_flex-video.scss */
.flex-video.vimeo {
  padding-top: 0;
}
/* line 35, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_flex-video.scss */
.flex-video iframe,
.flex-video object,
.flex-video embed,
.flex-video video {
  height: 100%;
  position: absolute;
  top: 0;
  width: 100%;
  left: 0;
}

/* Standard Forms */
/* line 387, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form {
  margin: 0 0 1rem;
}

/* Using forms within rows, we need to set some defaults */
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .row {
  margin: 0 -0.5rem;
}
/* line 94, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .row .column,
form .row .row .columns {
  padding: 0 0.5rem;
}
/* line 98, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .row.collapse {
  margin: 0;
}
/* line 100, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .row.collapse .column,
form .row .row.collapse .columns {
  padding: 0;
}
/* line 102, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .row.collapse input {
  -webkit-border-bottom-right-radius: 0;
  -webkit-border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
/* line 108, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row input.column,
form .row input.columns,
form .row textarea.column,
form .row textarea.columns {
  padding-left: 0.5rem;
}

/* Label Styles */
/* line 393, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
label {
  color: #4d4d4d;
  cursor: pointer;
  display: block;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.5;
  margin-bottom: 0;
  /* Styles for required inputs */
}
/* line 394, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
label.right {
  float: none !important;
  text-align: right;
}
/* line 395, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
label.inline {
  margin: 0 0 1rem 0;
  padding: 0.5625rem 0;
}
/* line 397, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
label small {
  text-transform: capitalize;
  color: #676767;
}

/* Attach elements to the beginning or end of an input */
/* line 404, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.prefix,
.postfix {
  border-style: solid;
  border-width: 1px;
  display: block;
  font-size: 0.875rem;
  height: 2.3125rem;
  line-height: 2.3125rem;
  overflow: visible;
  padding-bottom: 0;
  padding-top: 0;
  position: relative;
  text-align: center;
  width: 100%;
  z-index: 2;
}

/* Adjust padding, alignment and radius if pre/post element is a button */
/* line 408, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.postfix.button {
  border-color: true;
}

/* line 409, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.prefix.button {
  border: none;
  padding-left: 0;
  padding-right: 0;
  padding-bottom: 0;
  padding-top: 0;
  text-align: center;
}

/* line 411, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.prefix.button.radius {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}

/* line 412, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.postfix.button.radius {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}

/* line 413, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.prefix.button.round {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px;
}

/* line 414, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.postfix.button.round {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}

/* Separate prefix and postfix styles when on span or label so buttons keep their own */
/* line 417, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
span.prefix, label.prefix {
  background: #f2f2f2;
  border-right: none;
  color: #333333;
  border-color: #cccccc;
}

/* line 418, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
span.postfix, label.postfix {
  background: #f2f2f2;
  color: #333333;
  border-color: #cccccc;
}

/* We use this to get basic styling on all basic form elements */
/* line 421, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="text"], input[type="password"], input[type="date"], input[type="datetime"], input[type="datetime-local"], input[type="month"], input[type="week"], input[type="email"], input[type="number"], input[type="search"], input[type="tel"], input[type="time"], input[type="url"], input[type="color"], textarea {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
  background-color: #FFFFFF;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.75);
  display: block;
  font-family: inherit;
  font-size: 0.875rem;
  height: 2.3125rem;
  margin: 0 0 1rem 0;
  padding: 0.5rem;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: border-color 0.15s linear, background 0.15s linear;
  -moz-transition: border-color 0.15s linear, background 0.15s linear;
  -ms-transition: border-color 0.15s linear, background 0.15s linear;
  -o-transition: border-color 0.15s linear, background 0.15s linear;
  transition: border-color 0.15s linear, background 0.15s linear;
}
/* line 138, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="text"]:focus, input[type="password"]:focus, input[type="date"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="email"]:focus, input[type="number"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="time"]:focus, input[type="url"]:focus, input[type="color"]:focus, textarea:focus {
  background: #fafafa;
  border-color: #999999;
  outline: none;
}
/* line 144, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="text"]:disabled, input[type="password"]:disabled, input[type="date"]:disabled, input[type="datetime"]:disabled, input[type="datetime-local"]:disabled, input[type="month"]:disabled, input[type="week"]:disabled, input[type="email"]:disabled, input[type="number"]:disabled, input[type="search"]:disabled, input[type="tel"]:disabled, input[type="time"]:disabled, input[type="url"]:disabled, input[type="color"]:disabled, textarea:disabled {
  background-color: #DDDDDD;
  cursor: default;
}
/* line 150, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="text"][disabled], input[type="text"][readonly], fieldset[disabled] input[type="text"], input[type="password"][disabled], input[type="password"][readonly], fieldset[disabled] input[type="password"], input[type="date"][disabled], input[type="date"][readonly], fieldset[disabled] input[type="date"], input[type="datetime"][disabled], input[type="datetime"][readonly], fieldset[disabled] input[type="datetime"], input[type="datetime-local"][disabled], input[type="datetime-local"][readonly], fieldset[disabled] input[type="datetime-local"], input[type="month"][disabled], input[type="month"][readonly], fieldset[disabled] input[type="month"], input[type="week"][disabled], input[type="week"][readonly], fieldset[disabled] input[type="week"], input[type="email"][disabled], input[type="email"][readonly], fieldset[disabled] input[type="email"], input[type="number"][disabled], input[type="number"][readonly], fieldset[disabled] input[type="number"], input[type="search"][disabled], input[type="search"][readonly], fieldset[disabled] input[type="search"], input[type="tel"][disabled], input[type="tel"][readonly], fieldset[disabled] input[type="tel"], input[type="time"][disabled], input[type="time"][readonly], fieldset[disabled] input[type="time"], input[type="url"][disabled], input[type="url"][readonly], fieldset[disabled] input[type="url"], input[type="color"][disabled], input[type="color"][readonly], fieldset[disabled] input[type="color"], textarea[disabled], textarea[readonly], fieldset[disabled] textarea {
  background-color: #DDDDDD;
  cursor: default;
}
/* line 433, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="text"].radius, input[type="password"].radius, input[type="date"].radius, input[type="datetime"].radius, input[type="datetime-local"].radius, input[type="month"].radius, input[type="week"].radius, input[type="email"].radius, input[type="number"].radius, input[type="search"].radius, input[type="tel"].radius, input[type="time"].radius, input[type="url"].radius, input[type="color"].radius, textarea.radius {
  border-radius: 3px;
}

/* line 441, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .prefix-radius.row.collapse input,
form .row .prefix-radius.row.collapse textarea,
form .row .prefix-radius.row.collapse select,
form .row .prefix-radius.row.collapse button {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 445, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .prefix-radius.row.collapse .prefix {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
/* line 448, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .postfix-radius.row.collapse input,
form .row .postfix-radius.row.collapse textarea,
form .row .postfix-radius.row.collapse select,
form .row .postfix-radius.row.collapse button {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
/* line 452, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .postfix-radius.row.collapse .postfix {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 455, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .prefix-round.row.collapse input,
form .row .prefix-round.row.collapse textarea,
form .row .prefix-round.row.collapse select,
form .row .prefix-round.row.collapse button {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}
/* line 459, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .prefix-round.row.collapse .prefix {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px;
}
/* line 462, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .postfix-round.row.collapse input,
form .row .postfix-round.row.collapse textarea,
form .row .postfix-round.row.collapse select,
form .row .postfix-round.row.collapse button {
  border-radius: 0;
  -webkit-border-bottom-left-radius: 1000px;
  -webkit-border-top-left-radius: 1000px;
  border-bottom-left-radius: 1000px;
  border-top-left-radius: 1000px;
}
/* line 466, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
form .row .postfix-round.row.collapse .postfix {
  border-radius: 0;
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}

/* line 471, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="submit"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  border-radius: 0;
}

/* Respect enforced amount of rows for textarea */
/* line 478, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
textarea[rows] {
  height: auto;
}

/* Not allow resize out of parent */
/* line 483, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
textarea {
  max-width: 100%;
}

/* line 488, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
::-webkit-input-placeholder {
  color: #cccccc;
}

/* line 492, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
:-moz-placeholder {
  /* Firefox 18- */
  color: #cccccc;
}

/* line 496, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
::-moz-placeholder {
  /* Firefox 19+ */
  color: #cccccc;
}

/* line 500, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
:-ms-input-placeholder {
  color: #cccccc;
}

/* Add height value for select elements to match text input height */
/* line 506, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select {
  -webkit-appearance: none !important;
  -moz-appearance: none !important;
  background-color: #FAFAFA;
  border-radius: 0;
  background-image: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZlcnNpb249IjEuMSIgeD0iMTJweCIgeT0iMHB4IiB3aWR0aD0iMjRweCIgaGVpZ2h0PSIzcHgiIHZpZXdCb3g9IjAgMCA2IDMiIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDYgMyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHBvbHlnb24gcG9pbnRzPSI1Ljk5MiwwIDIuOTkyLDMgLTAuMDA4LDAgIi8+PC9zdmc+);
  background-position: 100% center;
  background-repeat: no-repeat;
  border-style: solid;
  border-width: 1px;
  border-color: #cccccc;
  color: rgba(0, 0, 0, 0.75);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: normal;
  padding: 0.5rem;
  border-radius: 0;
  height: 2.3125rem;
}
/* line 337, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select::-ms-expand {
  display: none;
}
/* line 360, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select.radius {
  border-radius: 3px;
}
/* line 361, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select:hover {
  background-color: #f3f3f3;
  border-color: #999999;
}
/* line 366, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select:disabled {
  background-color: #DDDDDD;
  cursor: default;
}
/* line 509, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
select[multiple] {
  height: auto;
}

/* Adjust margin for form elements below */
/* line 515, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="file"],
input[type="checkbox"],
input[type="radio"],
select {
  margin: 0 0 1rem 0;
}

/* line 522, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="checkbox"] + label,
input[type="radio"] + label {
  display: inline-block;
  margin-left: 0.5rem;
  margin-right: 1rem;
  margin-bottom: 0;
  vertical-align: baseline;
}

/* Normalize file input width */
/* line 532, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input[type="file"] {
  width: 100%;
}

/* HTML5 Number spinners settings */
/* We add basic fieldset styling */
/* line 546, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
fieldset {
  border: 1px solid #DDDDDD;
  margin: 1.125rem 0;
  padding: 1.25rem;
}
/* line 279, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
fieldset legend {
  background: #FFFFFF;
  font-weight: bold;
  margin-left: -0.1875rem;
  margin: 0;
  padding: 0 0.1875rem;
}

/* Error Handling */
/* line 553, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
[data-abide] .error small.error, [data-abide] .error span.error, [data-abide] span.error, [data-abide] small.error {
  display: block;
  font-size: 0.75rem;
  font-style: italic;
  font-weight: normal;
  margin-bottom: 1rem;
  margin-top: -1px;
  padding: 0.375rem 0.5625rem 0.5625rem;
  background: #f04124;
  color: #FFFFFF;
}
/* line 556, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
[data-abide] span.error, [data-abide] small.error {
  display: none;
}

/* line 559, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
span.error, small.error {
  display: block;
  font-size: 0.75rem;
  font-style: italic;
  font-weight: normal;
  margin-bottom: 1rem;
  margin-top: -1px;
  padding: 0.375rem 0.5625rem 0.5625rem;
  background: #f04124;
  color: #FFFFFF;
}

/* line 564, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error input,
.error textarea,
.error select {
  margin-bottom: 0;
}
/* line 570, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error input[type="checkbox"],
.error input[type="radio"] {
  margin-bottom: 1rem;
}
/* line 575, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error label,
.error label.error {
  color: #f04124;
}
/* line 580, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error small.error {
  display: block;
  font-size: 0.75rem;
  font-style: italic;
  font-weight: normal;
  margin-bottom: 1rem;
  margin-top: -1px;
  padding: 0.375rem 0.5625rem 0.5625rem;
  background: #f04124;
  color: #FFFFFF;
}
/* line 585, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error > label > small {
  background: transparent;
  color: #676767;
  display: inline;
  font-size: 60%;
  font-style: normal;
  margin: 0;
  padding: 0;
  text-transform: capitalize;
}
/* line 597, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
.error span.error-message {
  display: block;
}

/* line 602, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
input.error,
textarea.error,
select.error {
  margin-bottom: 0;
}

/* line 607, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_forms.scss */
label.error {
  color: #f04124;
}

/* line 347, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar {
  display: inline-block;
  font-size: 0;
  width: 100%;
  background: #333333;
}
/* line 45, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * {
  display: block;
  float: left;
  font-size: 1rem;
  margin: 0 auto;
  padding: 1.25rem;
  text-align: center;
  width: 25%;
}
/* line 54, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i, .icon-bar > * img {
  display: block;
  margin: 0 auto;
}
/* line 58, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i + label, .icon-bar > * img + label {
  margin-top: .0625rem;
}
/* line 63, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i {
  font-size: 1.875rem;
  vertical-align: middle;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * img {
  height: 1.875rem;
  width: 1.875rem;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.label-right > * i, .icon-bar.label-right > * img {
  display: inline-block;
  margin: 0 .0625rem 0 0;
}
/* line 80, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.label-right > * i + label, .icon-bar.label-right > * img + label {
  margin-top: 0;
}
/* line 85, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.label-right > * label {
  display: inline-block;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.vertical.label-right > * {
  text-align: left;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.vertical, .icon-bar.small-vertical {
  height: 100%;
  width: auto;
}
/* line 96, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.vertical .item, .icon-bar.small-vertical .item {
  float: none;
  margin: auto;
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.medium-vertical {
    height: 100%;
    width: auto;
  }
  /* line 108, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.medium-vertical .item {
    float: none;
    margin: auto;
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 115, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.large-vertical {
    height: 100%;
    width: auto;
  }
  /* line 120, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.large-vertical .item {
    float: none;
    margin: auto;
    width: auto;
  }
}
/* line 137, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * {
  font-size: 1rem;
  padding: 1.25rem;
}
/* line 143, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i + label, .icon-bar > * img + label {
  margin-top: .0625rem;
  font-size: 1rem;
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i {
  font-size: 1.875rem;
}
/* line 153, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * img {
  height: 1.875rem;
  width: 1.875rem;
}
/* line 177, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * label {
  color: #FFFFFF;
}
/* line 179, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > * i {
  color: #FFFFFF;
}
/* line 182, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a:hover {
  background: #008CBA;
}
/* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a:hover label {
  color: #FFFFFF;
}
/* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a:hover i {
  color: #FFFFFF;
}
/* line 191, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a.active {
  background: #008CBA;
}
/* line 195, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a.active label {
  color: #FFFFFF;
}
/* line 197, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar > a.active i {
  color: #FFFFFF;
}
/* line 201, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar .item.disabled {
  cursor: not-allowed;
  opacity: 0.7;
  pointer-events: none;
}
/* line 205, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar .item.disabled > * {
  opacity: 0.7;
  cursor: not-allowed;
}
/* line 246, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.two-up .item {
  width: 50%;
}
/* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.two-up.vertical .item, .icon-bar.two-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 248, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.two-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 253, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.two-up.large-vertical .item {
    width: auto;
  }
}
/* line 260, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.three-up .item {
  width: 33.3333%;
}
/* line 261, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.three-up.vertical .item, .icon-bar.three-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.three-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 267, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.three-up.large-vertical .item {
    width: auto;
  }
}
/* line 274, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.four-up .item {
  width: 25%;
}
/* line 275, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.four-up.vertical .item, .icon-bar.four-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 276, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.four-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.four-up.large-vertical .item {
    width: auto;
  }
}
/* line 288, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.five-up .item {
  width: 20%;
}
/* line 289, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.five-up.vertical .item, .icon-bar.five-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 290, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.five-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 295, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.five-up.large-vertical .item {
    width: auto;
  }
}
/* line 302, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.six-up .item {
  width: 16.66667%;
}
/* line 303, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.six-up.vertical .item, .icon-bar.six-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 304, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.six-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 309, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.six-up.large-vertical .item {
    width: auto;
  }
}
/* line 316, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.seven-up .item {
  width: 14.28571%;
}
/* line 317, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.seven-up.vertical .item, .icon-bar.seven-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 318, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.seven-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 323, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.seven-up.large-vertical .item {
    width: auto;
  }
}
/* line 330, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.eight-up .item {
  width: 12.5%;
}
/* line 331, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.eight-up.vertical .item, .icon-bar.eight-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 332, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.eight-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 337, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.eight-up.large-vertical .item {
    width: auto;
  }
}

/* line 362, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.two-up .item {
  width: 50%;
}
/* line 363, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.two-up.vertical .item, .icon-bar.two-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 364, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.two-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 369, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.two-up.large-vertical .item {
    width: auto;
  }
}
/* line 376, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.three-up .item {
  width: 33.3333%;
}
/* line 377, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.three-up.vertical .item, .icon-bar.three-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 378, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.three-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 383, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.three-up.large-vertical .item {
    width: auto;
  }
}
/* line 390, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.four-up .item {
  width: 25%;
}
/* line 391, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.four-up.vertical .item, .icon-bar.four-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 392, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.four-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 397, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.four-up.large-vertical .item {
    width: auto;
  }
}
/* line 404, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.five-up .item {
  width: 20%;
}
/* line 405, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.five-up.vertical .item, .icon-bar.five-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 406, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.five-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 411, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.five-up.large-vertical .item {
    width: auto;
  }
}
/* line 418, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.six-up .item {
  width: 16.66667%;
}
/* line 419, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.six-up.vertical .item, .icon-bar.six-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 420, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.six-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.six-up.large-vertical .item {
    width: auto;
  }
}
/* line 432, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.seven-up .item {
  width: 14.28571%;
}
/* line 433, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.seven-up.vertical .item, .icon-bar.seven-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 434, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.seven-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 439, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.seven-up.large-vertical .item {
    width: auto;
  }
}
/* line 446, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.eight-up .item {
  width: 12.5%;
}
/* line 447, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
.icon-bar.eight-up.vertical .item, .icon-bar.eight-up.small-vertical .item {
  width: auto;
}
@media only screen and (min-width: 30em) {
  /* line 448, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.eight-up.medium-vertical .item {
    width: auto;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 453, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_icon-bar.scss */
  .icon-bar.eight-up.large-vertical .item {
    width: auto;
  }
}

/* line 53, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_inline-lists.scss */
.inline-list {
  list-style: none;
  margin-left: -1.375rem;
  margin-right: 0;
  margin: 0 auto 1.0625rem auto;
  overflow: hidden;
  padding: 0;
}
/* line 42, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_inline-lists.scss */
.inline-list > li {
  display: block;
  float: left;
  list-style: none;
  margin-left: 1.375rem;
}
/* line 47, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_inline-lists.scss */
.inline-list > li > * {
  display: block;
}

/* Foundation Joyride */
/* line 48, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-list {
  display: none;
}

/* Default styles for the container */
/* line 51, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide {
  background: #333333;
  color: #FFFFFF;
  display: none;
  font-family: inherit;
  font-weight: normal;
  position: absolute;
  top: 0;
  width: 95%;
  z-index: 101;
  left: 2.5%;
}

/* line 64, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.lt-ie9 .joyride-tip-guide {
  margin-left: -400px;
  max-width: 800px;
  left: 50%;
}

/* line 70, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-content-wrapper {
  padding: 1.125rem 1.25rem 1.5rem;
  width: 100%;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-content-wrapper .button {
  margin-bottom: 0 !important;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-content-wrapper .joyride-prev-tip {
  margin-right: 10px;
}

/* Add a little css triangle pip, older browser just miss out on the fanciness of it */
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub {
  border: 10px solid #333333;
  display: block;
  height: 0;
  position: absolute;
  width: 0;
  left: 22px;
}
/* line 89, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub.top {
  border-color: #333333;
  border-top-color: transparent !important;
  border-top-style: solid;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  top: -20px;
}
/* line 97, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub.bottom {
  border-color: #333333 !important;
  border-bottom-color: transparent !important;
  border-bottom-style: solid;
  border-left-color: transparent !important;
  border-right-color: transparent !important;
  bottom: -20px;
}
/* line 106, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub.right {
  right: -20px;
}
/* line 107, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide .joyride-nub.left {
  left: -20px;
}

/* Typography */
/* line 112, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide h1,
.joyride-tip-guide h2,
.joyride-tip-guide h3,
.joyride-tip-guide h4,
.joyride-tip-guide h5,
.joyride-tip-guide h6 {
  color: #FFFFFF;
  font-weight: bold;
  line-height: 1.25;
  margin: 0;
}

/* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-tip-guide p {
  font-size: 0.875rem;
  line-height: 1.3;
  margin: 0 0 1.125rem 0;
}

/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-timer-indicator-wrap {
  border: solid 1px #555555;
  bottom: 1rem;
  height: 3px;
  position: absolute;
  width: 50px;
  right: 1.0625rem;
}

/* line 137, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-timer-indicator {
  background: #666666;
  display: block;
  height: inherit;
  width: 0;
}

/* line 144, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-close-tip {
  color: #777777 !important;
  font-size: 24px;
  font-weight: normal;
  line-height: .5 !important;
  position: absolute;
  text-decoration: none;
  top: 10px;
  right: 12px;
}
/* line 154, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-close-tip:hover, .joyride-close-tip:focus {
  color: #EEEEEE !important;
}

/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-modal-bg {
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  display: none;
  height: 100%;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 100;
  left: 0;
}

/* line 170, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-expose-wrapper {
  background-color: #FFFFFF;
  border-radius: 3px;
  box-shadow: 0 0 15px #FFFFFF;
  position: absolute;
  z-index: 102;
}

/* line 178, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
.joyride-expose-cover {
  background: transparent;
  border-radius: 3px;
  left: 0;
  position: absolute;
  top: 0;
  z-index: 9999;
}

/* Styles for screens that are at least 768px; */
@media only screen and (min-width: 30em) {
  /* line 190, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
  .joyride-tip-guide {
    width: 300px;
    left: inherit;
  }
  /* line 192, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.bottom {
    border-color: #333333 !important;
    border-bottom-color: transparent !important;
    border-left-color: transparent !important;
    border-right-color: transparent !important;
    bottom: -20px;
  }
  /* line 199, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.right {
    border-color: #333333 !important;
    border-right-color: transparent !important;
    border-bottom-color: transparent !important;
    border-top-color: transparent !important;
    left: auto;
    right: -20px;
    top: 22px;
  }
  /* line 207, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_joyride.scss */
  .joyride-tip-guide .joyride-nub.left {
    border-color: #333333 !important;
    border-bottom-color: transparent !important;
    border-left-color: transparent !important;
    border-top-color: transparent !important;
    left: -20px;
    right: auto;
    top: 22px;
  }
}
/* line 54, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_keystrokes.scss */
.keystroke,
kbd {
  background-color: #ededed;
  border-color: #dddddd;
  color: #222222;
  border-style: solid;
  border-width: 1px;
  font-family: "Consolas", "Menlo", "Courier", monospace;
  font-size: inherit;
  margin: 0;
  padding: 0.125rem 0.25rem 0;
  border-radius: 3px;
}

/* line 91, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label {
  display: inline-block;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
  line-height: 1;
  margin-bottom: auto;
  position: relative;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  padding: 0.25rem 0.5rem 0.25rem;
  font-size: 0.6875rem;
  background-color: #008CBA;
  color: #FFFFFF;
}
/* line 96, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.radius {
  border-radius: 3px;
}
/* line 97, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.round {
  border-radius: 1000px;
}
/* line 99, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.alert {
  background-color: #f04124;
  color: #FFFFFF;
}
/* line 100, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.warning {
  background-color: #f08a24;
  color: #FFFFFF;
}
/* line 101, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.success {
  background-color: #43AC6A;
  color: #FFFFFF;
}
/* line 102, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.secondary {
  background-color: #e7e7e7;
  color: #333333;
}
/* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_labels.scss */
.label.info {
  background-color: #a0d3e8;
  color: #333333;
}

/* line 18, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_magellan.scss */
[data-magellan-expedition], [data-magellan-expedition-clone] {
  background: #FFFFFF;
  min-width: 100%;
  padding: 10px;
  z-index: 50;
}
/* line 24, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_magellan.scss */
[data-magellan-expedition] .sub-nav, [data-magellan-expedition-clone] .sub-nav {
  margin-bottom: 0;
}
/* line 26, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_magellan.scss */
[data-magellan-expedition] .sub-nav dd, [data-magellan-expedition-clone] .sub-nav dd {
  margin-bottom: 0;
}
/* line 27, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_magellan.scss */
[data-magellan-expedition] .sub-nav a, [data-magellan-expedition-clone] .sub-nav a {
  line-height: 1.8em;
}

@-webkit-keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotate {
  from {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -ms-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/* Orbit Graceful Loading */
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper {
  position: relative;
}
/* line 84, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper ul {
  list-style-type: none;
  margin: 0;
}
/* line 90, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper ul li,
.slideshow-wrapper ul li .orbit-caption {
  display: none;
}
/* line 94, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper ul li:first-child {
  display: block;
}
/* line 97, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper .orbit-container {
  background-color: transparent;
}
/* line 100, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper .orbit-container li {
  display: block;
}
/* line 102, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper .orbit-container li .orbit-caption {
  display: block;
}
/* line 104, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper .orbit-container .orbit-bullets li {
  display: inline-block;
}
/* line 110, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.slideshow-wrapper .preloader {
  border-radius: 1000px;
  animation-duration: 1.5s;
  animation-iteration-count: infinite;
  animation-name: rotate;
  animation-timing-function: linear;
  border-color: #555555 #FFFFFF;
  border: solid 3px;
  display: block;
  height: 40px;
  left: 50%;
  margin-left: -20px;
  margin-top: -20px;
  position: absolute;
  top: 50%;
  width: 40px;
}

/* line 130, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container {
  background: none;
  overflow: hidden;
  position: relative;
  width: 100%;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slides-container {
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slides-container img {
  display: block;
  max-width: 100%;
}
/* line 151, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slides-container > * {
  position: absolute;
  top: 0;
  width: 100%;
  margin-left: 100%;
}
/* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slides-container > *:first-child {
  margin-left: 0;
}
/* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slides-container > * .orbit-caption {
  bottom: 0;
  position: absolute;
  background-color: rgba(51, 51, 51, 0.8);
  color: #FFFFFF;
  font-size: 0.875rem;
  padding: 0.625rem 0.875rem;
  width: 100%;
}
/* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slide-number {
  left: 10px;
  background: transparent;
  color: #FFFFFF;
  font-size: 12px;
  position: absolute;
  top: 10px;
  z-index: 10;
}
/* line 194, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-slide-number span {
  font-weight: 700;
  padding: 0.3125rem;
}
/* line 199, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-timer {
  position: absolute;
  top: 12px;
  right: 10px;
  height: 6px;
  width: 100px;
  z-index: 10;
}
/* line 209, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-timer .orbit-progress {
  height: 3px;
  background-color: rgba(255, 255, 255, 0.3);
  display: block;
  width: 0;
  position: relative;
  right: 20px;
  top: 5px;
}
/* line 223, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-timer > span {
  border: solid 4px #FFFFFF;
  border-bottom: none;
  border-top: none;
  display: none;
  height: 14px;
  position: absolute;
  top: 0;
  width: 11px;
  right: 0;
}
/* line 237, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-timer.paused > span {
  top: 0;
  width: 11px;
  height: 14px;
  border: inset 8px;
  border-left-style: solid;
  border-color: transparent;
  border-left-color: #FFFFFF;
  right: -4px;
}
/* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-timer.paused > span.dark {
  border-left-color: #333333;
}
/* line 256, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container:hover .orbit-timer > span {
  display: block;
}
/* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev,
.orbit-container .orbit-next {
  background-color: transparent;
  color: white;
  height: 60px;
  line-height: 50px;
  margin-top: -25px;
  position: absolute;
  text-indent: -9999px !important;
  top: 45%;
  width: 36px;
  z-index: 10;
}
/* line 272, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev:hover,
.orbit-container .orbit-next:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
/* line 276, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev > span,
.orbit-container .orbit-next > span {
  border: inset 10px;
  display: block;
  height: 0;
  margin-top: -10px;
  position: absolute;
  top: 50%;
  width: 0;
}
/* line 286, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev {
  left: 0;
}
/* line 287, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev > span {
  border-right-style: solid;
  border-color: transparent;
  border-right-color: #FFFFFF;
}
/* line 292, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-prev:hover > span {
  border-right-color: #FFFFFF;
}
/* line 296, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-next {
  right: 0;
}
/* line 297, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-next > span {
  border-color: transparent;
  border-left-style: solid;
  border-left-color: #FFFFFF;
  left: 50%;
  margin-left: -4px;
}
/* line 304, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-container .orbit-next:hover > span {
  border-left-color: #FFFFFF;
}

/* line 310, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-bullets-container {
  text-align: center;
}

/* line 311, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-bullets {
  display: block;
  float: none;
  margin: 0 auto 30px auto;
  overflow: hidden;
  position: relative;
  text-align: center;
  top: 10px;
}
/* line 320, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-bullets li {
  background: #CCCCCC;
  cursor: pointer;
  display: inline-block;
  float: none;
  height: 0.5625rem;
  margin-right: 6px;
  width: 0.5625rem;
  border-radius: 1000px;
}
/* line 332, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-bullets li.active {
  background: #999999;
}
/* line 336, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.orbit-bullets li:last-child {
  margin-right: 0;
}

/* line 342, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.touch .orbit-container .orbit-prev,
.touch .orbit-container .orbit-next {
  display: none;
}
/* line 346, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
.touch .orbit-bullets {
  display: none;
}

@media only screen and (min-width: 30em) {
  /* line 354, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .touch .orbit-container .orbit-prev,
  .touch .orbit-container .orbit-next {
    display: inherit;
  }
  /* line 358, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .touch .orbit-bullets {
    display: block;
  }
}
@media only screen and (max-width: 29.9375em) {
  /* line 365, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-slides-container {
    height: auto !important;
  }
  /* line 366, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-slides-container > * {
    margin: 0  !important;
    opacity: 1 !important;
    position: relative;
  }
  /* line 372, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-stack-on-small .orbit-slide-number {
    display: none;
  }

  /* line 378, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-timer {
    display: none;
  }

  /* line 381, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-next, .orbit-prev {
    display: none;
  }

  /* line 384, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_orbit.scss */
  .orbit-bullets {
    display: none;
  }
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination {
  display: block;
  margin-left: -0.3125rem;
  min-height: 1.5rem;
}
/* line 104, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li {
  color: #222222;
  font-size: 0.875rem;
  height: 1.5rem;
  margin-left: 0.3125rem;
}
/* line 110, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li a, ul.pagination li button {
  border-radius: 3px;
  transition: background-color 300ms ease-out;
  background: none;
  color: #999999;
  display: block;
  font-size: 1em;
  font-weight: normal;
  line-height: inherit;
  padding: 0.0625rem 0.625rem 0.0625rem;
}
/* line 122, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li:hover a,
ul.pagination li a:focus, ul.pagination li:hover button,
ul.pagination li button:focus {
  background: #e6e6e6;
}
/* line 51, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li.unavailable a, ul.pagination li.unavailable button {
  cursor: default;
  color: #999999;
}
/* line 55, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li.unavailable:hover a, ul.pagination li.unavailable a:focus, ul.pagination li.unavailable:hover button, ul.pagination li.unavailable button:focus {
  background: transparent;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li.current a, ul.pagination li.current button {
  background: #008CBA;
  color: #FFFFFF;
  cursor: default;
  font-weight: bold;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li.current a:hover, ul.pagination li.current a:focus, ul.pagination li.current button:hover, ul.pagination li.current button:focus {
  background: #008CBA;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
ul.pagination li {
  display: block;
  float: left;
}

/* Pagination centred wrapper */
/* line 154, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
.pagination-centered {
  text-align: center;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pagination.scss */
.pagination-centered ul.pagination li {
  display: inline-block;
  float: none;
}

/* Panels */
/* line 86, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel {
  border-style: solid;
  border-width: 1px;
  border-color: #d8d8d8;
  margin-bottom: 1.25rem;
  padding: 1.25rem;
  background: #f2f2f2;
  color: #333333;
}
/* line 61, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel > :first-child {
  margin-top: 0;
}
/* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel > :last-child {
  margin-bottom: 0;
}
/* line 67, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel h1, .panel h2, .panel h3, .panel h4, .panel h5, .panel h6, .panel p, .panel li, .panel dl {
  color: #333333;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel h1, .panel h2, .panel h3, .panel h4, .panel h5, .panel h6 {
  line-height: 1;
  margin-bottom: 0.625rem;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel h1.subheader, .panel h2.subheader, .panel h3.subheader, .panel h4.subheader, .panel h5.subheader, .panel h6.subheader {
  line-height: 1.4;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout {
  border-style: solid;
  border-width: 1px;
  border-color: #d8d8d8;
  margin-bottom: 1.25rem;
  padding: 1.25rem;
  background: #ecfaff;
  color: #333333;
}
/* line 61, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout > :first-child {
  margin-top: 0;
}
/* line 62, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout > :last-child {
  margin-bottom: 0;
}
/* line 67, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout h1, .panel.callout h2, .panel.callout h3, .panel.callout h4, .panel.callout h5, .panel.callout h6, .panel.callout p, .panel.callout li, .panel.callout dl {
  color: #333333;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout h1, .panel.callout h2, .panel.callout h3, .panel.callout h4, .panel.callout h5, .panel.callout h6 {
  line-height: 1;
  margin-bottom: 0.625rem;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout h1.subheader, .panel.callout h2.subheader, .panel.callout h3.subheader, .panel.callout h4.subheader, .panel.callout h5.subheader, .panel.callout h6.subheader {
  line-height: 1.4;
}
/* line 90, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout a:not(.button) {
  color: #008CBA;
}
/* line 93, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.callout a:not(.button):hover, .panel.callout a:not(.button):focus {
  color: #0078a0;
}
/* line 100, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_panels.scss */
.panel.radius {
  border-radius: 3px;
}

/* Pricing Tables */
/* line 139, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table {
  border: solid 1px #DDDDDD;
  margin-left: 0;
  margin-bottom: 1.25rem;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table * {
  list-style: none;
  line-height: 1;
}
/* line 142, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table .title {
  background-color: #333333;
  color: #EEEEEE;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 1rem;
  font-weight: normal;
  padding: 0.9375rem 1.25rem;
  text-align: center;
}
/* line 143, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table .price {
  background-color: #F6F6F6;
  color: #333333;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 2rem;
  font-weight: normal;
  padding: 0.9375rem 1.25rem;
  text-align: center;
}
/* line 144, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table .description {
  background-color: #FFFFFF;
  border-bottom: dotted 1px #DDDDDD;
  color: #777777;
  font-size: 0.75rem;
  font-weight: normal;
  line-height: 1.4;
  padding: 0.9375rem;
  text-align: center;
}
/* line 145, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table .bullet-item {
  background-color: #FFFFFF;
  border-bottom: dotted 1px #DDDDDD;
  color: #333333;
  font-size: 0.875rem;
  font-weight: normal;
  padding: 0.9375rem;
  text-align: center;
}
/* line 146, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_pricing-tables.scss */
.pricing-table .cta-button {
  background-color: #FFFFFF;
  padding: 1.25rem 1.25rem 0;
  text-align: center;
}

/* Progress Bar */
/* line 57, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress {
  background-color: #F6F6F6;
  border: 1px solid white;
  height: 1.5625rem;
  margin-bottom: 0.625rem;
  padding: 0.125rem;
}
/* line 61, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress .meter {
  background: #008CBA;
  display: block;
  height: 100%;
}
/* line 64, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.secondary .meter {
  background: #e7e7e7;
  display: block;
  height: 100%;
}
/* line 65, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.success .meter {
  background: #43AC6A;
  display: block;
  height: 100%;
}
/* line 66, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.alert .meter {
  background: #f04124;
  display: block;
  height: 100%;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.radius {
  border-radius: 3px;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.radius .meter {
  border-radius: 2px;
}
/* line 72, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.round {
  border-radius: 1000px;
}
/* line 73, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_progress-bars.scss */
.progress.round .meter {
  border-radius: 999px;
}

/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider {
  border: 1px solid #DDDDDD;
  margin: 1.25rem 0;
  position: relative;
  -ms-touch-action: none;
  touch-action: none;
  display: block;
  height: 1rem;
  width: 100%;
  background: #FAFAFA;
}
/* line 134, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.vertical-range {
  border: 1px solid #DDDDDD;
  margin: 1.25rem 0;
  position: relative;
  -ms-touch-action: none;
  touch-action: none;
  display: inline-block;
  height: 12.5rem;
  width: 1rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.vertical-range .range-slider-handle {
  bottom: -10.5rem;
  margin-left: -0.5rem;
  margin-top: 0;
  position: absolute;
}
/* line 142, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.vertical-range .range-slider-active-segment {
  border-bottom-left-radius: inherit;
  border-bottom-right-radius: inherit;
  border-top-left-radius: initial;
  bottom: 0;
  height: auto;
  width: 0.875rem;
}
/* line 151, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.radius {
  background: #FAFAFA;
  border-radius: 3px;
}
/* line 153, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.radius .range-slider-handle {
  background: #008CBA;
  border-radius: 3px;
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.radius .range-slider-handle:hover {
  background: #007ba4;
}
/* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.round {
  background: #FAFAFA;
  border-radius: 1000px;
}
/* line 157, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.round .range-slider-handle {
  background: #008CBA;
  border-radius: 1000px;
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.round .range-slider-handle:hover {
  background: #007ba4;
}
/* line 159, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.disabled, .range-slider[disabled] {
  background: #FAFAFA;
  cursor: not-allowed;
  opacity: 0.7;
}
/* line 161, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.disabled .range-slider-handle, .range-slider[disabled] .range-slider-handle {
  background: #008CBA;
  cursor: default;
  opacity: 0.7;
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider.disabled .range-slider-handle:hover, .range-slider[disabled] .range-slider-handle:hover {
  background: #007ba4;
}

/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider-active-segment {
  background: #e5e5e5;
  border-bottom-left-radius: inherit;
  border-top-left-radius: inherit;
  display: inline-block;
  height: 0.875rem;
  position: absolute;
}

/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider-handle {
  border: 1px solid none;
  cursor: pointer;
  display: inline-block;
  height: 1.375rem;
  position: absolute;
  top: -0.3125rem;
  width: 2rem;
  z-index: 1;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  background: #008CBA;
}
/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_range-slider.scss */
.range-slider-handle:hover {
  background: #007ba4;
}

/* line 169, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal-bg {
  background: #000000;
  background: rgba(0, 0, 0, 0.45);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1004;
  left: 0;
}

/* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal {
  border-radius: 3px;
  display: none;
  position: absolute;
  top: 0;
  visibility: hidden;
  width: 100%;
  z-index: 1005;
  left: 0;
  background-color: #FFFFFF;
  padding: 1.875rem;
  border: solid 1px #666666;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.4);
}
@media only screen and (max-width: 29.9375em) {
  /* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal {
    min-height: 100vh;
  }
}
/* line 89, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal .column, .reveal-modal .columns {
  min-width: 0;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal > :first-child {
  margin-top: 0;
}
/* line 94, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal > :last-child {
  margin-bottom: 0;
}
@media only screen and (min-width: 30em) {
  /* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 80%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 171, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal {
    top: 6.25rem;
  }
}
/* line 182, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal.radius {
  border-radius: 3px;
}
/* line 183, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal.round {
  border-radius: 1000px;
}
/* line 184, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal.collapse {
  padding: 0;
}
@media only screen and (min-width: 30em) {
  /* line 185, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.tiny {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 30%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.small {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 40%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 187, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.medium {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 60%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.large {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 70%;
  }
}
@media only screen and (min-width: 30em) {
  /* line 189, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.xlarge {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 95%;
  }
}
/* line 190, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal.full {
  height: 100vh;
  height: 100%;
  left: 0;
  margin-left: 0 !important;
  max-width: none !important;
  min-height: 100vh;
  top: 0;
}
@media only screen and (min-width: 30em) {
  /* line 190, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
  .reveal-modal.full {
    left: 0;
    margin: 0 auto;
    max-width: 62.5rem;
    right: 0;
    width: 100%;
  }
}
/* line 202, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal.toback {
  z-index: 1003;
}
/* line 206, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_reveal.scss */
.reveal-modal .close-reveal-modal {
  color: #AAAAAA;
  cursor: pointer;
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  position: absolute;
  top: 0.625rem;
  right: 1.375rem;
}

/* line 118, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav {
  display: block;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  list-style-position: outside;
  list-style-type: none;
  margin: 0;
  padding: 0.875rem 0;
}
/* line 71, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li {
  font-size: 0.875rem;
  font-weight: normal;
  margin: 0 0 0.4375rem 0;
}
/* line 76, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li a:not(.button) {
  color: #008CBA;
  display: block;
  margin: 0;
  padding: 0.4375rem 0.875rem;
}
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li a:not(.button):hover, .side-nav li a:not(.button):focus {
  background: rgba(0, 0, 0, 0.025);
  color: #1cc7ff;
}
/* line 86, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li a:not(.button):active {
  color: #1cc7ff;
}
/* line 91, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li.active > a:first-child:not(.button) {
  color: #1cc7ff;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-weight: normal;
}
/* line 97, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li.divider {
  border-top: 1px solid;
  height: 0;
  list-style: none;
  padding: 0;
  border-top-color: #e6e6e6;
}
/* line 105, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_side-nav.scss */
.side-nav li.heading {
  color: #008CBA;
  font-size: 0.875rem;
  font-weight: bold;
  text-transform: uppercase;
}

/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button {
  position: relative;
  padding-right: 5.0625rem;
}
/* line 77, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span {
  display: block;
  height: 100%;
  position: absolute;
  right: 0;
  top: 0;
  border-left: solid 1px;
}
/* line 86, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span:after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  display: block;
  border-style: inset;
  top: 50%;
  left: 50%;
}
/* line 97, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span:active {
  background-color: rgba(0, 0, 0, 0.1);
}
/* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span {
  border-left-color: rgba(255, 255, 255, 0.5);
}
/* line 140, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span {
  width: 3.09375rem;
}
/* line 141, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span:after {
  border-top-style: solid;
  border-width: 0.375rem;
  margin-left: -0.375rem;
  top: 48%;
}
/* line 166, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button span:after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.secondary span {
  border-left-color: rgba(255, 255, 255, 0.5);
}
/* line 166, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.secondary span:after {
  border-color: #FFFFFF transparent transparent transparent;
}
/* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.alert span {
  border-left-color: rgba(255, 255, 255, 0.5);
}
/* line 103, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.success span {
  border-left-color: rgba(255, 255, 255, 0.5);
}
/* line 179, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.tiny {
  padding-right: 3.75rem;
}
/* line 112, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.tiny span {
  width: 2.25rem;
}
/* line 113, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.tiny span:after {
  border-top-style: solid;
  border-width: 0.375rem;
  margin-left: -0.375rem;
  top: 48%;
}
/* line 180, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.small {
  padding-right: 4.375rem;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.small span {
  width: 2.625rem;
}
/* line 127, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.small span:after {
  border-top-style: solid;
  border-width: 0.4375rem;
  margin-left: -0.375rem;
  top: 48%;
}
/* line 181, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.large {
  padding-right: 5.5rem;
}
/* line 154, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.large span {
  width: 3.4375rem;
}
/* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.large span:after {
  border-top-style: solid;
  border-width: 0.3125rem;
  margin-left: -0.375rem;
  top: 48%;
}
/* line 182, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.expand {
  padding-left: 2rem;
}
/* line 166, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.secondary span:after {
  border-color: #333333 transparent transparent transparent;
}
/* line 186, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.radius span {
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 187, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.round span {
  -webkit-border-bottom-right-radius: 1000px;
  -webkit-border-top-right-radius: 1000px;
  border-bottom-right-radius: 1000px;
  border-top-right-radius: 1000px;
}
/* line 189, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.no-pip span:before {
  border-style: none;
}
/* line 190, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.no-pip span:after {
  border-style: none;
}
/* line 191, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_split-buttons.scss */
.split.button.no-pip span > i {
  display: block;
  left: 50%;
  margin-left: -0.28889em;
  margin-top: -0.48889em;
  position: absolute;
  top: 50%;
}

/* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav {
  display: block;
  margin: -0.25rem 0 1.125rem;
  overflow: hidden;
  padding-top: 0.25rem;
  width: auto;
}
/* line 67, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt {
  text-transform: uppercase;
}
/* line 71, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt,
.sub-nav dd,
.sub-nav li {
  color: #999999;
  float: left;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 0.875rem;
  font-weight: normal;
  margin-left: 1rem;
  margin-bottom: 0;
}
/* line 82, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt a,
.sub-nav dd a,
.sub-nav li a {
  color: #999999;
  padding: 0.1875rem 1rem;
  text-decoration: none;
}
/* line 87, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt a:hover,
.sub-nav dd a:hover,
.sub-nav li a:hover {
  color: #737373;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt.active a,
.sub-nav dd.active a,
.sub-nav li.active a {
  border-radius: 3px;
  background: #008CBA;
  color: #FFFFFF;
  cursor: default;
  font-weight: normal;
  padding: 0.1875rem 1rem;
}
/* line 100, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_sub-nav.scss */
.sub-nav dt.active a:hover,
.sub-nav dd.active a:hover,
.sub-nav li.active a:hover {
  background: #0078a0;
}

/* line 215, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch {
  border: none;
  margin-bottom: 1.5rem;
  outline: 0;
  padding: 0;
  position: relative;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/* line 58, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label {
  background: #DDDDDD;
  color: transparent;
  cursor: pointer;
  display: block;
  margin-bottom: 1rem;
  position: relative;
  text-indent: 100%;
  width: 4rem;
  height: 2rem;
  transition: left 0.15s ease-out;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input {
  left: 10px;
  opacity: 0;
  padding: 0;
  position: absolute;
  top: 9px;
}
/* line 81, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input + label {
  margin-left: 0;
  margin-right: 0;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label:after {
  background: #FFFFFF;
  content: "";
  display: block;
  height: 1.5rem;
  left: .25rem;
  position: absolute;
  top: .25rem;
  width: 1.5rem;
  -webkit-transition: left 0.15s ease-out;
  -moz-transition: left 0.15s ease-out;
  -o-transition: translate3d(0, 0, 0);
  transition: left 0.15s ease-out;
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}
/* line 110, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input:checked + label {
  background: #008CBA;
}
/* line 114, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input:checked + label:after {
  left: 2.25rem;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label {
  height: 2rem;
  width: 4rem;
}
/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label:after {
  height: 1.5rem;
  width: 1.5rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input:checked + label:after {
  left: 2.25rem;
}
/* line 157, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label {
  color: transparent;
  background: #DDDDDD;
}
/* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch label:after {
  background: #FFFFFF;
}
/* line 166, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch input:checked + label {
  background: #008CBA;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.large label {
  height: 2.5rem;
  width: 5rem;
}
/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.large label:after {
  height: 2rem;
  width: 2rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.large input:checked + label:after {
  left: 2.75rem;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.small label {
  height: 1.75rem;
  width: 3.5rem;
}
/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.small label:after {
  height: 1.25rem;
  width: 1.25rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.small input:checked + label:after {
  left: 2rem;
}
/* line 126, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.tiny label {
  height: 1.5rem;
  width: 3rem;
}
/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.tiny label:after {
  height: 1rem;
  width: 1rem;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.tiny input:checked + label:after {
  left: 1.75rem;
}
/* line 229, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.radius label {
  border-radius: 4px;
}
/* line 230, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.radius label:after {
  border-radius: 3px;
}
/* line 234, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.round {
  border-radius: 1000px;
}
/* line 235, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.round label {
  border-radius: 2rem;
}
/* line 236, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_switches.scss */
.switch.round label:after {
  border-radius: 2rem;
}

/* line 131, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table {
  background: #FFFFFF;
  border: solid 1px #DDDDDD;
  margin-bottom: 1.25rem;
  table-layout: auto;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table caption {
  background: transparent;
  color: #222222;
  font-size: 1rem;
  font-weight: bold;
}
/* line 78, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table thead {
  background: #F5F5F5;
}
/* line 82, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table thead tr th,
table thead tr td {
  color: #222222;
  font-size: 0.875rem;
  font-weight: bold;
  padding: 0.5rem 0.625rem 0.625rem;
}
/* line 92, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table tfoot {
  background: #F5F5F5;
}
/* line 96, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table tfoot tr th,
table tfoot tr td {
  color: #222222;
  font-size: 0.875rem;
  font-weight: bold;
  padding: 0.5rem 0.625rem 0.625rem;
}
/* line 107, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table tr th,
table tr td {
  color: #222222;
  font-size: 0.875rem;
  padding: 0.5625rem 0.625rem;
  text-align: left;
}
/* line 115, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table tr.even, table tr.alt, table tr:nth-of-type(even) {
  background: #F9F9F9;
}
/* line 120, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tables.scss */
table thead tr th,
table tfoot tr th,
table tfoot tr td,
table tbody tr th,
table tbody tr td,
table tr td {
  display: table-cell;
  line-height: 1.125rem;
}

/* line 30, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs {
  margin-bottom: 0 !important;
  margin-left: 0;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tabs:before, .tabs:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tabs:after {
  clear: both;
}
/* line 35, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs dd,
.tabs .tab-title {
  float: left;
  list-style: none;
  margin-bottom: 0 !important;
  position: relative;
}
/* line 42, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs dd > a,
.tabs .tab-title > a {
  display: block;
  background-color: #EFEFEF;
  color: #222222;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 1rem;
  padding: 1rem 2rem;
}
/* line 50, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs dd > a:hover,
.tabs .tab-title > a:hover {
  background-color: #e1e1e1;
}
/* line 55, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs dd.active a,
.tabs .tab-title.active a {
  background-color: #FFFFFF;
  color: #222222;
}
/* line 64, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs.radius dd:first-child a,
.tabs.radius .tab:first-child a {
  -webkit-border-bottom-left-radius: 3px;
  -webkit-border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
/* line 69, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs.radius dd:last-child a,
.tabs.radius .tab:last-child a {
  -webkit-border-bottom-right-radius: 3px;
  -webkit-border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
/* line 74, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs.vertical dd,
.tabs.vertical .tab-title {
  position: inherit;
  float: none;
  display: block;
  top: auto;
}

/* line 84, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content {
  margin-bottom: 1.5rem;
  width: 100%;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tabs-content:before, .tabs-content:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tabs-content:after {
  clear: both;
}
/* line 89, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content > .content {
  display: none;
  float: left;
  padding: 0.9375rem 0;
  width: 100%;
}
/* line 95, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content > .content.active {
  display: block;
  float: none;
}
/* line 99, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content > .content.contained {
  padding: 0.9375rem;
}
/* line 104, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content.vertical {
  display: block;
}
/* line 107, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.tabs-content.vertical > .content {
  padding: 0 0.9375rem;
}

@media only screen and (min-width: 30em) {
  /* line 115, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
  .tabs.vertical {
    float: left;
    margin: 0;
    margin-bottom: 1.25rem !important;
    max-width: 20%;
    width: 20%;
  }

  /* line 125, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
  .tabs-content.vertical {
    float: left;
    margin-left: -1px;
    max-width: 80%;
    padding-left: 1rem;
    width: 80%;
  }
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tabs.scss */
.no-js .tabs-content > .content {
  display: block;
  float: none;
}

/* Image Thumbnails */
/* line 59, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_thumbs.scss */
.th {
  border: solid 4px #FFFFFF;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.2);
  display: inline-block;
  line-height: 0;
  max-width: 100%;
  transition: all 200ms ease-out;
}
/* line 48, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_thumbs.scss */
.th:hover, .th:focus {
  box-shadow: 0 0 6px 1px rgba(0, 140, 186, 0.5);
}
/* line 63, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_thumbs.scss */
.th.radius {
  border-radius: 3px;
}

/* Tooltips */
/* line 38, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.has-tip {
  border-bottom: dotted 1px #CCCCCC;
  color: #333333;
  cursor: help;
  font-weight: bold;
}
/* line 44, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.has-tip:hover, .has-tip:focus {
  border-bottom: dotted 1px #003f54;
  color: #008CBA;
}
/* line 50, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.has-tip.tip-left, .has-tip.tip-right {
  float: none !important;
}

/* line 54, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip {
  background: #333333;
  color: #FFFFFF;
  display: none;
  font-size: 0.875rem;
  font-weight: normal;
  line-height: 1.3;
  max-width: 300px;
  padding: 0.75rem;
  position: absolute;
  width: 100%;
  z-index: 1006;
  left: 50%;
}
/* line 68, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip > .nub {
  border-color: transparent transparent #333333 transparent;
  border: solid 5px;
  display: block;
  height: 0;
  pointer-events: none;
  position: absolute;
  top: -10px;
  width: 0;
  left: 5px;
}
/* line 79, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip > .nub.rtl {
  left: auto;
  right: 5px;
}
/* line 85, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip.radius {
  border-radius: 3px;
}
/* line 88, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip.round {
  border-radius: 1000px;
}
/* line 90, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip.round > .nub {
  left: 2rem;
}
/* line 95, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tooltip.opened {
  border-bottom: dotted 1px #003f54 !important;
  color: #008CBA !important;
}

/* line 101, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
.tap-to-close {
  color: #777777;
  display: block;
  font-size: 0.625rem;
  font-weight: normal;
}

@media only screen and (min-width: 30em) {
  /* line 110, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
  .tooltip > .nub {
    border-color: transparent transparent #333333 transparent;
    top: -10px;
  }
  /* line 114, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
  .tooltip.tip-top > .nub {
    border-color: #333333 transparent transparent transparent;
    bottom: -10px;
    top: auto;
  }
  /* line 120, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
  .tooltip.tip-left, .tooltip.tip-right {
    float: none !important;
  }
  /* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
  .tooltip.tip-left > .nub {
    border-color: transparent transparent transparent #333333;
    left: auto;
    margin-top: -5px;
    right: -10px;
    top: 50%;
  }
  /* line 130, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_tooltips.scss */
  .tooltip.tip-right > .nub {
    border-color: transparent #333333 transparent transparent;
    left: -10px;
    margin-top: -5px;
    right: auto;
    top: 50%;
  }
}
/* line 113, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
meta.foundation-mq-topbar {
  font-family: "/only screen and (min-width:30em)/";
  width: 30em;
}

/* Wrapped around .top-bar to contain to grid width */
/* line 119, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.contain-to-grid {
  width: 100%;
  background: #333333;
}
/* line 123, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.contain-to-grid .top-bar {
  margin-bottom: 0;
}

/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.fixed {
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 99;
  left: 0;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.fixed.expanded:not(.top-bar) {
  height: auto;
  max-height: 100%;
  overflow-y: auto;
  width: 100%;
}
/* line 142, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.fixed.expanded:not(.top-bar) .title-area {
  position: fixed;
  width: 100%;
  z-index: 99;
}
/* line 149, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.fixed.expanded:not(.top-bar) .top-bar-section {
  margin-top: 2.8125rem;
  z-index: 98;
}

/* line 156, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar {
  background: #333333;
  height: 2.8125rem;
  line-height: 2.8125rem;
  margin-bottom: 0;
  overflow: hidden;
  position: relative;
}
/* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar ul {
  list-style: none;
  margin-bottom: 0;
}
/* line 170, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .row {
  max-width: none;
}
/* line 174, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar form,
.top-bar input,
.top-bar select {
  margin-bottom: 0;
}
/* line 180, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar input,
.top-bar select {
  font-size: 0.75rem;
  height: 1.75rem;
  padding-bottom: .35rem;
  padding-top: .35rem;
}
/* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .button, .top-bar button {
  font-size: 0.75rem;
  margin-bottom: 0;
  padding-bottom: 0.4125rem;
  padding-top: 0.4125rem;
}
@media only screen and (max-width: 29.9375em) {
  /* line 188, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar .button, .top-bar button {
    position: relative;
    top: -1px;
  }
}
/* line 204, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .title-area {
  margin: 0;
  position: relative;
}
/* line 209, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .name {
  font-size: 16px;
  height: 2.8125rem;
  margin: 0;
}
/* line 214, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .name h1, .top-bar .name h2, .top-bar .name h3, .top-bar .name h4, .top-bar .name p, .top-bar .name span {
  font-size: 1.0625rem;
  line-height: 2.8125rem;
  margin: 0;
}
/* line 219, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .name h1 a, .top-bar .name h2 a, .top-bar .name h3 a, .top-bar .name h4 a, .top-bar .name p a, .top-bar .name span a {
  color: #FFFFFF;
  display: block;
  font-weight: normal;
  padding: 0 0.9375rem;
  width: 75%;
}
/* line 230, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .toggle-topbar {
  position: absolute;
  right: 0;
  top: 0;
}
/* line 235, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .toggle-topbar a {
  color: #FFFFFF;
  display: block;
  font-size: 0.8125rem;
  font-weight: bold;
  height: 2.8125rem;
  line-height: 2.8125rem;
  padding: 0 0.9375rem;
  position: relative;
  text-transform: uppercase;
}
/* line 248, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .toggle-topbar.menu-icon {
  margin-top: -16px;
  top: 50%;
}
/* line 252, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar .toggle-topbar.menu-icon a {
  color: #FFFFFF;
  height: 34px;
  line-height: 33px;
  padding: 0 2.5rem 0 0.9375rem;
  position: relative;
}
/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.top-bar .toggle-topbar.menu-icon a span::after {
  content: "";
  display: block;
  height: 0;
  position: absolute;
  margin-top: -8px;
  top: 50%;
  right: 0.9375rem;
  box-shadow: 0 0 0 1px #FFFFFF, 0 7px 0 1px #FFFFFF, 0 14px 0 1px #FFFFFF;
  width: 16px;
}
/* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.top-bar .toggle-topbar.menu-icon a span:hover:after {
  box-shadow: 0 0 0 1px "", 0 7px 0 1px "", 0 14px 0 1px "";
}
/* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar.expanded {
  background: transparent;
  height: auto;
}
/* line 272, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar.expanded .title-area {
  background: #333333;
}
/* line 277, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar.expanded .toggle-topbar a {
  color: #888888;
}
/* line 280, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar.expanded .toggle-topbar a span::after {
  box-shadow: 0 0 0 1px #888888, 0 7px 0 1px #888888, 0 14px 0 1px #888888;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  /* line 293, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar.expanded .top-bar-section .has-dropdown.moved > .dropdown,
  .top-bar.expanded .top-bar-section .dropdown {
    clip: initial;
  }
  /* line 299, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar.expanded .top-bar-section .has-dropdown:not(.moved) > ul {
    padding: 0;
  }
}

/* line 308, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section {
  left: 0;
  position: relative;
  width: auto;
  transition: left 300ms ease-out;
}
/* line 314, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul {
  display: block;
  font-size: 16px;
  height: auto;
  margin: 0;
  padding: 0;
  width: 100%;
}
/* line 323, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .divider,
.top-bar-section [role="separator"] {
  border-top: solid 1px #1a1a1a;
  clear: both;
  height: 1px;
  width: 100%;
}
/* line 331, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li {
  background: #333333;
}
/* line 334, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a {
  color: #FFFFFF;
  display: block;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 0.8125rem;
  font-weight: normal;
  padding-left: 0.9375rem;
  padding: 12px 0 12px 0.9375rem;
  text-transform: none;
  width: 100%;
}
/* line 345, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button {
  font-size: 0.8125rem;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button:hover, .top-bar-section ul li > a.button:focus {
  background-color: #007095;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button:hover, .top-bar-section ul li > a.button:focus {
  color: #FFFFFF;
}
/* line 352, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.secondary:hover, .top-bar-section ul li > a.button.secondary:focus {
  background-color: #b9b9b9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.secondary:hover, .top-bar-section ul li > a.button.secondary:focus {
  color: #333333;
}
/* line 353, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button.success {
  background-color: #43AC6A;
  border-color: #368a55;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.success:hover, .top-bar-section ul li > a.button.success:focus {
  background-color: #368a55;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.success:hover, .top-bar-section ul li > a.button.success:focus {
  color: #FFFFFF;
}
/* line 354, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button.alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.alert:hover, .top-bar-section ul li > a.button.alert:focus {
  background-color: #cf2a0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.alert:hover, .top-bar-section ul li > a.button.alert:focus {
  color: #FFFFFF;
}
/* line 355, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button.warning {
  background-color: #f08a24;
  border-color: #cf6e0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.warning:hover, .top-bar-section ul li > a.button.warning:focus {
  background-color: #cf6e0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.warning:hover, .top-bar-section ul li > a.button.warning:focus {
  color: #FFFFFF;
}
/* line 356, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > a.button.info {
  background-color: #a0d3e8;
  border-color: #61b6d9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.info:hover, .top-bar-section ul li > a.button.info:focus {
  background-color: #61b6d9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > a.button.info:hover, .top-bar-section ul li > a.button.info:focus {
  color: #FFFFFF;
}
/* line 359, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button {
  font-size: 0.8125rem;
  padding-left: 0.9375rem;
  padding-right: 0.9375rem;
  background-color: #008CBA;
  border-color: #007095;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button:hover, .top-bar-section ul li > button:focus {
  background-color: #007095;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button:hover, .top-bar-section ul li > button:focus {
  color: #FFFFFF;
}
/* line 365, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button.secondary {
  background-color: #e7e7e7;
  border-color: #b9b9b9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.secondary:hover, .top-bar-section ul li > button.secondary:focus {
  background-color: #b9b9b9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.secondary:hover, .top-bar-section ul li > button.secondary:focus {
  color: #333333;
}
/* line 366, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button.success {
  background-color: #43AC6A;
  border-color: #368a55;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.success:hover, .top-bar-section ul li > button.success:focus {
  background-color: #368a55;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.success:hover, .top-bar-section ul li > button.success:focus {
  color: #FFFFFF;
}
/* line 367, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button.alert {
  background-color: #f04124;
  border-color: #cf2a0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.alert:hover, .top-bar-section ul li > button.alert:focus {
  background-color: #cf2a0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.alert:hover, .top-bar-section ul li > button.alert:focus {
  color: #FFFFFF;
}
/* line 368, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button.warning {
  background-color: #f08a24;
  border-color: #cf6e0e;
  color: #FFFFFF;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.warning:hover, .top-bar-section ul li > button.warning:focus {
  background-color: #cf6e0e;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.warning:hover, .top-bar-section ul li > button.warning:focus {
  color: #FFFFFF;
}
/* line 369, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li > button.info {
  background-color: #a0d3e8;
  border-color: #61b6d9;
  color: #333333;
}
/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.info:hover, .top-bar-section ul li > button.info:focus {
  background-color: #61b6d9;
}
/* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_buttons.scss */
.top-bar-section ul li > button.info:hover, .top-bar-section ul li > button.info:focus {
  color: #FFFFFF;
}
/* line 373, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li:hover:not(.has-form) > a {
  background-color: #555555;
  color: #FFFFFF;
  background: #222222;
}
/* line 383, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li.active > a {
  background: #008CBA;
  color: #FFFFFF;
}
/* line 387, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section ul li.active > a:hover {
  background: #0078a0;
  color: #FFFFFF;
}
/* line 395, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-form {
  padding: 0.9375rem;
}
/* line 400, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-dropdown {
  position: relative;
}
/* line 404, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-dropdown > a:after {
  border: inset 5px;
  content: "";
  display: block;
  height: 0;
  width: 0;
  border-color: transparent transparent transparent rgba(255, 255, 255, 0.4);
  border-left-style: solid;
  margin-right: 0.9375rem;
  margin-top: -4.5px;
  position: absolute;
  top: 50%;
  right: 0;
}
/* line 417, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-dropdown.moved {
  position: static;
}
/* line 420, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-dropdown.moved > .dropdown {
  position: static !important;
  height: auto;
  width: auto;
  overflow: visible;
  clip: auto;
  display: block;
  position: absolute !important;
  width: 100%;
}
/* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .has-dropdown.moved > a:after {
  display: none;
}
/* line 432, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown {
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute !important;
  width: 1px;
  display: block;
  padding: 0;
  position: absolute;
  top: 0;
  z-index: 99;
  left: 100%;
}
/* line 440, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li {
  height: auto;
  width: 100%;
}
/* line 444, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li a {
  font-weight: normal;
  padding: 8px 0.9375rem;
}
/* line 447, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li a.parent-link {
  font-weight: normal;
}
/* line 452, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li.title h5, .top-bar-section .dropdown li.parent-link {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 1.125rem;
}
/* line 458, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li.title h5 a, .top-bar-section .dropdown li.parent-link a {
  color: #FFFFFF;
  display: block;
}
/* line 462, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li.title h5 a:hover, .top-bar-section .dropdown li.parent-link a:hover {
  background: none;
}
/* line 466, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li.has-form {
  padding: 8px 0.9375rem;
}
/* line 470, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown li .button,
.top-bar-section .dropdown li button {
  top: auto;
}
/* line 476, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.top-bar-section .dropdown label {
  color: #777777;
  font-size: 0.625rem;
  font-weight: bold;
  margin-bottom: 0;
  padding: 8px 0.9375rem 2px;
  text-transform: uppercase;
}

/* line 487, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
.js-generated {
  display: block;
}

@media only screen and (min-width: 30em) {
  /* line 492, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar {
    background: #333333;
    overflow: visible;
  }
  /* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
  .top-bar:before, .top-bar:after {
    content: " ";
    display: table;
  }
  /* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
  .top-bar:after {
    clear: both;
  }
  /* line 497, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar .toggle-topbar {
    display: none;
  }
  /* line 499, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar .title-area {
    float: left;
  }
  /* line 500, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar .name h1 a,
  .top-bar .name h2 a,
  .top-bar .name h3 a,
  .top-bar .name h4 a,
  .top-bar .name h5 a,
  .top-bar .name h6 a {
    width: auto;
  }
  /* line 507, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar input,
  .top-bar select,
  .top-bar .button,
  .top-bar button {
    font-size: 0.875rem;
    height: 1.75rem;
    position: relative;
    top: 0.53125rem;
  }
  /* line 517, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar.expanded {
    background: #333333;
  }

  /* line 522, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .contain-to-grid .top-bar {
    margin-bottom: 0;
    margin: 0 auto;
    max-width: 62.5rem;
  }

  /* line 528, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section {
    transition: none 0 0;
    left: 0 !important;
  }
  /* line 532, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section ul {
    display: inline;
    height: auto !important;
    width: auto;
  }
  /* line 537, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section ul li {
    float: left;
  }
  /* line 539, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section ul li .js-generated {
    display: none;
  }
  /* line 545, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section li.hover > a:not(.button) {
    background-color: #555555;
    background: #222222;
    color: #FFFFFF;
  }
  /* line 555, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section li:not(.has-form) a:not(.button) {
    background: #333333;
    line-height: 2.8125rem;
    padding: 0 0.9375rem;
  }
  /* line 559, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section li:not(.has-form) a:not(.button):hover {
    background-color: #555555;
    background: #222222;
  }
  /* line 569, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section li.active:not(.has-form) a:not(.button) {
    background: #008CBA;
    color: #FFFFFF;
    line-height: 2.8125rem;
    padding: 0 0.9375rem;
  }
  /* line 574, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section li.active:not(.has-form) a:not(.button):hover {
    background: #0078a0;
    color: #FFFFFF;
  }
  /* line 584, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown > a {
    padding-right: 2.1875rem !important;
  }
  /* line 586, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown > a:after {
    border: inset 5px;
    content: "";
    display: block;
    height: 0;
    width: 0;
    border-color: rgba(255, 255, 255, 0.4) transparent transparent transparent;
    border-top-style: solid;
    margin-top: -2.5px;
    top: 1.40625rem;
  }
  /* line 594, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown.moved {
    position: relative;
  }
  /* line 595, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown.moved > .dropdown {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
    display: block;
  }
  /* line 601, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown.hover > .dropdown, .top-bar-section .has-dropdown.not-click:hover > .dropdown {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
    display: block;
    position: absolute !important;
  }
  /* line 606, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown > a:focus + .dropdown {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
    display: block;
    position: absolute !important;
  }
  /* line 613, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-dropdown .dropdown li.has-dropdown > a:after {
    border: none;
    content: "\00bb";
    top: 0.1875rem;
    right: 5px;
  }
  /* line 625, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown {
    left: 0;
    background: transparent;
    min-width: 100%;
    top: auto;
  }
  /* line 632, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown li a {
    background: #333333;
    color: #FFFFFF;
    line-height: 2.8125rem;
    padding: 12px 0.9375rem;
    white-space: nowrap;
  }
  /* line 641, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown li:not(.has-form):not(.active) > a:not(.button) {
    background: #333333;
    color: #FFFFFF;
  }
  /* line 646, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown li:not(.has-form):not(.active):hover > a:not(.button) {
    background-color: #555555;
    color: #FFFFFF;
    background: #222222;
  }
  /* line 655, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown li label {
    background: #333333;
    white-space: nowrap;
  }
  /* line 661, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .dropdown li .dropdown {
    left: 100%;
    top: 0;
  }
  /* line 668, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section > ul > .divider,
  .top-bar-section > ul > [role="separator"] {
    border-right: solid 1px #4e4e4e;
    border-bottom: none;
    border-top: none;
    clear: none;
    height: 2.8125rem;
    width: 0;
  }
  /* line 678, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .has-form {
    background: #333333;
    height: 2.8125rem;
    padding: 0 0.9375rem;
  }
  /* line 686, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .right li .dropdown {
    left: auto;
    right: 0;
  }
  /* line 690, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .right li .dropdown li .dropdown {
    right: 100%;
  }
  /* line 694, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .left li .dropdown {
    right: auto;
    left: 0;
  }
  /* line 698, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .top-bar-section .left li .dropdown li .dropdown {
    left: 100%;
  }

  /* line 708, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .no-js .top-bar-section ul li:hover > a {
    background-color: #555555;
    background: #222222;
    color: #FFFFFF;
  }
  /* line 717, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .no-js .top-bar-section ul li:active > a {
    background: #008CBA;
    color: #FFFFFF;
  }
  /* line 725, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .no-js .top-bar-section .has-dropdown:hover > .dropdown {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
    display: block;
    position: absolute !important;
  }
  /* line 730, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_top-bar.scss */
  .no-js .top-bar-section .has-dropdown > a:focus + .dropdown {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
    display: block;
    position: absolute !important;
  }
}
/* line 155, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.text-left {
  text-align: left !important;
}

/* line 156, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.text-right {
  text-align: right !important;
}

/* line 157, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.text-center {
  text-align: center !important;
}

/* line 158, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.text-justify {
  text-align: justify !important;
}

@media only screen and (max-width: 29.9375em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-only-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-only-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-only-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .small-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 30em) and (max-width: 64em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-only-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-only-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-only-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 30em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .medium-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 64.0625em) and (max-width: 90em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-only-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-only-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-only-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 64.0625em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .large-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 90.0625em) and (max-width: 120em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-only-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-only-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-only-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 90.0625em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xlarge-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 120.0625em) and (max-width: 6249999.9375em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-only-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-only-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-only-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-only-text-justify {
    text-align: justify !important;
  }
}
@media only screen and (min-width: 120.0625em) {
  /* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-text-left {
    text-align: left !important;
  }

  /* line 163, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-text-right {
    text-align: right !important;
  }

  /* line 164, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-text-center {
    text-align: center !important;
  }

  /* line 165, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  .xxlarge-text-justify {
    text-align: justify !important;
  }
}
/* Typography resets */
/* line 193, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
p,
blockquote,
th,
td {
  margin: 0;
  padding: 0;
}

/* Default Link Styles */
/* line 217, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
a {
  color: #008CBA;
  line-height: inherit;
  text-decoration: none;
}
/* line 222, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
a:hover, a:focus {
  color: #0078a0;
}
/* line 230, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
a img {
  border: none;
}

/* Default paragraph styles */
/* line 234, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
p {
  font-family: inherit;
  font-size: 1rem;
  font-weight: normal;
  line-height: 1.6;
  margin-bottom: 1.25rem;
  text-rendering: none;
}
/* line 242, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
p.lead {
  font-size: 1.21875rem;
  line-height: 1.6;
}
/* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
p aside {
  font-size: 0.875rem;
  font-style: italic;
  line-height: 1.35;
}

/* Default header styles */
/* line 252, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h1, h2, h3, h4, h5, h6 {
  color: white;
  font-family: Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  margin-top: 0.2rem;
  text-rendering: none;
}
/* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small {
  color: white;
  font-size: 60%;
  line-height: 0;
}

/* line 269, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h1 {
  font-size: 1.1875rem;
}

/* line 270, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h2 {
  font-size: 1.6875rem;
}

/* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h3 {
  font-size: 1.375rem;
}

/* line 272, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h4 {
  font-size: 1.125rem;
}

/* line 273, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h5 {
  font-size: 1.125rem;
}

/* line 274, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
h6 {
  font-size: 1rem;
}

/* line 276, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.subheader {
  line-height: 1.4;
  color: white;
  font-weight: normal;
  margin-top: 0.2rem;
  margin-bottom: 0.5rem;
}

/* line 278, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
hr {
  border: solid #DDDDDD;
  border-width: 1px 0 0;
  clear: both;
  height: 0;
  margin: 1.25rem 0 1.1875rem;
}

/* Helpful Typography Defaults */
/* line 287, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
em,
i {
  font-style: italic;
  line-height: inherit;
}

/* line 293, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
strong,
b {
  font-weight: bold;
  line-height: inherit;
}

/* line 299, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
small {
  font-size: 60%;
  line-height: inherit;
}

/* line 304, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
code {
  background-color: #f8f8f8;
  border-color: #dfdfdf;
  border-style: solid;
  border-width: 1px;
  color: #333333;
  font-family: Consolas, "Liberation Mono", Courier, monospace;
  font-weight: normal;
  padding: 0.125rem 0.3125rem 0.0625rem;
}

/* Lists */
/* line 316, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul,
ol,
dl {
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.6;
  list-style-position: outside;
  margin-bottom: 1.25rem;
}

/* line 326, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul {
  margin-left: 1.1rem;
}
/* line 328, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.no-bullet {
  margin-left: 0;
}
/* line 331, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.no-bullet li ul,
ul.no-bullet li ol {
  margin-left: 1.25rem;
  margin-bottom: 0;
  list-style: none;
}

/* Unordered Lists */
/* line 344, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul li ul,
ul li ol {
  margin-left: 1.25rem;
  margin-bottom: 0;
}
/* line 353, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.square li ul, ul.circle li ul, ul.disc li ul {
  list-style: inherit;
}
/* line 356, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.square {
  list-style-type: square;
  margin-left: 1.1rem;
}
/* line 357, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.circle {
  list-style-type: circle;
  margin-left: 1.1rem;
}
/* line 358, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.disc {
  list-style-type: disc;
  margin-left: 1.1rem;
}
/* line 359, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ul.no-bullet {
  list-style: none;
}

/* Ordered Lists */
/* line 363, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ol {
  margin-left: 1.4rem;
}
/* line 366, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
ol li ul,
ol li ol {
  margin-left: 1.25rem;
  margin-bottom: 0;
}

/* Definition Lists */
/* line 376, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
dl dt {
  margin-bottom: 0.3rem;
  font-weight: bold;
}
/* line 380, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
dl dd {
  margin-bottom: 0.75rem;
}

/* Abbreviations */
/* line 384, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
abbr,
acronym {
  text-transform: uppercase;
  font-size: 90%;
  color: #383737;
  cursor: help;
}

/* line 391, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
abbr {
  text-transform: none;
}
/* line 393, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
abbr[title] {
  border-bottom: 1px dotted #DDDDDD;
}

/* Blockquotes */
/* line 399, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
blockquote {
  margin: 0 0 1.25rem;
  padding: 0.5625rem 1.25rem 0 1.1875rem;
  border-left: 1px solid #DDDDDD;
}
/* line 404, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
blockquote cite {
  display: block;
  font-size: 0.8125rem;
  color: white;
}
/* line 408, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
blockquote cite:before {
  content: "\2014 \0020";
}
/* line 412, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
blockquote cite a,
blockquote cite a:visited {
  color: white;
}

/* line 418, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
blockquote,
blockquote p {
  line-height: 1.6;
  color: white;
}

/* Microformats */
/* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.vcard {
  display: inline-block;
  margin: 0 0 1.25rem 0;
  border: 1px solid #DDDDDD;
  padding: 0.625rem 0.75rem;
}
/* line 431, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.vcard li {
  margin: 0;
  display: block;
}
/* line 435, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.vcard .fn {
  font-weight: bold;
  font-size: 0.9375rem;
}

/* line 442, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.vevent .summary {
  font-weight: bold;
}
/* line 444, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
.vevent abbr {
  cursor: default;
  text-decoration: none;
  font-weight: bold;
  border: none;
  padding: 0 0.0625rem;
}

@media only screen and (min-width: 30em) {
  /* line 455, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.4;
  }

  /* line 456, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h1 {
    font-size: 1.5rem;
  }

  /* line 457, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h2 {
    font-size: 2.3125rem;
  }

  /* line 458, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h3 {
    font-size: 1.6875rem;
  }

  /* line 459, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h4 {
    font-size: 1.4375rem;
  }

  /* line 460, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h5 {
    font-size: 1.125rem;
  }

  /* line 461, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_type.scss */
  h6 {
    font-size: 1rem;
  }
}
/* line 386, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.off-canvas-wrap {
  -webkit-backface-visibility: hidden;
  position: relative;
  width: 100%;
  overflow: hidden;
}
/* line 136, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.off-canvas-wrap.move-right, .off-canvas-wrap.move-left {
  min-height: 100%;
  -webkit-overflow-scrolling: touch;
}

/* line 387, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.inner-wrap {
  position: relative;
  width: 100%;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
}
/* line 172, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.inner-wrap:before, .inner-wrap:after {
  content: " ";
  display: table;
}
/* line 173, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.inner-wrap:after {
  clear: both;
}

/* line 389, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar {
  -webkit-backface-visibility: hidden;
  background: #333333;
  color: #FFFFFF;
  height: 2.8125rem;
  line-height: 2.8125rem;
  position: relative;
}
/* line 170, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar h1, .tab-bar h2, .tab-bar h3, .tab-bar h4, .tab-bar h5, .tab-bar h6 {
  color: #FFFFFF;
  font-weight: bold;
  line-height: 2.8125rem;
  margin: 0;
}
/* line 176, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar h1, .tab-bar h2, .tab-bar h3, .tab-bar h4 {
  font-size: 1.125rem;
}

/* line 391, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-small {
  height: 2.8125rem;
  position: absolute;
  top: 0;
  width: 2.8125rem;
  border-right: solid 1px #1a1a1a;
  left: 0;
}

/* line 392, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-small {
  height: 2.8125rem;
  position: absolute;
  top: 0;
  width: 2.8125rem;
  border-left: solid 1px #1a1a1a;
  right: 0;
}

/* line 394, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section {
  height: 2.8125rem;
  padding: 0 0.625rem;
  position: absolute;
  text-align: center;
  top: 0;
}
/* line 204, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section.left {
  text-align: left;
}
/* line 205, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section.right {
  text-align: right;
}
/* line 209, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section.left {
  left: 0;
  right: 2.8125rem;
}
/* line 213, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section.right {
  left: 2.8125rem;
  right: 0;
}
/* line 217, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar-section.middle {
  left: 2.8125rem;
  right: 2.8125rem;
}

/* line 398, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.tab-bar .menu-icon {
  color: #FFFFFF;
  display: block;
  height: 2.8125rem;
  padding: 0;
  position: relative;
  text-indent: 2.1875rem;
  transform: translate3d(0, 0, 0);
  width: 2.8125rem;
}
/* line 129, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tab-bar .menu-icon span::after {
  content: "";
  display: block;
  height: 0;
  position: absolute;
  top: 50%;
  margin-top: -0.5rem;
  left: 0.90625rem;
  box-shadow: 0 0 0 1px #FFFFFF, 0 7px 0 1px #FFFFFF, 0 14px 0 1px #FFFFFF;
  width: 1rem;
}
/* line 162, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_global.scss */
.tab-bar .menu-icon span:hover:after {
  box-shadow: 0 0 0 1px #b3b3b3, 0 7px 0 1px #b3b3b3, 0 14px 0 1px #b3b3b3;
}

/* line 422, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-off-canvas-menu {
  -webkit-backface-visibility: hidden;
  background: #333333;
  bottom: 0;
  box-sizing: content-box;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  transition: transform 500ms ease 0s;
  width: 15.625rem;
  z-index: 1001;
  -webkit-transform: translate3d(-100%, 0, 0);
  -moz-transform: translate3d(-100%, 0, 0);
  -ms-transform: translate(-100%, 0);
  -ms-transform: translate3d(-100%, 0, 0);
  -o-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
  left: 0;
}
/* line 106, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-off-canvas-menu * {
  -webkit-backface-visibility: hidden;
}

/* line 423, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-off-canvas-menu {
  -webkit-backface-visibility: hidden;
  background: #333333;
  bottom: 0;
  box-sizing: content-box;
  -webkit-overflow-scrolling: touch;
  -ms-overflow-style: -ms-autohiding-scrollbar;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  transition: transform 500ms ease 0s;
  width: 15.625rem;
  z-index: 1001;
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  -ms-transform: translate(100%, 0);
  -ms-transform: translate3d(100%, 0, 0);
  -o-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  right: 0;
}
/* line 106, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-off-canvas-menu * {
  -webkit-backface-visibility: hidden;
}

/* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
ul.off-canvas-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
/* line 231, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
ul.off-canvas-list li label {
  background: #444444;
  border-bottom: none;
  border-top: 1px solid #5e5e5e;
  color: #999999;
  display: block;
  font-size: 0.75rem;
  font-weight: bold;
  margin: 0;
  padding: 0.3rem 0.9375rem;
  text-transform: uppercase;
}
/* line 243, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
ul.off-canvas-list li a {
  border-bottom: 1px solid #262626;
  color: rgba(255, 255, 255, 0.7);
  display: block;
  padding: 0.66667rem;
  transition: background 300ms ease;
}
/* line 249, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
ul.off-canvas-list li a:hover {
  background: #242424;
}
/* line 252, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
ul.off-canvas-list li a:active {
  background: #242424;
}

/* line 431, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.move-right > .inner-wrap {
  -webkit-transform: translate3d(15.625rem, 0, 0);
  -moz-transform: translate3d(15.625rem, 0, 0);
  -ms-transform: translate(15.625rem, 0);
  -ms-transform: translate3d(15.625rem, 0, 0);
  -o-transform: translate3d(15.625rem, 0, 0);
  transform: translate3d(15.625rem, 0, 0);
}
/* line 434, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.move-right .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}
@media only screen and (min-width: 30em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
  .move-right .exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* line 438, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.move-left > .inner-wrap {
  -webkit-transform: translate3d(-15.625rem, 0, 0);
  -moz-transform: translate3d(-15.625rem, 0, 0);
  -ms-transform: translate(-15.625rem, 0);
  -ms-transform: translate3d(-15.625rem, 0, 0);
  -o-transform: translate3d(-15.625rem, 0, 0);
  transform: translate3d(-15.625rem, 0, 0);
}
/* line 442, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.move-left .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}
@media only screen and (min-width: 30em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
  .move-left .exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* line 445, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap .left-off-canvas-menu, .offcanvas-overlap .right-off-canvas-menu {
  -ms-transform: none;
  -webkit-transform: none;
  -moz-transform: none;
  -o-transform: none;
  transform: none;
  z-index: 1003;
}
/* line 453, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}
@media only screen and (min-width: 30em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
  .offcanvas-overlap .exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* line 456, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap-left .right-off-canvas-menu {
  -ms-transform: none;
  -webkit-transform: none;
  -moz-transform: none;
  -o-transform: none;
  transform: none;
  z-index: 1003;
}
/* line 464, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap-left .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}
@media only screen and (min-width: 30em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
  .offcanvas-overlap-left .exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* line 467, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap-right .left-off-canvas-menu {
  -ms-transform: none;
  -webkit-transform: none;
  -moz-transform: none;
  -o-transform: none;
  transform: none;
  z-index: 1003;
}
/* line 475, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.offcanvas-overlap-right .exit-off-canvas {
  -webkit-backface-visibility: hidden;
  box-shadow: -4px 0 4px rgba(0, 0, 0, 0.5), 4px 0 4px rgba(0, 0, 0, 0.5);
  cursor: pointer;
  transition: background 300ms ease;
  -webkit-tap-highlight-color: transparent;
  background: rgba(255, 255, 255, 0.2);
  bottom: 0;
  display: block;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1002;
}
@media only screen and (min-width: 30em) {
  /* line 281, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
  .offcanvas-overlap-right .exit-off-canvas:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

/* line 480, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.no-csstransforms .left-off-canvas-menu {
  left: -15.625rem;
}
/* line 481, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.no-csstransforms .right-off-canvas-menu {
  right: -15.625rem;
}
/* line 483, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.no-csstransforms .move-left > .inner-wrap {
  right: 15.625rem;
}
/* line 484, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.no-csstransforms .move-right > .inner-wrap {
  left: 15.625rem;
}

/* line 487, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu {
  -webkit-backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  background: #333333;
  bottom: 0;
  box-sizing: content-box;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  width: 15.625rem;
  z-index: 1002;
  -webkit-transform: translate3d(-100%, 0, 0);
  -moz-transform: translate3d(-100%, 0, 0);
  -ms-transform: translate(-100%, 0);
  -ms-transform: translate3d(-100%, 0, 0);
  -o-transform: translate3d(-100%, 0, 0);
  transform: translate3d(-100%, 0, 0);
  left: 0;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
}
/* line 292, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu * {
  -webkit-backface-visibility: hidden;
}
/* line 319, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu .back > a {
  background: #444;
  border-bottom: none;
  border-top: 1px solid #5e5e5e;
  color: #999999;
  font-weight: bold;
  padding: 0.3rem 0.9375rem;
  text-transform: uppercase;
  margin: 0;
}
/* line 328, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu .back > a:hover {
  background: #303030;
  border-bottom: none;
  border-top: 1px solid #5e5e5e;
}
/* line 352, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu .back > a:before {
  content: "\AB";
  margin-right: .5rem;
  display: inline;
}
/* line 489, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-submenu.move-right, .left-submenu.offcanvas-overlap-right, .left-submenu.offcanvas-overlap {
  -webkit-transform: translate3d(0%, 0, 0);
  -moz-transform: translate3d(0%, 0, 0);
  -ms-transform: translate(0%, 0);
  -ms-transform: translate3d(0%, 0, 0);
  -o-transform: translate3d(0%, 0, 0);
  transform: translate3d(0%, 0, 0);
}

/* line 494, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu {
  -webkit-backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  background: #333333;
  bottom: 0;
  box-sizing: content-box;
  margin: 0;
  overflow-x: hidden;
  overflow-y: auto;
  position: absolute;
  top: 0;
  width: 15.625rem;
  z-index: 1002;
  -webkit-transform: translate3d(100%, 0, 0);
  -moz-transform: translate3d(100%, 0, 0);
  -ms-transform: translate(100%, 0);
  -ms-transform: translate3d(100%, 0, 0);
  -o-transform: translate3d(100%, 0, 0);
  transform: translate3d(100%, 0, 0);
  right: 0;
  -webkit-transition: -webkit-transform 500ms ease;
  -moz-transition: -moz-transform 500ms ease;
  -ms-transition: -ms-transform 500ms ease;
  -o-transition: -o-transform 500ms ease;
  transition: transform 500ms ease;
}
/* line 292, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu * {
  -webkit-backface-visibility: hidden;
}
/* line 319, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu .back > a {
  background: #444;
  border-bottom: none;
  border-top: 1px solid #5e5e5e;
  color: #999999;
  font-weight: bold;
  padding: 0.3rem 0.9375rem;
  text-transform: uppercase;
  margin: 0;
}
/* line 328, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu .back > a:hover {
  background: #303030;
  border-bottom: none;
  border-top: 1px solid #5e5e5e;
}
/* line 341, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu .back > a:after {
  content: "\BB";
  margin-left: .5rem;
  display: inline;
}
/* line 496, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-submenu.move-left, .right-submenu.offcanvas-overlap-left, .right-submenu.offcanvas-overlap {
  -webkit-transform: translate3d(0%, 0, 0);
  -moz-transform: translate3d(0%, 0, 0);
  -ms-transform: translate(0%, 0);
  -ms-transform: translate3d(0%, 0, 0);
  -o-transform: translate3d(0%, 0, 0);
  transform: translate3d(0%, 0, 0);
}

/* line 509, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.left-off-canvas-menu ul.off-canvas-list li.has-submenu > a:after {
  content: "\BB";
  margin-left: .5rem;
  display: inline;
}

/* line 512, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_offcanvas.scss */
.right-off-canvas-menu ul.off-canvas-list li.has-submenu > a:before {
  content: "\AB";
  margin-right: .5rem;
  display: inline;
}

/* small displays */
@media only screen {
  /* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-small-only, .show-for-small-up, .show-for-small, .show-for-small-down, .hide-for-medium-only, .hide-for-medium-up, .hide-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-small-only, .hide-for-small-up, .hide-for-small, .hide-for-small-down, .show-for-medium-only, .show-for-medium-up, .show-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }

  /* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .visible-for-small-only, .visible-for-small-up, .visible-for-small, .visible-for-small-down, .hidden-for-medium-only, .hidden-for-medium-up, .hidden-for-medium, .visible-for-medium-down, .hidden-for-large-only, .hidden-for-large-up, .hidden-for-large, .visible-for-large-down, .hidden-for-xlarge-only, .hidden-for-xlarge-up, .hidden-for-xlarge, .visible-for-xlarge-down, .hidden-for-xxlarge-only, .hidden-for-xxlarge-up, .hidden-for-xxlarge, .visible-for-xxlarge-down {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
  }

  /* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hidden-for-small-only, .hidden-for-small-up, .hidden-for-small, .hidden-for-small-down, .visible-for-medium-only, .visible-for-medium-up, .visible-for-medium, .hidden-for-medium-down, .visible-for-large-only, .visible-for-large-up, .visible-for-large, .hidden-for-large-down, .visible-for-xlarge-only, .visible-for-xlarge-up, .visible-for-xlarge, .hidden-for-xlarge-down, .visible-for-xxlarge-only, .visible-for-xxlarge-up, .visible-for-xxlarge, .hidden-for-xxlarge-down {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
  }

  /* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.show-for-small-only, table.show-for-small-up, table.show-for-small, table.show-for-small-down, table.hide-for-medium-only, table.hide-for-medium-up, table.hide-for-medium, table.show-for-medium-down, table.hide-for-large-only, table.hide-for-large-up, table.hide-for-large, table.show-for-large-down, table.hide-for-xlarge-only, table.hide-for-xlarge-up, table.hide-for-xlarge, table.show-for-xlarge-down, table.hide-for-xxlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge, table.show-for-xxlarge-down {
    display: table !important;
  }

  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.show-for-small-only, thead.show-for-small-up, thead.show-for-small, thead.show-for-small-down, thead.hide-for-medium-only, thead.hide-for-medium-up, thead.hide-for-medium, thead.show-for-medium-down, thead.hide-for-large-only, thead.hide-for-large-up, thead.hide-for-large, thead.show-for-large-down, thead.hide-for-xlarge-only, thead.hide-for-xlarge-up, thead.hide-for-xlarge, thead.show-for-xlarge-down, thead.hide-for-xxlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge, thead.show-for-xxlarge-down {
    display: table-header-group !important;
  }

  /* line 265, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.show-for-small-only, tbody.show-for-small-up, tbody.show-for-small, tbody.show-for-small-down, tbody.hide-for-medium-only, tbody.hide-for-medium-up, tbody.hide-for-medium, tbody.show-for-medium-down, tbody.hide-for-large-only, tbody.hide-for-large-up, tbody.hide-for-large, tbody.show-for-large-down, tbody.hide-for-xlarge-only, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge, tbody.show-for-xlarge-down, tbody.hide-for-xxlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge, tbody.show-for-xxlarge-down {
    display: table-row-group !important;
  }

  /* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.show-for-small-only, tr.show-for-small-up, tr.show-for-small, tr.show-for-small-down, tr.hide-for-medium-only, tr.hide-for-medium-up, tr.hide-for-medium, tr.show-for-medium-down, tr.hide-for-large-only, tr.hide-for-large-up, tr.hide-for-large, tr.show-for-large-down, tr.hide-for-xlarge-only, tr.hide-for-xlarge-up, tr.hide-for-xlarge, tr.show-for-xlarge-down, tr.hide-for-xxlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge, tr.show-for-xxlarge-down {
    display: table-row;
  }

  /* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.show-for-small-only, td.show-for-small-only, th.show-for-small-up, td.show-for-small-up, th.show-for-small, td.show-for-small, th.show-for-small-down, td.show-for-small-down, th.hide-for-medium-only, td.hide-for-medium-only, th.hide-for-medium-up, td.hide-for-medium-up, th.hide-for-medium, td.hide-for-medium, th.show-for-medium-down, td.show-for-medium-down, th.hide-for-large-only, td.hide-for-large-only, th.hide-for-large-up, td.hide-for-large-up, th.hide-for-large, td.hide-for-large, th.show-for-large-down, td.show-for-large-down, th.hide-for-xlarge-only, td.hide-for-xlarge-only, th.hide-for-xlarge-up, td.hide-for-xlarge-up, th.hide-for-xlarge, td.hide-for-xlarge, th.show-for-xlarge-down, td.show-for-xlarge-down, th.hide-for-xxlarge-only, td.hide-for-xxlarge-only, th.hide-for-xxlarge-up, td.hide-for-xxlarge-up, th.hide-for-xxlarge, td.hide-for-xxlarge, th.show-for-xxlarge-down, td.show-for-xxlarge-down {
    display: table-cell !important;
  }
}
/* medium displays */
@media only screen and (min-width: 30em) {
  /* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .show-for-medium-only, .show-for-medium-up, .show-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .hide-for-medium-only, .hide-for-medium-up, .hide-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }

  /* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hidden-for-small-only, .visible-for-small-up, .hidden-for-small, .hidden-for-small-down, .visible-for-medium-only, .visible-for-medium-up, .visible-for-medium, .visible-for-medium-down, .hidden-for-large-only, .hidden-for-large-up, .hidden-for-large, .visible-for-large-down, .hidden-for-xlarge-only, .hidden-for-xlarge-up, .hidden-for-xlarge, .visible-for-xlarge-down, .hidden-for-xxlarge-only, .hidden-for-xxlarge-up, .hidden-for-xxlarge, .visible-for-xxlarge-down {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
  }

  /* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .visible-for-small-only, .hidden-for-small-up, .visible-for-small, .visible-for-small-down, .hidden-for-medium-only, .hidden-for-medium-up, .hidden-for-medium, .hidden-for-medium-down, .visible-for-large-only, .visible-for-large-up, .visible-for-large, .hidden-for-large-down, .visible-for-xlarge-only, .visible-for-xlarge-up, .visible-for-xlarge, .hidden-for-xlarge-down, .visible-for-xxlarge-only, .visible-for-xxlarge-up, .visible-for-xxlarge, .hidden-for-xxlarge-down {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
  }

  /* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.hide-for-small-only, table.show-for-small-up, table.hide-for-small, table.hide-for-small-down, table.show-for-medium-only, table.show-for-medium-up, table.show-for-medium, table.show-for-medium-down, table.hide-for-large-only, table.hide-for-large-up, table.hide-for-large, table.show-for-large-down, table.hide-for-xlarge-only, table.hide-for-xlarge-up, table.hide-for-xlarge, table.show-for-xlarge-down, table.hide-for-xxlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge, table.show-for-xxlarge-down {
    display: table !important;
  }

  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.hide-for-small-only, thead.show-for-small-up, thead.hide-for-small, thead.hide-for-small-down, thead.show-for-medium-only, thead.show-for-medium-up, thead.show-for-medium, thead.show-for-medium-down, thead.hide-for-large-only, thead.hide-for-large-up, thead.hide-for-large, thead.show-for-large-down, thead.hide-for-xlarge-only, thead.hide-for-xlarge-up, thead.hide-for-xlarge, thead.show-for-xlarge-down, thead.hide-for-xxlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge, thead.show-for-xxlarge-down {
    display: table-header-group !important;
  }

  /* line 265, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.hide-for-small-only, tbody.show-for-small-up, tbody.hide-for-small, tbody.hide-for-small-down, tbody.show-for-medium-only, tbody.show-for-medium-up, tbody.show-for-medium, tbody.show-for-medium-down, tbody.hide-for-large-only, tbody.hide-for-large-up, tbody.hide-for-large, tbody.show-for-large-down, tbody.hide-for-xlarge-only, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge, tbody.show-for-xlarge-down, tbody.hide-for-xxlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge, tbody.show-for-xxlarge-down {
    display: table-row-group !important;
  }

  /* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.hide-for-small-only, tr.show-for-small-up, tr.hide-for-small, tr.hide-for-small-down, tr.show-for-medium-only, tr.show-for-medium-up, tr.show-for-medium, tr.show-for-medium-down, tr.hide-for-large-only, tr.hide-for-large-up, tr.hide-for-large, tr.show-for-large-down, tr.hide-for-xlarge-only, tr.hide-for-xlarge-up, tr.hide-for-xlarge, tr.show-for-xlarge-down, tr.hide-for-xxlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge, tr.show-for-xxlarge-down {
    display: table-row;
  }

  /* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.hide-for-small-only, td.hide-for-small-only, th.show-for-small-up, td.show-for-small-up, th.hide-for-small, td.hide-for-small, th.hide-for-small-down, td.hide-for-small-down, th.show-for-medium-only, td.show-for-medium-only, th.show-for-medium-up, td.show-for-medium-up, th.show-for-medium, td.show-for-medium, th.show-for-medium-down, td.show-for-medium-down, th.hide-for-large-only, td.hide-for-large-only, th.hide-for-large-up, td.hide-for-large-up, th.hide-for-large, td.hide-for-large, th.show-for-large-down, td.show-for-large-down, th.hide-for-xlarge-only, td.hide-for-xlarge-only, th.hide-for-xlarge-up, td.hide-for-xlarge-up, th.hide-for-xlarge, td.hide-for-xlarge, th.show-for-xlarge-down, td.show-for-xlarge-down, th.hide-for-xxlarge-only, td.hide-for-xxlarge-only, th.hide-for-xxlarge-up, td.hide-for-xxlarge-up, th.hide-for-xxlarge, td.hide-for-xxlarge, th.show-for-xxlarge-down, td.show-for-xxlarge-down {
    display: table-cell !important;
  }
}
/* large displays */
@media only screen and (min-width: 64.0625em) {
  /* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .hide-for-medium-only, .show-for-medium-up, .hide-for-medium, .hide-for-medium-down, .show-for-large-only, .show-for-large-up, .show-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .show-for-medium-only, .hide-for-medium-up, .show-for-medium, .show-for-medium-down, .hide-for-large-only, .hide-for-large-up, .hide-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }

  /* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hidden-for-small-only, .visible-for-small-up, .hidden-for-small, .hidden-for-small-down, .hidden-for-medium-only, .visible-for-medium-up, .hidden-for-medium, .hidden-for-medium-down, .visible-for-large-only, .visible-for-large-up, .visible-for-large, .visible-for-large-down, .hidden-for-xlarge-only, .hidden-for-xlarge-up, .hidden-for-xlarge, .visible-for-xlarge-down, .hidden-for-xxlarge-only, .hidden-for-xxlarge-up, .hidden-for-xxlarge, .visible-for-xxlarge-down {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
  }

  /* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .visible-for-small-only, .hidden-for-small-up, .visible-for-small, .visible-for-small-down, .visible-for-medium-only, .hidden-for-medium-up, .visible-for-medium, .visible-for-medium-down, .hidden-for-large-only, .hidden-for-large-up, .hidden-for-large, .hidden-for-large-down, .visible-for-xlarge-only, .visible-for-xlarge-up, .visible-for-xlarge, .hidden-for-xlarge-down, .visible-for-xxlarge-only, .visible-for-xxlarge-up, .visible-for-xxlarge, .hidden-for-xxlarge-down {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
  }

  /* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.hide-for-small-only, table.show-for-small-up, table.hide-for-small, table.hide-for-small-down, table.hide-for-medium-only, table.show-for-medium-up, table.hide-for-medium, table.hide-for-medium-down, table.show-for-large-only, table.show-for-large-up, table.show-for-large, table.show-for-large-down, table.hide-for-xlarge-only, table.hide-for-xlarge-up, table.hide-for-xlarge, table.show-for-xlarge-down, table.hide-for-xxlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge, table.show-for-xxlarge-down {
    display: table !important;
  }

  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.hide-for-small-only, thead.show-for-small-up, thead.hide-for-small, thead.hide-for-small-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.hide-for-medium, thead.hide-for-medium-down, thead.show-for-large-only, thead.show-for-large-up, thead.show-for-large, thead.show-for-large-down, thead.hide-for-xlarge-only, thead.hide-for-xlarge-up, thead.hide-for-xlarge, thead.show-for-xlarge-down, thead.hide-for-xxlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge, thead.show-for-xxlarge-down {
    display: table-header-group !important;
  }

  /* line 265, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.hide-for-small-only, tbody.show-for-small-up, tbody.hide-for-small, tbody.hide-for-small-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.show-for-large-only, tbody.show-for-large-up, tbody.show-for-large, tbody.show-for-large-down, tbody.hide-for-xlarge-only, tbody.hide-for-xlarge-up, tbody.hide-for-xlarge, tbody.show-for-xlarge-down, tbody.hide-for-xxlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge, tbody.show-for-xxlarge-down {
    display: table-row-group !important;
  }

  /* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.hide-for-small-only, tr.show-for-small-up, tr.hide-for-small, tr.hide-for-small-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.hide-for-medium, tr.hide-for-medium-down, tr.show-for-large-only, tr.show-for-large-up, tr.show-for-large, tr.show-for-large-down, tr.hide-for-xlarge-only, tr.hide-for-xlarge-up, tr.hide-for-xlarge, tr.show-for-xlarge-down, tr.hide-for-xxlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge, tr.show-for-xxlarge-down {
    display: table-row;
  }

  /* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.hide-for-small-only, td.hide-for-small-only, th.show-for-small-up, td.show-for-small-up, th.hide-for-small, td.hide-for-small, th.hide-for-small-down, td.hide-for-small-down, th.hide-for-medium-only, td.hide-for-medium-only, th.show-for-medium-up, td.show-for-medium-up, th.hide-for-medium, td.hide-for-medium, th.hide-for-medium-down, td.hide-for-medium-down, th.show-for-large-only, td.show-for-large-only, th.show-for-large-up, td.show-for-large-up, th.show-for-large, td.show-for-large, th.show-for-large-down, td.show-for-large-down, th.hide-for-xlarge-only, td.hide-for-xlarge-only, th.hide-for-xlarge-up, td.hide-for-xlarge-up, th.hide-for-xlarge, td.hide-for-xlarge, th.show-for-xlarge-down, td.show-for-xlarge-down, th.hide-for-xxlarge-only, td.hide-for-xxlarge-only, th.hide-for-xxlarge-up, td.hide-for-xxlarge-up, th.hide-for-xxlarge, td.hide-for-xxlarge, th.show-for-xxlarge-down, td.show-for-xxlarge-down {
    display: table-cell !important;
  }
}
/* xlarge displays */
@media only screen and (min-width: 90.0625em) {
  /* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .hide-for-medium-only, .show-for-medium-up, .hide-for-medium, .hide-for-medium-down, .hide-for-large-only, .show-for-large-up, .hide-for-large, .hide-for-large-down, .show-for-xlarge-only, .show-for-xlarge-up, .show-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .show-for-medium-only, .hide-for-medium-up, .show-for-medium, .show-for-medium-down, .show-for-large-only, .hide-for-large-up, .show-for-large, .show-for-large-down, .hide-for-xlarge-only, .hide-for-xlarge-up, .hide-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }

  /* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hidden-for-small-only, .visible-for-small-up, .hidden-for-small, .hidden-for-small-down, .hidden-for-medium-only, .visible-for-medium-up, .hidden-for-medium, .hidden-for-medium-down, .hidden-for-large-only, .visible-for-large-up, .hidden-for-large, .hidden-for-large-down, .visible-for-xlarge-only, .visible-for-xlarge-up, .visible-for-xlarge, .visible-for-xlarge-down, .hidden-for-xxlarge-only, .hidden-for-xxlarge-up, .hidden-for-xxlarge, .visible-for-xxlarge-down {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
  }

  /* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .visible-for-small-only, .hidden-for-small-up, .visible-for-small, .visible-for-small-down, .visible-for-medium-only, .hidden-for-medium-up, .visible-for-medium, .visible-for-medium-down, .visible-for-large-only, .hidden-for-large-up, .visible-for-large, .visible-for-large-down, .hidden-for-xlarge-only, .hidden-for-xlarge-up, .hidden-for-xlarge, .hidden-for-xlarge-down, .visible-for-xxlarge-only, .visible-for-xxlarge-up, .visible-for-xxlarge, .hidden-for-xxlarge-down {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
  }

  /* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.hide-for-small-only, table.show-for-small-up, table.hide-for-small, table.hide-for-small-down, table.hide-for-medium-only, table.show-for-medium-up, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-large-only, table.show-for-large-up, table.hide-for-large, table.hide-for-large-down, table.show-for-xlarge-only, table.show-for-xlarge-up, table.show-for-xlarge, table.show-for-xlarge-down, table.hide-for-xxlarge-only, table.hide-for-xxlarge-up, table.hide-for-xxlarge, table.show-for-xxlarge-down {
    display: table !important;
  }

  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.hide-for-small-only, thead.show-for-small-up, thead.hide-for-small, thead.hide-for-small-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-large-only, thead.show-for-large-up, thead.hide-for-large, thead.hide-for-large-down, thead.show-for-xlarge-only, thead.show-for-xlarge-up, thead.show-for-xlarge, thead.show-for-xlarge-down, thead.hide-for-xxlarge-only, thead.hide-for-xxlarge-up, thead.hide-for-xxlarge, thead.show-for-xxlarge-down {
    display: table-header-group !important;
  }

  /* line 265, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.hide-for-small-only, tbody.show-for-small-up, tbody.hide-for-small, tbody.hide-for-small-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-large-only, tbody.show-for-large-up, tbody.hide-for-large, tbody.hide-for-large-down, tbody.show-for-xlarge-only, tbody.show-for-xlarge-up, tbody.show-for-xlarge, tbody.show-for-xlarge-down, tbody.hide-for-xxlarge-only, tbody.hide-for-xxlarge-up, tbody.hide-for-xxlarge, tbody.show-for-xxlarge-down {
    display: table-row-group !important;
  }

  /* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.hide-for-small-only, tr.show-for-small-up, tr.hide-for-small, tr.hide-for-small-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-large-only, tr.show-for-large-up, tr.hide-for-large, tr.hide-for-large-down, tr.show-for-xlarge-only, tr.show-for-xlarge-up, tr.show-for-xlarge, tr.show-for-xlarge-down, tr.hide-for-xxlarge-only, tr.hide-for-xxlarge-up, tr.hide-for-xxlarge, tr.show-for-xxlarge-down {
    display: table-row;
  }

  /* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.hide-for-small-only, td.hide-for-small-only, th.show-for-small-up, td.show-for-small-up, th.hide-for-small, td.hide-for-small, th.hide-for-small-down, td.hide-for-small-down, th.hide-for-medium-only, td.hide-for-medium-only, th.show-for-medium-up, td.show-for-medium-up, th.hide-for-medium, td.hide-for-medium, th.hide-for-medium-down, td.hide-for-medium-down, th.hide-for-large-only, td.hide-for-large-only, th.show-for-large-up, td.show-for-large-up, th.hide-for-large, td.hide-for-large, th.hide-for-large-down, td.hide-for-large-down, th.show-for-xlarge-only, td.show-for-xlarge-only, th.show-for-xlarge-up, td.show-for-xlarge-up, th.show-for-xlarge, td.show-for-xlarge, th.show-for-xlarge-down, td.show-for-xlarge-down, th.hide-for-xxlarge-only, td.hide-for-xxlarge-only, th.hide-for-xxlarge-up, td.hide-for-xxlarge-up, th.hide-for-xxlarge, td.hide-for-xxlarge, th.show-for-xxlarge-down, td.show-for-xxlarge-down {
    display: table-cell !important;
  }
}
/* xxlarge displays */
@media only screen and (min-width: 120.0625em) {
  /* line 244, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-small-only, .show-for-small-up, .hide-for-small, .hide-for-small-down, .hide-for-medium-only, .show-for-medium-up, .hide-for-medium, .hide-for-medium-down, .hide-for-large-only, .show-for-large-up, .hide-for-large, .hide-for-large-down, .hide-for-xlarge-only, .show-for-xlarge-up, .hide-for-xlarge, .hide-for-xlarge-down, .show-for-xxlarge-only, .show-for-xxlarge-up, .show-for-xxlarge, .show-for-xxlarge-down {
    display: inherit !important;
  }

  /* line 247, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-small-only, .hide-for-small-up, .show-for-small, .show-for-small-down, .show-for-medium-only, .hide-for-medium-up, .show-for-medium, .show-for-medium-down, .show-for-large-only, .hide-for-large-up, .show-for-large, .show-for-large-down, .show-for-xlarge-only, .hide-for-xlarge-up, .show-for-xlarge, .show-for-xlarge-down, .hide-for-xxlarge-only, .hide-for-xxlarge-up, .hide-for-xxlarge, .hide-for-xxlarge-down {
    display: none !important;
  }

  /* line 251, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hidden-for-small-only, .visible-for-small-up, .hidden-for-small, .hidden-for-small-down, .hidden-for-medium-only, .visible-for-medium-up, .hidden-for-medium, .hidden-for-medium-down, .hidden-for-large-only, .visible-for-large-up, .hidden-for-large, .hidden-for-large-down, .hidden-for-xlarge-only, .visible-for-xlarge-up, .hidden-for-xlarge, .hidden-for-xlarge-down, .visible-for-xxlarge-only, .visible-for-xxlarge-up, .visible-for-xxlarge, .visible-for-xxlarge-down {
    position: static !important;
    height: auto;
    width: auto;
    overflow: visible;
    clip: auto;
  }

  /* line 254, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .visible-for-small-only, .hidden-for-small-up, .visible-for-small, .visible-for-small-down, .visible-for-medium-only, .hidden-for-medium-up, .visible-for-medium, .visible-for-medium-down, .visible-for-large-only, .hidden-for-large-up, .visible-for-large, .visible-for-large-down, .visible-for-xlarge-only, .hidden-for-xlarge-up, .visible-for-xlarge, .visible-for-xlarge-down, .hidden-for-xxlarge-only, .hidden-for-xxlarge-up, .hidden-for-xxlarge, .hidden-for-xxlarge-down {
    clip: rect(1px, 1px, 1px, 1px);
    height: 1px;
    overflow: hidden;
    position: absolute !important;
    width: 1px;
  }

  /* line 259, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.hide-for-small-only, table.show-for-small-up, table.hide-for-small, table.hide-for-small-down, table.hide-for-medium-only, table.show-for-medium-up, table.hide-for-medium, table.hide-for-medium-down, table.hide-for-large-only, table.show-for-large-up, table.hide-for-large, table.hide-for-large-down, table.hide-for-xlarge-only, table.show-for-xlarge-up, table.hide-for-xlarge, table.hide-for-xlarge-down, table.show-for-xxlarge-only, table.show-for-xxlarge-up, table.show-for-xxlarge, table.show-for-xxlarge-down {
    display: table !important;
  }

  /* line 262, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.hide-for-small-only, thead.show-for-small-up, thead.hide-for-small, thead.hide-for-small-down, thead.hide-for-medium-only, thead.show-for-medium-up, thead.hide-for-medium, thead.hide-for-medium-down, thead.hide-for-large-only, thead.show-for-large-up, thead.hide-for-large, thead.hide-for-large-down, thead.hide-for-xlarge-only, thead.show-for-xlarge-up, thead.hide-for-xlarge, thead.hide-for-xlarge-down, thead.show-for-xxlarge-only, thead.show-for-xxlarge-up, thead.show-for-xxlarge, thead.show-for-xxlarge-down {
    display: table-header-group !important;
  }

  /* line 265, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.hide-for-small-only, tbody.show-for-small-up, tbody.hide-for-small, tbody.hide-for-small-down, tbody.hide-for-medium-only, tbody.show-for-medium-up, tbody.hide-for-medium, tbody.hide-for-medium-down, tbody.hide-for-large-only, tbody.show-for-large-up, tbody.hide-for-large, tbody.hide-for-large-down, tbody.hide-for-xlarge-only, tbody.show-for-xlarge-up, tbody.hide-for-xlarge, tbody.hide-for-xlarge-down, tbody.show-for-xxlarge-only, tbody.show-for-xxlarge-up, tbody.show-for-xxlarge, tbody.show-for-xxlarge-down {
    display: table-row-group !important;
  }

  /* line 268, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.hide-for-small-only, tr.show-for-small-up, tr.hide-for-small, tr.hide-for-small-down, tr.hide-for-medium-only, tr.show-for-medium-up, tr.hide-for-medium, tr.hide-for-medium-down, tr.hide-for-large-only, tr.show-for-large-up, tr.hide-for-large, tr.hide-for-large-down, tr.hide-for-xlarge-only, tr.show-for-xlarge-up, tr.hide-for-xlarge, tr.hide-for-xlarge-down, tr.show-for-xxlarge-only, tr.show-for-xxlarge-up, tr.show-for-xxlarge, tr.show-for-xxlarge-down {
    display: table-row;
  }

  /* line 271, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.hide-for-small-only, td.hide-for-small-only, th.show-for-small-up, td.show-for-small-up, th.hide-for-small, td.hide-for-small, th.hide-for-small-down, td.hide-for-small-down, th.hide-for-medium-only, td.hide-for-medium-only, th.show-for-medium-up, td.show-for-medium-up, th.hide-for-medium, td.hide-for-medium, th.hide-for-medium-down, td.hide-for-medium-down, th.hide-for-large-only, td.hide-for-large-only, th.show-for-large-up, td.show-for-large-up, th.hide-for-large, td.hide-for-large, th.hide-for-large-down, td.hide-for-large-down, th.hide-for-xlarge-only, td.hide-for-xlarge-only, th.show-for-xlarge-up, td.show-for-xlarge-up, th.hide-for-xlarge, td.hide-for-xlarge, th.hide-for-xlarge-down, td.hide-for-xlarge-down, th.show-for-xxlarge-only, td.show-for-xxlarge-only, th.show-for-xxlarge-up, td.show-for-xxlarge-up, th.show-for-xxlarge, td.show-for-xxlarge, th.show-for-xxlarge-down, td.show-for-xxlarge-down {
    display: table-cell !important;
  }
}
/* Orientation targeting */
/* line 285, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.show-for-landscape,
.hide-for-portrait {
  display: inherit !important;
}

/* line 287, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.hide-for-landscape,
.show-for-portrait {
  display: none !important;
}

/* Specific visibility for tables */
/* line 292, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
table.hide-for-landscape, table.show-for-portrait {
  display: table !important;
}

/* line 296, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
thead.hide-for-landscape, thead.show-for-portrait {
  display: table-header-group !important;
}

/* line 300, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
tbody.hide-for-landscape, tbody.show-for-portrait {
  display: table-row-group !important;
}

/* line 304, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
tr.hide-for-landscape, tr.show-for-portrait {
  display: table-row !important;
}

/* line 309, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
td.hide-for-landscape, td.show-for-portrait,
th.hide-for-landscape,
th.show-for-portrait {
  display: table-cell !important;
}

@media only screen and (orientation: landscape) {
  /* line 314, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-landscape,
  .hide-for-portrait {
    display: inherit !important;
  }

  /* line 316, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-landscape,
  .show-for-portrait {
    display: none !important;
  }

  /* Specific visibility for tables */
  /* line 321, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.show-for-landscape, table.hide-for-portrait {
    display: table !important;
  }

  /* line 325, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.show-for-landscape, thead.hide-for-portrait {
    display: table-header-group !important;
  }

  /* line 329, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.show-for-landscape, tbody.hide-for-portrait {
    display: table-row-group !important;
  }

  /* line 333, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.show-for-landscape, tr.hide-for-portrait {
    display: table-row !important;
  }

  /* line 338, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  td.show-for-landscape, td.hide-for-portrait,
  th.show-for-landscape,
  th.hide-for-portrait {
    display: table-cell !important;
  }
}
@media only screen and (orientation: portrait) {
  /* line 344, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-portrait,
  .hide-for-landscape {
    display: inherit !important;
  }

  /* line 346, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-portrait,
  .show-for-landscape {
    display: none !important;
  }

  /* Specific visibility for tables */
  /* line 351, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.show-for-portrait, table.hide-for-landscape {
    display: table !important;
  }

  /* line 355, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.show-for-portrait, thead.hide-for-landscape {
    display: table-header-group !important;
  }

  /* line 359, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.show-for-portrait, tbody.hide-for-landscape {
    display: table-row-group !important;
  }

  /* line 363, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.show-for-portrait, tr.hide-for-landscape {
    display: table-row !important;
  }

  /* line 368, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  td.show-for-portrait, td.hide-for-landscape,
  th.show-for-portrait,
  th.hide-for-landscape {
    display: table-cell !important;
  }
}
/* Touch-enabled device targeting */
/* line 374, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.show-for-touch {
  display: none !important;
}

/* line 375, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.hide-for-touch {
  display: inherit !important;
}

/* line 376, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch .show-for-touch {
  display: inherit !important;
}

/* line 377, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch .hide-for-touch {
  display: none !important;
}

/* Specific visibility for tables */
/* line 380, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
table.hide-for-touch {
  display: table !important;
}

/* line 381, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch table.show-for-touch {
  display: table !important;
}

/* line 382, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
thead.hide-for-touch {
  display: table-header-group !important;
}

/* line 383, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch thead.show-for-touch {
  display: table-header-group !important;
}

/* line 384, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
tbody.hide-for-touch {
  display: table-row-group !important;
}

/* line 385, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch tbody.show-for-touch {
  display: table-row-group !important;
}

/* line 386, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
tr.hide-for-touch {
  display: table-row !important;
}

/* line 387, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch tr.show-for-touch {
  display: table-row !important;
}

/* line 388, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
td.hide-for-touch {
  display: table-cell !important;
}

/* line 389, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch td.show-for-touch {
  display: table-cell !important;
}

/* line 390, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
th.hide-for-touch {
  display: table-cell !important;
}

/* line 391, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.touch th.show-for-touch {
  display: table-cell !important;
}

/* Screen reader-specific classes */
/* line 394, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.show-for-sr {
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute !important;
  width: 1px;
}

/* line 397, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.show-on-focus {
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute !important;
  width: 1px;
}
/* line 400, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.show-on-focus:focus, .show-on-focus:active {
  position: static !important;
  height: auto;
  width: auto;
  overflow: visible;
  clip: auto;
}

/*
 * Print styles.
 *
 * Inlined to avoid required HTTP connection: www.phpied.com/delay-loading-your-print-css/
 * Credit to Paul Irish and HTML5 Boilerplate (html5boilerplate.com)
*/
/* line 414, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
.print-only {
  display: none !important;
}

@media print {
  /* line 416, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  * {
    background: transparent !important;
    box-shadow: none !important;
    color: #000000 !important;
    /* Black prints faster: h5bp.com/s */
    text-shadow: none !important;
  }

  /* line 422, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-print {
    display: block;
  }

  /* line 423, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-print {
    display: none;
  }

  /* line 425, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.show-for-print {
    display: table !important;
  }

  /* line 426, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.show-for-print {
    display: table-header-group !important;
  }

  /* line 427, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.show-for-print {
    display: table-row-group !important;
  }

  /* line 428, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.show-for-print {
    display: table-row !important;
  }

  /* line 429, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  td.show-for-print {
    display: table-cell !important;
  }

  /* line 430, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.show-for-print {
    display: table-cell !important;
  }

  /* line 432, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  a,
  a:visited {
    text-decoration: underline;
  }

  /* line 434, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  a[href]:after {
    content: " (" attr(href) ")";
  }

  /* line 436, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  abbr[title]:after {
    content: " (" attr(title) ")";
  }

  /* line 439, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }

  /* line 443, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  pre,
  blockquote {
    border: 1px solid #999999;
    page-break-inside: avoid;
  }

  /* line 449, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead {
    display: table-header-group;
    /* h5bp.com/t */
  }

  /* line 451, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr,
  img {
    page-break-inside: avoid;
  }

  /* line 454, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  img {
    max-width: 100% !important;
  }

  @page {
    margin: .5cm;
  }
  /* line 458, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }

  /* line 465, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  h2,
  h3 {
    page-break-after: avoid;
  }

  /* line 468, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-on-print {
    display: none !important;
  }

  /* line 469, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .print-only {
    display: block !important;
  }

  /* line 470, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-print {
    display: none !important;
  }

  /* line 471, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-print {
    display: inherit !important;
  }
}
/* Print visibility */
@media print {
  /* line 477, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-print {
    display: block;
  }

  /* line 478, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .hide-for-print {
    display: none;
  }

  /* line 480, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  table.show-for-print {
    display: table !important;
  }

  /* line 481, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  thead.show-for-print {
    display: table-header-group !important;
  }

  /* line 482, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tbody.show-for-print {
    display: table-row-group !important;
  }

  /* line 483, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  tr.show-for-print {
    display: table-row !important;
  }

  /* line 484, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  td.show-for-print {
    display: table-cell !important;
  }

  /* line 485, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  th.show-for-print {
    display: table-cell !important;
  }
}
@media not print {
  /* line 489, /home/<USER>/.rvm/gems/ruby-2.3.8/gems/foundation-rails-*******/vendor/assets/stylesheets/foundation/components/_visibility.scss */
  .show-for-print {
    display: none !important;
  }
}
