require File.expand_path('../boot', __FILE__)

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)
require './lib/normalized_request_middleware'

module MirrawMobile
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    config.time_zone = 'Mumbai'

    # The default locale is :en and all translations from config/locales/*.rb,yml are auto loaded.
    # config.i18n.load_path += Dir[Rails.root.join('my', 'locales', '*.{rb,yml}').to_s]
    # config.i18n.default_locale = :de

    # Do not swallow errors in after_commit/after_rollback callbacks.
    config.active_record.raise_in_transactional_callbacks = true

    ActionMailer::Base.smtp_settings = {
      address: ENV.fetch('MAILER_HOST'), port: ENV.fetch('MAILER_PORT'),
      domain: ENV.fetch('MIRRAW_DOMAIN'), authentication: :plain,
      user_name: ENV.fetch('MAILER_USER_NAME'),
      password: ENV.fetch('MAILER_PASSWORD')
    }

    config.action_mailer.default_url_options = {
      host: ENV.fetch('MIRRAW_DOMAIN')
    }

    config.google_analytics = ENV.fetch('GOOGLE_ANALYTICS')
    config.google_analytics_amp = ENV.fetch('GOOGLE_ANALYTICS_AMP')
    config.google_analytics_url = Rails.env.production? ? 'mirraw.com' : 'staging-mobile.mirraw.in'
    config.google_optimize = ENV.fetch('GOOGLE_OPTIMIZE')
    config.fb_app_id = ENV.fetch('FB_APP_ID')
    config.fb_app_secret = ENV.fetch('FB_APP_SECRET')
    config.fb_namespace = ENV.fetch('FB_NAMESPACE')
    config.fb_channel_url_base = ENV.fetch('FB_CHANNEL_URL_BASE')

    config.partial_protocol = (Rails.env.production? || Rails.env.staging?) ? 'https' : 'http'

    config.gtm_mobile_amp_id = ENV['GOOGLE_TAG_MANAGER_MOBILE_AMP_ID']
    #dial2vrify Token
    config.dial2verify_token = 'RA$46C22BF1-BF3E-11E4-B16C-94DE80A5CEAB'

    # Settings for the pool of renderers
    config.react.server_renderer_pool_size  ||= 1  # ExecJS doesn't allow more than one on MRI
    config.react.server_renderer_timeout    ||= 15 # seconds
    config.react.server_renderer = React::ServerRendering::SprocketsRenderer
    config.react.server_renderer_options = {
      files: ["react.js", "components.js"], # files to load for prerendering
      replay_console: true,                 # if true, console.* will be replayed client-side
    }

    config.paperclip_defaults = {
      storage: :s3,
      s3_credentials: "#{Rails.root}/config/s3.yml",
      path: ":class/:id/:basename_:style.:extension",
      bucket: ENV['S3_BUCKET'],
      s3_region: ENV['AWS_REGION'],
      default_url: "/processing.jpg",
      url: ":s3_alias_url",
      s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate },
      s3_protocol: (ENV['HTTP2_ENABLE'] == 'true' ? :https : ''),
      s3_host_alias:  if ENV.fetch('AKAMAI') == '1'  && ENV.fetch('PREFIX_AKAMAI_ASSETS_URL')
                        Proc.new {|a| "assets0.mirraw.com" }
                      elsif Rails.env.production?
                        Proc.new {|a| "assets0.mirraw.com" }
                      else
                        ENV['CLOUDFRONT_URL']
                      end
    }

    config.pay_with_amazon = YAML.load(ERB.new(File.read("config/pay_with_amazon.yml")).result)[Rails.env].symbolize_keys!
    config.g2a = YAML.load(ERB.new(File.read("#{Rails.root}/config/g2a.yml")).result)[Rails.env].symbolize_keys!
    # Background JOBS
    config.active_job.queue_adapter = :sidekiq

    # config.cache_store = :dalli_store,{pool_size: [ENV['MEMCACHIER_POOL_SIZE'].to_i,5].max}
    config.cache_store = :null_store
    config.action_dispatch.ip_spoofing_check = false

    # Forcing ssl redirection for api requests
    if false #Rails.env.production? || Rails.env.staging?
      config.middleware.insert_before 0, Rack::SslEnforcer,
        strict: true,
        force_secure_cookies: false
        # only: ['/api', '/orders/payu_response', '/orders/new', '/accounts', '/orders/paytm_response'],
        #ignore: %w{/api/v1/user/wallets/apply_wallet_referral /api/v1/user/wallets/remove_wallet_referral /api/v1/user/wallets/apply_wallet_refund  /api/v1/user/wallets/remove_wallet_refund /api/v1/menu /api/v1/banner_slider /api/v1/frontpage /api/v1/frontpage_with_banners /api/v1/feature_products /api/v1/addresses/pincode_info } << %r{/api/v1/designs/(\d)+/reviews/rating} << %r{/api/v1/designs/(\d)+/reviews} << %r{/assets} << %r{/orders} << %w{/order/amazon_success /order/cod_verify} << %w{/carts/generate_otp /carts/verify_otp} << %r{/country}
    end
    
    config.middleware.use Rack::Attack
    config.middleware.use "NormalizedRequestMiddleware"
    config.middleware.use Rack::Deflater

    # Enable serving of images, stylesheets, and JavaScripts from an asset server.
    # with %d it generates consistent arbitrary host modulo 4 (0 to 3)
    # if host =~ /%d/ host = host % (Zlib.crc32(source) % 4)
    if ENV['ASSET_HOST'].present?
      config.action_controller.asset_host = Proc.new { |source|
        asset_host = ENV['ASSET_HOST']
        if asset_host =~ /%d/
          asset_host = asset_host % (Zlib.crc32(source) % 2)
        end
        asset_host
      }
    end

    if Rails.env.development?
      config.logger = Logger.new('/dev/null')
    else
      config.logger = Logger.new(STDOUT) if ENV.fetch('LOG_ENABLED') == '1'
    end
    config.action_dispatch.default_headers.merge!('Cache-Control' => 'no-transform, max-age=0')

    # Load react errors as in development mode in all enviroments
    config.react.variant = :development
    config.exceptions_app = self.routes
    # razorpay payment gateway
    config.razor_key_id = ENV['RAZOR_KEY_ID']
    # BranchIO key
    config.branch_key = ENV.fetch('BRANCH_KEY')

    # Unbxd config values
    config.unbxd = {UNBXD_API_KEY: ENV.fetch('UNBXD_API_KEY'), UNBXD_SITE_KEY: ENV.fetch('UNBXD_SITE_KEY'), UNBXD_SERVICE_URL: ENV.fetch('UNBXD_SERVICE_URL')}
    
    config.paypal_smart_pay = YAML.load(ERB.new(File.read("#{Rails.root}/config/paypal_smart_checkout.yml")).result)[Rails.env].symbolize_keys!
    config.gokwik_urls = YAML.load(ERB.new(File.read("#{Rails.root}/config/gokwik.yml")).result)[Rails.env].symbolize_keys!
    config.gokwik_credentials = {appid: ENV.fetch('GOKWIK_APP_ID'), appsecret: ENV.fetch('GOKWIK_APP_SECRET')}
    config.autoload_paths << Rails.root.join('lib')
  end
end
