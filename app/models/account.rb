# == Schema Information
#
# Table name: accounts
#
#  id                     :integer          not null, primary key
#  email                  :string(255)      default(""), not null
#  encrypted_password     :string(128)      default(""), not null
#  reset_password_token   :string(255)
#  reset_password_sent_at :datetime
#  remember_created_at    :datetime
#  sign_in_count          :integer          default(0)
#  current_sign_in_at     :datetime
#  last_sign_in_at        :datetime
#  current_sign_in_ip     :string(255)
#  last_sign_in_ip        :string(255)
#  accountable_id         :integer
#  accountable_type       :string(255)
#  uid                    :string(255)
#  provider               :string(255)
#  created_at             :datetime
#  updated_at             :datetime
#  notified               :integer
#  notified_date          :datetime
#  token                  :string(255)
#  confirmation_token     :string(255)
#  confirmed_at           :datetime
#  confirmation_sent_at   :datetime
#  unconfirmed_email      :string(255)
#  tokens                 :text
#

class Account < ActiveRecord::Base
  # Include default devise modules. Others available are:
  # :token_authenticatable, :encryptable, :confirmable, :lockable, :timeoutable
  devise :database_authenticatable, :registerable, :async,
    :recoverable, :rememberable, :trackable, :validatable, :confirmable, authentication_keys: {email: false, login: false}

  belongs_to :accountable, polymorphic: true
  has_one :api_data
  has_one :api_notification_settings, through: :api_data
  after_update :mark_non_guest_account, if: :encrypted_password_changed?
  has_many :video_listings
  include DeviseTokenAuth::Concerns::User
  devise :omniauthable, :omniauth_providers => [:facebook, :google_oauth2]
  validates :phone, uniqueness: true, numericality: true, allow_nil: true
  validates_length_of :phone, minimum: 7, maximum: 13, allow_blank: true
 
  validates :password, format: { without: /\s/, message: "cannot contain spaces" }

  # validates :email, presence: { message: I18n.t('errors.messages.blank_field') }
  # validate :validate_email_format

  # validates :password, presence: { message: I18n.t('errors.messages.blank_field') }, if: :password_required?
  # validates :password_confirmation, presence: { message: I18n.t('errors.messages.blank_password_confirmation') },  allow_nil: true, if: -> { password_required? && !skip_confirmation_validation? }
  attr_accessor :login
  # attr_accessor :skip_confirmation_validation
  def login
    @login || self.phone || self.email
  end

  def verify_otp(otp)
    return false unless self.phone.present?
    OneTimePassword.verify('91'+self.phone, otp)
  end

  # def validate_email_format
  #   if email.present? && !email.match(URI::MailTo::EMAIL_REGEXP)
  #     errors.add(:email, I18n.t('errors.messages.invalid_email'))
  #   end
  # end

  # def skip_confirmation_validation?
  #   self.skip_confirmation_validation || false
  # end

  # def password_required?
  #   !persisted? || !password.nil? || !password_confirmation.nil?
  # end

  # Commenting out this code to prevent issues on the guest checkout address page.
  # Keep this commented, as it may reappear when pulling updates in the future.
  
  class << self
    # Overriding the new_with_session method of devise for adding user for account while
    # registration and also assigning uid(as email) to account.
    #
    # == Returns
    #  Account
    #

    def find_first_by_auth_conditions(warden_conditions)
      conditions = warden_conditions.dup
      if login = conditions.delete(:login)
        where(conditions).where(["phone = :value OR email = :value", { :value => login.downcase }]).first
      else
        conditions.permit! if conditions.class.to_s == "ActionController::Parameters"
        where(conditions).first
      end
    end

    def new_with_session(params, session)
      super.tap do |account|
        if session["devise.facebook_data"] && data = session["devise.facebook_data"]['info']
          account.email = data["email"]
        elsif session["devise.google_oauth2_data"] && data = session["devise.google_oauth2_data"]['info']
          account.email = data['email']
        end
        # Skip confirmation if value is set in system constant
        if SystemConstant.find_by_name('SKIP_ACCOUNT_CONFIRMATION').value == 'true' && !account.guest_account
          account.skip_confirmation!
        end
      end
    end

    def from_omniauth(auth)
      where(email: auth.info.email).first_or_initialize do |account|
        if account.new_record?
          account.attributes = {
            provider: 'email',
            uid: auth.info.email,
            password: Devise.friendly_token[0,20],
            accountable: User.new(
              email: auth.info.email,
              image_url: auth.info.image
            )
          }
          # account.skip_confirmation_validation = true
          account.skip_confirmation!
          
          account.save
        end
      end
    end

    def auto_create_account_user(params)
      acc = Account.new(params)
      acc.uid = acc.email
      acc.provider = "email"
      acc.accountable = User.new( email: acc.email )
      acc.skip_confirmation!
      acc.save!
      acc
    end
  end


  # Provides accountable object if accountable is an user
  #
  # == Return:
  # User / Nil Object
  #
  def user
    accountable if user?
  end

  def name
    first, last = self.email.split('@')
    first
  end

  # Checks wether account belongs to user object
  #
  # == Return:
  # Boolean
  #
  def user?
    accountable_type == 'User'
  end

  def set_api_data_values(device_id, app_source, app_version, country)
    current_api_data = self.api_data
    if current_api_data.present?
      current_api_data.update_data(device_id, app_source, app_version, country)
    else
      self.create_api_data({
        device_id: device_id,
        currency_convert_id: country.id,
        app_source: app_source,
        app_version: app_version,
        wishlist_public: true
      })
    end
    if self.api_data.present? && self.api_data.api_notification_settings.nil? && ALLOWED_APP_VERSIONS.include?(app_version)
      self.api_data.create_api_notification_settings({
        designer_coupon: true,
        designer_new_uploads: true,
        designer_weekly_hot_selling: true,
        price_drop_cart: true,
        price_drop_wishlist: true,
        quantity_drop_cart: true,
        quantity_drop_wishlist: true,
        user_rated_designs: true,
        user_wishlist: true
      })
    end
  end

  def send_registration_details(password, path)
    Account.sidekiq_delay.send_registration_mail_to_guest_user(self.id, password.tr(ALPHANUM, ENCODING_KEY), path)
  end

  def add_phone_to_guest_account(address_params)
    if self.guest_account && self.phone.nil?
      address_phone = address_params[:phone]
      if (country = Country.find_by_name(address_params[:country])).present?
        dial_code = "+" + country.dial_code
        if address_phone.start_with?(dial_code)
          actual_phone = address_phone.sub!(dial_code, "")
          account_exists = Account.exists?(phone: actual_phone)
          if !account_exists
            self.phone = actual_phone
            self.dial_code = dial_code
            self.save
          end
        end
      end
    end
  end

  def send_pwd_reset_link_via_sms
    raw = set_reset_password_token
    phone_no = dial_code + phone
    phone_no.gsub!('+', '')
    url = "https://#{MIRRAW_DOMAIN}/accounts/password/edit?reset_password_token=#{raw}"
    sms_template = "Please click on the below link to start your journey with Mirraw.\n#{url}\nThis link will expire after #{Devise.reset_password_within / 3600} hours."
    sms_template = URI.encode(sms_template)
    sms_api_params = SMS_API.sub('{phone}', phone_no).sub('{template}', sms_template)
    res = HTTParty.get(sms_api_params, timeout: 10)
  end

  def set_reset_password_token
    raw, enc = Devise.token_generator.generate(self.class, :reset_password_token)
    self.reset_password_token   = enc
    self.reset_password_sent_at = Time.current
    self.skip_confirmation! if !!self.guest_account
    save(validate: false)
    raw
  end

  def add_to_subscribers(source_url, country_code, app_source, ip_address)
    params = {
      email: email,
      source_url: source_url,
      appsource: app_source,
      country: country_code,
      ip_address: ip_address || current_sign_in_ip
    }
    Subscription.add(params)
  end

  # delayed
  def self.send_registration_mail_to_guest_user(_account_id, _password, _path); end

  def self.push_api_params_to_s3(req); end
  
  # used by devise
  # overriding to allow guest user to change password without old password
  def update_without_password(params, *options)
    if guest_account?
      result = update_attributes(params, *options)
      clean_up_passwords
      result
    else
      super
    end
  end

  def mark_non_guest_account
    if encrypted_password_changed? && guest_account? && !encrypted_password_was.nil?
      update_attribute(:guest_account, false)
    end
  end

  include ForceDowncaseWriters.new(:email)
end
