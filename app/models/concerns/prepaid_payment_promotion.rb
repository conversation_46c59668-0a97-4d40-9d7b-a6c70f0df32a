class PrepaidPaymentPromotion
  
  module Promotable

    def percent_luxe
      @percent ||= parsed_variables_hash['percent_luxe'].to_i
    end

    def limit_luxe
      @limit ||= parsed_variables_hash['price_luxe'].to_i
    end

    def amount_over_luxe
      @amount_over ||= parsed_variables_hash['amount_over_luxe'].to_i
    end

    def design_message
      "Get 10% Off with Coupon Code <span style='font-weight: bold;'>'LUXE10'</span> ".html_safe
    end
  end

  attr_reader :cart, :country_code

  def initialize(cart, country_code )
    @cart = cart
    @country_code = country_code
    @cart.prepaid_payment_promotion = self
  end

  def self.promotions
    PromotionPipeLine.where{name =~ 'prepaid_payment_promotion_luxe%'}
  end

  def self.active_promotions(country_code)
    RequestStore.cache_fetch("prepaid_payment_promotion_luxe_#{country_code}_#{User.app_source.split('-')[0]}", :expires_in => 24.hours) do
      pp = promotions.active_promotions.app_source(User.app_source.split('-')[0]).for_country(country_code)
      pp.each{|promotion| class << promotion; include Promotable ; end }.to_a
    end
  end

  def self.active?(country_code)
    active_promotions(country_code).present?
  end

  def self.design_message(country_code)
    active_promotions(country_code).collect(&:design_message).first
  end

  def self.tnc_message
    Rails.cache.fetch("prepaid_payment_promotion_tnc_IN") do
      promotion = active_promotions('IN').last
      tnc_msg = [
      "What is the offer ?"
      ]
      if promotion.amount_over_luxe > 0
        tnc_msg << "You will receive a #{promotion.percent}% off Discount on shopping over Rs.#{promotion.amount_over_luxe} upto Rs.#{promotion.limit}."
        tnc_msg << "Offer valid upon minimum order amount of Rs.#{promotion.amount_over_luxe}."
      else
        tnc_msg << "You will receive a #{promotion.percent}% off discount on Online Payment upto Rs.#{promotion.limit}."
      end
      tnc_msg << "Online payment includes payment using Cards, Net Banking,  PayPal, UPI ,Wallets (Paytm, Phonepe, Mobikwik, Amazon Pay, Freecharge etc)."
      tnc_msg << "Cash On Delivery is not valid for the offer."
      tnc_msg << "Offer terms are subject to change."
      tnc_msg
    end
  end

  def self.cart_message(country_code)
    if (promotion = active_promotions(country_code).last).present?
      offer = "Get Special #{promotion.percent}% OFF on Online Payment"
      if promotion.amount_over_luxe == 0
        "#{offer}"
      else
        "#{offer} on Shopping Above Rs. #{promotion.amount_over_luxe}"
      end
    end
  end

  def is_promotable?
    eligible_promotion.present?
  end

  def percent
    is_promotable? ? eligible_promotion.percent : 0
  end

  def discounted_price(amount)
    if is_promotable?
      promotion = active_promotions.last
      if amount >= promotion.amount_over_luxe
        off = (amount * promotion.percent / 100)
        off <= promotion.limit ? off : promotion.limit
      end
    end
  end

  def cart_message
    if is_promotable?
      promotion = active_promotions.last
      offer = "Get Special <span style='color:RGB(56,118,29);font-weight:bold'>#{promotion.percent}% OFF </span> on Online Payment"
      if promotion.amount_over_luxe == 0
        "#{offer}"
      else
        "#{offer} on Shopping Above Rs. #{promotion.amount_over_luxe}"
      end
    end
  end

  def active_promotions
    @active_promotions = self.class.active_promotions(@country_code)
  end

  def variables_hash=(value)
    @percent = @limit =  @amount_over = nil
    super
  end

  private

  def eligible_promotion
    @eligible_promotion ||= active_promotions.last
  end
end