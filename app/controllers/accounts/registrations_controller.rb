class Accounts::RegistrationsController < Devise::RegistrationsController
  include <PERSON><PERSON><PERSON>til

  before_filter :user_currency
  prepend_before_filter :require_no_authentication, only: [:new, :create, :cancel, :create_guest]
  respond_to :html, :js
  
  def create
    build_resource(sign_up_params)
    resource.save
    yield resource if block_given?
    if resource.persisted?
      if resource.active_for_authentication?
        set_flash_message(:notice, :signed_up)
        sign_up(resource_name, resource)
        respond_with resource, location: after_sign_up_path_for(resource)
      else
        set_flash_message(:notice, :"signed_up_but_#{resource.inactive_message}")
        expire_data_after_sign_in!
        respond_with resource, location: after_inactive_sign_up_path_for(resource)
      end
    else
      clean_up_passwords resource
      set_minimum_password_length
      respond_with resource
    end
  end

  def create_guest
    redirect_to new_order_path if account_signed_in?
    update_phone_params_with_dial_code
    shipping_params = address_params[:shipping_address]
    if params[:ship_to_same_address].present? || params[:ship_to_same_address] != '1'
      @shipping_address = Address.new(shipping_params) if shipping_params.present?
    end
    @address = Address.new(address_params.except(:shipping_address))
    account_email = params[:address][:email].try(:downcase)
    if (account_email.present?) && (account = Account.find_by_email(account_email)).present?
      account.user? ? self.resource = account : failure_reason = 'Email does not belong to a user'
    elsif account_email.present?
      hash = {email: account_email, guest_account: true}
      hash[:password] = hash[:password_confirmation] = Devise.friendly_token.first(6)
      build_resource(hash)
      resource.user.first_name = resource.email.split('@').first if resource.email.present?
      failure_reason = 'Invalid email' unless resource.save
    else
      hash = {email: Faker::Name.first_name+Faker::Number.number(10)+"<EMAIL>", guest_account: true}
      hash[:password] = hash[:password_confirmation] = Devise.friendly_token.first(6)
      build_resource(hash)
      resource.user.first_name = resource.email.split('@').first if resource.email.present?
      failure_reason = 'Invalid email' unless resource.save
    end
    @address.user_id = resource.user.id unless failure_reason.present?
    if @shipping_address.present?
      @shipping_address.user_id = resource.user.id unless failure_reason.present?
      @shipping_address.save
    end
    if failure_reason.nil? && @address.save
      sign_in_as_guest
      session[:shipping_address] = @shipping_address.id if @shipping_address.present?
      session[:guest_shipping_address] = @address.id
      if resource.terms_of_service
        resource.add_to_subscribers('mobile/guest/login', @country_code, 'mobile', request.headers['True-Client-IP'] || request.ip)
      end
      redirect_to new_order_path
    else
      flash.now[:error] = @address.errors.full_messages.join(', ')
      @countries = Country.priority_wise_countries.pluck(:name).uniq
      flash.now[:error] = failure_reason if failure_reason.present?
      if luxe_site?
        render 'luxe/devise/sessions/guest_login', layout: 'application_luxe'
      else
        render 'devise/sessions/guest_login'
      end
    end
  end

  protected
  # Build a devise resource passing in the session. Useful to move
  # temporary session data to the newly created user.
  def build_resource(hash=nil)
    self.resource = resource_class.new_with_session(hash || {}, session)
    self.resource.uid = resource.email
    self.resource.provider = 'email'
    self.resource.accountable = User.new( email: resource.email )

    # Skip confirmation if value is set in system constant
    if SystemConstant.find_by_name('SKIP_ACCOUNT_CONFIRMATION').value == 'true' && !self.resource.guest_account
      self.resource.skip_confirmation!
    end
  end


  def sign_in_as_guest
    sign_out if account_signed_in?
    session[:guest_account_id] = resource.id
  end

  def after_sign_up_path_for(resource)
    resource.add_to_subscribers('mobile/email/login', @country_code, 'mobile', request.headers['True-Client-IP'] || request.ip) if resource.terms_of_service
    super
  end

  def update_resource(resource, params)
    if resource.guest_account
      resource.update_without_password(params)
    else
      super
    end
  end

  def address_params
    params.require(:address).permit(:first_name, :last_name, :name, :street_address, :landmark, :city,
      :state, :country, :pincode, :phone, :default, *Address.street_address_lines, shipping_address: [:first_name, :last_name, :name, :street_address, :landmark, :city,
        :state, :country, :pincode, :phone, :default, *Address.street_address_lines])
  end
end 
