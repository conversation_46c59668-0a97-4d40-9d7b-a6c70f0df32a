class Accounts::<PERSON><PERSON>ontroller < Devise::SessionsController
  prepend_before_filter :require_no_authentication, only: [:new, :create, :guest_login, :create_guest_session]

  def create
    super do |resource|

      # No need of guest account details after account sign in
      session.delete(:guest_account_id)
      session.delete(:guest_shipping_address)

      # Adding User to account whose users was deleted from system
      if resource.accountable_type.nil? || resource.accountable_type == 'User'
        if resource.user.blank?
          resource.accountable = User.new( email: resource.email )
          resource.save
        end
      else
        flash.delete(:notice)
        sign_out(resource)
        session.delete("account_return_to")
        flash[:alert] = 'Unauthorized to access the page. please contact administrator!'
      end
      reset_current_web_cart(resource)
    end
  end

  def guest_login
    if account_signed_in?
      redirect_to new_order_path
      return
    end
    unless current_web_cart || current_web_cart.line_items.exists?
      redirect_to root_path
      return
    end
    @mobile_checkout =  if params[:mc]=='mobile_checkout'
                          true
                        elsif M<PERSON><PERSON>LE_CHECKOUT_PERCENTAGE == 1
                          true
                        else
                          rand(1..MOBILE_CHECKOUT_PERCENTAGE) == 1
                        end
    self.resource = resource_class.new(sign_in_params)
    clean_up_passwords(resource)
    @countries = Country.priority_wise_countries.pluck(:name).uniq
    address_ga_hash_v2
    # Aapno Rajasthan
    # if current_web_cart.present?
    #   items = current_web_cart.line_items.includes(design: :categories)
    #   @countries -=  ['India'] if (items.any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && items.any?{|item| item.design.categories.any?{|c|c.name == 'rakhi-international'}})
    # end
  end
  
  def address_ga_hash_v2
    @ga_hash_new = {}
    design_prices = []
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    b1g1_offers = ApplicationController.helpers.get_price_in_currency(current_web_cart.bmgnx_discounts(bmgnx_hash = PromotionPipeLine.bmgnx_hash), @rate)
    b1g1_offers = (b1g1_offers *= market_rate).round(CurrencyConvert.round_to)
    @ga_hash_new['value'] = current_web_cart.total_currency(1)
    @ga_hash_new['shipping_tier'] = "Regular"
    @ga_hash_new['tax'] = 0
    @ga_hash_new['offers_discount'] = b1g1_offers
    @ga_hash_new['shipping'] = current_web_cart.shipping_cost_currency(nil,1)
    @ga_hash_new['currency'] = 'INR'
    @ga_hash_new['coupon'] = current_web_cart.coupon.try(:code) || ""
    @ga_hash_new['coupon_discount'] = 0
    @ga_hash_new['country_code'] = @country_code
  
    @ga_hash_new['items'] =  current_web_cart.line_items.map do |line_item|
        design = Design.where(id: line_item.design_id).first
        category = design.categories.first.breadcrumb_path.keys
        category_title = design.categories.first.title
        category_id = design.categories.first.id
        line_item_data = line_item.ga_data
        line_item_data['item_category'] = category[1] || ""
        line_item_data['item_category2'] = category[2] || ""
        line_item_data['item_category3'] = category[3] || ""
        line_item_data['item_category4'] = category[4] || ""
        line_item_data['item_category5'] = category[5] || ""
        line_item_data['discount'] *= line_item.quantity
        line_item_data['price'] *= line_item.quantity
        line_item_data['item_customization'] *= line_item.quantity
        design_prices << line_item_data['price']
        line_item_data
    end
    @totalvalue = design_prices.count() > 1 ? design_prices.sum : design_prices[0] || 0
    @totalvalue = (@totalvalue - b1g1_offers).round(CurrencyConvert.round_to)
  end

  def mobile_login
    account = Account.find_by_phone(params[:user_phone])
    if account && account.verify_otp(params[:one_time_password])
      sign_in(account)
      flash[:notice] = 'Signed in successfully!'
      render json: {error: false, status: 200, location: after_sign_in_path_for(account)}
    else
      render json: {error: true, status: 422}
    end
  end

  def destroy
    super do |resource_name|
      flash.delete(:notice) 
      set_flash_message :error, :signed_out if Devise.sign_out_all_scopes   
    end
  end
end
