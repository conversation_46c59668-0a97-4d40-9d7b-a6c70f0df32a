class Accounts::<PERSON><PERSON><PERSON>roller < Devise::SessionsController
  prepend_before_filter :require_no_authentication, only: [:new, :create, :guest_login, :create_guest_session]
  respond_to :html, :js
  def create
    super do |resource|

      # No need of guest account details after account sign in
      session.delete(:guest_account_id)
      session.delete(:guest_shipping_address)
      # Adding User to account whose users was deleted from system
      if resource.accountable_type.nil? || resource.accountable_type == 'User'
        if resource.user.blank?
          resource.accountable = User.new( email: resource.email )
          resource.save
        end
      else
        flash.delete(:notice)
        sign_out(resource)
        session.delete("account_return_to")
        flash[:alert] = 'Unauthorized to access the page. please contact administrator!'
      end
      reset_current_web_cart(resource)
      if params[:redirect_to_checkout] == 'true' || request.referer&.include?("redirect_to_checkout=true")
        render js: "window.location = '#{new_order_path}'"
        return
      end
    end
  end

  def guest_login
    if account_signed_in?
      redirect_to new_order_path
      return
    end
    unless current_web_cart || current_web_cart.line_items.exists?
      redirect_to root_path
      return
    end
    @mobile_checkout =  if params[:mc]=='mobile_checkout'
                          true
                        elsif MOBILE_CHECKOUT_PERCENTAGE == 1
                          true
                        else
                          rand(1..MOBILE_CHECKOUT_PERCENTAGE) == 1
                        end
    self.resource = resource_class.new(sign_in_params)
    clean_up_passwords(resource)
    @countries = Country.priority_wise_countries.pluck(:name).uniq
    address_ga_hash_v2
    # Aapno Rajasthan
    # if current_web_cart.present?
    #   items = current_web_cart.line_items.includes(design: :categories)
    #   @countries -=  ['India'] if (items.any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && items.any?{|item| item.design.categories.any?{|c|c.name == 'rakhi-international'}})
    # end
    render 'luxe/devise/sessions/guest_login', layout: 'application_luxe' if luxe_site?
  end

  def address_ga_hash_v2
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    item_total = ((current_web_cart.items_total_without_addons(@rate))  * market_rate).round(2)
    coupon_discounts = (current_web_cart.total_discounts_currency(@rate,@country_code) * market_rate).round(2)
    coupon_percent = ((coupon_discounts/item_total) * 100).round(2)
    value = (item_total - (item_total * coupon_percent)/100).round(2)
    coupon_code = current_web_cart.coupon.present? ? current_web_cart.coupon.code : ""
    @ga_hash_new = {
      "event": "add_shipping_info",
      "ecommerce": {
        "currency":  "INR",
        "value":  value,
        "shipping": "",
        "shipping_tier": "Regular",
        "coupon": coupon_code,
        "coupon_discounts": coupon_discounts,
        "items": []
      }
    }
  
      current_web_cart.line_items.each_with_index do |line_item, index|
        hash = line_item.design.categories.first.breadcrumb_path
        crumbs_category = hash.keys if hash.present?
        item_category = crumbs_category[1] if crumbs_category.present?
        item_category2 = crumbs_category[2] if crumbs_category.present?
        item_category3 = crumbs_category[3] if crumbs_category.present?
        item_category4 = crumbs_category[4] if crumbs_category.present?
        item_category5 = crumbs_category[5] if crumbs_category.present?
        last_category_name = crumbs_category[-1] if crumbs_category.present?
        assign_category_title = line_item.design.categories.first.name
        last_category_id = Category.find_by("LOWER(name) = '#{last_category_name.try(:downcase)}'").try(:id) if last_category_name.present? 
        price = line_item.design.price_currency(@rate)
        discount_price = line_item.design.effective_price_currency(@rate)
        price_without_coupon = ((discount_price) * market_rate).round(2)
        price_with_coupon = (price_without_coupon - (price_without_coupon * coupon_percent)/100).round(2)
        price_with_coupon = (price_without_coupon - (price_without_coupon * coupon_percent)/100).round(2)
        item = {
          "item_id": "#{line_item.design.id}",
          "item_name": line_item.design.title,
          "price": (price_with_coupon * line_item.quantity).round(2),
          "discount": (((price-discount_price) * market_rate).round(2) * line_item.quantity).round(2),
          "item_brand": line_item.design.designer.name || "",  
          "item_category": item_category || "",
          "item_category2": item_category2 || "",
          "item_category3": item_category3 || "",
          "item_category4": item_category4 || "",
          "item_category5": item_category5 || "",
          "item_variant": line_item.design.color || " ",
          # "content_category": line_item.design.categories.first.name.titleize || "",
          "quantity": line_item.quantity,
          "item_list_name": last_category_name || "",
          "item_list_id": last_category_id.to_s || ""
        } 
        @ga_hash_new[:ecommerce][:items] << item
      end
  end
  
  def mobile_login
    account = Account.find_by_phone(params[:user_phone])
    if account && account.verify_otp(params[:one_time_password])
      sign_in(account)
      flash[:notice] = 'Signed in successfully!'
      render json: {error: false, status: 200, location: after_sign_in_path_for(account)}
    else
      render json: {error: true, status: 422}
    end
  end

  def destroy
    super do |resource_name|
      flash.delete(:notice) 
      set_flash_message :error, :signed_out if Devise.sign_out_all_scopes   
    end
  end
end
