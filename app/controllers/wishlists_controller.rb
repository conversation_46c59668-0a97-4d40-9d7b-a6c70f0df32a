class WishlistsController < ApplicationController
  before_action :ensure_user_account!

  def index
    @designs = current_user.unordered_wishlist_designs.
      includes(:designer, :master_image, :dynamic_price_for_current_country).
      page(params[:page]).per(30)
    @ga_list = "wishlist / user_wishlist"
    render 'luxe/wishlists/index', layout: 'application_luxe' if luxe_site?
  end

  def create
    @wishlist = Wishlist.new(wishlist_params)
    @wishlist.assign_attributes(user: current_user, conversion_rate: @rate, 
      price: @wishlist.design.try(:effective_price),
      country_code: @country_code, wish: true, state: 'added', app_source: @app_source
    )
    respond_to do |format|
      if @wishlist.save
        format.json { render json: {}, status: :created}
        format.html { redirect_to :back }
      else
        format.json { render json: {error: @wishlist.errors.full_messages}}
        format.html { redirect_to :back }
      end
    end
  end

  def filter
    if params[:design_ids].present?
      wishlist_design_ids = current_user.wishlists.where(design_id: params[:design_ids].split(',')).pluck(:design_id)
    end
    render json: {wishlist_design_ids: wishlist_design_ids || []}
  end

  def destroy
    @wishlist = current_user.wishlists.find_by(design_id: params[:design_id])
    @wishlist.destroy if @wishlist.present?
    respond_to do |format|
      format.json { head :no_content }
      format.html { redirect_to :back }
    end
  end

  private
  
    # Never trust parameters from the scary internet, only allow the white list through.
    def wishlist_params
      params.require(:wishlist).permit(:design_id)
    end

    def ensure_user_account!
      authenticate_account!
      unless current_user
        respond_to do |format|
          format.html do
            flash[:error] = 'Access denied'
            redirect_to root_url
          end
          format.json {render json: {error: 'Access Denied'}, status: :unauthorized}
        end
      end
    end
end
