class DesignsController < ApplicationController
  include ApplicationHelper
  skip_before_filter :set_cart, :user_currency, :set_menu, only: [:get_line_items_count]

  def show
    redirect_to api_v1_design_slug_path(params) and return if params['api']
    @design = get_response_data_for('design_slug', params) || {}
    # @designer = Designer.find_by_cached_slug(@design['designer_cached_slug'])
    @ga_designer_id = @designer.try(:id)
    @designer = Designer.find(params[:designer_id])
    #@design_policies = get_response_data_for('design_policies') || {}
    params = {id: @design['id'], request_url_provided: true}
    @current_user = current_user
    @review = @current_user.review_for(@design['id']) if @current_user.present?
    @similar_designs = get_response_data_for('similar_designs_api_v1_design', params)
    @my_design =  @designer.designs.find(params[:id])
    @sibling_designs  = @my_design.sibling_designs.published.preload(:designer, :master_img)
    @category = Category.find_by_namei(@design['categories'][0].try(:[],'name'))
    more_category_param = {:category_parent_id => @category.try(:id), :items_per_page=>7}
    @more_from_category = get_response_data_for('search', more_category_param)['search'] || {}
    more_designer_param = {:designer_id => @designer.try(:id), :items_per_page=>15}
    @more_from_designer = get_response_data_for('search', more_designer_param)
    if (@design.present? && @design['state'] == 'reject') || @design.empty?
      # category_name = @design['categories'][0]['name']
      # redirect_to "/store/#{category_name}",
      # notice: 'We couldn\'t find the design you were looking for.'
      if luxe_site?
        return render "luxe/pages/error_404", status: :not_found
      else
        return render "pages/error_404", status: :not_found
      end
    end
    if session[:viewing_history].present? && !session[:viewing_history].include?(@design['id'])
      #Design.increment_counter(:clicks, @design['id'])
      session[:viewing_history] << @design['id']
      session[:viewing_history].delete_at(0) if session[:viewing_history].size >= 30
    elsif session[:viewing_history].present? && session[:viewing_history].include?(@design['id'])
    else
      session[:viewing_history] = []
      session[:viewing_history] << @design['id']
      #Design.increment_counter(:clicks, @design['id'])
    end
    view_ga_data
    google_ads_data
    gtm_data_layer.push({pageType: 'product', productId: @design['id']})
    if  @design['title'].present?
      design_url = designer_design_url(@design['designer_cached_slug'], @design['cached_slug'])
      @breadcrumbs = generate_luxe_breadcrumb(category: nil, design_title: @design['title'], design_url: design_url)
    end
    @eta = 0
    if @design.present? && (pincode = request.params[:pincode]).present? && (design = Design.find request.params[:pdd_product_id]).present?
      pickup = design.sor_available? ? 'bhiwandi' : design.designer.pickup_location.try(:downcase)
      drop_city = Pincode.where(pin_code: params[:pincode]).pluck(:city).first
      city_pdd = Design.get_city_based_pdd(drop_city, [pickup]) if drop_city.present? && pickup.present?
      if city_pdd.to_i > 0
        city_pdd += design.eta unless design.sor_available?
        city_pdd += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless design.sor_available?
        city_pdd += 1 if Time.current.advance(days: city_pdd).sunday?
        @eta = (Time.now + city_pdd.days).strftime('%d %b, %Y')
      end
    end
    render 'luxe/designs/show', layout: 'application_luxe' if luxe_site?
  end

  def view_ga_data
    cc = CurrencyConvert.find_by(country_code: @country_code)
    market_rate = cc.present? ? cc.market_rate : 1
    rate = cc.present? ? cc.rate : 1
    hash = @design["breadcrumb_path"]
    crumbs_category = hash.keys if hash.present?
    item_category = crumbs_category[1] if crumbs_category.present?
    item_category2 = crumbs_category[2] if crumbs_category.present?
    item_category3 = crumbs_category[3] if crumbs_category.present?
    item_category4 = crumbs_category[4] if crumbs_category.present?
    item_category5 = crumbs_category[5] if crumbs_category.present?
    array_index = crumbs_category.length > 2 ? 2 : 1 if crumbs_category.present?
    title = crumbs_category[array_index] if crumbs_category.present? && array_index.present?
    item_listing_name, item_listing_id = [title, Category.find_by("LOWER(title) = '#{title.try(:downcase)}'").try(:id)] if title.present?
    last_category_name = crumbs_category[-1] if crumbs_category.present?
    assign_category_title = @design["category_name"]
    last_category_id = Category.find_by("LOWER(name) = '#{last_category_name.try(:downcase)}'").try(:id) if last_category_name.present?
    @ga_hash_new = {
      "event": "view_item",
      "ecommerce": {
        "currency":  "INR",
        "value":  (@design["discount_price"] * market_rate).round(2),
        "items": [
          {
            "item_id": "#{@design["id"]}",
            "item_name": @design["title"],
            "price": (@design["discount_price"] * market_rate).round(2),
            "discount": ((@design["price"] - @design["discount_price"]) * market_rate).round(2),
            "item_brand":  @design["brand"],
            "item_category": item_category || "",
            "item_category2": item_category2 || "",
            "item_category3": item_category3 || "",
            "item_category4": item_category4 || "",
            "item_category5": item_category5 || "",
            "item_variant": @design["color"] || " ",
            "content_category": @design["category_name"].titleize || "",
            "quantity": 1,
            "item_list_name": last_category_name || "",
            "item_list_id": last_category_id.to_s || ""
          }
        ]
      }
    }
  
  end

  def google_ads_data
    @googe_add_hash_new = {
    "item_ids":
    [{
        "id": "#{@design["id"]}",
        "google_business_vertical": "retail"
      }],
    "content_ids": ["#{@design["id"]}"],
    "contents": [{"id": "#{@design["id"]}","quantity":1}]
    }
  end
  def generate_breadcrumb(category: nil, design_title: nil, design_url: nil)
    if category.present?
      current_category_url = nil
      breadcrumbs = category.breadcrumb_path.collect do |title, url|
        current_category_url = url
        {title: title.to_s, url: url}
      end
      if design_title.present? && design_title.split.size > 4
        breadcrumbs << {title: design_title.titleize.reverse.truncate_words(4, omission: "").reverse, url: design_url}
      else
        breadcrumbs << {title: design_title.titleize, url: design_url}
      end
      breadcrumbs
    end.to_a
  end

  def generate_luxe_breadcrumb(category: nil, design_title: nil, design_url: nil)
    if category.present?
      current_category_url = nil
      breadcrumbs = category.breadcrumb_path.collect do |title, url|
        current_category_url = url
        {title: title.to_s, url: url}
      end
    else
      breadcrumbs = [{title: 'Home', url: '/'}]
      if false #design_title.present? && design_title.split.size > 4
        breadcrumbs << {title: design_title.titleize.reverse.truncate_words(4, omission: "").reverse, url: design_url}
      else
        breadcrumbs << {title: design_title.titleize, url: design_url}
      end
      breadcrumbs
    end.to_a
  end



  def similar
    @design = Design.find_by_cached_slug(params[:design_id])
    if @design.present?
      if (cached_designs = Rails.cache.fetch('m_similar_designs_id_' + @design.id.to_s)).present?
        @designs = Design.where(:id => cached_designs).includes([:designer, :master_image, :categories])
      else
        begin
          @similar_designs = Sunspot.more_like_this(@design) do
            fields :category_name, :designer_name, :title, :description, :specification
            with(:state, 'in_stock')
            with(:cluster_winner, 1)
            paginate :page => params[:page], :per_page => 40
          end
          ids = @similar_designs.raw_results.collect{|rr| rr.primary_key.to_i}
          @designs = Design.where(:id => ids).includes([:designer, :master_image, :categories])
          Rails.cache.write('m_similar_designs_id_' + @design.id.to_s, ids, :expires_in => 24.hours)
        rescue Errno::ECONNREFUSED
          @designs = nil
          @designs = @design.designer.designs.paginate :page => params[:page], :per_page => 40
        end
      end
    else
      redirect_to "/404", notice: 'We couldn\'t find the design you were looking for.'
    end
  end

  def get_line_items_count
    number_of_line_items = Rails.cache.fetch("line_item_count_for_design_#{params[:id]}", expires_in: 24.hours) do
      # Temp change it to faker & disabled it due to performance issue
      # LineItem.where('created_at > ?', Time.now - 1.day).where(design_id:  params[:id]).count
      rand(3..10)
    end
    render json: {count: number_of_line_items, message: Design.get_message_for(params[:quantity].to_i , number_of_line_items)}
  end

  def pdd_design
    response = {error: true, error_text: 'Something went wronge', eta: 0}
    if params[:id].present? && params[:pincode].present? && (design = Design.find params[:id]).present?
      pickup = design.sor_available? ? 'bhiwandi' : design.designer.pickup_location.try(:downcase)
      drop_city = Pincode.where(pin_code: params[:pincode]).pluck(:city).first
      city_pdd = Design.get_city_based_pdd(drop_city, [pickup]) if drop_city.present? && pickup.present?
      if city_pdd.to_i > 0
        city_pdd += design.eta unless design.sor_available?
        city_pdd += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless design.sor_available?
        city_pdd += 1 if Time.current.advance(days: city_pdd).sunday?
        response = {error: false, error_text: '', eta: (Time.now + city_pdd.days).strftime('%d %b %Y')}
      end
    end
    render json: response
  end 

  private
  
  def get_more_from_category_data(design_id)
    design = Design.find_by_cached_slug(design_id)
    category = Category.find_by_namei(design.categories.first.try(:[], 'name'))
    more_category_param = { category_parent_id: category.try(:id), items_per_page: 10 }
    response_data = get_response_data_for('search', more_category_param)['search'] || {}
    { category: category, response_data: response_data }
  end
  
  
  

end
