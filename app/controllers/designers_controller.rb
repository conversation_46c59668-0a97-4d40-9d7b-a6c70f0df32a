class DesignersController < ApplicationController
  def index
    @all_designers = RequestStore.cache_fetch('designers_list_cache', expires_in: 24.hours) do
      Designer
      .select("UPPER(REGEXP_REPLACE(name, '^[^a-zA-Z]*([a-zA-Z]).*$', '\\1')) AS first_char, ARRAY_AGG(id) AS ids,ARRAY_AGG(name) AS names, ARRAY_AGG(cached_slug) AS cached_slugs")
      .where(state_machine: ['approved', 'review'])
      .where.not(name: nil)
      .group("UPPER(REGEXP_REPLACE(name, '^[^a-zA-Z]*([a-zA-Z]).*$', '\\1'))")
      .order("first_char ASC").each_with_object({}) do |designer, hash|
        hash[designer.first_char] = {
        ids: designer.ids,
        names: designer.names,
        cached_slugs: designer.cached_slugs
      }
      end
    end
  end
end
