class SubscriptionsController < ApplicationController
  def create
    response, subscription = Subscription.add(subscription_params, @country_code, @hex_symbol)
    unless response[:error]
      cookies.permanent[:subscriptions_id] = subscription.try(:id)
      cookies.permanent[:subscribe] = 'subscribed'
    end
    render json: response
  end

  private

  def subscription_params
    subscription_params = params.require(:subscriptions).permit(:email, :source_url, :appsource, :country, :ip_address, :mobile_number)
    subscription_params[:ip_address] = request.headers['True-Client-IP'] || request.ip
    subscription_params[:country] = @country_code || session[:country][:country_code]
    subscription_params[:source_url] = subscription_params[:source_url].truncate(100)
    subscription_params.permit!
  end

end
