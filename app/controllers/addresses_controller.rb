class AddressesController < ApplicationController
  before_filter :authenticate_current_cart!, except: [:get_states_and_dial_code, :get_pincode_format]
  before_filter :authenticate_account!, except: [:collect, :store, :get_states_and_dial_code,:get_pincode_format, :create, :guest_edit, :update]
  before_filter :get_countries, only: [:new, :edit, :create, :collect, :guest_edit]
  before_filter :update_phone_params_with_dial_code, only: [:update, :store, :create]
  def index
    @addresses = @user.addresses
    unless @addresses.exists?
      redirect_to new_user_address_url
    else
      address_ga_hash_v2
      render 'luxe/addresses/index', layout: 'application_luxe' if luxe_site? && @addresses.present?
    end
  end

  def new
    @address_type = params[:address_type] || 'Billing Address'
    @address = @user.addresses.new
    address_ga_hash_v2
    fetch_account_info if luxe_site?
    render 'luxe/addresses/new', layout: 'application_luxe' if luxe_site?
  end

  def collect
    @address = Address.find_by_id(params[:address_id].to_i) if params[:address_id].present?
    # Aapno Rajasthan
    # if current_web_cart.present?
    #   items = current_web_cart.line_items.includes(design: :categories)
    #   @countries -=  ['India'] if (items.any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && items.any?{|item| item.design.categories.any?{|c|c.name == 'rakhi-international'}})
    # end
    redirect_to new_order_path if account_signed_in? && @address.nil?
    session.delete(:guest_shipping_address)
  end

  def store
    @address = @user.addresses.new(address_params)
    if @address.save
      session[:guest_shipping_address] = @address.id
      redirect_to new_order_path
    else
      get_countries
      render :collect
    end
  end

  def create
    @address = @user.addresses.new(address_params)
    if @address.save
      flash[:notice] = 'Address Added Successfully'
      if params[:ship_to_same_address].blank? || params[:ship_to_same_address] == '1'
        session[:shipping_id] = @address.id
        redirect_to new_order_path
      else
        redirect_to new_user_address_path(address_type: 'Shipping Address :')
      end
    else
      flash.now[:error] = @address.errors.full_messages.join(', ')
      get_countries
      render :new
    end
  end

  def edit
    @address_type = 'Edit Address'
    @address = @user.addresses.find_by(id: params[:id])
    fetch_account_info
    address_ga_hash_v2
    render 'luxe/addresses/edit', layout: 'application_luxe' if luxe_site?
  end

  def guest_edit
    @address_type = 'Edit Address'
    @address = @user.addresses.find_by(id: params[:id])
    fetch_account_info
    address_ga_hash_v2
    if luxe_site?
      render 'luxe/addresses/edit', layout: 'application_luxe'
    else
      @address.present? ? render(:edit) : redirect_to(collect_addresses_path)
    end
  end


  def address_ga_hash_v2
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    item_total = ((current_web_cart.items_total_without_addons(@rate))  * market_rate).round(2)
    coupon_discounts = (current_web_cart.total_discounts_currency(@rate,@country_code) * market_rate).round(2)
    coupon_percent = ((coupon_discounts/item_total) * 100).round(2)
    value = (item_total - (item_total * coupon_percent)/100).round(2)
    coupon_code = current_web_cart.coupon.present? ? current_web_cart.coupon.code : ""
    @ga_hash_new = {
      "event": "add_shipping_info",
      "ecommerce": {
        "currency":  "INR",
        "value":  value,
        "shipping": "",
        "shipping_tier": "Regular",
        "coupon": coupon_code,
        "coupon_discounts": coupon_discounts,
        "items": []
      }
    }
  
      current_web_cart.line_items.each_with_index do |line_item, index|
        hash = line_item.design.categories.first.breadcrumb_path
        crumbs_category = hash.keys if hash.present?
        item_category = crumbs_category[1] if crumbs_category.present?
        item_category2 = crumbs_category[2] if crumbs_category.present?
        item_category3 = crumbs_category[3] if crumbs_category.present?
        item_category4 = crumbs_category[4] if crumbs_category.present?
        item_category5 = crumbs_category[5] if crumbs_category.present?
        last_category_name = crumbs_category[-1] if crumbs_category.present?
        assign_category_title = line_item.design.categories.first.name
        last_category_id = Category.find_by("LOWER(name) = '#{last_category_name.try(:downcase)}'").try(:id) if last_category_name.present? 
        price = line_item.design.price_currency(@rate)
        discount_price = line_item.design.effective_price_currency(@rate)
        price_without_coupon = ((discount_price) * market_rate).round(2)
        price_with_coupon = (price_without_coupon - (price_without_coupon * coupon_percent)/100).round(2)
        item = {
          "item_id": "#{line_item.design.id}",
          "item_name": line_item.design.title,
          "price": (price_with_coupon * line_item.quantity).round(2),
          "discount": (((price-discount_price) * market_rate).round(2) * line_item.quantity).round(2),
          "item_brand": line_item.design.designer.name || "",  
          "item_category": item_category || "",
          "item_category2": item_category2 || "",
          "item_category3": item_category3 || "",
          "item_category4": item_category4 || "",
          "item_category5": item_category5 || "",
          "item_variant": line_item.design.color || " ",
          # "content_category": line_item.design.categories.first.name.titleize || "",
          "quantity": line_item.quantity,
          "item_list_name": last_category_name || "",
          "item_list_id": last_category_id.to_s || ""
        } 
        @ga_hash_new[:ecommerce][:items] << item
      end
  end

  # Updates the address with given params
  #
  # [PATCH/PUT] /user/addresses/:id
  #
  # == Returns
  # JSON
  #
  def update
    @address = @user.addresses.find_by(id: params[:address_id])
    if @address && @address.update_attributes(address_params)
      if request.referrer.include?('/guest_edit') || !account_signed_in?
        redirect_to new_order_path
      else
        flash[:notice] = 'Address Updated Successfully'
        redirect_to user_addresses_path
      end
    else
      flash.now[:error] = @address.errors.full_messages.join(', ')
      get_countries
      render 'luxe/addresses/edit', layout: 'application_luxe' if luxe_site?
    end
  end

  # Deletes address for user
  #
  # DELETE /user/addresses/:id
  #
  def destroy
    if address = @user.addresses.find_by_id(params[:id])
      if address.destroy
        flash[:notice] = 'Address Deleted Successfully'
      else
        flash[:error] = address.errors.full_messages.join(', ')
      end
    else
      flash[:error] = 'Address not found!'
    end
    redirect_to user_addresses_path
  end

  # Provides names and codes for the country whose id is passed in params
  #
  # == Returns:
  # Array
  #
  def get_states_and_dial_code
    if (country = Country.find_by_name(params[:country])).present?
      response = { state_list: country.states.pluck(:name).sort, dial_code: country.dial_code }
      status = 200
    else
      response = { error: true }
      status = 422
      Order.sidekiq_delay.notify_exceptions("Invalid / Unavailable country fetched!", "country fetched at checkout page is unavailable or invalid", { actual_country_code: @country_code, params: params})
    end
    render json: response,status: status
  end

  def get_pincode_format
    pincode_format = []
    pincode_format << PINCODE_FORMATS[params[:country]]
    autocomplete_zipcode_data = if AUTOCOMPLETE_ZIPCODE_ACTIVATED
      AUTOCOMPLETE_ZIPCODE[params[:country].to_s.downcase]
    end || []
    render :json => [pincode_format,autocomplete_zipcode_data]
  end

  private

  # Permitting only allowed params
  #
  def address_params
    params.require(:address).permit(:first_name, :last_name, :name, :street_address, :landmark, :city,
      :state, :country, :pincode, :phone, :default, *Address.street_address_lines)
  end
  
  # Provides names and codes for all countries.
  #
  # == Returns:
  # ActiveRecord Object
  #
  def get_countries
    @countries = Country.priority_wise_countries.pluck(:name).uniq
  end

  def fetch_account_info
    @account = Account.find_by_email(@user.email)
    @phone = @account&.phone || @address&.phone
    @email = @user.email
  end
end
