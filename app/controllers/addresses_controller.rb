class AddressesController < ApplicationController
  before_filter :authenticate_current_cart!, except: [:get_states_and_dial_code, :get_pincode_format]
  before_filter :authenticate_account!, except: [:collect, :store, :get_states_and_dial_code,:get_pincode_format, :create, :guest_edit, :update]
  before_filter :get_countries, only: [:new, :edit, :create, :collect, :guest_edit]
  before_filter :update_phone_params_with_dial_code, only: [:update, :store, :create]
  def index
    @addresses = @user.addresses
    redirect_to new_user_address_url unless @addresses.exists?
    address_ga_hash_v2
  end
  
  def billing_address
    @addresses = @user.addresses
    redirect_to new_user_address_url unless @addresses.exists?
    address_ga_hash_v2
  end

  def new
    @address_type = params[:address_type] || 'Billing Address'
    @address = @user.addresses.new
    address_ga_hash_v2
  end

  def collect
    @address = Address.find_by_id(params[:address_id].to_i) if params[:address_id].present?
    # Aapno Rajasthan
    # if current_web_cart.present?
    #   items = current_web_cart.line_items.includes(design: :categories)
    #   @countries -=  ['India'] if (items.any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && items.any?{|item| item.design.categories.any?{|c|c.name == 'rakhi-international'}})
    # end
    redirect_to new_order_path if account_signed_in? && @address.nil?
    session.delete(:guest_shipping_address)
  end

  def store
    @address = @user.addresses.new(address_params.except(:shipping_address))
    if @address.save
      session[:guest_shipping_address] = @address.id
      redirect_to new_order_path
    else
      get_countries
      render :collect
    end
  end

  def create
    @address = @user.addresses.new(address_params.except(:shipping_address))
    shipping_params = address_params[:shipping_address]
    @shipping_address = @user.addresses.new(shipping_params) if shipping_params.present?
    @shipping_address.save if @shipping_address.present?
    if @address.save
      flash[:notice] = 'Address Added Successfully'
      if params[:ship_to_same_address].blank? || params[:ship_to_same_address] == '1'
        session[:shipping_address_id] = @address.id
        redirect_to new_order_path
      else
        if @shipping_address.present?
          session[:shipping_address_id] = @shipping_address.id
        else
          session[:shipping_address_id] = @address.id
        end
        redirect_to new_order_path
      end
    else
      flash.now[:error] = @address.errors.full_messages.join(', ')
      get_countries
      render :new
    end
  end

  def edit
    @address_type = 'Edit Address'
    @address = @user.addresses.find_by(id: params[:id])
    if @country_code != 'IN' && @address.present?
      country= Country.find_by_name @address.country
      dial_code = country.dial_code
      phone_num = @address.phone
      phone_num = phone_num.gsub('+', "")
      phone_num.slice!(0, dial_code.length)
      @address.phone = phone_num
    end
    address_ga_hash_v2
  end

  def guest_edit
    @address_type = 'Edit Address'
    @address = @user.addresses.find_by(id: params[:id])
    if @country_code != 'IN' && @address.present?
      country= Country.find_by_name @address.country
      dial_code = country.dial_code
      phone_num = @address.phone
      phone_num = phone_num.gsub('+', "")
      phone_num.slice!(0, dial_code.length)
      @address.phone = phone_num
    end
    address_ga_hash_v2
    @address.present? ? render(:edit) : redirect_to(collect_addresses_path)
  end

  # Updates the address with given params
  #
  # [PATCH/PUT] /user/addresses/:id
  #
  # == Returns
  # JSON
  #
  def update
    @address = @user.addresses.find_by(id: params[:address_id])
    if @address && @address.update_attributes(address_params.except(:shipping_address))
      if request.referrer.include?('/guest_edit')
        redirect_to new_order_path
      else
        flash[:notice] = 'Address Updated Successfully'
        if request.referrer.include?('billing_address')
          redirect_to billing_address_addresses_path
        else
          redirect_to user_addresses_path
        end
      end
    else
      flash.now[:error] = @address.errors.full_messages.join(', ')
      get_countries
      render :edit
    end
  end

  # Deletes address for user
  #
  # DELETE /user/addresses/:id
  #
  def destroy
    if address = @user.addresses.find_by_id(params[:id])
      if address.destroy
        flash[:notice] = 'Address Deleted Successfully'
      else
        flash[:error] = address.errors.full_messages.join(', ')
      end
    else
      flash[:error] = 'Address not found!'
    end
    redirect_to user_addresses_path
  end

  # Provides names and codes for the country whose id is passed in params
  #
  # == Returns:
  # Array
  #
  def get_states_and_dial_code
    if (country = Country.find_by_name(params[:country])).present?
      response = { state_list: country.states.pluck(:name).sort, dial_code: country.dial_code }
      status = 200
    else
      response = { error: true }
      status = 422
      Order.sidekiq_delay.notify_exceptions("Invalid / Unavailable country fetched!", "country fetched at checkout page is unavailable or invalid", { actual_country_code: @country_code, params: params})
    end
    render json: response,status: status
  end

  def get_pincode_format
    pincode_format = []
    pincode_format << PINCODE_FORMATS[params[:country]]
    autocomplete_zipcode_data = if AUTOCOMPLETE_ZIPCODE_ACTIVATED
      AUTOCOMPLETE_ZIPCODE[params[:country].to_s.downcase]
    end || []
    render :json => [pincode_format,autocomplete_zipcode_data]
  end
  
  def address_ga_hash_v2
    @ga_hash_new = {}
    design_prices = []
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    b1g1_offers = ApplicationController.helpers.get_price_in_currency(current_web_cart.bmgnx_discounts(bmgnx_hash = PromotionPipeLine.bmgnx_hash), @rate)
    b1g1_offers = (b1g1_offers *= market_rate).round(CurrencyConvert.round_to)
    @ga_hash_new['value'] = current_web_cart.total_currency(1)
    @ga_hash_new['shipping_tier'] = "Regular"
    @ga_hash_new['tax'] = 0
    @ga_hash_new['offers_discount'] = b1g1_offers
    @ga_hash_new['shipping'] = current_web_cart.shipping_cost_currency(nil,1)
    @ga_hash_new['currency'] = 'INR'
    @ga_hash_new['coupon'] = current_web_cart.coupon.try(:code) || ""
    @ga_hash_new['coupon_discount'] = 0
    @ga_hash_new['country_code'] = @country_code
  
    @ga_hash_new['items'] =  current_web_cart.line_items.map do |line_item|
        design = Design.where(id: line_item.design_id).first
        category = design.categories.first.breadcrumb_path.keys
        category_title = design.categories.first.title
        category_id = design.categories.first.id
        line_item_data = line_item.ga_data
        line_item_data['item_category'] = category[1] || ""
        line_item_data['item_category2'] = category[2] || ""
        line_item_data['item_category3'] = category[3] || ""
        line_item_data['item_category4'] = category[4] || ""
        line_item_data['item_category5'] = category[5] || ""
        line_item_data['discount'] *= line_item.quantity
        line_item_data['price'] *= line_item.quantity
        line_item_data['item_customization'] *= line_item.quantity
        design_prices << line_item_data['price']
        line_item_data
    end
    @totalvalue = design_prices.count() > 1 ? design_prices.sum : design_prices[0] || 0
    @totalvalue = (@totalvalue - b1g1_offers).round(CurrencyConvert.round_to)
  end

  private

  # Permitting only allowed params
  #
  def address_params
    params.require(:address).permit(:first_name, :last_name, :name, :street_address, :landmark, :city,
      :state, :country, :pincode, :phone, :default, *Address.street_address_lines, shipping_address: [:first_name, :last_name, :name, :street_address, :landmark, :city,
        :state, :country, :pincode, :phone, :default, *Address.street_address_lines])
  end
  
  # Provides names and codes for all countries.
  #
  # == Returns:
  # ActiveRecord Object
  #
  def get_countries
    @countries = Country.priority_wise_countries.pluck(:name).uniq
  end
end
