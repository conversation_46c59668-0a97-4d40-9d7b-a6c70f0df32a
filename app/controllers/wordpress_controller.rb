require 'openssl'
OpenSSL::SSL::VERIFY_PEER = OpenSSL::SSL::VERIFY_NONE

class WordpressController < ApplicationController
  WP_URL = 'blog.shopmuzai.com'.freeze
  COMPANY_URL = 'www.shopmuzai.com'.freeze

  def index
    if params['path'].present?
      request_url = "https://#{WP_URL}/blog/#{params['path']}/" # Trailing slash required by WP
    else
      request_url = "https://#{WP_URL}/"
    end

    # Make the request to the Wordpress blog
    p request_url
    blog_html = Net::HTTP.get(URI(request_url))
    render html: rewrite_links(blog_html)
  end

  # Update all page links to our official blog URL so that users don't
  # end up back on the original Wordpress URL
  def rewrite_links(html)
    parsed_html = html.gsub("#{WP_URL}/blog", "#{COMPANY_URL}/blog")
    parsed_html.html_safe
  end
end