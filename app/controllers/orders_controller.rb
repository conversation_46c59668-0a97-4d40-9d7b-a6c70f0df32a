class OrdersController < ApplicationController

  include OrdersHelper

  NON_SERVICABLE = LOCKDOWN_NON_SERVICABLE_PINCODES

  before_action :authenticate_current_cart!, only: [:create, :new]
  before_action :current_web_cart, only: [:display_payment_details, :complete_payment, :retry, :retry_cod]
  before_action :validate_cart, only: [:new]
  before_filter :set_smart_pay_visiblity, only: [:new, :retry]
  before_filter :set_juspay_visibility, only: [:new]
  before_action :validate_shipping_amount, only: [:create]
  before_action :validate_pincode_deliverable, only: [:create]
  before_action :authenticate_account!, except: [:complete_payment, :display_payment_details, :retry,:retry_cod,:show, :create, :new, :shipping_address, :payu_response, :paypal_response, :paypal_success, :paypal_cancel, :send_feedback,:razorpay_submission, :amazon_success, :paytm_response, :get_order_details,:send_order_ack_email,:g2a_success, :paypal_retry, :pay_by_paypal, :paypal_response_handling, :paypal_execute, :redirect_for_stripe_payment, :stripe_response, :juspay_response]
  skip_before_filter :verify_authenticity_token, only: [:send_feedback]

  def shipping_address
    get_user_shipping_address
    if @shipping_address.blank? && current_user
      @user.addresses.new
      redirect_to new_user_address_path
    elsif @shipping_address
      session[:shipping_id] = @shipping_address.id
      redirect_to new_order_path
    end
  end

  # This method show user order list
  def index
    if current_user.present?
      @orders = Order.unscoped.where(user_id: current_user.id).where('created_at >= ?',(params[:old] ? 1.years.ago.beginning_of_day : 3.months.ago.beginning_of_day)).order('created_at desc').preload(line_items: [designer_order: :designer, design: :master_image])
      @orders = @orders.page(params[:page]).per(10)
      render 'luxe/orders/index', layout: 'application_luxe' if luxe_site?
    end
  end

  def g2a_success
    order = Order.find_by_number params[:order]
    order.update_attributes(g2a_txn_id: params[:transaction_id])
    redirect_to order_path(order.number),notice: 'Thank You for ordering with Mirraw.'
  end

  def new
    @email = current_user.email if current_user.present?
    @cart.update_attributes(user_id: @user.id, email: @user.email) unless @cart.user_id.present?
    gon.enable_cod_with_range_and_countries = ENABLE_COD_WITH_RANGE_AND_COUNTRIES
    # When signed in user clicks on buy this now on cart
    if account_signed_in? and session[:shipping_id].blank?
      @billing_address = @shipping_address = @user.default_address || @user.addresses.last
    # guest checkoutnew
    elsif guest_logged_in? && session[:guest_shipping_address].present?
      @shipping_address = @billing_address = @user.addresses.find_by(id: session[:guest_shipping_address])
    else
    # User have selected an address as shipping address
      @billing_address = @user.default_address
      @shipping_address = @user.addresses.find_by(id: session[:shipping_id])
    end
    ActiveRecord::Associations::Preloader.new.preload(@cart.line_items,[:variant, design: [:sized_designs, :categories, :designer, :master_image],line_item_addons: [:size_chart, addon_type_value: [:addon_option_types, :addon_type_value_group]]])
    cart_items = @cart.line_items
    Address.shipping_country = @shipping_address.try(:country)
    @offers = @cart.conditional_offers(@rate, Address.shipping_country)
    gon.prepaid_discount = @cart.prepaid_discount.to_i
    @mastercard_discount = @cart.mastercard_discount(@country_code, @rate).to_i
    country_name = @shipping_address.try(:country).presence || @billing_address.try(:country).presence || @user.addresses.last
    gon.grandtotal = @cart.total_currency(@rate,@country_code,country_name)
    if @mastercard_discount.to_f > 0
      gon.mastercard_discount = @mastercard_discount
    end
    if @shipping_address.present?
      @cod_charges_inr = Order.domestic_cod_charge(@shipping_address.country,@shipping_address.pincode, @cart)
      if COD_REQ_ORDER_ENABLE == 'true' && @cart.allowed_cod_country(@shipping_address.country)
        Order.create_cod_order_request(@cod_charges_inr,@cart,@shipping_address,@cart.total_currency(1),request.remote_ip,@rate)
      end
    end
    @auto_select_retry_cod = true if params[:auto_select_retry_cod]
    @prepaid_failed_retry = true if params[:prepaid_failed_retry]
    if CUSTOM_DUTY_COUNTRY['enable'] && @shipping_address.present?
      custom_charge_limit = CUSTOM_DUTY_COUNTRY['country'][@shipping_address.country.try(:downcase)]
      if custom_charge_limit
        @customs_charge_message = if @cart.total_currency(@rate, @country_code, Address.shipping_country) < custom_charge_limit
          CUSTOM_DUTY_COUNTRY['message']['no_duty']
        else
          CUSTOM_DUTY_COUNTRY['message']['duty']
        end
      end
      if gon.country_name.downcase != Address.shipping_country.downcase
        gon.country_name = 'false'
      end
    end
    redirect_to shipping_address_orders_path and return unless @shipping_address && @billing_address
    ###########################################
    # session[:order_id] this is previous     #
    # order_id of the user. It is used in     #
    # multiple scenarios.                      #
    #------------Razorpay---------------------#
    # session[:razorpay] has multiple values! #
    # true: for rendering razorpay form       #
    # false: for not rendering razorpay form  #
    # 0:to retry order when form is dismissed #
    # -----------Paypal-----------------------#
    # session[:retry_paypal] is used for      #
    # redirecting user back to paypal by      #
    # showing a pop up (_paypal_success.haml) #
    ###########################################
    if session[:order_id].present? && (order = Order.where('id = ?', session[:order_id]).first).present?
      @order = order.dup
      if (@razorpay_form = session[:razorpay])
        session[:razorpay] = false #use 0 for retry order
        begin
          currency_code = @order.currency_code == 'Rs' ? 'INR' : 'INR' # change this when multiple country support is available
          @razorpay_order = Razorpay::Order.create(amount: @order.total * 100, receipt: @order.number, currency: currency_code)
          session[:order_id] = nil
          return
        rescue
          session[:razorpay] == false
          @razorpay_form = false
        end
      elsif (@retry_paypal_form = session[:retry_paypal])
        session[:retry_paypal] = nil
        #return as we have set the @order, paypal error popup will appear.
      end
      session[:order_id] = nil
    else
      session[:paypal_retry_count] = nil
      @order = @user.orders.new
    end
    min_val = CurrencyConvert.to_currency(@country_code, LUXE_INTERNATIONAL_MIN_CART_VAL.to_i).round(2)
    discounted_item_total = @cart.item_total(1) - @cart.additional_discounts(@actual_country)
    if @cart.check_for_min_cart_condition?(MIN_CART_VALUE_ON_HEADER['mobile'], @country_code, Address.shipping_country) && discounted_item_total < LUXE_INTERNATIONAL_MIN_CART_VAL.to_i
      discounted_item_total_in_currency = ApplicationController.helpers.get_price_in_currency(discounted_item_total, @rate)
      flash.now[:error] = "International Orders should have a minimum order value of #{min_val}. Your current Order Value is #{@symbol} #{discounted_item_total_in_currency} (shipping charges excluded)"
    end
    if NON_SERVICABLE.include?@shipping_address.try(:pincode)
      flash.now[:error] = 'We are currently not delivering to this location.'
    end
    if Address.shipping_country.try(:downcase).to_s == 'india' && !@cart.pincode_servicable_for_category?(@shipping_address.try(:pincode))
      flash.now[:error] = PINCODE_BASED_CATEGORY_DELIVERY['reason']
    end
    # if (Address.shipping_country == "India" && cart_items.preload(design: :categories).any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && cart_items.any?{|item| item.design.categories.any?{|c|c.name == 'rakhi-international'}})
    #   flash.now[:error] = "Order cannot be shipped to India. Please change shipping address."
    # elsif RAKHI_PRE_ORDER == 'true'
    if RAKHI_PRE_ORDER[0] == 'true'
      value = @cart.rakhi_pre_order
      if (Address.shipping_country == 'India' && value['rakhi_with_other_designs'].present?)
        flash.now[:info] = "Your order contains multiple items, only Rakhi will be delivered between #{RAKHI_PRE_ORDER[1]}."
      elsif ((Address.shipping_country == 'India' && value['rakhi_all_schedule'].present?) || (value['rakhi_with_other_designs'].present? || value['rakhi_all_schedule'].present?))
        flash.now[:info] = "Order will be delivered between #{RAKHI_PRE_ORDER[1]} (Schedule Delivery)."
      end
    end
    if @juspay_ec
      juspay_params = {'app_source': @app_source.downcase, "cart_id": @cart.id}
      juspay_params = juspay_params.merge({
      "location": {
        "billing":{"id": @billing_address.id},
        "shipping": {"id": @shipping_address.id}
        }
      }) if @billing_address.present? && @shipping_address.present?
      @juspay_payment_options = get_response_data_for('juspay_payment_options', juspay_params)
      @juspay_payment_options.deep_symbolize_keys! if @juspay_payment_options.present?
      @saved_cards = if current_account.present? && current_user.juspay_customer.present?
        response_hash = current_user.get_saved_cards
        response_hash['cards']
      else
        []
      end
    end
    @dom_offers = OfferPanel.active_domestic_offers('mobile') if @juspay_ec
    gtm_data_layer.push({pageType: 'orderNew'}.merge!(@cart.details_for_gtm))
    cart_ga_hash
    cart_ga_hash_v2_new
    google_ads_data_val
    render 'luxe/orders/new', layout: 'application_luxe' if luxe_site?
  end

  def google_ads_data_val
    @googe_add_hash_new = {
      "item_ids": [],
      "content_ids": [],
      "contents": [],
      "num_items": []
    }
    @googe_add_hash_new[:num_items] << @cart.line_items.count
    @cart.line_items.each_with_index do |line_item, index|
      content_item = {
      "id": "#{line_item.design.id}",
      "google_business_vertical": "retail"
      }
      contents = {
        "id": "#{line_item.design.id}",
        "quantity": line_item.quantity
      }
      @googe_add_hash_new[:item_ids] << content_item
      @googe_add_hash_new[:content_ids] << "#{line_item.design.id}"
      @googe_add_hash_new[:contents] << contents
    end
  end

  def cart_ga_hash_v2_new
    market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
    item_total = ((@cart.items_total_without_addons(@rate))  * market_rate).round(2)
    coupon_discounts = (@cart.total_discounts_currency(@rate,@country_code) * market_rate).round(2)
    coupon_percent = ((coupon_discounts/item_total) * 100).round(2)
    value = (item_total - (item_total * coupon_percent)/100).round(2)
    coupon_code = @cart.coupon.present? ? @cart.coupon.code : ""
    @ga_hash_new = {
      "event": "begin_checkout",
      "ecommerce": {
        "currency":  "INR",
        "value": value ,
        "shipping": "",
        "coupon": coupon_code,
        "coupon_discounts": coupon_discounts,
        "items": []
      }
    }
  
      @cart.line_items.each_with_index do |line_item, index|
        price = line_item.design.price_currency(@rate)
        discount_price = line_item.design.effective_price_currency(@rate)
        price_without_coupon = ((discount_price) * market_rate).round(2)
        price_with_coupon = (price_without_coupon - (price_without_coupon * coupon_percent)/100).round(2)
        hash = line_item.design.categories.first.breadcrumb_path
        crumbs_category = hash.keys if hash.present?
        item_category = crumbs_category[1] if crumbs_category.present?
        item_category2 = crumbs_category[2] if crumbs_category.present?
        item_category3 = crumbs_category[3] if crumbs_category.present?
        item_category4 = crumbs_category[4] if crumbs_category.present?
        item_category5 = crumbs_category[5] if crumbs_category.present?
        last_category_name = crumbs_category[-1] if crumbs_category.present?
        assign_category_title = line_item.design.categories.first.name
        last_category_id = Category.find_by("LOWER(name) = '#{last_category_name.try(:downcase)}'").try(:id) if last_category_name.present? 
        item = {
          "item_id": "#{line_item.design.id}",
          "item_name": line_item.design.title,
          "price": (price_with_coupon * line_item.quantity).round(2),
          "discount": (((price-discount_price) * market_rate).round(2) * line_item.quantity).round(2),
          "item_brand": line_item.design.designer.name,
          "item_category": item_category || "",
          "item_category2": item_category2 || "",
          "item_category3": item_category3 || "",
          "item_category4": item_category4 || "",
          "item_category5": item_category5 || "",
          "item_variant": line_item.design.color || " ",
          "content_category": line_item.design.categories.first.name.titleize || "",
          "quantity": line_item.quantity,
          "item_list_name": last_category_name || "",
          "item_list_id": last_category_id.to_s || ""
        } 
        @ga_hash_new[:ecommerce][:items] << item
      end
  end

  def create
    @order = @user.orders.new(cart_id: @cart.id, app_source: @app_source)
    @order.app_name = 'luxe' if luxe_site?

    if order_params[:shipping_address].present?
      @order.build_address_by_address_id(order_params[:shipping_address], 'shipping')
    end
    if is_banned_country([@order.country.try(:downcase), @order.billing_country.try(:downcase), @actual_country.try(:downcase)])
      flash[:error] = BANNED_COUNTRIES['reason']
      redirect_to root_path and return
    end
    if @order.present? && order_params.present?
      country = ((order_params[:pay_type].to_s == Order::COD || (@order.country.try(:downcase) == @actual_country.try(:downcase) && @actual_country.try(:downcase) == 'india')) ? 'india' : 'international')
      redirect_to @cart, notice:'Cart contains out of stock product please remove them before checkout' and return if @cart.check_out_of_stock?(country)
    end
    @order.add_order_addons if GIFT_WRAP_PRICE.to_f >= 0 && session[:gift_wrap]
    if params[:prepaid_discount].to_f > 0
      if (amount = (@cart.prepaid_discount).to_i) > 0
        @order.order_discounts.new(discount_type: 'prepaid', amount: amount)
      end
    end
    if params[:mastercard_discount].to_f > 0 && (amount = (@cart.mastercard_discount(@country_code, @rate))) > 0
      @order.order_discounts.new(discount_type: 'mastercard', amount: amount)
    end
    @order.build_by(order_params.except(:prepaid_discount).merge!({
      country_code: @country_code, rate: @rate, symbol: @symbol}
    ))
    country = @order.country || Address.find(order_params[:shipping_address].to_i).country
    @order.express_delivery = Country.get_express_delivery_charge(country) if params[:delivery_type] == 'express' && @cart.ready_to_ship_designs? && Country.get_express_delivery_countries.include?(country) && EXPRESS_DELIVERY.to_i >= 0
    if SOR_READY_TO_SHIP && @cart.warehouse_available_designs?
      delivery_time, show_msg, available_in_warehouse = @cart.get_delivery_time(country.try(:downcase))
      if available_in_warehouse
        @order.express_delivery = 0
        sor_order = true
      end
    end
    set_campaign_details(@order)
    @order.assign_warehouse_addresses_to_designer_orders
    @payment_option = params[:payment_option]
    @order.attempted_payment_gateway = params[:order][:attempted_payment_gateway] if params[:order].present? && params[:order][:attempted_payment_gateway].present?
    set_wallet_pay_type if params[:wallet_return].present? && params[:wallet_return] == "fully_used_wallet"
    if @order.save
      @order.add_notes_without_callback('Ready_To_Ship_Order_SOR', 'delivery') if sor_order
      Order.sidekiq_delay_until(30.minutes.from_now, queue: 'high').cancel_unless_confirmed_mobile(@order.id) if @order.cancel_unless_confirmed?
      Order.sidekiq_delay.update_paypal_rate_mobile(@order.id) if @order.international?
      @order.do_wallet_transaction
      if @order.is_fully_paid_by_wallet?
        @order.update_column(:pay_type, Order::WALLET)
      end
      if params[:juspay_form].to_i == 1 && [Order::JP_CARD, Order::JP_SAVED_CARD, Order::JP_WALLET, Order::JP_UPI, Order::JP_NETBANKING, Order::JP_PAYPAL].include?(@order.pay_type)
        redirect_for_juspay_payment
      else
        redirect_for_payment
      end
      if order_params[:pay_type] == Order::COD
        @order.create_cod_request if COD_REQUEST_ENABLE == 'true'
      elsif cookies[:payment_failure_count].to_i > 0 && order_params[:pay_type] == Order::BANK_DEPOSIT
        @order.add_notes("Retries: #{cookies[:payment_failure_count]}", true)
        cookies[:payment_failure_count] = { value: 0, expires: 12.hours.from_now }
      end
      if params[:newsletter_confirmation] == 'send_newsletter'
        @user.account.update_column(:terms_of_service, true)
        @user.account.add_to_subscribers('mobile/orders/new', @country_code, 'mobile', request.headers['True-Client-IP'] || request.ip)
      end
      # Order.delay(queue: 'high_priority', priority: 0).generate_thirdwatch_payload(session.id, request.ip, request.user_agent, @order.id) if Rails.env.production? || Rails.env.staging?
    else
      @billing_address = @user.default_address
      @shipping_address = @user.addresses.find_by(id: session[:shipping_id])
      if (errors = @order.errors).any?
        Order.sidekiq_delay.notify_exceptions('Order Validation Error', {message:
          { order: @order.inspect,
            validation_errors: errors.messages,
            designer_orders: @order.designer_orders.inspect,
            cart: @order.cart.inspect,
            line_item: @order.cart.try(:line_items).inspect
          },
        params1: params})
      end
      set_smart_pay_visiblity
      if @order.pay_type == Order::PAYPAL && @paypal_domestic && request.format.json?
        respond_to do |format|
          format.json {render json: {name: 'VALIDATION_ERROR', errors: @order.errors.messages.values.flatten.join(', ')}}
        end
      else
        flash[:error] = 'Order Not Placed'
        redirect_to root_path
      end
    end
    rescue Paypal::Exception::APIError => error
      @order.add_log_details(error)
      redirect_to @cart, notice: 'Payment Failed!'
  end

  def paypal_retry
    if params[:order_id].present? && (@order = Order.where(id: params[:order_id]).first).present?
      @order.skip_filter = true
      @order.ccavenue_api_error = 'PPREDIRECT'
      session[:paypal_retry_count] = session[:paypal_retry_count].to_i + 1
      @order.paypal_retry_count = session[:paypal_retry_count]
      if @order.save
        redirect_for_paypal_payment(@order)
      else
        redirect_to_order_new('Your payment failed. Please try again')
      end
    else
      redirect_to_order_new('Something went wrong. Please try again')
    end
  end

  def show
    order_number = params[:id].upcase.gsub(/[^A-Z0-9]/,'')
    @retry_state = false;
    @newly_added_products = {}
    # if @country_code == 'IN'
    #   @newly_added_products = get_response_data_for('newly_added_products')
    # end
    if @user.present?
      @order =   @user.orders.includes([designer_orders: [:designer,line_items: [design: [:master_image,:designer]]]]).where(:number => order_number).first if order_number.present?
    else
      @order =   Order.includes([designer_orders: [:designer,line_items: [design: [:master_image,:designer]]]]).where(:number => order_number).first if order_number.present?
      @retry_state = true;
    end

    session[:paypal_retry_count] = nil
    @track = false
    #@order.send_cod_verification(orders_cod_verify_url) if @order && @order.cod? && @order.state=='pending' && @order.cod_verification_attempt_pending?

    if @order.present?
      city = @order.billing_city
      @city = city.present? ? Digest::SHA256.hexdigest(city) : nil
      country = @order.billing_country
      @country = country.present? ? Digest::SHA256.hexdigest(country) : nil
      email = @order.billing_email
      @email = email.present? ? Digest::SHA256.hexdigest(email) : nil
      pincode = @order.billing_pincode
      @pincode = pincode.present? ? Digest::SHA256.hexdigest(pincode) : nil
      state = @order.billing_state
      @state = state.present? ? Digest::SHA256.hexdigest(state) : nil
      cleaned_phone_number = @order.billing_phone.gsub(/\D/, '').sub(/^0+/, '')
      @phone_number = cleaned_phone_number.present? ? Digest::SHA256.hexdigest(cleaned_phone_number) : nil
      if @order.country_code.present? && @order.country_code != 'IN'
        order_currency = CurrencyConvert.currency_convert_cache_by_country_code(@order.country_code)
        order_currency = order_currency || CurrencyConvert.currency_convert_cache_by_country_code('US')
        @adwords_total = ((@order.total/order_currency.rate) * (order_currency.market_rate)).round(2)
      else
        @adwords_total = @order.total
      end
      @all_line_items = @order.line_items.where('line_items.status is null OR line_items.status =?','buyer_return').where('designer_orders.state not in (?)',['canceled','vendor_canceled'])
      order_ga_hash
      if @order.track.nil? && @order.state != 'cancel' && @order.app_source == @app_source
        @track = true
        @order.update_column(:track,1)
        session[:utm_source].clear if session[:utm_source].present?
        session[:utm_medium].clear if session[:utm_medium].present?
        session[:utm_campaign].clear if session[:utm_campaign].present?
        product_ids = @order.line_items.collect(&:design_id)
        # gtm_data_layer.push(event: 'transaction', orderProductIds: product_ids,
          # orderTotal: (@order.total/@order.currency_rate * @order.currency_rate_market_value).round(2), orderTransactionId: @order.number
        # )
      end
      cart_ga_hash_v2
      google_ads_data
      render 'luxe/orders/show', layout: 'application_luxe' if luxe_site?
    else
      flash[:notice] = 'Order not found'
      redirect_to root_path
    end
  end

  def cart_ga_hash_v2
    cc = CurrencyConvert.find_by(country_code: @country_code)
    market_rate = cc.present? ? cc.market_rate : 1
    item_total = (@order.item_total_without_addons(@rate) * market_rate).round(2)
    coupon_discounts = (@order.total_discount_currency(@rate) * market_rate).round(2)
    coupon_percent = ((coupon_discounts/item_total) * 100).round(2)
    value = (item_total - (item_total * coupon_percent)/100).round(2)
    coupon_code = @order.coupon.present? ? @order.coupon.code : ""
    @ga_hash_new = {
      "event": "purchase",
      "ecommerce": {
        "currency":  "INR",
        "value":  value,
        "shipping": "",
        "transaction_id": "#{@order.number}",
        "coupon": coupon_code,
        "coupon_discounts": coupon_discounts,
        "items": []
      }
    }
  
      @order.line_items.each_with_index do |line_item, index|
        hash = line_item.design.categories.first.breadcrumb_path
        crumbs_category = hash.keys if hash.present?
        item_category = crumbs_category[1] if crumbs_category.present?
        item_category2 = crumbs_category[2] if crumbs_category.present?
        item_category3 = crumbs_category[3] if crumbs_category.present?
        item_category4 = crumbs_category[4] if crumbs_category.present?
        item_category5 = crumbs_category[5] if crumbs_category.present? 
        last_category_name = crumbs_category[-1] if crumbs_category.present?
        assign_category_title = line_item.design.categories.first.name
        last_category_id = Category.find_by("LOWER(name) = '#{last_category_name.try(:downcase)}'").try(:id) if last_category_name.present? 
        price = line_item.design.price_currency(@rate)
        discount_price = line_item.design.effective_price_currency(@rate)
        price_without_coupon = ((discount_price) * market_rate).round(2)
        price_with_coupon = (price_without_coupon - (price_without_coupon * coupon_percent)/100).round(2)
        item = {
          "item_id": "#{line_item.design.id}",
          "item_name": line_item.design.title,
          "price": (price_with_coupon * line_item.quantity).round(2),
          "discount": (((price-discount_price) * market_rate).round(2) * line_item.quantity).round(2),
          "item_brand": line_item.design.designer.name || "",  
          "item_category": item_category || "",
          "item_category2": item_category2 || "",
          "item_category3": item_category3 || "",
          "item_category4": item_category4 || "",
          "item_category5": item_category5 || "",
          "item_variant": line_item.design.color || " ",
          "content_category": line_item.design.categories.first.name.titleize || "",
          "quantity": line_item.quantity,
          "item_list_name": last_category_name || "",
          "item_list_id": last_category_id.to_s || ""
        } 
        @ga_hash_new[:ecommerce][:items] << item
      end
  end

  def google_ads_data
    @googe_add_hash_new = {
      "item_ids": [],
      "content_ids": [],
      "contents": [],
      "num_items": []
    }
    @googe_add_hash_new[:num_items] << @order.line_items.count
    @order.line_items.each_with_index do |line_item, index|
      content_item = {
      "id": "#{line_item.design.id}",
      "google_business_vertical": "retail"
      }
      contents = {
        "id": "#{line_item.design.id}",
        "quantity": line_item.quantity
      }
      @googe_add_hash_new[:item_ids] << content_item
      @googe_add_hash_new[:content_ids] << "#{line_item.design.id}"
      @googe_add_hash_new[:contents] << contents
    end
  end

  def cod_verify
    if params.present? && params['SID'].present?
      order = Order.find_by_zipdial_transaction_token(params['SID'])
      if order.present?
        unless order.sane?
          unless order.check_similar_confirmed_order_week?
            order.good_data!
          else
            order.tag_list.add('duplicate')
            order.add_notes_without_callback("Added tag duplicate", 'other', current_account)
            order.save!
          end
        end
      end
    end
    render :nothing => true
  end



  # GET /orders/retry/:id
  def retry
    @canceled_order = Order.where('number = ?', params[:id]).first
    if @canceled_order && (@canceled_order.state == 'cancel' || session[:razorpay] == 0)
      session[:razorpay] = false
      email = account_signed_in? ? current_account.email : @canceled_order.email
      @cart = Cart.create(:email => email, :hash1 => SecureRandom.hex(16))
      session[:cart_id] = @cart.id
      @hide_menu = true
      @app_source = @app_source

      @canceled_order.line_items.preload(design: :designer).each do |item|
        @line_item, @line_item_message = @cart.add_product(item.design, @app_source)
        @line_item.quantity = item.quantity
        @line_item.line_item_addons << item.line_item_addons
        @line_item.variant_id = item.variant_id
        @line_item.buy_get_free = item.design.buy_get_free if ['inr', 'rs'].exclude?(@symbol.try(:downcase)) && PromotionPipeLine.bmgnx_hash.present?
        @line_item.app_name = 'luxe'
        @line_item.save
      end
      @mastercard_discount = @cart.mastercard_discount(@country_code, @rate).to_i
      if @mastercard_discount.to_f > 0
        gon.mastercard_discount = @mastercard_discount
      end
      ActiveRecord::Associations::Preloader.new.preload(@cart.line_items,[ design: [ :designer, :master_image]])
      if current_web_cart.check_out_of_stock?
        redirect_to cart_url(id: current_web_cart.hash1)
        return true
      end
      @order = Order.new
      @order.app_name = 'luxe'
      # When signed in user clicks on buy this now on cart
      if account_signed_in? and session[:shipping_id].blank?
        @billing_address = @shipping_address = @user.default_address || @user.addresses.last
      # guest checkout
      elsif guest_logged_in? && session[:guest_shipping_address].present?
        @shipping_address = @billing_address = @user.addresses.find_by(id: session[:guest_shipping_address])
      else
        authenticate_current_cart!
        redirect_to current_web_cart unless performed?
        return true
      end

      unless @shipping_address && @billing_address
        redirect_to collect_addresses_path
        return true
      end

      @cod_charges_inr = Order.domestic_cod_charge(@shipping_address.country,@shipping_address.pincode, @cart) if @shipping_address.present?
      @offers = @cart.conditional_offers(@rate, Address.shipping_country)
      cart_ga_hash
      return render 'luxe/orders/new', layout: 'application_luxe' if luxe_site?
      respond_to do |format|
        format.html {render "new"}
      end
    else
      redirect_to root_url
    end
  end

  # POST /orders
  # POST /orders.json
  def retry_cod
    # Order Creation from canceled orders
    @canceled_order = Order.where('number = ?', params[:id]).first || Order.where('number = ?', params[:order_id]).first

    if @canceled_order && @canceled_order.state == 'cancel' || @canceled_order.state == 'cancel_complete' || @canceled_order.state == 'new'
      @canceled_order.state = 'new'
      if params[:prepaid_failed_retry] == "true"
        @canceled_order.g2a_txn_status = "retry_pop_up"
      else
        @canceled_order.utm_source = 'Redirect from mail, order updated to COD, Desktop'
      end
      @canceled_order.pay_type = Order::COD
      @canceled_order.payment_state = 'pending_payment'
      @canceled_order.save!

      respond_to do |format|
        if @canceled_order.save
          @canceled_order.attempt_auto_confirm if @canceled_order.cod?
          post_order_confirmation_work(@canceled_order)
          # cod limits won't be applied because the order is not sane at this point.
          # total_cod_charges = Order.post_domestic_cod_charge(@canceled_order.pincode, @canceled_order.designer_orders)
          # @canceled_order.cod_charge = total_cod_charges
          # @canceled_order.save!
          format.html { redirect_to(order_url(@canceled_order.number), :notice => 'Thank you for your order.') }
        end
      end
    else
     redirect_to root_url
    end
  end

  #OrderMailer will be actually called from main-mirraw
  def post_order_confirmation_work(order)
    # Mail order details
    Order.sidekiq_delay.mail_order_details_to_buyer(order.id)
  end

  def send_order_ack_email
    if params[:number].present? && (order = Order.find_by_number(params[:number]))
      if session["#{order.id}_mail_sent"].present?
        render json: {error: false, mail_sent: false}
      else
        session["#{order.id}_mail_sent"] = true
        post_order_confirmation_work(order)
        render json: {error: false, mail_sent: true}
      end
    else
      render json: {error: true}
    end
  end

  def create_payment
    if (order = Order.find_by_id(session[:order_id]) || Order.find_by(cart_id: @cart.id)).present?
      order.update_column(:attempted_payment_gateway, Order::PAYPAL)
    end
    get_user_shipping_address
    if guest_logged_in?
      country_code = Country.get_country_code(@shipping_address.country)
    else
      get_user_billing_address
      country_code = Country.get_country_code(@billing_address.country)
    end
    country = CurrencyConvert.currency_convert_memcached.find{|cc| cc.country_code == (country_code ||  'US')}
    CurrencyConvert.round_to = country.try(:round_to)
    redirect_url = Order.create_paypal_payment(@cart, @shipping_address, @rate, @symbol, @country_code, complete_payment_orders_url, new_order_url)
    redirect_to redirect_url
  end

  def display_payment_details
    get_user_shipping_address
    response_hash = Order.get_payment_details(params)
    @payment = response_hash[:data] if !response_hash[:error]
    unless @payment.present?
      flash[:notice] = 'There was a problem processing the Payment, Please try again.'
      redirect_to new_order_path
    end
  end

  def complete_payment
    response_hash = Order.execute_payment_details(params)
    unless response_hash[:error]
      @payment = response_hash[:data]
      if (@order = Order.find_by_id(session[:order_id]) || Order.find_by(cart_id: @cart.id)).present?
        session[:order_id] = nil
        @order.app_source = @app_source
        @order.token = params[:token]
        @order.payment_gateway = Order::PAYPAL
        if @order.save
          paypal_update_order(@payment)
          redirect_to order_path(@order.number)
        else
          flash[:notice] = 'Order Not Placed'
          redirect_to root_path
        end
      else
        flash[:notice] = 'Order Not Found'
        redirect_to root_path
      end
    else
      flash[:notice] = 'Payment execution was not completed'
      redirect_to root_path
    end
  end

  def amazon_success
    amazon_order_id = params["amznPmtsOrderIds"]
    status = params[:amznPmtsPaymentStatus]
    @message = "Your order has been received, and your payment has been #{status}. Thank you for your purchase! Your Amazon Payments ID is: #{amazon_order_id}. Note: In-case of payment failure, you will receive an email from 'Pay with Amazon' asking you to revise the payment."
    order= Order.where(amazon_order_id: amazon_order_id,pay_type: Order::PAY_WITH_AMAZON).first  if amazon_order_id.present?
    if order.present?
      if cart = order.cart
        session[:cart_id] = nil if cart.id == session[:cart_id]
        cart.update_column(:used,true) unless cart.used
      end
      redirect_to order_path(order.number),notice: @message
    end
  end

  # Receives response from payu for succecss, cancel and errors case.
  #
  # POST orders/payu_response
  #
  # == Returns
  # HTML Response
  #
  def stripe_response
    redirect_to new_order_path if ((key = session[:stripe]).blank? || (order = Order.find(session[:order_id])).blank?)
    session[:stripe] = nil
    session[:order_id] = nil
    response = order.retrieve_intent(key)
    order.payment_gateway = 'stripe'
    order.payment_gateway_details = response
    order.paypal_error = response.last_payment_error
    order.paypal_payer_id = response.id
    order.save
    if order.currency_code.downcase == 'jpy'
      amount = response.amount_received * order.currency_rate
    else
      amount = (response.amount_received / 100.0) * order.currency_rate
    end
    Order.sidekiq_delay(queue: 'critical').process_stripe_order!(order.id, amount, response.status)
    respond_to do |format|
      format.html {
        if response.status == 'succeeded'
          redirect_to order_path(order.number), notice: 'Your order is placed successfully!'
        else
          flash[:error] = 'Your order was cancelled!'
          redirect_to new_order_path
        end
      }
    end
  end

  def payu_response
    if Order.valid_payu_response_checksum?( params[:hash], params)
      if (@order = Order.find_by(number: params[:txnid]))
        @order.assign_payu_attributes(params)
        @order.save
        Order.sidekiq_delay(queue: 'critical').process_payu_order!(@order.id, params[:amount])
        respond_to do |format|
          format.html {
            if params[:status] == 'success'
              redirect_to order_path(@order.number), notice: 'Your order is placed successfully!'
            elsif @order.cart.present?
              if @order.cart.cod?(@order.cod_charge,@order.country,@order.billing_pincode,@order.currency_rate)
                auto_select_retry_cod = true
                flash[:error] = 'Sorry, Your Payment was not completed. Please retry using Cash On Delivery or another Payment Method.'
              else
                auto_select_retry_cod = false
                flash[:error] = 'Sorry, Your Payment was not completed. Please retry using another Payment Method.'
              end
              redirect_to new_order_path(auto_select_retry_cod: auto_select_retry_cod)
            else
              flash[:error] = 'Your order was cancelled!'
              redirect_to order_path(@order.number)
            end
          }
        end
        return
      end
      @message = params[:status] == 'success' ? 'Don\'t worry just contact our customer care.' : 'Please place the order again.'
    end
    redirect_to root_url, notice: "Something went wrong. #{@message}"
  end

  def juspay_response
    if is_response_signature_valid?(params)
      # order = Order.find_by_number(params[:order_id])
      # if order.present? && order.juspay_order_status.blank?
      #   order_status = order.get_juspay_order_status
      #   Order.delay(queue: 'high_priority', priority: -10).process_juspay_order!(order_status)
      # end
      if params[:status] == 'CHARGED'
        flash[:notice] = 'Thank you for your order.'
        redirect_url = order_path(params[:order_id])
      elsif params[:status] == 'AUTHORIZING'
        flash[:notice] = 'Order Processing...'
        redirect_url = order_path(params[:order_id])
      elsif PREPAID_POPUP_SHOW == 'true' && ( params[:status] == 'AUTHORIZATION_FAILED' || params[:status] == 'JUSPAY_DECLINED' ) && @country_code == 'IN'
        @prepaid_failed_retry = true
        redirect_url = new_order_path(prepaid_failed_retry: @prepaid_failed_retry, order_id: params[:order_id])
      else
        flash[:notice] = 'Transaction not completed'
        redirect_url = new_order_path
      end
    else
      redirect_url = root_url
    end
    redirect_to redirect_url
  end

  def paytm_response
    paytmparams = request.request_parameters.to_hash
    if Order.valid_paytm_response?(paytmparams) && (order = Order.find_by_number(paytmparams['ORDERID'])) &&
       (order.paytm_txn_id.nil? || order.paytm_txn_id != paytmparams['TXNID'])
      order.assign_paytm_attributes(paytmparams)
      order.save
      Order.sidekiq_delay.process_paytm_order!(order.id, paytmparams['TXNAMOUNT'])
      message = order.paytm_txn_status == 'TXN_SUCCESS' ? 'Your order is placed successfully!' : 'Your order was cancelled!'
      redirect_to order_path(order), notice: message
    else
      flash[:error] = 'Paytm is unavailable, please use other payment options.'
      redirect_to new_order_path
    end
  end

  def paypal_success
    handle_callback do |order|
      response = order.complete!(params[:PayerID])
      if response.try(:ack) == 'Success'
        order.paypal_update_cc_order(response)
        flash[:notice] = t('order_create_success_notice')
        session[:cart_id] = nil
        order_url(order.number)
      else
        order.update_column(:ccavenue_api_error, 'PPRESERROR_MOBILE')
        error = response.try(:raw) || {}
        if error[:L_ERRORCODE0].to_i == 10486
          session[:order_id] = order.id
          session[:retry_paypal] = true
          Order.sidekiq_delay.notify_exceptions("Paypal Redirect Mobile", 'User Redirected to Paypal', { order: order.number.to_s, response: response.inspect.to_s })
          new_order_url
        else
          Order.sidekiq_delay.notify_exceptions("Paypal Response Error Mobile", 'Problem in processing the payment', { order: order.number.to_s, response: response.inspect.to_s })
          redirect_to_order_new('Something went wrong.')
        end
      end
    end
  end

  def paypal_cancel
    handle_callback do |order|
      order.update_column(:ccavenue_api_error, 'PPUSERCANCEL_MOBILE')
      Order.sidekiq_delay.notify_exceptions("Paypal Cancel", 'Paypal request cancel mobile', { order: order.number.to_s })
      flash[:alert] = 'Payment Request Canceled'
      order_cancelled_handler
    end
  end

  def send_feedback
    Order.sidekiq_delay(queue: 'high').send_feedback_mail(params[:name], params[:feedback])
    render js: "$('#feedback_form').remove();
                $('.thanks').text('Thank You');"
  end

  def razorpay_submission
    @order = Order.where('number = ?', params[:order_id]).first
    @order.update_column(:attempted_payment_gateway, 'razorpay')
    Order.sidekiq_delay.process_razorpayment(@order.id, params[:razorpay_payment_id])
    redirect_to order_path(@order.number), notice: t('order_create_success_notice')
  end

  def pay_by_paypal
    @order = Order.find_by_number(params[:number])
    if ['new', 'pending', 'cancel', 'followup'].include?(@order.state)
      @order.move_to_pending! if @order.state == 'cancel'
      session[:guest_account_id] = @order.user.account.id
      redirect_for_paypal_payment(@order)
    else
      flash[:notice] = 'Order not found!'
      redirect_to root_url
    end
  end

  def paypal_response_handling
    return_url = params[:error].present? ? (params[:redirect_root].present? && params[:redirect_root] != 'undefined' ? root_url : new_order_url) : params[:returnUrl]
    notice = params[:error].present? ? (params[:messages].present? && params[:messages] != 'undefined' ? params[:messages] : 'Something Went Wrong. Please Try with difference payment gateway.') : 'Thank you for your order.'
    redirect_to return_url, notice: notice
  end

  def redirect_for_stripe_payment
    order = Order.find_by_number(params[:number])
    intent = order.create_intent
    session[:stripe] = intent.id
    session[:order_id] = order.id
    order.update_column(:attempted_payment_gateway, 'stripe')
    gon.clientSecret = intent.client_secret
    gon.key = ENV['STRIPE_PUBLISHABLE_KEY']
    amount = ApplicationController.helpers.get_price_with_symbol(ApplicationController.helpers.get_price_in_currency(order.total, @rate),order.currency_code)
    if luxe_site?
      render template: 'luxe/orders/stripe_checkout', locals: {amount: amount}, layout: 'application_luxe'
    else
      render template: 'orders/stripe_checkout', locals: {amount: amount}
    end
  end

  def paypal_execute
    begin      
      order = Order.find_by_token params[:orderID] 
      payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
      payment_response = payapal_api.capture_payment
      if payment_response.key?("has_error") 
        order.update_column("paypal_error", [payment_response['name'], payment_response['message'], payment_response['errors']].compact.join(' || '))
      else
        order.cart.update_column(:used, true)
        Order.sidekiq_delay.store_paypal_smartpay_v2_api_details(payment_response, order.number)
      end
      render json: payment_response
    rescue => error
      Order.sidekiq_delay.notify_exceptions("Paypal Rutime Exception", error.inspect, {order_number: session[:order_number]})
      Order.where(number: session[:order_number]).update_all(paypal_error: error.inspect)
      render json: {has_error: true, message: "Some Exception Occured Please Try Again"}
    end
  end

  def paypal_execute
    begin      
      order = Order.find_by_token params[:orderID] 
      payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
      payment_response = payapal_api.capture_payment
      if payment_response.key?("has_error") 
        order.update_column("paypal_error", [payment_response['name'], payment_response['message'], payment_response['errors']].compact.join(' || '))
      else
        order.cart.update_column(:used, true)
        Order.sidekiq_delay.store_paypal_smartpay_v2_api_details(payment_response, order.number)
      end
      render json: payment_response
    rescue => error
      Order.sidekiq_delay.notify_exceptions("Paypal Rutime Exception", error.inspect, {order_number: session[:order_number]})
      Order.where(number: session[:order_number]).update_all(paypal_error: error.inspect)
      render json: {has_error: true, message: "Some Exception Occured Please Try Again"}
    end
  end

  private

  def validate_cart
    redirect_to @cart, notice:'Cart contains out of stock product please remove them before checkout' and return if @cart.check_out_of_stock?
  end

  def is_banned_country(countries = [])
    (BANNED_COUNTRIES['countries'] & countries).present?
  end

  def redirect_to_order_new(notice=nil)
    flash[:notice] = notice if notice.present?
    new_order_url
  end

  def handle_callback
    order = Order.find_by_token! params[:token]
    @redirect_uri = yield order
    if order.present?
      redirect_to @redirect_uri
    else
      render :close_flow, layout: false
    end
  end

  def paypal_update_order(payment)
    paypal_txn_id = payment.id
    if paypal_txn_id.present?
      unless Order.where(paypal_txn_id: paypal_txn_id).exists?
        currency_code = !CURRENCY_CONVERT_TO_USD.keys.include?(@order.country_code) && Order.paypal_currency_allowed?(@symbol) ? @symbol : 'USD'
        country_code = !CURRENCY_CONVERT_TO_USD.keys.include?(@order.country_code) && Order.paypal_currency_allowed?(@symbol) ? @country_code : 'US'
        currency =  CurrencyConvert.currency_convert_cache_by_country_code(country_code)
        total = payment.transactions[0].amount.total
        @order.paypal_txn_id = paypal_txn_id
        @order.paid_currency_rate = currency.rate
        @order.paid_currency_code = currency_code
        @order.amount_sent_payment_gateway = total
        @order.skip_filter = true
        @order.save
        Order.sidekiq_delay(queue: 'critical').paypal_update_order(@order.id)
      end
    end
  end

  # Required params for create
  #
  def order_params
    params.require(:order).permit(:shipping_address, :billing_address, :pay_type)
  end

  # Create hash object required for ga depending on cart object
  #
  def cart_ga_hash
    @ga_hash = {}
    @ga_hash['data'] = []
    design_ids = []
    design_prices = []
    @cart.line_items.each do |item|
      ga_hash = item.ga_data
      design_ids << ga_hash['id']
      design_prices << ga_hash['price']
      ga_hash['dimension2'] = cookies[:theme] if cookies[:theme].present?
      ga_hash['dimension6'] = item.design.experiment_id(session[:exp_category]) if session[:exp_category].present?
      @ga_hash['data'] << ga_hash
    end
    @ga_hash['action_name'] = 'checkout'
    @ga_hash['action_data'] = {'step' => 1}

    @prodid = design_ids.count() > 1 ? design_ids : design_ids[0]
    @pagetype = "cart"
    @totalvalue = design_prices.count() > 1 ? design_prices : design_prices[0]
  end
  
  
  # Create hash object required for ga depending on order object
  #
  def order_ga_hash
    @ga_hash = {}
    @ga_hash['data'] = []
    design_ids = []
    design_prices = []
    @order.designer_orders.includes(:line_items).each do |designer_order|
      designer_order.line_items.each do |item|
        ga_hash = item.ga_data
        design_ids << ga_hash['item_id']
        design_prices << ga_hash['price']
        ga_hash['dimension2'] = cookies[:theme] if cookies[:theme].present?
        ga_hash['dimension6'] = item.design.experiment_id(session[:exp_category]) if session[:exp_category].present?
        @ga_hash['data'] << ga_hash
      end
    end
    @ga_hash['action_name'] = 'purchase'
    @ga_hash['action_data'] = @order.ga_action_data_hash(@ga_hash['action_name'])
    @prodid = design_ids.count() > 1 ? design_ids : design_ids[0]
    @pagetype = "purchase"
    @totalvalue = design_prices.count() > 1 ? design_prices : design_prices[0]
  end

  def get_user_shipping_address
    redirect_to accounts_guest_login_path and return unless current_user
    @shipping_address = if guest_logged_in? && session[:guest_shipping_address].present?
      @user.addresses.find_by(id: session[:guest_shipping_address])
    elsif (shipping_id = params[:shipping_id] || session[:shipping_id]).present?
      @user.addresses.find_by(id: shipping_id)
    elsif (params[:order] && params[:order][:shipping_address])
      @user.addresses.find_by(id: params[:order][:shipping_address])
    end
    @shipping_address = @user.default_address if @shipping_address.blank?
  end

  # redirects to payments url depending on payment options chose.
  #
  def redirect_for_payment
    if @order.total > 0
      case @order.pay_type
      when Order::PAYPAL_CREDIT, Order::PAYMENT_GATEWAY, Order::PAYU_MONEY, Order::PAYPAL, Order::PAYTM, /#{Order::PAYU_GPAY}/
        if @order.billing_country.downcase == 'india'
          if @order.pay_type == Order::PAYU_MONEY || @order.pay_type.end_with?(Order::PAYU_GPAY)
            @values = payu_money_info(@order)
            render 'payu_request'
          elsif @order.pay_type == Order::PAYTM
            @values = @order.paytm_non_seamless_parameters
            render 'paytm_request', layout: false
          elsif @order.actual_country_code != 'IN'
            if PAYU_ENABLE_ON_INTERNATIONAL && (path = payu_redirect_url(@order)).present?
              redirect_to path
            else
              redirect_for_paypal_payment(@order)
            end
          elsif (path = payu_redirect_url(@order)).present?
            if rand(ENV['REDIRECT_TO_RAZORPAY'].to_i)==1
              session[:order_id] = @order.id
              session[:razorpay] = true
              redirect_to new_order_path
            else
              redirect_to path
            end
          else
            redirect_to root_path, alert: 'Something went wrong with payu gateway!'
          end
        elsif @order.billing_country.downcase != 'india'
          if PAYU_ENABLE_ON_INTERNATIONAL && (path = payu_redirect_url(@order)).present?
            redirect_to path
          # elsif (@order.pay_type == Order::PAYMENT_GATEWAY) && rand(1..ENV['REDIRECT_TO_STRIPE'].to_i)==1 && @order.stripe_currency_allowed?
          #   redirect_to stripe_checkout_path(@order.number)
          # elsif @country_code == 'US' && (@order.pay_type == Order::PAYPAL || @order.pay_type == Order::PAYPAL_CREDIT)    #here we need to add if it the USER is using from US and pay_type is PAYPAL AND PAYPAL CREDIT
          #   redirect_for_paypal_payment(@order)
          # elsif @country_code == 'AU' && (@order.pay_type == Order::PAYPAL || @order.pay_type == Order::PAYPAL_CREDIT)
          #   redirect_for_paypal_payment(@order)
          # elsif @country_code == 'GB' && (@order.pay_type == Order::PAYPAL || @order.pay_type == Order::PAYPAL_CREDIT)
          #   redirect_for_paypal_payment(@order)
          else
            session[:order_id] = @order.id
            create_payment
          end
        else
          redirect_to root_path, alert: 'Something went wrong with payment gateway!'
        end
      when Order::WALLET
        Order.sidekiq_delay.mobile_good_data(@order.id) if @order.is_fully_paid_by_wallet?
        redirect_to order_path(@order.number), notice: t('order_create_success_notice')
      else
        if @order.attempted_payment_gateway == Order::PAYPAL_SMART_PAY
          respond_to do |format|
            format.json {paypal_create(@order, @country_code, @symbol)}
          end
        else
          redirect_to order_path(@order.number), notice: t('order_create_success_notice')
        end
      end
    else
      Order.sidekiq_delay.mobile_good_data(@order.id)
      redirect_to @order
    end
  end

  def redirect_for_juspay_payment
    return_url = juspay_response_orders_url
    create_order_response = @order.create_juspay_order(return_url, @payment_option)
    @order.update_columns(attempted_payment_gateway: 'Juspay', payment_gateway_details: create_order_response.to_json)
    payment_response = {}
    if create_order_response['error'].nil? && create_order_response['status'] == 'CREATED'
      transaction_order = @order.create_transaction_order(juspay_order_id: create_order_response['id'])
      if (jp_customer = current_user.juspay_customer).present?
        jp_customer.transaction_orders << transaction_order
        jp_customer.save
      end
      case @order.pay_type
      when Order::JP_CARD
        other_options = {card_token: params[:card_token], save_to_locker: params[:save_to_locker].to_i == 1}
        payment_response = @order.create_juspay_payment('CARD', nil, other_options)
      when Order::JP_SAVED_CARD
        other_options = {card_token: params[:card_token], card_security_code: params[:card_security_code]}
        payment_response = @order.create_juspay_payment('CARD', nil, other_options)
      when Order::JP_WALLET, Order::JP_PAYPAL
        payment_response = @order.create_juspay_payment('WALLET', params[:payment_option])
      when Order::JP_UPI
        if params[:payment_option] == 'GOOGLEPAY'
          other_options = {mobile_number: params[:upi_id]}
        else
          other_options = {upi_vpa: params[:upi_id]}
        end
        payment_response = @order.create_juspay_payment('UPI', params[:payment_option], other_options)
      when Order::JP_NETBANKING
        payment_response = @order.create_juspay_payment('NB', params[:payment_option])
      end
    else
      Order.sidekiq_delay.notify_exceptions("Juspay order create error", "Juspay order not created for order no :#{@order.number}", { params: params, create_order_response: create_order_response})
    end

    if payment_response['payment'].present?
      redirect_url = payment_response['payment']['authentication']['url']
    else
      Order.sidekiq_delay.notify_exceptions("Juspay order create error", "Juspay order not created for order no :#{@order.number}", { params: params, create_order_response: create_order_response})
      redirect_url = new_order_url
    end

    redirect_to redirect_url
  end

  def redirect_for_g2a_payment(order)
    get_user_shipping_address
    order.update_column(:attempted_payment_gateway, 'G2A')
    url,notice = order.g2asetup!(
      g2a_success_orders_url(order: order.number),
      new_order_url, @shipping_address
    )
    unless notice.nil?
      redirect_to url,alert: notice
    else
      redirect_to url
    end
  end

  def redirect_for_paypal_payment(order)
    paid_currency_rate_value, paid_currency_code_value = order.get_paypal_paid_rate_code
    order.update_attributes(attempted_payment_gateway: 'paypal', paid_currency_rate: paid_currency_rate_value, paid_currency_code: paid_currency_code_value)
    get_user_shipping_address
    response = order.setup!(
      paypal_success_orders_url,
      paypal_cancel_orders_url, @rate, @symbol, @shipping_address
    )
    redirect_to response.redirect_uri
  end

  def set_smart_pay_visiblity
    if @country_code == 'IN'
      @paypal_domestic = PAYPAL_SMARTPAY_SWITCH == 1
    end
  end

  def set_juspay_visibility
    if params[:jp]=='jus_pay_it'
      @juspay_ec = true
    elsif @country_code == 'IN' && JUSPAY_ENABLE
      if ENV['JUSPAY_EXPRESS_CHECKOUT'].present?
        @juspay_ec = if ENV['JUSPAY_EXPRESS_CHECKOUT'].to_i == 1
          true
        elsif !session[:juspay_ec].nil?
          session[:juspay_ec]
        else
          session[:juspay_ec] = rand(1..ENV['JUSPAY_EXPRESS_CHECKOUT'].to_i) == 1
        end
      end
    else
      false
    end
  end

  def paypal_create(order, country_code, symbol)
    begin
      order.cart.update_column(:used, false)
      return_url = "/orders/#{order.number}"
      # currency_symbol = symbol
      rate = order.currency_rate
      order.add_notes_without_callback("paypal: waiting", 'payment', current_account)
      payapal_api = Payment::Paypal::PaypalV2RestApi.new(order)
      auth_token = payapal_api.get_authentication_token
      render json: {"has_error" => true, "errors" => "Some Error Accoured Please Try Again In Some Time"} and return if !auth_token
      paypal_request = Payment::Paypal::PaypalApiData.new(order, country_code, symbol)
      payload = paypal_request.get_checkout_v2_request_payload
      response_hash = payapal_api.create_payment(payload, auth_token)
      if !response_hash.key?("has_error")
        session[:order_number] = order.number
        if response_hash['id'].present?
          order.token = response_hash['id']
        else
          error_message = [response_hash['payer']['name'], response_hash['purchase_units'].first["invoice_id"]].join(' || ')
          params = { response: response_hash, order_number: order.number} 
          Order.sidekiq_delay.notify_exceptions("Paypal Smartpay Mobile empty ID/Token Error-", error_message, order.number)
        end
      end
      order.skip_filter = true
      order.save!
      render json: response_hash.merge({:return_url => return_url})
    rescue => error
      Order.sidekiq_delay.notify_exceptions("Paypal Rutime Exception Mobile", error.inspect, {order_number: order.number})
      Order.where(number: order.number).update_all(paypal_error: error.inspect)
      render json: {has_error: true, message: "Some Exception Occured Please Try Again Later"}
    end
  end

  def set_campaign_details(order)
    order.order_notification.store(:show_customs_message,session[:show_customs_message]) if session[:show_customs_message].present?
    order.multi_channel_marketing = ''
    if session[:utm_source].present?
      order.utm_source = session[:utm_source].split(',').last(1).join
      order.multi_channel_marketing.concat('Source : ' + session[:utm_source][1..231] + '-----')
    end
    if session[:utm_medium].present?
      order.utm_medium = session[:utm_medium].split(',').last(1).join
      order.multi_channel_marketing.concat('Medium: ' + session[:utm_medium][1..231] + '-----')
    end
    if session[:utm_campaign].present?
      order.utm_campaign = session[:utm_campaign].split(',').last(1).join
      order.multi_channel_marketing.concat('Campaign: ' + session[:utm_campaign][1..1000] + '-----')
      campaigns = session[:utm_campaign][1..1000].split(',').uniq
      campaigns.each do |campaign|
        order.tag_list.add("convert-mkt-#{campaign}")
      end
    end
    order.tag_list.add("convert-mkt-#{cookies[:theme]}") if cookies[:theme].present?
    order.utm_term = session[:utm_term][0..200] if session[:utm_term].present?
    order.utm_content = session[:utm_content][0..200] if session[:utm_content].present?
    order.ip_address = request.remote_ip if request.remote_ip.present?
    order.other_details['user_agent'] = request.user_agent
    order.other_details['session_id'] = session.id
    order.icn_term = session[:icn_term][0..200] if session[:icn_term].present?
    if session[:referral_campaign].present?
      order.icn_term = session[:referral_campaign]
      session.delete(:referral_campaign)
    end
    order.utm_exp_id = session[:utm_expid] if session[:utm_expid].present?
    order.utm_exp_id = session[:exp_sorted].keys if session[:exp_sorted].present?
  end

  def get_user_billing_address
    @billing_address = @user.default_address || @user.addresses.find_by(id: params[:order][:billing_address])
  end


  def validate_shipping_amount
    shipping_country = Address.find_by_id(params[:order][:shipping_address]).try(:country)
    if @cart.check_for_min_cart_condition?(MIN_CART_VALUE_ON_HEADER['mobile'], @country_code, shipping_country)  && params[:order][:pay_type] != "Cash On Delivery"
      ActiveRecord::Associations::Preloader.new.preload(@cart.line_items,:line_item_addons)
      discounted_item_total = @cart.item_total(1).to_i - @cart.additional_discounts(@actual_country)
      if discounted_item_total < LUXE_INTERNATIONAL_MIN_CART_VAL.to_i
        redirect_to :back
      end
    # Aapno rajastan commented
    # elsif shipping_country.try(:downcase)  == 'india'
    #   cart_items = @cart.line_items.preload(design:  :categories)
    #   redirect_to :back if (cart_items.any?{|item| item.design.designer_id == Designer.get_id_by_name('Aapno Rajasthan')} && cart_items.any?{|item| item.design.categories.any?{|c| c.name == 'rakhi-international'}})
    end
  end

  def validate_pincode_deliverable
    shipping_address = Address.find_by_id(params[:order][:shipping_address])
    shipping_pincode = shipping_address.try(:pincode)
    if NON_SERVICABLE.include?shipping_pincode
      redirect_to :back
    end
    if shipping_address.try(:country) == 'India' && !@cart.pincode_servicable_for_category?(shipping_pincode)
      redirect_to :back
    end
  end
end
