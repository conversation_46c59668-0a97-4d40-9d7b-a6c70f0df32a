class Api::V1::DeviseOverrides::OmniauthCallbacksController < DeviseTokenAuth::OmniauthCallbacksController
  
  include MirrawUtil

  APPLE_PEM_URL = "https://appleid.apple.com/auth/keys"
  before_filter :user_currency
  # skip_before_filter :verify_authenticity_token

  # REF: https://github.com/lynndylanhurley/devise_token_auth/blob/master/app/controllers/devise_token_auth/omniauth_callbacks_controller.rb#L89
  #
  # Overridden for rendering json.
  #
  def render_data_or_redirect(message, data, user_data = {})

    # We handle inAppBrowser and newWindow the same, but it is nice
    # to support values in case people need custom implementations for each case
    # (For example, nbrustein does not allow new users to be created if logging in with
    # an inAppBrowser)
    #
    # See app/views/devise_token_auth/omniauth_external_window.html.erb to understand
    # why we can handle these both the same.  The view is setup to handle both cases
    # at the same time.
    if ['inAppBrowser', 'newWindow'].include?(omniauth_window_type)
      render_data(message, data)

    elsif auth_origin_url # default to same-window implementation, which forwards back to auth_origin_url

      # build and redirect to destination url
      redirect_to DeviseTokenAuth::Url.generate(auth_origin_url, data)
    elsif !data.key?(:error)
      user = @resource.user
      render json: {
        data: @resource.token_validation_response.merge(
          name: user.full_name,
          image: user.image_url ||"#{SITE_PROTOCOL}#{MOBILE_SITE_URL}/" + ActionController::Base.helpers.image_url("fb_default.jpg"),
          gender: user.gender,
          total_designers: user.designers_count,
          total_users: user.users_count,
          total_followers: user.followers_count,
          referral_login: !!user.referral,
          new_referral_account: !!@new_referral_account,
          user_type: user.user_type,
          account_id: @resource.id
        )
      }
    else
      
      # there SHOULD always be an auth_origin_url, but if someone does something silly
      # like coming straight to this url or refreshing the page at the wrong time, there may not be one.
      # In that case, just render in plain text the error message if there is one or otherwise 
      # a generic message.
      fallback_render data[:error] || 'An error occurred'
    end
  end


  # REF: https://github.com/lynndylanhurley/devise_token_auth/blob/master/app/controllers/devise_token_auth/omniauth_callbacks_controller.rb#L149
  #
  # Overriden as original method was explicitely adding nickname and image which is not available in our case
  #
  def assign_provider_attrs(user, auth_hash)
    user.assign_attributes({ email: auth_hash['info']['email'] })
  end

  # REF: https://github.com/lynndylanhurley/devise_token_auth/blob/master/app/controllers/devise_token_auth/omniauth_callbacks_controller.rb#L127
  #
  # Overriden for displaying setting auth_hash and rendering error if any
  # Also have passed block to super method to create user for account
  #
  def omniauth_success
    auth_hash_data = auth_hash
    if @error.present?
      if @error=='Expired JWT token'
        status_code = 401
      else
        status_code = 422
      end
      render json: {error: @error}, status: status_code
    elsif is_login_restricted(auth_hash_data)
      render json: {error: "#{account.accountable_type} login restricted"}, status: 401
    else
      super do |resource|
        user = User.where(email: @resource.email).first_or_initialize
        user.assign_attributes(
          user.attributes.slice(
            'email', 'first_name', 'last_name', 'image_url'
          ).merge(auth_hash_data['info'])
        )
        app_versions = ALLOWED_APP_VERSIONS[22..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
        if (REFERRALS_ACTIVE == 'true') && user.new_record? && (params[:referral] == 'true') && ApiData.find_by_device_id(request.headers['Device-ID']).nil? && (referrer_email = User.find_by_id(params[:referrer]).try(:email)).present?
          user.assign_attributes(referral: true)
          Referrer.new_referral(referrer_email, @resource.email, (REFERRAL_TYPES - ['new_user_wallet']).first)
          @new_referral_account = true
        elsif REFERRAL_TYPES.include?('new_user_wallet') && user.new_record? && WELCOME_OFFER_COUNTRIES.include?(@country_code) && app_versions.include?(request.headers['App-Version']) && ApiData.find_by_device_id(request.headers['Device-ID']).nil?
          user.assign_attributes(referral: true)
          Referrer.new_referral('<EMAIL>', @resource.email, 'new_user_wallet')
        end
        user.save
        @resource.update_attributes(accountable: user, oauth_id: auth_hash_data['id'], fcm_registration_token: request.headers['Fcm-Token'])
        @resource.add_to_subscribers("omniauth/#{request.headers.fetch('Provider')}/login",
                                     @country_code,
                                     "API/#{request.headers['App-Source']}",
                                     request.headers['True-Client-IP'] || request.ip)
        update_auth_header
      end
    end
  rescue => e
    render json: { errors: { full_messages: ['Email Already in Use.'] } }, status: 403
  end

  # REF: https://github.com/lynndylanhurley/devise_token_auth/blob/master/app/controllers/devise_token_auth/omniauth_callbacks_controller.rb#L234
  #
  # Overriden for getting auth_hash from facebook and google for client flow
  #
  def auth_hash
    begin
      @_auth_hash ||= if session.key?('dta.omniauth.auth')
          session.delete('dta.omniauth.auth')
        elsif request.headers.fetch('Provider') == 'facebook'
          get_auth_hash({
            url: "https://graph.facebook.com/me?access_token=#{request.headers.fetch('Access-Token')}&fields=id,email,name,picture.type(large),first_name,last_name",
            headers: {
              'accept' => 'application/json'
            }
          })
        elsif request.headers.fetch('Provider') == 'google_oauth2'
          get_auth_hash({
            url: 'https://www.googleapis.com/userinfo/v2/me',
            headers: {'accept' => 'applicaiton/json','Authorization' => "Bearer #{request.headers.fetch('Access-Token')}"}
          })
        elsif request.headers.fetch('Provider') == 'apple'
          if !request.headers.fetch('Access-Token').present? || !request.headers.fetch('email').present? || !request.headers.fetch('name').present? || !request.headers.fetch('userIdentity').present?
            @error = 'Missing params'
          else
            token_validity = validate_tokens
            if token_validity['success']
              get_auth_hash(false,token_validity['data'])
            end
          end
        end
    rescue StandardError => e
      @error = e.message
    end
    @_auth_hash
  end

  # Retrieves user information with given options and formats hash for auth_hash
  #
  # ==Return
  # Hash
  #
  def get_auth_hash(options=false, user_info=false)
    if options
      user_info = HTTParty.get(options[:url], headers: options[:headers]).parsed_response
    end
    raise user_info['error']['message'] if user_info['error'].present?
    raise 'Email not received' if user_info['email'].blank?
    fname, lname = user_info['name'].split(' ') unless user_info['name'].nil?
    {
      'uid' => user_info['email'],
      'id' => user_info['id'],
      'provider' => request.headers.fetch('Provider')=='apple' ? 'apple' : 'email', 
      'info' => {
        'email' => user_info['email'],
        'first_name' => user_info['given_name'] || user_info['first_name'] || fname,
        'last_name' => user_info['family_name'] || user_info['last_name'] || lname,
        'image_url' => user_info['picture'].is_a?(Hash) ? user_info['picture']['data']['url'] : user_info['picture']
      }
    }
  end

  # Overriden for getting auth_hash from facebook and google for client flow
  #
  def get_resource_from_auth_hash
    # find or create user by provider and provider uid
    @resource = Account.where(email: auth_hash['uid']).first_or_initialize
    @resource.uid = auth_hash['uid']
    @resource.provider = auth_hash['provider']

    if @resource.new_record?
      @oauth_registration = true
      set_random_password
    end
    # sync user info with provider, update/generate auth token
    assign_provider_attrs(@resource, auth_hash)

    # assign any additional (whitelisted) attributes
    extra_params = whitelisted_params
    @resource.assign_attributes(extra_params) if extra_params

    @resource
  end

  def is_login_restricted(auth_hash_data)
    account = Account.find_by(email:auth_hash_data['uid'])
    auth_hash_data['uid'].present? && account.present? && !account.user?
  end

  def validate_tokens
    name = request.headers.fetch('name')
    userIdentity = request.headers.fetch('userIdentity')
    jwt = request.headers.fetch('Access-Token')
    email = request.headers.fetch('email')

    begin
      header_segment = JSON.parse(Base64.decode64(jwt.split(".").first))
      alg = header_segment["alg"]
      kid = header_segment["kid"]

      #TODO: add timeout here!
      apple_response = Net::HTTP.get(URI.parse(APPLE_PEM_URL))
      apple_certificate = JSON.parse(apple_response)

      keyHash = ActiveSupport::HashWithIndifferentAccess.new(apple_certificate["keys"].select {|key| key["kid"] == kid}[0])

      key = OpenSSL::PKey::RSA.new
      key.e = OpenSSL::BN.new(UrlSafeBase64.decode64(keyHash["e"]), 2)
      key.n = OpenSSL::BN.new(UrlSafeBase64.decode64(keyHash["n"]), 2)

      #token_data = JWT.decode(jwt, jwk.public_key, true, {algorithm: alg})[0]
      token_data = JWT.decode(jwt, key, true, {algorithm: alg})[0]

      if token_data.has_key?("sub") && token_data.has_key?("email") && userIdentity == token_data["sub"]
        {
          'success'=>true,
          'data'=> {'name'=>name, 'email'=>token_data['email'], 'id'=>userIdentity, 'picture':nil}
        }
      else
        @error = e.class.to_s
        {'success'=>false}
      end
    rescue StandardError => e
      if e.class.to_s =='JWT::ExpiredSignature'
        @error = 'Expired JWT token'
      end
      {'success'=>false}
    end
  end
end
