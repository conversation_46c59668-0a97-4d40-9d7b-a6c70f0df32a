class Api::V1::DeviseOverrides::<PERSON><PERSON><PERSON>roller < DeviseTokenAuth::SessionsController

  before_action :authenticate_user, only: :create
  # skip_before_filter :verify_authenticity_token

  # Assign the cart to user after after log in
  #
  # REF: https://github.com/lynndylanhurley/devise_token_auth#passing-blocks-to-controllers
  #
  def create
    field = (resource_params.keys.map(&:to_sym) & resource_class.authentication_keys.keys).first

    @resource = nil
    if field
      q_value = get_case_insensitive_field_from_resource_params(field)
      
      @resource = find_resource(field, q_value)
    end

    if @resource && valid_params?(field, q_value) && (!@resource.respond_to?(:active_for_authentication?) || @resource.active_for_authentication?)
      valid_password = @resource.valid_password?(resource_params[:password])
      if (@resource.respond_to?(:valid_for_authentication?) && !@resource.valid_for_authentication? { valid_password }) || !valid_password
       return render_create_error_bad_credentials
      end
      @client_id, @token = @resource.create_token
      @resource.save

      sign_in(:user, @resource, store: false, bypass: false)

      yield @resource if block_given?

      render_create_success
    elsif @resource && !(!@resource.respond_to?(:active_for_authentication?) || @resource.active_for_authentication?)
      if @resource.respond_to?(:locked_at) && @resource.locked_at
        render_create_error_account_locked
      else
        render_create_error_not_confirmed
      end
    else
      render_create_error_bad_credentials
    end

    update_cart_fcm_token
    # @resource.send_confirmation_instructions if @resource && !@resource.try(:confirmed?)
  end

  def render_create_success
    user = @resource.user
    render json: {
      data: @resource.token_validation_response.merge(
        name: user.full_name,
        image: user.image_url || "#{SITE_PROTOCOL}#{MOBILE_SITE_URL}/" + ActionController::Base.helpers.image_url("fb_default.jpg"),
        gender: user.gender,
        total_designers: user.designers_count,
        total_users: user.users_count,
        total_followers: user.followers_count,
        referral_login: !!user.referral,
        user_type: user.user_type,
        account_id: @resource.id
      )
    }
  end

  def refresh
    status = false
    if (uid = params['Uid']).present? &&
      (client = params['Client']).present?
      if (account = Account.find_by_email(uid)).present?
        if account.tokens[client].present?
          response.headers.merge!(account.create_new_auth_token(client))
          status = true
        end
      end
    end
    if status
      render json: {data: account.token_validation_response}
    else
      head :unauthorized
    end
  end

  def mobile_login
    @resource = find_resource_by_phone
    if @resource and @resource.verify_otp(params[:one_time_password])
      @client_id, @token = @resource.create_token
      @resource.save
      sign_in(:user, @resource, store: false, bypass: false)
      render_create_success
      update_cart_fcm_token
    else
      render json: {error: 'Incorrect OTP'}, status: 401
    end

  end

  def update_cart_fcm_token
    if @resource.try(:user?) && (device_id = request.headers['Device-ID']).present?
      # unless Cart.where(user_id: @resource.accountable_id,
      #   device_id: device_id, used: false).exists?
        Cart.order('id DESC').find_or_create_by(device_id: device_id,
          used: false).update_attributes(user_id: @resource.accountable_id)
        @resource.update_attributes(fcm_registration_token: request.headers['Fcm-Token'])
      # end
    end
  end
end

private

def find_resource_by_phone
  Account.find_by_phone(params[:user_phone])
end

def authenticate_user
  account = Account.find_by_email(params[:email].downcase)
  if account && !account.user?
    render json: {errors: ["#{account.accountable_type} login restricted"]}, status: 401
  end
end