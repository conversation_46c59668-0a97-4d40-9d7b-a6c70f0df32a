# Builds the query for solr
class SolrPreferenceQuery

  include Api::V1::StoreHelper
  PERMITTED_KEYS = [:category_kind, :facets, :collection, :catalogue, :buy_get_free, :max_odr, :created_at,
      :category_ids, :designer_ids, :option_type_value_ids,
      :colour_property_value_ids, :property_value_ids, :min_rating,
      :min_discount, :max_discount, :min_carat, :max_carat, :min_price, :max_price]

  PERMITTED_VALUES = {
    min_rating: {min: 0, max: 5},
    min_discount: {min: 0},
    max_discount: {min: 0, max_discount: 100},
    min_price: {min: 0, max: MAX_PRICE_PER_PRODUCT},
    max_price: {min: 0, max: MAX_PRICE_PER_PRODUCT},
    max_odr: {min: 0, max: 1}
  }

  attr_accessor :query_string, :query_options, :kind
  delegate :to_param, :to_s, to: :query_options

  def initialize(query_string, kind: nil)
    self.kind = kind
    self.query_string = query_string
  end

  def query_string=(value)
    @query_string = value
    update_options
  end

  def query_options
    @query_options ||= {}
  end

  def update_options
    new_q_options = sanitize_query(raw_query)

    new_q_options.each do |key, value|
      if key.ends_with?('_ids') || key == 'designer_id'
        new_q_options[key] = value.split(',').collect(&:to_i)
      end
    end
    if (facets = new_q_options.delete(:facets)).present?
      property_value_ids = PropertyValue.get_property_values_for_facets(kind, facets)
      new_q_options[:property_value_ids] = property_value_ids if property_value_ids.present?
    end
    if new_q_options[:created_at].present?
      new_q_options[:created_at] = new_q_options[:created_at].to_i.days.ago
    end
    if (category_kind = new_q_options.delete(:category_kind)).present?
      if (category = Category.find_by_namei(category_kind)).present?
        new_q_options[:category_parents] = category.id
      end
    end
    if (designer_ids = new_q_options.delete(:designer_ids)).present?
      new_q_options[:designer_id] = designer_ids
    end
    if (new_q_options[:min_discount]).present?
      new_q_options[:min_discount_percent] = adjust_promo_discount(new_q_options.delete(:min_discount).to_i)
    end
    if (new_q_options[:max_discount]).present?
      new_q_options[:max_discount_percent] = adjust_promo_discount(new_q_options.delete(:max_discount).to_i)
    end
    if (new_q_options[:min_price]).present?
      sale_factor = get_sale_factor
      # new_q_options[:min_price] = new_q_options[:min_price].to_i * SALE_FACTOR
      price_field = price_field_solr
      new_q_options[:"min_#{price_field}"] = new_q_options[:min_price].to_i * sale_factor
    end
    if (new_q_options[:max_price]).present?
      sale_factor ||= get_sale_factor
      price_field ||= price_field_solr
      # new_q_options[:max_price] = new_q_options[:max_price].to_i * SALE_FACTOR
      new_q_options[:"max_#{price_field}"] = new_q_options[:max_price].to_i * sale_factor
    end
    if new_q_options[:buy_get_free].present?
      unless (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
        new_q_options.delete(:buy_get_free)
      else
        if bmgnx_hash[:buy_get_free].to_i == 1
          new_q_options[:max_price] = bmgnx_hash[:filter].to_i
          new_q_options.delete(:buy_get_free)
        end
      end
    end
    if (odr_90 = new_q_options.delete(:max_odr).to_f / 100.0) > 0
      new_q_options[:odr_90_day] = odr_90
    end
    if (min_rating = new_q_options.delete(:min_rating)).present?
      new_q_options[:average_rating] = (min_rating.to_i..5)
    end
    merge_min_max_options(new_q_options)
    self.query_options = new_q_options.stringify_keys
  end

  def raw_query
    if query_string.present? && query_string =~ /.+~.+(_and_.+~.+)*/
      query_string.split('_and_').collect{|query| query.split('~')}.to_h.with_indifferent_access
    else
      {}
    end
  end

  def execute(scope)
    scope.instance_exec(query_options, &query_proc)
  end

  def sanitize_query(parameters)

    # remove un-necessary parameters
    parameters.slice!(*PERMITTED_KEYS)

    # set limits for min max
    parameters.each do |key, value|
      if range_hash = PERMITTED_VALUES[key]
        min_value = range_hash[:min].to_f || 0.0
        max_value = range_hash[:max].to_f || Float::INFINITY
        if value.to_f < min_value
          new_value = min_value
        elsif value.to_f > max_value
          new_value = max_value
        end
        parameters[key] = new_value if new_value
      end
    end
    parameters
  end

  private
  # Provides Proc to be excecuted in Sunspot:DSL:Scope
  def query_proc
    Proc.new do |boost_options|
      if boost_options.present?
        boost(5.0) do
          boost_options.each do |key, value|
            if (new_key = key.match(/min_(.*)/).try(:[], 1)).present?
              with(new_key).greater_than(value)
            elsif (new_key = key.match(/max_(.*)/).try(:[], 1)).present?
              with(new_key).less_than(value)
            elsif key == 'created_at'
              with(key).greater_than(value)
            else
              with(key, value)
            end
          end
        end
      end
    end
  end

  def merge_min_max_options(options)
    min_max_hash = {}
    options.each do |key, value|
      if p_name = key.match(/min_(.*)/).try(:[], 1)
        min_value = value
        max_value = options["max_#{p_name}"]
      elsif p_name = key.match(/max_(.*)/).try(:[], 1)
        max_value = value
        min_value = options["min_#{p_name}"]
      end
      if p_name && min_value && max_value
        options.except!("max_#{p_name}", "min_#{p_name}")
        min_max_hash[p_name] = (min_value.to_f..max_value.to_f)
      end
    end
    options.merge!(min_max_hash)
  end
end