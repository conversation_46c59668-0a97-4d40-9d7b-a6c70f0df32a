# require 'httparty'
class UnbxdSearch
  include HTTParty
  
  base_uri MirrawMobile::Application.config.unbxd[:UNBXD_SERVICE_URL]

  class << self
    def get_search_results_from_unbxd params
      response = post("/search", body: params.to_json)
      if response.key?('search')
        design_ids = response['search']['designs'].collect{|design| design['id']}
        designs = Design.where(id: design_ids).preload(:designer, :categories)
        discounts = {}
        designs.each do |design|
          discounts[design.id] = design.effective_price_currency(1)
        end
        response['search']['designs'].each do |design|
          design['discount_price'] = discounts[design['id'].to_i]
          design['inr_discount_price'] = discounts[design['id'].to_i]
          design_obj = Design.find design['id'].to_i
          design['ready_to_ship'] = design_obj.rts_available(@actual_country)
        end
      end
      return response
    end
  end
end