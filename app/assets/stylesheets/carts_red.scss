// Place all the styles related to the Carts controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables_red';
@import 'red_theme';
@import 'unbxd_recommendations_red';

$gray: #e3e3e3;
$light_gray: #f1f1f1;
$blue: #70DBFF;
body{
  font-family: "Inter", sans-serif !important;
}
.carts_show.page{
  #container{
    margin-top: 0 !important;
  }
  .font-gray{
    color: $text_light_gray;
  }
}
@media only screen and (min-width: 768px){
  .carts_show.page{
    #container{
      margin-top: 2rem !important;
    }
  }
}
.enableLink{
  color: #c13960 !important;
  font-weight: bold !important;
  cursor: pointer !important;
}
#carts_block {
  h2.heading{
    color: $text_black;
  }
  ul.addons-notes.accordion {
    padding-left: 0em;
    color: $text_light_gray;
    margin-left: 0em;
    font-size: 14px;
    padding-right: 0;
    .accordion-navigation {
      > a {
        background: inherit;
        font-family: "Inter", sans-serif !important;
        color: inherit;
        padding: 0rem;
        font-size: $cart_font;
        &:before {
          content: '»';
          float: left;
          color: $dark_red;
          font-size: 14px;
          font-weight: 700;
          padding-right: 2px;
          margin-top: -2px;
        }
      }
      .text-right{
        float: right;
      }
      > .content {
        padding: 0rem;
        &.active {
          background: inherit;
        }
      }
      &.active > a:before {
        content: '«';
      }
    }
  }
  .item_block {
    background-color: #fff;
    border: 1px solid $light_gray;

    margin-bottom: 1em;
    padding: 0.5em 0.5em;
    margin-left: 0;
    margin-right: 0;
    .image-box{
      padding: 0 5px 0 0px;
      .truncate{
        color: $text_light_gray;
        text-align: center;
        padding: 5px;
      }
    }
    .design_quantity{
      color: $text_black;
      .quantity_list{
        width: 42%;
      }
    }
    .design-title{
      font-size: 0.9rem;
      font-weight: 600;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .right{
      padding-right: 0;
      text-align: right;
    }
    .item-price-font{
      font-size: 12px;
      // margin-top: 2px;
    }
    .action-buttons-container {
      border-top: 1px solid #9c9c9c;
      height: 35px;
      text-align: center;
      font-size: 0.8rem;
      text-transform: uppercase;
      font-weight: bold;
      .action-button {
        margin: 5px 0;
        padding: 5px 0;
      }
      .action-button-cart {
        border-right: 1px solid #9c9c9c;
        a {
          color: black;
        }
      }
    }
    .saree-addon-checkbox{
      outline: none;
      appearance: none;
      -webkit-appearance: none;
      width: 22px;
      height: 22px;
      border: 2px solid grey;
      border-radius: 2px;
      margin: 0.3em;
      &:not(:checked){
        &:after{
          content: "+";
          color: grey;
          padding: 0 0.25em;
          font-size: 1.3em;
        }
      }
      &:checked{
        border: 2px solid $dark_red;
        background: $dark_red;
        &:after{
          content: "✔";
          color: white;
          padding: 0 0.15em;
          font-size: 1em;
        }
      }
    }
    .saree-bag-addon{
      margin: 0;
    }
    .saree-bag-addon-quantity{
      width: 80% !important;
      margin: 0.3em 0;
    }
    .design-pr{
      margin: 0.4em 0;
    }
  }
  .close {
    margin-top: -6px;
    line-height: 1em;
    font-size: 22px;
    color: $text_black;
  }

  .panel_block {
    margin-top: 0px !important;
    overflow: visible;
    .shopping-cart-text{
      position: relative;
      bottom: 1rem;
    }
    #fixed_checkout_button {
      @media only screen and (min-width: 991px){
        display: none;
      }
      right: 0;
      left: 0;
      top: auto;
      background-color: white;
      padding: 0px !important;
      user-select: none;
      -o-user-select:none;
      -moz-user-select: none;
      -khtml-user-select: none;
      -webkit-user-select: none;
      a {
        margin: 0 !important;
        color: white;
      }
      .cart_checkout_button {
        background-color: $text_red;
        padding: 14px !important;
      }
      .cart-checkout-btn{
        padding: 0px;
      }
      .view_details_button {
        background-color: $body_white;
        color: $text_black;
        font-size:16px;
        margin-top: 3px !important;
        .view_details_text {
          font-size: 12px;
          letter-spacing: 1px;
          font-weight:normal;
          margin-top: 7px;
        }
      }
      .button {
        padding: 0px;
        margin: 0px;
        width: 100%;
        font-weight: bold;
        font-size: $big_font
      }
    }
    .fixed_cart_button {
      position: fixed;
      bottom: 0;
      z-index: 10;
    }
    .save_cart {
      font-size: $cart_font;
      span{
        vertical-align: -webkit-baseline-middle;
      }
    }
    .panel_heading{
      padding: 0px;
      overflow: visible;
      .save_email{
        padding: 8px 10px;
        margin-bottom: 0px;
        border: none;
        font-size: $font_size;
        float: right;
        text-transform: uppercase;
        width: 100%;
        margin-top: -8px;
        background: none;
        color: #721220;
        border: 1px solid #721220;
      }
      .save_email_box{
        padding: 0px 5px 0px 5px;
        margin: 0px 0px 0px 5px;
        border: none;
        box-shadow: $gray_btn_box;
        line-height: 0px;
        width: 95%;
      }
      input[type="email"]{
        display: inline;
        padding: 0.1rem 0.5rem;
        margin: 0px;
        &:focus{
          box-shadow: 0 1px 0 0 $input_focus_color;
          border-bottom: 1px solid $input_focus_color;
        }
      }
      .email_form{
        display: none;
      }
      .email_display{
        padding: 1px 2px;
        font-size: 0.9rem;
        display: none;
      }
    }
    .panel_content {
      padding: 1em 0px 0px 0px;
      font-size: $cart_font;
      overflow: visible;
      img {
        width: 100%;
      }
      .minimum_cart_value_message{
        background-color: $light_red;
        color: white;
        padding: 12px;
        font-size: 13px;
        border-radius: 3px;
        margin-bottom: 10px;
      }
      .cart-discount-message{
        h6.text-center{
          margin-bottom: 0.2rem;
        }
      }
      .design_quantity {
        padding: 0;
        select {
          padding: 0.1em;
          height: inherit;
          // background: #eee;
        }
        // &:after{
        //   content: "";
        //   position: absolute;
        //   right: 32px;
        //   top: 3px;
        //   border-right: 1px solid #303030;
        //   border-bottom: 1px solid #303030;
        //   width: 8px;
        //   height: 8px;
        //   transform: 90deg;
        //   transform: rotate(45deg);
        // }
      }
    }
  }
  .columns.large-3.cart_right_side_bar {
    position: sticky;
    top: 22%;
    right: 0;
}
  .blue-link{
    color: $snow_white;
  }
  #totals_block{
    table{
      border: none !important;
      width: 100%;
      background: none !important;
      margin-bottom: 0px !important;
    }
    tr{
      background: none !important;
    }
    td,
    th {
      padding: 0px !important;
    }
    .add_more_items{
      float : right;
      .charges {
        color: #670e19;
      }
    }
  }
  .item-total{
    font-size: 95%;
    padding-left: 0px !important;
    padding-right: 0px !important;
  }
  .coupon, .wallet{
    color: $text_black;
    background: transparent;
    border: 1px solid #b7b6b6;
    height: 2.3125rem;
    padding: 0.5rem 0.75rem 0.5rem 0.75rem;
  }
  input[name=coupon_code], .small-4 > .button{
    margin-bottom: 0.5rem;
    border: none;
    border-bottom: 1px solid #9e9e9e;
    box-shadow: none;
    font-family: "Inter", sans-serif;
    &:focus {
      box-shadow: 0 1px 0 0 $input_focus_color;
      border-bottom: 1px solid $input_focus_color;
    }
    .small-4 > .button{
      padding: 0px;
      box-shadow: $gray_btn_box;
    }
    .coupon-box,.wallet-box{
      display: none;
    }
  }
  .item-text{
    padding: 0px;
  }
  .featured_products > *:first-child{
    position: static;
  }
}

#apply-coupon-block{
  .remove-coupon-container {
    margin-top: 4px;
    margin-bottom: 10px;
    height: 51px;
    width: 99%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 2px solid #ccc;
    padding: 14px;
    margin-left: 2px;
  }
  .coupon-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    line-height: 1.2;
    margin-top: 4px;
  }
  .saved-amount {
    color: green;
    font-size: 14px;
  }
  .remove-coupon-button-css{
    margin-top: 18px;
    font-size: 15px;
    background-color: transparent;
    color: #681e19;
    padding: 5px;
    margin-left: 2px;
  }
  .remove-coupon-button-css:hover {
    background-color: #eaeaec;
    color: black;
    cursor: pointer;
    margin-left: 2px;
  }
  .apply_coupon {
    margin-bottom: -15px;
  }
  .coupon-button-css{
    margin-left: 2px;
    background: none;
    color: #721220;
    width: 99%;
    border: 1px solid #721220;
    padding: 7px;
  }
  .coupon-button-css:hover {
    margin-left: 2px;
    cursor: pointer;
  }
  label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
}

.apply_wallet{
  .usable-reward-text{
    font-size: 14px;
    padding-right: 10px;
    span{
      font-weight: bold;
    }
  }
}
.coupon-box,.wallet-box{
      display: none;
    }
.use-msg{
  font-size: 12px;
  .apply-btn-link{
    background: transparent;
    border: none !important;
    padding: 0px !important;
    &:focus{outline: none;}
  }
}
.giftwrap {
  margin-left: 2px;
  width: 99%;
  border: 1px solid #b7b6b7;
  margin-bottom: 15px;
  text-align: center;
  padding: 5px;
  .giftwrapped-text {
    border-right: 1px solid;
  }
  .giftwrap-remove {
    color: $dark_red !important;
  }
  a.giftwrapped {
    .giftwrapped-col {
      color: $text_black;
    }
  }
  .f_gift_wrap {
    background: image-url('sprite.png') no-repeat;
    -webkit-filter: invert(45%);
    -moz-filter: invert(45%);
    -o-filter: invert(45%);
    -ms-filter: invert(45%);
    filter: invert(45%);
    display: inline-block;
    width: 20px;
    height: 18px;
    background-position: 28% 0;
    background-size: 110px;
  }
}
.design_offer_panel_content{
  padding: 0px;
}
.best_offer {
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  text-align: center;
  padding: 0px;
  font-size: 15px;
  background-color: $body_white;
  display: flex;
  color: $text_light_gray;
  font-size: 14px;
  .info_icon {
    border-radius: 50%;
    background-color: $dark_red;
    padding: 1px 8px;
    font-size: 13px;
    font-style: italic;
    font-weight: 700;
    color: $body_white;
    height: 50%;
    margin-left: 5px;
  }
  .design_offer_cart_offer_msg {
    color: white;
    background: #8f1c1e;
    a{
      font-style: italic;
      color: white;
      text-decoration: underline;
    }
  }
  span{
    margin-left: 5px;
    .hyperlink_text {
      font-style: italic;
      text-decoration: underline;
    }
  }
}
.offer_messages_block {
  padding: 5px;
  ul{
    font-size: 14px;
    li {
      padding: 5px;
    }
    i{
      font-size: 12px;
    }
  }
 
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: $body_white;
}

.reveal-modal-bg {
  background: rgba(0, 0, 0, 0.45);
  bottom: 0;
  display: none;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 99;
  left: 0;
}
// bmgn tnc modal
@media only screen and (min-width: 767px) {
  div#bmgnTncModal{
    width: 65%;
    margin: 10% 18%; 
    height: 65%;
  }
  .modal-body {
    margin-top: -3rem;  
  }  
}

#bmgnTncModal,#qpmTncModal,#pdTncModal,#shippingTncModal{
  display: none;
  background-color: transparent;
  height: 100%;
  overflow: auto;
  width: 90%;
  margin: 0px 5%;
  outline: none;
  top: 0 !important;
  .modal-dialog{
    .modal-content{
      height: 100%;
      background-color: $text_white;
      color: $text_black;
      font-size: 1.25em;
      margin-top: 45px;
      .modal-header{
        float: right;
        position: absolute;
        right: 5px;
        font-size: 30px;
      }
      .modal-body{
        padding: 16px;
        font-size: 12px;
        .modal-text{
          .ans{
            margin-bottom: 20px;
          }
          ul{
            font-size: 12px;
          }
        }
      }
    }
    .modal-footer{
      text-align: center;
      background: #f4f4f4;
      padding: 15px 0px;
      display: none;
      a{
        padding: 20px 0px;
      }
    }
  }
}
.desk_web{
  display: none;
}
@media only screen and (min-width: 1024px){
  .desk_web{
    display: block;
  }
  #checkout_button{
    background-color: #8f1b1d;
    padding: 14px;
    margin: 0px;
    width: 100%;
    font-weight: bold;
    font-size: 0.875rem;
  }
  .m_web{
    display: none;
  }
  #apply-coupon-block{
    .remove-coupon-container {
      padding: 5px;
    }
    .saved-amount {
      color: green;
      font-size: 13px;
      padding-top: 3px;
      }
  }
  #carts_block .item_block{
    padding: 16px;
    position: relative;
    background-color: #ffffff;
    border: 2px solid #f0f2f9;
  }
  .cart_right_side_bar {
    padding: 16px;
    position: relative;
    background-color: #ffffff;
    border: 2px solid #f0f2f9;
  }
}
div#totals_block {
  border-top: 1px solid #eee;
  padding: 10px 0 0;
  margin-top: 10px;
  .main-heading {
    font-size: 16px;
    font-weight: 500;
    padding: 0px 0 10px !important;
    text-align: center;
}
}

.cart-discount-message {
  border-top: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  margin: 15px 0 6px;
  h5 {
    font-size: 16px;
    font-weight: 500;
    padding: 0;
    margin-bottom: 0;
    color: #222222;
    font-family: "Inter", sans-serif !important;
  }
  .off-canvas-wrap{
    overflow: hidden !important;
  }
}
div#carts_block {
  padding: 60px 0;
  display: block;
  .improved_link {
    background: none !important;
    color: #670b19 !important;
}
  .empty-cart-head {
    color: #3f3333;
    font-weight: 500;
    font-size: 42px;
}
.empty-cart p {
  font-size: 18px;
  margin-bottom: 12px;
  font-weight: 500;
}
.add_cont_shop {
  background: none;
  color: #670b19;
  font-weight: bold;
  font-size: 1rem;
  background: #670b19;
  color: #fff;
  padding: 20px 40px;
  border-radius: 5px;
  position: relative;
  width: 100%;}
}
@media screen and (max-width: 767px)
{
  div#carts_block {
    padding: 0 0 80px;
    .empty-cart-head {
      font-size: 32px;
  }
}
.empty-cart p {
  font-size: 16px;
}
.add_cont_shop {
font-size: 0.69rem;
padding: 10px 20px;
font-weight: 500;
}
.off-canvas-wrap{
  overflow: hidden !important;
}
}
@import 'unbxd_recommendations_red'
