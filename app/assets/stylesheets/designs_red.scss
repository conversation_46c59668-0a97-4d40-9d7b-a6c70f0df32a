//importing variables
@import 'variables_red';
@import 'red_theme';
@import 'social-share';
@import 'lightslider';
@import 'default-skin';
@import 'photoswipe';
@import 'jquery.fancybox';
@import 'jquery.fancybox-buttons';
@import 'jquery.fancybox-thumbs';
body {
    font-family: "Inter", sans-serif !important;
}
body.modal-open{
  overflow: hidden;
  position: fixed;
}

.pb {
  padding-bottom: 5px;
}

.lSAction{
  a{
    opacity: 1 !important;
  }
}
.Mcustom-checkbox+label {
  padding-left: 26px !important;
  cursor: pointer;
  display: inline-block;
  line-height: 20px;
  -khtml-user-select: none;
  position: relative;
  height: 20px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.Mcustom-checkbox:checked,
.Mcustom-checkbox:not(:checked) {
  position: absolute;
  left: -9999px;
  opacity: 0
}
.Mcustom-checkbox:not(.filled-in)+label:after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 12px;
  height: 12px;
  z-index: 0;
  border: 2px solid #696969;
  border-radius: 1px;
  margin-top: 2px;
  transition: .2s;
  transform: scale(0)
}
.Mcustom-checkbox.filled-in+label:after {
  border-radius: 2px
}
.Mcustom-checkbox.filled-in+label:after,
.Mcustom-checkbox.filled-in+label:before {
  content: '';
  left: 0;
  position: absolute;
  transition: border .25s, background-color .25s, width .2s .1s, height .2s .1s, top .2s .1s, left .2s .1s;
  z-index: 1
}
.Mcustom-checkbox.filled-in:not(:checked)+label:before {
  width: 0;
  height: 0;
  border: 3px solid transparent;
  left: 6px;
  top: 10px;
  -webkit-transform: rotateZ(37deg);
  transform: rotateZ(37deg);
  -webkit-transform-origin: 20% 40%;
  transform-origin: 100% 100%
}
.Mcustom-checkbox.filled-in:not(:checked)+label:after {
  height: 14px;
  width: 14px;
  background-color: transparent;
  border: 1px solid #cecbcb;
  top: 3px;
  z-index: 0
}
.Mcustom-checkbox.filled-in:checked+label:before {
  top: 2px;
  left: -1px;
  width: 7px;
  height: 11px;
  border-top: 2px solid transparent;
  border-left: 2px solid transparent;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
  -webkit-transform: rotateZ(37deg);
  transform: rotateZ(37deg);
  -webkit-transform-origin: 100% 100%;
  transform-origin: 100% 100%
}
.Mcustom-checkbox.filled-in:checked+label:after {
  top: 3px;
  left: -1px;
  width: 14px;
  height: 14px;
  border: 2px solid #7b0e1d;
  background-color: #7b0e1d;
  z-index: 0
}
.orbit-prev{
  background-image: none !important;
  left: 5%;
  top: 45%;
  position: absolute;
  width: 30px;
  height: 30px;
  span{
    content: '';
    border-left: 2px solid $text_black;
    border-bottom: 2px solid $text_black;
    width: 20px;
    height: 20px;
    position: absolute;
    transform: rotate(45deg);
    top: 15%;
  }
}
.orbit-next{
  background-image: none !important;
  left: 90%;
  position: absolute;
  top: 45%;
  width: 30px;
  height: 30px;
  span{
    content: '';
    border-right: 2px solid $text_black;
    border-top: 2px solid $text_black;
    width: 20px;
    height: 20px;
    position: absolute;
    transform: rotate(45deg);
    top: 15%;
  }
}
.orbit-bullets-container{
  text-align: center;
  .orbit-bullets{
    float: none;
    margin: 0 auto 6px auto;
    overflow: hidden;
    position: relative;
    text-align: center;
    top: 5px;
    li{
      background: #CCCCCC;
      cursor: pointer;
      display: inline-block;
      float: none;
      height: 0.4rem;
      margin-right: 6px;
      width: 0.4rem;
      border-radius: 1000px;
    }
    .active{
      background: $dark_red;
    }
  }
}
.orbit-timer, .orbit-next, .orbit-prev, .orbit-bullets {
  display: block;
}
.pswp--touch .pswp__button--arrow--left, .pswp--touch .pswp__button--arrow--right{
  visibility: visible;
}
body {
  overflow: scroll;
  .designs_show.page{
    .off-canvas-wrap {
      #container{
        width: 96vw;
        overflow-y: hidden;
        padding: 0em;
        margin: 2%;
        overflow-x: hidden;
      }
    }
    .hidden{
      display: none;
    }
    .action_button_btn {
      text-align: center;
      margin-bottom: 15px;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .overflow_messages{
      text-align: center;
      padding: 3%;
      z-index: 100 !important;
    }
    #add_to_cart_message{
      padding: 10px;
      width: 100%;
      max-width: 300px;
      margin: auto;
      color: #670b19;
      left: 42%;
      position: absolute;
      bottom: 139px;
      border-radius: 6px;
      background: #f1f1f1;
      border: 1px solid #670b19;
      z-index: 9999999;
    }
    #action_buttons {
      transform: translate3d(0px,0px,0px);
      .cart_button {
        margin-bottom: 0;
        background-color: #0d997c;
        width: 100%;
        font-weight: 700;
      }
      .right-button{
        margin-bottom: 0;
        background-color: #670b19;
        width: 100%;
        padding: 16px 0;
        font-weight: 700;
        border-radius: 6px;
        font-size: 14px;
        margin-left: 10px;
      }
      .action_button_btn {
        padding-bottom: 0px;
      }
      .left-button{
        background-color: #fff9fa;
        color: #670b19;
        margin-bottom: 0;
        width: 100%;
        font-weight: 700;
        padding: 15px 0;
        border-radius: 6px;
        border: 1px solid;
        font-size: 14px;
      }
    }
    .heading_underline {
      text-decoration: underline;
    }
    #action_buttons, #secondary_action_buttons{
      &.fixed {
        bottom: 0;
        top: auto;
        .add_place_order {
          background-color: $dark_green;
        }
        .add_to_cart {
          background-color: black;
          color: $dark_green;
        }
        margin-bottom: 0;
        a, input[type='submit'] {
          margin-bottom: 0em;
          width: 100%;
          font-weight: bold;
          line-height: inherit;
        }
        li {
          padding-bottom: 0em;
          padding: 0em;
        }
      }
    }
    #secondary_action_buttons{
      margin: 3.7em 0;
    }
    .panel_block {
      background-color: $bluish_black;
      border: 0.1em solid $bluish_black;
      margin-top: 1em;
      border-radius: .5em;
      .panel_content {
        background-color: $snow_white;
        border: 1px solid black;
        border-radius: 6px;
      }

      .panel_heading {
        padding: 0em .2em;
        ul {
          margin: .2em auto;
        }
      }
    }
    .line_through_text{
      text-decoration: line-through;
    }
    .truncate {
      width: 100%;
      color: $text_black;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .no-bullet.shipping_desc {
      li {
        color: white;
      }
    }
    .close-icon{
      background-image: image-url('close-32.png');
      background-repeat: no-repeat;
      display: inline-block;
      width: 1em;
      height: 1em;
      vertical-align: middle;
      background-size: 1em;
      background-color: black;
      border-radius: 5em;
    }
    .bordered_block {
      padding: 1em;
      margin-bottom: 1em;
      box-shadow: 0 0 2em black;
      border: 0.1em solid $gray;
    }

    .opera_checkout_position_fix{
      width: 100%;
      margin-top: 18px;
      background-color: #0E9A7D !important;
    }

    .mirraw_cert_logo{
      display: inline;
      opacity:0.85;
    }

    .small_msg{
      font-size: 12px;
    }

    .wrap_total{
      white-space: nowrap;
    }

    .sign_in_button a{
      color: white;
      font-size: 0.825rem;
      text-transform: uppercase;
    }
  }

  #disclaimer-box{
    font-size: 13px;
    text-align: center;
    margint-top: 8px;
    font-style: italic;
  }
}

#dynamicSizeChartModal, #sizeChartModal{
}
.fixed_button{
  position: fixed;
  bottom: 0;
  z-index: 10;
  right: 0;
  left: 0;
  top: auto;
  // box-shadow: 0 -1px 15px 0 rgba(0,0,0,.2);
  // animation: smoothScroll 0.3s forwards;
  -webkit-transition: -webkit-transform 0.3s ease-out;
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out,
  -webkit-transform 0.3s ease-out;
  -webkit-transform: translateY(63px);
  transform: translateY(63px);
  background: $white;
  // background-image: linear-gradient(transparent, white);
  .action_button_btn{
    margin: 0.375rem;
  }
}
// @keyframes smoothScroll {
//   0% {
//     transform: translateY(20px);
//   }
//   100% {
//     transform: translateY(0px);
//   }
// }
#design_image_block {
  #design_image_div li{
    padding: 0;
    color: #3C4345CC;
    .small-centered {
      padding: 0 10px 0px;
    }
  }
  #stitching_testimonials{
    width: 100%;
    border-top: 4px solid $red_background;
    .testimonial-header{
      text-align: center;
      letter-spacing: 1px;
      font-size: $big_font;
    }
    ul{
      margin-top: 1em;
      text-align: center;
      height: 3.65em !important;
      li{
        color: $red_background;
        text-align: center;
      }
    }
  }
  .wishlist-forms {
    position: relative;
    .wishlist-heart-button {
      font-size: 1.9rem;
      margin: 0;
      padding: 0;
      float: right;
      background: transparent;
      color: #8f1b1d;
      position: absolute;
      right: 0px;
      top: 0px;
      &:focus {
        outline: none;
      }
    }
    .wishlist-heart-button.empty-heart {
      color: gray;
    }
  }
  .designer-panel{
    text-align: center;
    margin-bottom: 14px;
    .rating-box{
      padding: 0.5rem 0 0 1.5rem;
      .small_rating{
        font-size: 12px;
        background-color: #1E7E34;
        color: white;
        padding: 5px;
        font-weight: bold;
        &.green-rating {
          background-color: #16be48;
          color: white;
        }
        &.red-rating {
          background-color: #FF5722;
        }
        &.orange-rating {
          background-color: #FFA000;
        }
      }
    }
    .designer-name{
      a{
        text-decoration: underline;
        text-transform: uppercase;
        font-size: $big_font;
        color: $text_black;
      }
      .small_rating{
        font-size: 12px;
        background-color: #8f1c1e;
        color: white;
        padding: 2px;
        font-weight: bold;
        margin-left: 5px;
        &.green-rating {
          background-color: #16be48;
        }
        &.red-rating {
          background-color: #FF5722;
        }
        &.orange-rating {
          background-color: #FFA000;
        }
      }
    }
    .designer-sub-panel{
      margin-bottom:10px;
      font-size: 14px;
      color: $text_light_gray;
      padding: 0.5rem 0 0 1.8rem;
    }
  }
  .tap_text{
    text-align: center;
    small{
      color: $text_light_gray;
    }
  }
  #design_images {
    margin: 0px;
    text-align: center;
    li{
      text-align: center;
      justify-content: center;
    }
    img {
      margin: auto;
    }
  }
  .th{
    border: none !important;
  }
  .lSGallery{
    margin-left: auto;
    margin-right: auto;
    border-radius: 2px;
  }
  ul.lSPager{
    li{
      margin: 0 2px;
      a{
        background-color: #969696;
        height: 6px;
        width: 6px;
        z-index: 9;
      }
      &.active{
        a{
          background-color: $dark_red;
        }
      }
    }
  }
  #design_addons {
    padding: 0px 3px 5px 8px;
    .hsw-block{
      margin-left: -3px;
      font-size: 1em;
      font-weight: 600;
      padding: 0 0 10px;
      color: #3C4345;
      a{
        border-radius: 50%;
        background-color: #fff;
        padding: 1px 6px;
        font-size: 14px;
        font-weight: 700;
        cursor: pointer;
        color: #670b19;
        border: 2px solid;
      }
    }
    select {
      padding: 0.5em;
      color: $text_light_gray;
    }
    .addon_option_values {
      border: none;
      margin: 0px 0px 10px 10px;
      .atov-name{
        font-weight: 700;
        font-size: 14px;
      }
    }
    .stitching_note{
      font-size: 13px;
      color: $text_light_gray;
      border-radius: 6px;
    }
    #salwar_kameez_default,#salwar_kameez_specific{
      text-align: justify;
      line-height: 1.2em;
      font-size: 13px;
      font-style: italic;
      display: block;
      margin-bottom: 10px;
      padding-left: 10px;
      padding-right: 10px;
      color: #b11f2c;
    }
  }
  .container_addons {
    display: block;
    position: relative;
  }
  .container_addons input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
  }
  .checkmark {
    display: inline-block;
    text-align: center;
    height: 25px;
    width: 25px;
    border-radius: 50%;
  }
  .checkmark:after {
    content: "";
    position: absolute;
    display: none;
  }
  .container_addons input:checked ~ .checkmark:after {
    display: block;
  }
  .fabric_color_selected {
    box-shadow: 0 0 0 4px #fff, 0 0 0 6px #8F1B1D;
  }
  .fabric_color_table {
    padding-bottom: 10px;
    padding-top: 10px;
    justify-content: left;
    margin-bottom: 0px;
    tr {
      background:none;
    }
    background: none;
    border: none;
    label {
      justify-content: center;
    }
    td {
      width: 60px;
      text-align: center;
      padding: 0px !important;
    }
  }

  .regular_plus_options, .custom_hide_option_type, .plus_size_blouse_custom {
    display: none;
  }
  .plus_size_custom_regular, .plus_size_custom {
    padding: 8px 5px 8px 5px;
    margin-left: 20px;
    font-size:13px;
  }
  .plus_size_blouse_regular {
    font-weight: 500;
    margin-left: 16px;
    margin-top: 2px;
    float: left;
    cursor: pointer;
    display: inline-table;
    background: transparent;
    border: 1px solid #dadada;
    border-radius: 10px;
    width: 100%;
    padding: 10px 0;
    max-width: 80px;
    color: #2b2b2b;
    font-size: 14px;
    line-height: normal;
    text-align: center;
  }
  .review-container .tabbed-view .review-row .block .view-more-btn {
    color: #670b19;
}
  .selected_custom_plus_size {
    background-color: #F1E8E9;
    color: #B10D28;
    height: 40px;
  }
  .custom-size-box{
    display: flex !important;
    justify-content: center;
    align-items: center;
    max-width: 180px;
    text-align: center;
    border-radius: 10px;
    margin: 0;
    border: 1px solid #E4E4E4;
    font-weight: 600;
    font-size: 13px;
    width: 100%;
  }
  .other_custom_plus_size {
    background-color: #fff;
    color: #3C4345;
  }
  .custom_select_size {
    text-align: left;
    margin-bottom: 10px;
  }
  .standard_option_values {
    padding-left: 10px;
    padding-right: 10px;
  }
  .plus_size_fabric_color {
    height: 15px;
    width: 15px;
    background-color: red;
    border-radius: 50%;
    display: inline-block;
  }
  .plus_size_regular_btn {
    background-color: $variant_background;
  }
  .plus_size_info {
    padding-top: 5px;
  }
  .plus_size_info_i {
    margin-left: 10px;
  }
  .plus_size_btn_info{
    display: flex;
  }
  .plus_size_custom_regular_align {
    padding-right: 0px;
  }
  .plus_size_additional_price {
    font-size: 14px;
  }
  .plus_size_buttons {
    padding-top: 5px;
    padding-bottom: 5px;
  }
  .product_price_wo_discount, .actual-price{
    text-decoration: line-through;
    padding: 0 2% 0 0;
    color: rgba(48, 48, 48, 0.76);
    font-size: 1.1em;
  }
  .available-offers-on-pdp{
    color: #3C4345;
    border-radius: 2px 2px 0px 0px;
    padding: 0px 0;
    margin-bottom: 5px;
    font-size: 1em;

  }
  .offer-message{
    padding-bottom: 2%;
  }
  .product_discount_price, .discounted-price,.price_label_div {
    font-size: 1.1em;
    color: #303030;
    font-weight: 600;
    padding: 0;
    padding-right: 2%;
  }
  .product_discount_div{
    display: flex;
  }
  .changeInPriceNote{
    display: none;
    position: absolute;
    span{
      background: #3ebe49;
      font-size: 12px;
      margin-left: -10px;
      padding: 0px 7px 0px 7px;
      border-radius: 4px;
      font-weight: bold;
      color: white;
      z-index: 10;
    }
    .arrow{
      background-color: #3ebe49 !important;
      width: 10px;
      height: 10px;
      left: 45%;
      top: -5px;
      transform: rotate(45deg);
      position: relative;
      display: inline-flex;
    }
  }
  .product_discount_percent {
    font-size: 0.9em;
    border-radius: 1em;
    width: 21%;
    padding: 0.7em;
    text-align: center;
  }
  
  .line_through_text {
    text-decoration: line-through;
    margin-top: 0.1em;
    font-size: 18px;
  }
  #design_price_block {
    border-top: 2px solid black;
    border-bottom: 2px solid black;
    margin-top: 1em;
    li {
      padding-bottom: 0em;
    }
  }
  #variants_block {
    padding: 4px 3px;
    margin-bottom: 16px;
    .var-title{
      padding-left: 4px;
      font-weight: 700;
    }
    .size-chart-text{
      font-size: 13px;
      margin-left: 12px;
    }
    a {
      padding: 0.625em 1.25em;
      margin: 0.3em 0.3em 1.6em 0.3em;;
      box-shadow: $variant_box_shadow;
      font-weight: 700;
      &.selected,&:focus,&:hover{
        background-color: $variant_background;
        .variant-qty-left-msg{
          display: block;
          visibility: visible;
          opacity: 1;
        }
      }
    }
    .variant{
      font-size: $font_size;
    }
  }
  .variant-qty-left-msg{
    visibility: hidden;
    transition: visibility 0s, opacity 0.5s linear;
    opacity: 0;
    position: absolute;
    color: $dark_red;
    top: 32px;
    left: 0;
    right: 0;
    font-size: $font_size;
  }
  .deal_ends{
    margin-bottom: 5px;
    font-size: 18px;
    border-radius: 0px;
    padding: 4px 2px;
    .countdown{
      text-align: center;
      .deal_text{
        display: inline-block;
        color: rgba(48,48,48,0.8);
        vertical-align: bottom;
        font-weight: bold;
        text-align: left;
      }
      .clock{
        text-align: right;
        color: rgba(48,48,48,0.8);
        font-weight: bold;
      }
    }
  }
  #line_items_count {
    display: none;
    color: #000000;
    text-align: center;
    padding: 2px;
    font-size: 14px;
    margin: 0.375rem 0;
    // .line_item_close{
    //   float: right;
    //   text-align: right;
    //   position: relative;
    //   left: 25px;
    //   bottom: 25px;
    //   padding: 0px 3px;
    //   cursor: pointer;
    //   border-radius: 50%;
    //   background-color: #a9a9a9;
    // }
    .line_items_count_text{
      span{
        color: $dark_red;
        text-transform: uppercase;
        font-weight: 700;
      }
    }
  }
  #add_review_button{
    button#add_review{
      margin-bottom: 0;
    }
  }
  .design-addon-products{
    padding: 0 0.4rem;
    .addon-product-header{
      padding: 0.4rem 0;
    }
    .addon-product{
      padding: 10px 0;
      border-top: $border_black;
      .addon-product-detail{
        img{
          border: 1px solid #c1bbbb;
        }
      }
      .columns{
        label{
          margin-left: 0;
          span{
            font-size: $font_size;
            margin: 0.3em;
          }
        }
        .addon_product{
          outline: none;
          appearance: none;
          -webkit-appearance: none;
          width: 22px;
          height: 22px;
          border: 2px solid grey;
          border-radius: 2px;
          margin: 0.3em;
          &:not(:checked){
            &:after{
              content: "+";
              color: grey;
              padding: 0 0.25rem;
              font-size: 1rem;
            }
          }
          &:checked{
            border: 2px solid $dark_red;
            background: $dark_red;
            &:after{
              content: "";
              top: 0.6em;
              left: 0.6em;
              width: 7px;
              height: 11px;
              border-top: 2px solid transparent;
              border-left: 2px solid transparent;
              border-right: 2px solid #fff;
              border-bottom: 2px solid #fff;
              -webkit-transform: rotateZ(37deg);
              transform: rotateZ(37deg);
              -webkit-transform-origin: 100% 100%;
              transform-origin: 100% 100%;
              position: absolute;
            }
          }
        }
      }
      .addon-details{
        padding-left: 0.9375rem;
        .product-title{
          color: $text_black;
          width: 90%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .product_design_price{
          margin: 0;
        }
      }
    }
  }


}

  @media only screen and (min-width: 768px){
      #design_image_div {
        display: flex;

      .design-content-container {
        padding: 20px !important;
        width: 40% !important;
        flex: 1;
        padding: 20px;
        box-sizing: border-box;
        overflow-y: auto; /* Enable scrolling for this section */
    
      }

      .design-image-container{
        box-sizing: border-box;
        width: 60% !important;
        // flex: 0 0 50%;
        // background-color: #f0f0f0;
        padding: 20px;
        align-self: flex-start;
        position: -webkit-sticky;
        position: sticky;
        box-sizing: border-box;
        top: 100px;
      }
    }

    .container{
      overflow: visible;
    }
    div#container{
      overflow: visible;
    }
    section#coupon-main {
      overflow: visible;
    }
    .off-canvas-wrap {
      overflow: visible;
    }
    .off-canvas-wrap #main-section {
      overflow: visible;
   }


  body .designs_show.page .off-canvas-wrap #container {
    overflow: visible;
  }
    
  }

.plus_size_info_modal {
  &.reveal-modal{
    height: 50%;
    min-height: 50vh;
    width: 80%;
    left: 10%;
    top: 25%;
  }
}
.animate{
  -webkit-transition: -webkit-transform .5s ease-out;
  transition: -webkit-transform .5s ease-out;
  transition: transform .5s ease-out;
  transition: transform .5s ease-out,-webkit-transform .5s ease-out;
}
#write_review{
  #rating-alert-message, #review-alert-message, #save-alert-message{
    display: none;
    color: #f54e1a;
    font-size: 14px;
  }
  .alert-border{
    border: 2px solid #f54e1a;
  }
  #form-rating-star{
    img{
      width: 20px;
    }
  }
  #review-text{
    background-color: #ffffff;
    color: #000000;
  }
}
.percent_disc {
  font-size:  1em;
  color: rgb(38, 165, 65);
  padding: 0 1%;
  margin-bottom: 0;
}
#designable_details {
  .tabs {
    .tab-title > a {
      padding: 0.5em 1em;
      margin: .03em;
      background-color: inherit;
      color: inherit;
      border: .1em solid;
    }
    .tab-title.active > a {
      background-color: #FFFFFF;
      color: #222222;
    }
  }
  ul.no-bullet {
    label b {
      text-decoration: underline;
    }
    label {
      color: white;
    }
  }
  table {
    background: inherit;
    border: none;
    padding: 0 10px;
    tr {
      &:nth-of-type(even) {
        background: inherit;
      }
      td {
        color: #3C4345CC;
        vertical-align: baseline;
      }
    }
  }
}

.padding-6{
  padding: 6px 0;
}

.border-left-yellow {
  padding: 10px 0;
  font-size: 15px !important;
  color: #55A630 !important;
  padding: 15px 0;
  margin: 0 !important;
  font-size: 1em;
}

.listing_panel_block {
  color: $text_black;
  li, label{
    cursor: auto;
  }
  .variant-price-text{
    font-size: 12px;

  }

  p{
    text-align: left;
    color: #3C4345;
    border-radius: 2px 2px 0px 0px;
    padding: 10px;
    margin-bottom: 0;
    font-size: 1em;
    font-weight: 600;
  }

  ul{
    font-size: $cart_font;
    padding: 4px;
    color: #3C4345CC;
    margin-bottom: 0;
    list-style: none;
  }
  
  td{
    vertical-align: baseline;
  }
  #cod_available{
    font-size: $big_font;
    color: black;
  }
  .delivery_block {
    font-size: 1em;
    font-weight: normal;
    padding-top: 0%;
    background-color: #fff;
    color: #2C6B1F;
    .notice {
      color: rgb(103, 11, 25);
      font-size:12px;
      line-height:15px;
      font-style:italic
    }
    del.strike_old_date {
      color: #606060;
    }
  }
  .sahre-earn{
    font-size: $big_font;
    text-decoration: underline;
  }
  .whatsapp-icon{
    width: 2.5em;
    border-left: 2px solid;
    padding-left: 0.75rem;
  }
}
.accordion{
  table, ul.sub-specs-table {
    display:none;
  }
  
  table.sub-specs-line{
    display: table;
  }
  a, .sub-grp{
    outline: none;
  }  
  .expand{
    display: none;
    float: right;
  }
  .collapse{
    float: right;
  }
  .spec-collapse{
    display: block;
  }
  .sub-grp{
    p{
      font-size: 0.9rem;
      margin-left: 0.6rem;
      .button-icon{
        transition: all .5s ease-in-out;
        display: inline-block;
        font-weight: 700;
        font-size: 1.1rem;
        line-height: 1.8;
      }
      .button-toggle{
        transform: rotate(90deg);
      }
    }
  }
  table.sub-specs-table, .sub-specs-line{
    margin: 0 0 0.5rem 0.6rem;
    td.spec{
      padding: 0.2rem 0.4rem;
    }
    td.spec.first{
      padding-right: 0px;
    }
  }
  table.sub-specs-table{
    padding-left: 0.7rem;
  }
}
  
#designable_details ul{
 margin-left:0;
} 

.pre-order{
  padding: 7px 20px;
  #pre-order-check{
    vertical-align: middle;
    margin: 0px -2px 0px 0px;
  }
  label{
    color: $text_white;
  }
}
//Image Modal box
#myImg {
  cursor: pointer;
  transition: 0.3s;
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: $body_white;
}

/* Modal Content (Image) */
.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
}

#loadingImage{
  position: fixed;
  left: 45%;
  top: 30%;
  border: 4px dotted $dark_red;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}

/* Add Animation - Zoom in the Modal */
.modal-content { 
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}

@keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}
/* shake effect */
.shake-effect {
  -webkit-animation-duration: 0.75s; 
  animation-duration: 0.75s; 
  -webkit-animation-fill-mode: none; 
  animation-fill-mode: none;
  -webkit-animation-name: shake; 
  animation-name: shake; 
  }
 @keyframes shake { 
    0%, 100% {transform: translateX(0);color: #ed8f03;} 
    20%, 60% {transform: translateX(-5px);color: #ed8f03;} 
    40%, 80% {transform: translateX(5px);color: #ed8f03;} 
  }
@-webkit-keyframes shake {
  0%, 100% {-webkit-transform: translateX(0);color: #ed8f03;} 
  20%, 60% {-webkit-transform: translateX(-5px);color: #ed8f03;} 
  40%, 80% {-webkit-transform: translateX(5px);color: #ed8f03;} 
}

/* The Close Button */
.close {
  font-size: 2em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
  display: block;
  text-align: right;
}

.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}


/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px){
  .modal-content {
    width: 100%;
  }
}

@media only screen and (min-width: 1200px){
  .modal-content {
    width: auto;
    height: auto;
  }
}

#branch-banner-iframe{
  z-index: 1 !important;
}

.notice_class {
  color:orange;
  font-size:14px;
  line-height:15px;
}

.product_design_price{
  margin: 0;
  .flash-icon{
    background-image: image-url('flash_icon.png');
    background-repeat: no-repeat;
    height: 1.2rem;
    background-size: 1.2em;
    width: 1.2rem;
    float: left;
    margin-top: 1px;
  }
}

.font_greyed{
  color: #55A630;
  &.delivery_day{
    font-size: $big_font;
    font-weight: normal;
    color: #2C6B1F !important;
  }
}
.rating_message{
  font-size: 14px;
  padding: 0 4px;
  a {
    border-bottom: none;
    color: #fff;
    background: #670b19;
    padding: 10px 6px;
    border-radius: 50px;
    font-size: 11px;
  }
}
.reviews_ratings{
  display: inline;
  .small_rating{
    font-size: 12px;
    background-color: #1E4620; 
    border-radius: 30px;
    color: white;
    padding: 6px;
    font-weight: bold;
    margin-bottom: 1%;
    display: inline-block;
    a {
      color: white; 
      padding-bottom: 3px;
    }
  }
  .green-rating{
    background-color: #1E7E34;
  }
  .red-rating{
    background-color: #FF5722;
  }
  .orange-rating{
    background-color: #FFA000;
  }

  .mirraw-recommended {
    display: inline-block;

    position: relative;

    &::before, &::after {
      content: '';

      position: absolute;
      left: -2px;
      height: 18px;
      width: 2px;

    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    & > img {
      height: 54px;
    }
  }
}
.review-opinion{
  font-size: 12px;
  font-weight: 600;
  margin-top: 5px;
}
.size-text{
  margin: 8px 0px 8px 16px;
  color: #303030;
  font-weight: 700;
  font-size: 14px;
}
button.btn-view-size {
  background: transparent;
  color: $light_red;
  padding: 4px 2px;
  font-size: 13px;
  margin: 0px;
  font-family: "Inter", sans-serif;
}
button.btn-view-size:hover {
  background: #009688;
  border: 1px solid #088074;
  color: #1c1b1c;
}
.size-chart-div {
  margin-bottom: 30px;
  margin: 0px 0px 10px 10px;
  padding: 0px;
  height: auto;
  .size-chart {
    margin: 8px 0px 8px 16px;
    a.size {
      float: left;
      cursor: pointer;
      display: inline-table;
      background: transparent;
      border: 1px solid #dadada;
      border-radius: 10px;
      width: 100%;
      padding: 10px 0;
      max-width: 55px;
      color: #2b2b2b;
      font-size: 14px;
      margin-right: 16px;
    }
    a.size.selected,
    a.size:hover {
      background-color: #F1E8E9;
      color: #B10D28;
      font-weight: bold;
    }
  }
  .show_rts_sizes {
    margin: -10px 0px 8px 12px;
  }
}
span.size-left,
span.size-right {
  position: absolute;
  padding: 4px 8px;
  font-size: 18px;
  font-weight: 700;
  background-color: #d2d2d2;
  top: 38px;
  color: #2d2b2b;
  z-index: 2;
  cursor: pointer;
  border-radius: 50%;
}
span.size-left {
  left: -2px;
  display: none;
}
span.size-right {
  right: -28px;
  display: block;
}
span.size-left:hover,
span.size-right:hover {
  background-color: #afafaf;
}

  #modal-size-chart{
    -webkit-overflow-scrolling: touch;
    height: 100%;
    .btn_close_top{
      float: right;
      padding: 0px 7px;
      margin: 10px 4px 0px 0px;
      font-size: 24px;
      border-radius: 50%;
    }
    .btn_close{
      padding: 8px 30px;
      margin: 20px 0px 80px;
      font-size: 20px;
      border-radius: 5px;
    }
    .modal-sm-size{
      .modal-content{
        width: 900px;
        .modal-header{
          h4{
            color: #670b19;
            text-align: justify;
            font-weight: 600;
          }
        }
        .modal-body{
          .radio-buttons{
            margin: 10px 0px;
            .size-label{
              color: $text_black;
              text-align: center;
            }
          }
        }
      }
    }
    .head_style{
      text-align: center;
      padding: 3px;
      font-size: 12px;
    }
  }
.stitching_offer{
  color: #EE9209;
  font-size: 15px;
  margin-top: 10px
}

.design_offer_label, .loyalty_label, .fd_message{
  display: flex;
  color: #fff;
  vertical-align: middle;
  padding: 0;
  width: 100%;
  font-size: 14px;
  .b1g1_tnc, .loyalty_tnc, .qpm_tnc, .fd_tnc{
    font-size: $big_font;
  }
}
// stitching conformation popup modal css
#stitchingModal{
  background-color:rgba(146, 140, 140, 0.84);
  height: 100%;
  overflow: auto;
  .modal-dialog{
    margin-top: 85px;
  }
  .modal-header{
    font-size: 13px;
    display: inline-flex;
    background-color: $menu_background;
    width:100%;
    #stitching-confirmation-message{
      padding: 11px;
      color: $text_white;
    }
    a{
      padding-left: 45%;
    }
  }
  .modal-content{
    width: 288px;
    height: 100%;
    background-color: #191919;
    color: $text_white;
    font-size: 1.25em;
  }
  .closeStitchingModal{
    color: white;
    font-size: 1.8em;
    padding: 1% 3% 1% 5%;
    right: 0.3em;
  }
  .modal-footer{
    text-align: center;
    padding: 2% 0 2%;
    .btn{
      padding: 7px !important;
      width: 46%;
      font-size: 12px !important;
      border-radius: 2px;
    }
    .stitch{
      background: $dark_green;
    }
    #add-cart{
      background: $text_white;
      color: $menu_background !important;
    }
  }
  .modal-body{
    text-align: center;
    padding:20px;
    font-size: 14px;
    .modal-message{
      padding-top:20px;
    }
  }
}
// bmgn loyalty rts flash deals and qpm tnc modal
.tnc-in-detail{
  display: none;
  background-color: transparent;
  height: 80%;
  overflow: hidden;
  width: 90%;
  margin: 10% 5%;
  outline: none;
  @media only screen and (min-width: 767px)   {
    width: 65%;
    margin: 10% 18%;
    height: 65%;
  }
  .modal-dialog{
    .modal-content{
      height: 100%;
      width: 100%;
      position: absolute;
      .modal-header{
        float: right;
        position: inherit;
        right: 5px;
        @media only screen and (min-width: 767px)   {
          right: 15px;
        }
      }
      .modal-body{
        padding: 16px;
        font-size: 12px;
        overflow: auto;
        height: 100%;
        background: white;
        padding-bottom: 40px;
        @media only screen and (min-width: 767px)   {
          margin-top: 0rem;
        }
        .modal-text{
          .qsn{
            font-weight: bold;
          }
          .ans{
            margin-bottom: 20px;
          }
          ul{
            font-size: 12px;
          }
        }
      }
    }
    .modal-footer{
      text-align: center;
      background: $red_background;
      padding: 15px 0px;
      position: inherit;
      width: 100%;
      bottom: 0;
      box-shadow: 2px -4px 20px 0px grey;
      color: $text_white !important;
      font-weight: bold;
      @media only screen and (min-width: 1088px)   {
        display: none;
        margin-left: 160px;
      }
    }
  }
}

.share-n-earn-modal{
  display: none;
  background-color: transparent;
  height: 65%;
  overflow: hidden;
  width: 90%;
  margin: 35% 5%;
  outline: none;
  .modal-dialog{
    .modal-content{
      height: 100%;
      position: absolute;
      .modal-header{
        float: right;
        position: inherit;
        right: 5px;
      }
      .modal-body{
        padding: 16px;
        font-size: 12px;
        overflow: auto;
        position: inherit;
        height: 100%;
        background: white;
        padding-bottom: 40px;
        .modal-text{
          ol {
            counter-reset:item; 
            margin:0; 
            padding-left:0; 
          }
          ol>li {
            counter-increment:item; 
            list-style:none inside; 
            margin: 20px 0;
            overflow: hidden;
            font-size: 13px !important;
            font-weight: bold;
            line-height: 1.3;
          }
          ol>li:before {
              content:counter(item) ;
              margin-right: 20px;
              padding: 7px;
              display: block;
              border-radius: 50%;
              width: 26px;
              height: 26px;
              background: #8f1b1d;
              color: #fff;
              text-align: center; 
              font-size: 13px ;
              font-weight: 80;
              float: left;
          }
        }
        .share-n-earn-tnc  {
          text-align: center;
          text-decoration: underline;
          margin-bottom: 15px;
        }
      }
    }
    .modal-footer{
      text-align: center;
      background: $red_background;
      padding: 15px 0px;
      position: inherit;
      width: 100%;
      bottom: 0;
      box-shadow: 2px -4px 20px 0px grey;
      color: $text_white !important;
      font-weight: bold;
    }
  }
}

.share-n-earn-block {
  padding-top: 4px;
  margin-bottom: 8px;
  color: black;
  .share-n-earn-title {
    font-size: 12px;
    font-weight: bold;
  }
  .share-n-earn-image {
    width: 22%;
  }
  .share-n-earn-body {
    height: 100%;
    width: 40%;
    font-weight: normal;
    margin-top: 4px;
    padding-top: 0px;
    padding-left: 3px;
    padding-right: 6px;
    line-height: 1.3;
    font-size: 11px;
  }
  .share-n-earn-button {
    width: 120px;
    height: 35px;
    padding: 0px;
    font-size: 11px;
    font-weight: bold;
    border-radius: 4px;
    border: 2px solid;
    margin-top: 4px;
    margin-right: 3px;
    .share-btn-text>img{
      width: 26px;
      padding-left: 2px;
    }
    .share-btn-text{
      text-align: center;
      padding: 4px 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

#ready_to_ship {
  background: image-url('rts_logo.png') no-repeat left top;
  display: inline-block;
  width: 107px;
  height: 20px;
  margin-left: 16px;
  background-size: 105px;
}

.price_match_guarantee {
  .price_match_description {
    display: none;
    background: white;
    color: black;
    padding: 10px;
    z-index: 1;
    position: absolute;
    margin-top: 10px;
  }
}

#design_images_zoom {
  height: 100% !important;
}

#myModal {
  height: 100%;
  @mixin button-color {
    background-color: white;
    border-radius: 25px;
    width: 40px;
    height: 40px;
    opacity: 0.6;
    background-image: none;
  }
  @mixin arrow-style {
    content: '';
    border-right: 5px solid #a99b9b;
    border-top: 5px solid #a99b9b;
    width: 15px;
    height: 15px;
    position: absolute;
  }
  .lSAction > .lSNext {
    @include button-color;
    &:before {
      @include arrow-style;
      transform: rotate(45deg);
      bottom: 13px;
      left: 12px;
    }
  }
  .lSAction > .lSPrev {
    @include button-color;
    &:before {
      @include arrow-style;
      transform: rotate(-135deg);
      bottom: 13px;
      left: 14px;
    }
  }
}

.design_wrap{
  position: relative;
  display: inline-block;
}

.label_for_image_box {
  position: absolute;
  display: none;
  top: 0;
  right: 0px;
  height: 125px;
  overflow: hidden;
  z-index: 1;
  width: 128px;
}
@media screen and (max-width:1024px) {
  .label_for_image_box{
    width: 145px;
  }
}

.label_text {
  display: none;
  top: 32px;
  width: 170px;
  padding: 5px 29px 5px;
  font-size: 15px;
  text-align: center;
  margin-left: -24px;
  color: #fff;
  z-index: 1;
  background-color: #c51134;
  position: absolute;
  left: 19px;
  -webkit-transform: rotate(45deg) translate3d(0, 0, 0);
  -moz-transform: rotate(45deg) translate3d(0, 0, 0);
  -ms-transform: rotate(45deg) translate3d(0, 0, 0);
  transform: rotate(45deg);
}

#standard_height_notice{
  background-color: #ffffff;
  position: absolute;
  border-radius: 5px;
  padding: 3px;
  z-index: 1;
  right: 0px;
  top: 111%;
}

#standard_height_notice:after{
  content: '';
  width: 0px;
  height: 0px;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-top:  10px solid transparent;
  border-bottom:  10px solid #ffffff;
  position: absolute;
  top: 0%;
  right: 7%;
  margin-top: -20px;
}

.info_message{
  display: none;
  padding: 10px;
  font-size: 15px !important;
  color: #55A630 !important;
  text-align: justify;
  font-size: 16px;
  background: #55A63026 !important;
  border-radius: 6px;
  margin-bottom: 0;
}
.plus_size_message{
  display: none;
}
.highlight{
  background: #b7b7b7 !important;
}

.select-variant{
  .variant-bg{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,.7);
    z-index: 99;
    opacity: 0;
    -webkit-transition: opacity .3s cubic-bezier(0,0,.3,1);
    transition: opacity .3s cubic-bezier(0,0,.3,1);
    pointer-events: none;
  }
  .variant_selection{
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 99;
    -webkit-transform: translate3d(0,120%,0);
    transform: translate3d(0,120%,0);
    will-change: transform;
    .content{
      margin: 0 1em;
      .variant-header{
        padding: 1em 0;
        border-bottom: $border_black;
        margin-bottom: 1em;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .close-variant-select{
          font-size: 2rem;
          float: right;
          line-height: 1;
          color: maroon;
          font-weight: 600;
          padding: 10px 30px;
          cursor: pointer;
        }
      }
      .variant-body{
        .columns{
          padding-left: 0.9375rem;
          padding-right: 0.9375rem;
        }
        .addon-details{
          .product-title{
            color: $text_black;
            width: 90%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .product_design_price{
            margin: 0;
          }
          .discount_details{
            .left{
              margin-left: 3px;
            }
          }
        }
        .text{
          .alert{
            background: $red_background;
          }
        }
      }
      .select-addon-variant{
        text-align: center;
        border: 1px solid #d4d4d4;
        padding: 0.6em;
        text-transform: uppercase;
        width: 100%;
        max-width: 220px;
        background: #670b19;
        color: #fff;
        border-radius: 6px;
      }
    }
  }
}
.design-breadcrumb {
  display: flex;
  padding-left: 10px;

  @media only screen
  and (min-width: 500px)
  and (max-width: 1040px) {
    margin-top: 15px;
  }
  ul{
    margin-bottom: 0px;
    margin-left: 0px;
  }
  li:not(:first-child):before {
    content: '/';
    margin-left: 0px;
    margin-right: 0px;
  }
  a {
    font-size: 12px !important;
    color: $text_black;
  }
  li {
    font-size: 12px !important;
    display:inline-block;
  }
  .final{
    font-weight: 500;
  }
}
#product_video_zoom{
  position : relative ;
  top : 50% ;
  transform : translateY(-51%) ;
  width : 100% ;
  max-height: 100% ;
}
@import 'unbxd_recommendations_red';
@import 'reviews_red';

#design_image_block input[type="text"]{height:25px;font: 15px, Arial, sans-serif; color: #333; letter-spacing: 1px; border-bottom: 0px; margin-left: 10px;}
@media only screen and (min-width: 768px){
  #design_image_block input[type="text"]{
    height:auto;
  }
}
.effect-1{height:35px ;background-color : white; display: inline-flex;border: 0; padding: 0px; border-bottom: 1px solid #ccc;}
.effect-1 ~ .focus-border{position: absolute; bottom: 0; left: 0; width: 0; height: 0; background-color: #3399FF; transition: 0.4s;}

#design_group{
  .design-group-by-color{
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 18%;
    column-gap: 0.2rem;
    overflow-x: auto;
    overscroll-behavior-inline: contain;
    .sibling-design-container{
      display: grid;
      grid-template-rows: min-content;
      justify-content: center;
      border: 1px solid #f1f1f1;
      padding: 10px 0 0;
    }
    .sibling-design{
      box-shadow: 0 0px 0px 0 transparent;
      overflow: hidden;
      cursor: pointer;
      text-align: center;
      max-width: 60px;
      border: 1px solid #eee;
      display: inline-block;
      &:hover {
        border: 1px solid black;
    }
    }
    .sibling-design img{
      display: block;
      width: 100%;
      height: 60px
      // clip-path: circle(50%);

    }
    .sibling-design-color-title{
      font-size: 12px;
      padding: 3px;
    }
  }
}
.space-top {
   margin-top: 10px;
}

#more_like_category{
  max-width: 76rem;
  margin: auto;
  .title-block{
    display: flex;
    justify-content: space-between;
    .view_all{
      color:#b11f2b !important;
      font-weight: bolder;
      font-size: small;
      min-width: 50px;
    }
  }
  .item-container{
    display: grid;
    .list-item{
      display: grid;
      padding-bottom: 1.2rem;
      grid-template-rows: min-content;
      text-align: center;
      grid-auto-flow: column;
      grid-auto-columns: 45%;
      column-gap: 1.6rem;
      overflow-x: auto;
      .product-container{
        padding: 0;
        box-shadow: 0 3px 9px 0 rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 10px 11px 5px 11px;
        width: 100%;
        height: auto;
        align-self: center;
        .price-tag{
          display: flex;
          text-align: center;
          justify-content: center;
          .product_price_wo_discount{
            text-decoration: line-through;
            padding: 0 2% 0 0;
            color: #bbb4b4c2;
            font-size: 1.1em;
          }
          .details_block{
            font-size: 0.7em; 
            padding-left: 2%;
            
          }
          .discount_price{
            font-size: 0.8em;
            color: $text_black;
            padding-left: 2%;
            font-weight: bolder;
            padding: 0;
          }
        }
        
      }
    }
  }

}

#unbxd_rec{
  max-width: 60rem;
  margin: auto;
  .title-block{
    display: flex;
    justify-content: space-between;
    .view_all{
      color:#b11f2b !important;
      font-weight: bolder;
      font-size: small;
      min-width: 50px;
    }
  }

  .item-container{
    display: grid;
    .list-item{
      display: grid;
      padding-bottom: 1.2rem;
      grid-template-rows: min-content;
      text-align: center;
      grid-auto-flow: column;
      grid-auto-columns: 45%;
      column-gap: 1.6rem;
      overflow-x: auto;
      
      @media only screen and (min-width: 368px){
        grid-auto-columns: 52%;
      }
      @media only screen and (min-width: 468px){
        grid-auto-columns: 38%;
      }
      @media only screen and (min-width: 568px){
          grid-auto-columns: 20%;
      }
    }
    .custom-discount,.percent_discount {
      display: inline-flex;
      color: #F44336;
    }
    .details_block.custom-home-page,.percent_discount, .p-detail-box  {
      font-weight: 600;
      color: #423f3f;
      width: 100%;
      text-wrap: wrap;
      flex-wrap: wrap;
      .actual_price {
        margin: 0 10px;
      }
    } 
  }

}

#shapewear-size-chart{
  -webkit-overflow-scrolling: touch;
  height: 100%;
  .btn_close_top{
    float: right;
    padding: 0px 7px;
    margin: 10px 4px 0px 0px;
    font-size: 24px;
    border-radius: 50%;
  }
  .btn_close{
    padding: 8px 30px;
    margin: 20px 0px 80px;
    font-size: 20px;
    border-radius: 5px;
  }
  .modal-sm-size{
    .modal-content{
      width: 900px;
      .modal-header{
        h4{
          color: $text_black;
        }
      }
      .modal-body{
        .radio-buttons{
          margin: 10px 0px;
          .size-label{
            color: $text_black;
            text-align: center;
          }
        }
      }
    .table-size-bordered{
      width: 100%
    }
  }
}
}

.recommendations_modal_btn {
  box-shadow: 1px 2px 12px #0000005c;
  aspect-ratio: 1; 
  border-radius: 50%; 
  display: grid; 
  place-items: center; 
  background-color: white; 
  position: absolute; 
  z-index: 9999;
  padding: 10px;  
  right: 4%; 
  bottom: 7px;
  max-width: 45px;
  @media only screen and (max-width: 50px) {
    img {
      width: 100%;
    }
  }
}
// img.image_height{
//   height: 250px;
// }


.uk-title{
  color: #636466;
}
.desktop-view-product-slider-wrapper {
  display: none;
}
.desk_web{
  display: none;
}
@media only screen and (min-width: 1024px){
  .desktop-view-product-slider-wrapper {
    display: block;
    .product-img-box.medium{
      height: 300px;
      margin: 2px 0;
      padding: 0 2px;
    }
    .product-img-box .p-img {
      -o-object-fit: cover;
      object-fit: cover;
      -o-object-position: top;
      object-position: top;
    }
    .cmn-img-fluid {
      width: 100%;
      height: 100%;
      max-width: 100%;
    }
    .columns + .columns:last-child {
      float: left;
    }
  }
  .listing_panel_block{
    background: #fff;
    margin-bottom: 5px;
  }
  .desk_web{
    display: block;
  }
  .action_button_btn{
    padding-bottom: 0px;
    text-align: center;
    margin-bottom: 15px;
    .add_to_buy_bow{
      width: 450px;
      padding: 20px;
      font-weight: 600;
      border-radius: 10px;
    }
  }
  .m_web{
    display: none;
  }
  .mobile-view-product-slider-wrapper {
    display: none;
  }
  .thumb_images{
    margin-bottom: 5px;
    float: left;

    a{
      width: 100%;
      max-width: 102px;
      justify-content: center;
      margin-bottom: 10px;
      border: 1px solid #dfdede;
      align-items: center;
      display: flex;
     }
  }
  #play_video{
    .play_button{
      position: absolute;
      color: $white;
      bottom: 24px;
      font-size: 25px;
      background-color: rgba(71, 56, 56, 0.73);
      width: 74px;
      height: 74px;
      padding: 25px;
      left: 1px;
      top: 1px;
    }
  }
  #fancybox-thumbs ul {
    margin-top:10px !important;
  }
  .fancybox-thumbs{
    height: 85px !important;
    img{
      background: $white !important;
      padding: 12px;
     
    }
  }
  .thumb_images a:hover{
    border: 1px solid #721220;
  }
  .fancybox-thumbs.active{
    border: 2px solid #777;
    align-items: center;
    display: flex;
    img{
      border: none;
    }
  }
  #fancybox-buttons ul {
    width: 100px !important;
  }

  #fancybox-thumbs ul li.active {
    opacity: 1 !important;
    padding: 0;
    border: 1px solid #FF0808 !important;
  }

  #fancybox-thumbs ul li {
      clear: both !important;
      margin-left: 10px !important;
      padding: 1px;
      opacity:1 !important;
      border: 1px solid #fff;
      margin-top:5px !important;
  }

  .large_device_img_div {
    padding-right:10px;
    min-width:240px;
    .wishlist{
      background: image-url('wishlist.png') no-repeat;
      width: 23px;
      height: 20px;
      position: absolute;
      cursor: pointer;
      right: 15px;
      // z-index: 1029;
      top: 15px;
    }
    .img_text{
      width: 100%;
      font-size: 11px;
      letter-spacing: 1px;
      color: #721220;
      padding-left: 158px;
    }
  }
  .large_img_div {
    min-height: 440px;
    border-radius: 20px;
    padding: 5px;
    margin-bottom: 10px;
    .large_img{
      width: 100%;
      text-align: center;
      ul{
        list-style: none;
      }
      .zoomWrapper{
        width: auto !important;
        margin: auto;
      }
      img{
        background: none !important;
        position: relative !important;
      }
      .wishlist-forms{
        position: absolute;
        right: 0;
        z-index: 10;
        top: 4px;
        font-size: 33px;
        color: $light-red-text;
        background: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: white;
        box-shadow: 0px 2px 8px 0px rgba(113, 103, 103, 0.75);
        right: 4px;
        .wishlist-heart-button{
          outline: none !important;
          padding: 0;
          margin: 0;
          background: none;
          margin-top: 5px;
          border: none;
          span{
            margin-right: 2px;
            margin-top: 3px;
          }
        }
      }
    }
  }
  #fancybox-thumbs{
    // display: none !important;
    position:absolute;
    top:0px !important;
    left: 0px !important;
  }


  .fancybox-skin{
    padding-left:80px !important;
  }
  .thumb_image_div {
    padding-top: 5px;
    padding-bottom: 15px;
    padding-left:0px;
    padding-right:0px;
  }

  #video{
    background: rgba(132, 132, 132, 0.4);
    .modal-dialog{
      width: 731px;
    }
    .modal-content{
      border-radius: 5px;
      padding: 0px 0px 0px 15px;
      margin-left: auto;
      margin-right: auto;
      .modal-header{
        margin-right: 4px;
        margin-bottom: 3px;
        .close{
          outline: none;
          color: black;
          opacity: 0.9;
          margin-bottom: -1px;
          font-size: 32px;
          font-weight: lighter;
          line-height: 1;
          font-weight: light;
          filter: alpha(opacity=20)
        }
      }
      .iframe_video{
        width: 700px;
        height: 649px;
        background-color: black
      }
      .modal-footer{
        padding-top: 1px;
        padding-bottom: 7px;
        margin-top: 10px;
        .discount_percent{
          font-size: 14px;
          padding: 4px;
        }
        .old_price_label{
          margin-top: 2px;
          font-size: 14px;
        }
        .add_to_cart_video_button{
          left: 103px;
          width: 220px !important;
          a{
            height: 43px;
            padding: 11px 25px;
            text-align: center;
            border: 1px solid $dark-red-text;
          }
        }
        h3{
          font-weight: bold;
          font-size: 18px;
        }
      }
    }
  }
  #more_like_category{
    .item-container{
      .list-item{
        grid-auto-columns: 20%;
      }
    }
  }
  #design_group{
    .design-group-by-color{
      display: grid;
      grid-auto-flow: column;
      grid-auto-columns: 18%;
      column-gap: 0.2rem;
      overflow-x: auto;
      overscroll-behavior-inline: contain;
      .sibling-design-container{
        display: grid;
        grid-template-rows: min-content;
        justify-content: center;
        border: 1px solid #f1f1f1;
        padding: 10px 0 0;
      }
      .sibling-design{
        box-shadow: 0 0px 0px 0 rgba(0,0,0,0);
        overflow: hidden;
        margin: 10px 11px 5px 11px;
        cursor: pointer;
        text-align: center;
        margin: 0px 0px 0px 0px;
        border: 1px solid #eee;
        display: inline-block;
      }
      .sibling-design img{
        display: block;
        width: 100%;
        height: 60px
      }
      .sibling-design-color-title{
        font-size: 14px;
        padding: 3px;
      }
    }
  }
}
.product-image{
  transition: 1s;
  border: 1px solid #eeeeee;
  width: 100%;
  box-shadow: rgba(0,0,0,0.45) 0px 25px 20px -27px;
}
.price-tag.d-flex {
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  color: #303030;
  font-size: 13px;
}
.percent_disc {
  font-size: 1.1em;
  color: #a10000;
  background-color: #fff;
  padding: 0 1%;
  margin-bottom: 0;
}
.actual_price.product_price_wo_discount {
  text-decoration: line-through;
  color: #595959;
}
h1.category-title {
  font-size: 18px;
}
.price-block {
  color: #303030;
  font-size: 13px;
}
.unbxd-img {
  transition: 1s;
  border: 1px solid #eeeeee;
  width: 100%;
  box-shadow: rgba(0,0,0,0.45) 0px 25px 20px -27px;
}
.design-breadcrumb .final{
  font-weight: 600;
color: #111111;
}
.pdp-heading{
    color: #111111;
    font-size: 1.2em !important;
    font-weight: 500;
}
.pdp-head-box,
.reviews_ratings,
.design_addons,
.avaiable-offers,
.design-box {
  padding: 0 10px 20px;
  border-bottom: 1px solid #f1f1f1;
}
#design_addon_form .columns {
  color: #3C4345;
  padding-bottom: 10px;
  font-size: 16px;
}
#design_image_block #design_addons select {
  padding: 0.5em;
  color: #303030;
  margin-bottom: 5px;
  background-color: #fff;
  border: 1px solid #d7d3d3;
  height: 45px;
  border-radius: 5px;
  font-family: "Inter", sans-serif !important;
}
.loyalty_cashback,
.loyalty_tnc {
  color: #b84224 !important;
  font-size: 14px;
  background-color: #fff;
}
table#one p, .sub-specs-line td.spec {
  font-weight: 500;
  color: #3C4345CC;
  font-size: 13px;
}
div#design_description, 
div#specifications,
div#stitching, 
div#payments, 
div#returns, 
div#color-wash-care {
  border-bottom: 1px solid #f1f1f1;
  table{
    margin: 0;
  }
}
h1#design_title {
  color: #111111 !important;
  font-size: 1.5em;
  font-weight: 500;
}
form#design_addon_form li, form#design_addon_form h5 {
  color: #55A630;
}
table#one p,
.sub-specs-line td.spec {
  font-weight: 500;
  color: #3C4345CC;
  font-size: 13px;
  padding: 0 10px;
}
.variant-price-text h5 i {
  color: #3C434599;
  font-style: normal;
}
a.b1g1, body .b1g1_tnc, 
.design_offer_message, 
.qpm_tnc, 
.fd_message, 
.fd_tnc {
  color: #f4846e !important;
  font-size: 14px;
}
.pincode {
  display: flex;
  padding: 20px 0px !important;
  width: 100%;
  p {
    padding: 6px 0;
    margin-right: 10px;
    width: 50%;
}
}
.effect-1 {
  height: 36px;
  background-color: white;
  display: inline-flex;
  border: 1px solid #f1f1f1;
  padding: 0px 0px 0px 10px;
  align-items: baseline;
  .fa {
    color: #303030;
}
a {
  background: #670b19;
  color: #fff;
  padding: 5px 6px;
}

}
#design_image_block input[type="text"] {
  height: 25px;
  font: 15px;
  color: #333;
  letter-spacing: 1px;
  border-bottom: 0px;
  margin-left: 10px;
}
.listing_panel_block #cod_available {
  font-size: 0.875rem;
  color: #55A630;
  background: #55A63026 !important;
  padding: 10px;
  border-radius: 6px;
}
.new_wishlist i.fa.fa-heart-o {
  font-size: 22px;
  color: #B10D28;
}
.shipping_info.listing_panel_block {
  border-top: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
}
@media screen and (max-width:767px) {
  h1#design_title {
    color: #111111;
    font-size: 1.2em;
}
#design_image_block #design_addons .hsw-block,
#design_image_block .available-offers-on-pdp,
.listing_panel_block p {
  font-size: 1em;
} 
div#design_description, 
div#specifications, 
div#stitching, 
div#payments, 
div#returns, 
div#color-wash-care {
  border-bottom: 1px solid #f1f1f1;
  padding: 5px 0;
}
#design_addon_form {
  padding-left: 0;
}

.small-centered.columns.addons button.btn-view-size {
  padding: 4px 20px 0 0;
}
.select-variant .variant_selection {
  .content {
    .variant-header {
      span strong {
        font-size: 1.25rem;
      }
      .close-variant-select {
        font-size: 28px;
      }
    }
  }
}
.select-size {
  font-weight: 500;
    color: #3C4345;
    font-size: 1em;
}
  }

button.alert, .button.alert {
  background: maroon !important;
  color: #fff !important;
}
.pincode-box {
  background: #fff !important;
}
.sold-out-btn.button.disabled.alert.tiny.sold-out {
  opacity: 1;
  margin-top: 40px;
  border-radius: 12px;
  padding: 20px;
  font-size: 18px;
}
@media only screen and (min-width: 1399px)
{
  .action_button_btn .add_to_buy_bow {
    width: 440px;
}
}
@media screen and (max-width:1199px){
  .action_button_btn .add_to_buy_bow {
    width: 345px;
  }
}

@media screen and (max-width:991px) {
  #design_image_div {
    display: flex;
    flex-wrap: wrap;
}
.controls{
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0px;
  .blaze-pagination button{
    width: 6px;
    height: 6px;
  }
}
.blaze-slider .blaze-track{
  display: block;
}
#design_image_div .design-image-container {
  box-sizing: border-box;
  width: 100% !important;
  padding: 20px;
  align-self: flex-start;
  position: unset;
}
.design_wrap {
  width: 100%;
  max-width: 380px;
  max-height: 32em;
  height: 32em;
}
.select-variant.size-box {
  .variant_selection .content {
    height: 500px;
  }
}
.pincode p {
  width: 20%;
}
.breadcrumb_stuff {
  width: 100%;
  ul {
    display: flex;
    width: 100%;
    padding: 0px 0 10px;
   flex-wrap: wrap;
}
}
}
@media screen and (max-width: 767px)
{
  .pincode p {
    width: 42%;
}
.pdp-page-rating {
  .heading_review {
    text-align: left;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    color: #3C4345;
    padding: 10px 7px;
    margin-bottom: 0;
    font-size: 1em;
  }
  .tabbed-view {
    margin-top: 0;
  }
  .tabbed-view .review-row .block {
    background-color: #ffffff;
    display: inline-block;
    width: 100%;
    padding: 0;
    margin-left: 2%;
    border-bottom: 1px solid #b9b6b6;
  }
}
  .review-text {
    height: unset !important;
    font-weight: 500;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;  
    overflow: hidden;
    font-size: 13px;
    font-weight: 500;
    padding: 0 0 5px;
    color: #403939;
}
}
.select-color {
  margin: 15px 0;
  border-bottom: 1px solid #f1f1f1;
  padding: 0 0 20px;
}
.design-group-by-color{
  &::-webkit-scrollbar {
    width: 3px;
    height: 5px;
  }
  &::-webkit-scrollbar-track
  {
    -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
    border-radius: 10px;
    background-color: #fff;
  }
  &::-webkit-scrollbar
  {
    width: 5px;
    background-color: #f1f1f1;
    border-radius: 10px;
  }
  &::-webkit-scrollbar-thumb
  {
    border-radius: 10px;
    background-color: #777;
  }
}
.review-container {
  .heading_review {
    .view_all {
      margin-right: 0px;
    }
  }
}
  .pincode p {
    width: 42%;
    text-align: left;
    border-radius: 2px 2px 0px 0px;
    padding: 6px 0;
    margin-bottom: 0;
    font-weight: 600;
    color: #3C4345;
    font-size: 16px;
}
.sold-out-btn.button.disabled.alert.tiny {
  padding: 10px;
  margin: 0px;
  font-weight: 600;
  border-radius: 10px;
  background: rgba(48, 48, 48, 0.76) !important;
  color: #fff !important;
  font-size: 16px;
  font-weight: 600;
}
#more-items-loader{
  display: none;
  border: 4px dotted $dark_red;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}
 .avaiable-offers{
  padding: 0 10px 0px;
}
#design_addon_form .columns .columns:last-child {
  padding-bottom: 0px; 
}
.reviews_ratings {
  display: flex;
  align-items: center;
  width: 100%;
  .custom_rating_message {
    border: 1px solid #670b19;
    margin: 0 10px;
    border-radius: 5px;
}
}
.select-color {
  padding: 5px 0 10px;
  border-bottom: 1px solid #f1f1f1;
}
.select-variant 
{
  .variant-header {
    span strong {
      color: maroon;
      font-size: 1.5rem;
  }
  .close-variant-select {
    font-size: 2rem;
    float: right;
    line-height: 1;
    color: maroon;
    font-weight: 600;
    padding: 10px 30px;
    cursor: pointer;
  }
  }
  .select-addon-variant {
    text-align: center;
    border: 1px solid #d4d4d4;
    padding: 0.4em;
    text-transform: uppercase;
    background: maroon;
    color: #fff;
    width: 100%;
    max-width: 200px;
    border-radius: 10px;
    cursor: pointer;
}
}
.small-centered.columns.addons a.variant.button.secondary.tiny {
  background: transparent;
  border: 1px solid #dadada;
  border-radius: 5px;
  padding: 8px 15px;
  color: #2b2b2b;
  font-size: 14px;
}
.design-group-title.select_color,
.select-size {
  color: #3C4345;
  font-size: 1em;
  font-weight: 600;
}
.current.sibling-design img {
  border: 3px solid #F4846E;
}
div#payments, div#returns{
  display: none;
}
div#videoModal {
  margin-top: 4rem;
  position: fixed;
}
.discount_old_price {
  margin-right: 20px;
}
.floatl {
  float: left;
}
.discount_percent {
  background: #f44336;
  font-size: 16px;
  color: #fff;
  padding: 10px 20px;
  border-radius: 100px;
  font-size: 0.8rem;
}
video.video {
  background: #000;
  height: auto;
  max-height: 340px;
}
  
  
// new pdp button
.pdp_product_detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding:0px 60px 0 60px;
  box-shadow: #00000042 0px 2px 8px 0px;
  .product_name {
    width: 80%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    h4{
      color: #111111;
    font-size: 1em;
    width: 65%;
    font-weight: 400;
    margin: 0;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    }
    .price_boxes {
      display: flex;
      width: 35%;
      align-items: center;
      margin-right: 15px;
      justify-content: flex-end;
  }
  .discount_price {
    font-size: 1em !important;
    color: #303030;
    font-weight: 600;
}
.actual_price.product_price_wo_discount {
  color: #303030c2;
  font-size: 1em !important;
  margin: 0 10px;
  padding: 0 !important;
}
.percent_disc {
  font-size: 1em !important;
  color: #d32f2f; /* A darker red with better contrast */
  padding-bottom: 3px;
  background-color: #ffffff; /* Ensure the background is light */
}
}
  .pdp_new_button {
    display: flex;
    align-items: baseline;
    width: 30%;
justify-content: space-between;
padding: 10px 0;
  }
  .sold_out_button{
    justify-content: center;
    .medium-uncollapse {
      width: 85%;
  }
  }
}
.pdp_product_detail .product_discount_price {
  font-size: 1em !important;
}
.size-box {
  .pdp_product_detail {
    box-shadow: none;
    padding: 5px 0;
    .product_name {
      display: none;
  }
   .pdp_new_button {
      display: flex;
      align-items: center;
      padding: 10px 0;
      width: 50%;
  }
 .small-uncollapse {
    padding-left: 0 !important;
}
  }
 
}
@media only screen and (min-width: 1024px){
  footer.pdp_footer.d_web.footer-component-wrapper {
  margin-bottom: 4rem !important
}
}
@media screen and (max-width:991px) {
  .pdp_product_detail{
    padding: 10px 10px;
    .product_name {
     display: none
  }
  .pdp_new_button{
    width: 100%;
  }
  }
  .mpdp_footer{
    margin-bottom: 4rem !important
  }
  .size-box {
    .pdp_product_detail {
     .pdp_new_button {
        width: 100%;
    }
  }
  }
  .designs_show.page {
    #action_buttons {
      .button {
        width: 100%;
        max-width: 365px;
    }
    .right-button {
      margin-left: 2px !important;
  }
    }
 
    }
#add_to_cart_message {
  max-width: 260px !important;
  left: 65px !important;
  bottom: 90px !important;
}
}
@media screen and (max-width:767px){
  .pdp_product_detail .pdp_new_button {
    padding: 0;
}
}
@media only screen and (min-width: 30em)
{
  .reveal-modal {
    overflow-y: hidden !important;
    top: 15% !important;
    transform: translate(0, 0);
    height: auto !important;
    max-block-size: fit-content;
    max-height: 400px;
}
}
div#payments, div#returns {
  display: none;
}
// @import 'recommendation';
@media screen and (max-width:1199px){
  .reviews_ratings {
    display: unset
}
}
div#videoModal {
  margin-top: 4rem;
  position: fixed;
}
.discount_old_price {
  margin-right: 20px;
}
.floatl {
  float: left;
}
.discount_percent {
  background: #f44336;
  font-size: 16px;
  color: #fff;
  padding: 10px 20px;
  border-radius: 100px;
  font-size: 0.8rem;
}
video.video {
  background: #000;
}

#more-items-loader{
  display: none;
  border: 4px dotted $dark_red;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}
div#line_items_count {
  border: 1px solid #670b19;
  width: 100%;
  max-width: 310px;
  padding: 20px;
  border-radius: 20px;
  display: flex;
  font-size: 16px;
  background: #fff9fa;
  color: #670b19;
  position: sticky;
  z-index: 9;
  display: none;
  cursor: pointer;
  .line_item_close {
    width: 10%;
    position: absolute;
    right: -6px;
    top: 21px;
}
} 
@media only screen and (min-width: 64.0625em) {
  .sub-image {
    width: 91px !important;
  }
}