//importing variables
@import 'variables';
@import 'white_theme';
@import 'social-share';
@import 'lightslider';

body.modal-open{
  overflow: hidden;
  position: fixed;
  font-family: "Inter", sans-serif !important;
}
body {
  overflow: scroll;
  .designs_show{
    .off-canvas-wrap {
      #container{
        width: 96vw;
        overflow-y: hidden;
        padding: 1em 0em;
        margin: 2%;
        margin-top: 3.5em;
        overflow-x: hidden;
        padding: 0em 0em !important;
      }
    }
    .hidden{
      display: none;
    }
    .action_button_btn {
      padding-bottom: 0px;
      text-align: center;
    }
    .overflow_messages{
      background: #008cba;
      text-align: center;
      padding: 3%;
      z-index: 100 !important;
    }
    #add_to_cart_message{
      padding: 10px;
    }
    #action_buttons {
      transform: translate3d(0px,0px,0px);
      .cart_button {
        margin-bottom: 0;
        background-color: #0d997c;
        width: 100%;
        font-weight: 700;
      }
      .right-button{
        margin-bottom: 0;
        background-color: #0d997c;
        width: 100%;
        padding: 1.2em 0;
        font-weight: 700;
      }
      .action_button_btn {
        padding-bottom: 0px;
        text-align: center;
      }
      .left-button{
        background-color: $snow_white;
        color: $green_btn;
        margin-bottom: 0;
        width: 100%;
        padding-left: 0%;
        font-weight: 700;
        padding-right: 0%;
        padding: 1.2em 0;
      }
    }
    .designs_show.page{
      .heading_underline {
        text-decoration: underline;
      }
      #action_buttons, #secondary_action_buttons{
        &.fixed {
          bottom: 0;
          top: auto;
          .add_place_order {
            background-color: $dark_green;
          }
          .add_to_cart {
            background-color: black;
            color: $dark_green;
          }
          margin-bottom: 0;
          a, input[type='submit'] {
            margin-bottom: 0em;
            width: 100%;
            font-weight: bold;
            line-height: inherit;
          }
          li {
            padding-bottom: 0em;
            padding: 0em;
          }
        }
      }
      #secondary_action_buttons{
        margin: 3.7em 0;
      }
      .panel_block {
        background-color: $bluish_black;
        border: 0.1em solid $bluish_black;
        margin-top: 1em;
        border-radius: .5em;
        .panel_content {
          background-color: $snow_white;
          border: 1px solid black;
          border-radius: 6px;
        }

        .panel_heading {
          padding: 0em .2em;
          ul {
            margin: .2em auto;
          }
        }
      }
      .line_through_text{
        text-decoration: line-through;
      }
      .truncate {
        width: 100%;
        color: $text_black;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .no-bullet.shipping_desc {
        li {
          color: white;
        }
      }
      .close-icon{
        background-image: image-url('close-32.png');
        background-repeat: no-repeat;
        display: inline-block;
        width: 1em;
        height: 1em;
        vertical-align: middle;
        background-size: 1em;
        background-color: black;
        border-radius: 5em;
      }
      .bordered_block {
        padding: 1em;
        margin-bottom: 1em;
        box-shadow: 0 0 2em black;
        border: 0.1em solid $gray;
      }

      .opera_checkout_position_fix{
        width: 100%;
        margin-top: 18px;
        background-color: #0E9A7D !important;
      }

      .mirraw_cert_logo{
        display: inline;
        opacity:0.85;
      }

      .small_msg{
        font-size: 12px;
      }

      .wrap_total{
        white-space: nowrap;
      }

      .sign_in_button a{
        color: rgb(181, 252, 181);
      }
    }
  }

  #disclaimer-box{
    font-size: 13px;
    text-align: center;
    margint-top: 8px;
    font-style: italic;
  }
}

#dynamicSizeChartModal, #sizeChartModal{
  background-color: $snow_white;
}

.design_wrap{
  position: relative;
  display: inline-block;
}

#design_image_block {
  padding: 0.5em;
  #design_image_div li{
    list-style: none;
    padding: 0;
    .small-centered{
      padding: 0 10px;
    }
  }
  .designer-panel{
    background-color: $snow_white;
    box-shadow: $card_box_shadow;
    margin-bottom:24px;
    .designer-title{
      padding: 0.5rem 0 0 1.5rem;
    }
    .rating-box{
      padding: 0.5rem 0 0 1.5rem;
      .small_rating{
        font-size: 12px;
        background-color: #16be48;
        color: white;
        padding: 5px;
        border-radius: 21%;
        font-weight: bold;
        &.green-rating {
          background-color: #16be48;
        }
        &.red-rating {
          background-color: #FF5722;
        }
        &.orange-rating {
          background-color: #FFA000;
        }
      }
    }
    .designer-name{
      padding: 0.5rem 0 0 1.8rem;
      a{
        font-size: 16px;
      }
    }
    .designer-sub-panel{
      margin-bottom:10px;
      font-size: 14px;
      color: $text_light_gray;
      padding: 0.5rem 0 0 1.8rem;
    }
  }
  .tap_text{
    text-align: center;
    small{
      color: $text_light_gray;
    }
  }
  #design_images, #design_images_zoom {
    text-align: center;
    margin: 0;
    img {
      margin: auto;
    }

  }
  .th{
    border: none !important;
  }
  .lSGallery{
    margin-left: auto;
    margin-right: auto;
    border-radius: 2px;
  }
  ul.lSPager{
    li{
      margin: 0 2px;
      a{
        background-color: #969696;
        height: 6px;
        width: 6px;
        z-index: 9;
      }
      &.active{
        a{
          background-color: $text_pink;
        }
      }
    }
  }
  #design_addons {
    background-color: $snow_white;
    box-shadow: $card_box_shadow;
    padding: 6px 3px 6px 8px;
    margin-bottom: 16px;
    .hsw-block{
      margin-left: -3px;
      a{
        border-radius: 50%;
        background-color: $text_pink;
        padding: 1px 6px;
        font-size: 14px;
        font-style: italic;
        font-weight: 700;
        font-family: 'Times New Roman';
        cursor: pointer;
        color: $text_white;
      }
    }
    select {
      padding: 0.5em;
      color: $text_light_gray;
    }
    .addon_option_values {
      border: none;
      margin: 0px 0px 10px 10px;
      .atov-name{
        font-weight: 700;
        font-size: 14px;
      }
    }
    .stitching_note{
      font-size: 13px;
      color: $text_light_gray;
    }
    #salwar_kameez_default,#salwar_kameez_specific{
      text-align: justify;
      font-size: 13px;
      font-style: italic;
      line-height: 1.2em;
      display: block;
      margin-bottom: 10px;
      padding-left: 10px;
      padding-right: 10px;
      font-family: Times new roman;
      color: #f1ebeb;
    }
  }
  .product_price_wo_discount{
    text-decoration: line-through;
    padding: 0 2% 0 0;
    color: $text_black;
    font-size: 13px;
    font-weight: bold;
    line-height: 27px;
  }
  .offer-message{
    padding-bottom: 2%;
  }
  .product_discount_price {
    font-size: 17px;
    color: $text_black;
    padding: 0;
    padding-right: 2%;
  }
  .changeInPriceNote{
    display: none;
    position: absolute;
    span{
      background: #3ebe49;
      font-size: 12px;
      margin-left: -10px;
      padding: 0px 7px 0px 7px;
      border-radius: 4px;
      font-weight: bold;
      color: white;
      z-index: 10;
    }
    .arrow{
      background-color: #3ebe49 !important;
      width: 10px;
      height: 10px;
      left: 45%;
      top: -5px;
      transform: rotate(45deg);
      position: relative;
      display: inline-flex;
    }
  }
  .product_discount_percent {
    font-size: 0.9em;
    border-radius: 1em;
    width: 21%;
    padding: 0.7em;
    text-align: center;
  }
  
  .line_through_text {
    text-decoration: line-through;
    margin-top: 0.1em;
    font-size: 18px;
  }
  #design_price_block {
    border-top: 2px solid black;
    border-bottom: 2px solid black;
    margin-top: 1em;
    li {
      padding-bottom: 0em;
    }
  }
  #variants_block {
    background: $snow_white;
    padding: 4px 3px;
    margin-bottom: 16px;
    .var-title{
      padding-left: 4px;
    }
    .size-chart-text{
      font-size: 13px;
      margin-left: 12px;
    }
    a {
      padding: 0.625em .85em;
      margin: 0.3em;
      box-shadow: $variant_box_shadow;
      font-weight: 700;
      &.selected,&:focus,&:hover{
        background-color: $variant_background;
      }
    }
    .variant{
      font-size: small;
    }
  }
  #deal_ends{
    background: linear-gradient(to left, #ED8F03 , #ED8F03);
    margin-bottom: 20px;
    font-size: 18px;
    box-shadow: $card_box_shadow;
    border-radius: 0px;
    padding: 4px 2px;
    #countdown{
      color: #080808;
      text-align: center;
      #deal_text{
        display: inline-block;
        font-family: inherit;
        color: #080808;
        vertical-align: bottom;
        font-weight: bold;
        text-align: left;
      }
      #clock{
        text-align: right;
        color: #103a4f;
        font-weight: bold;
      }
    }
  }
  #line_items_count {
    display: none;
    width: 290px;
    background-color: #cecece;
    color: #000000;
    text-align: center;
    padding: 16px;
    position: fixed;
    z-index: 10;
    right: 8px;
    bottom: 1%;
    font-size: 16px;
    line-height: 21px;
    border: 2px solid #e29cb4;
    .line_item_close{
      float: right;
      text-align: right;
      position: relative;
      left: 25px;
      bottom: 25px;
      padding: 0px 3px;
      cursor: pointer;
      border-radius: 50%;
      background-color: #a9a9a9;
    }
    .line_items_count_text{
      span{
        color:#670b19;
        text-transform: uppercase;
        font-weight: 700;
      }
    }
  }
  #add_review_button{
    button#add_review{
      margin-bottom: 0px;
    }
  }
}
#write_review{
  #rating-alert-message, #review-alert-message, #save-alert-message{
    display: none;
    color: #e82424;
    font-size: 14px;
  }
  .alert-border{
    border: 2px solid #e63939;
  }
  #form-rating-star{
    img{
      width: 20px;
    }
  }
  #review-text{
    background-color: #e8e8e8;
  }
}
.percent_disc {
  font-size: 13px;
  padding: 0 1%;
  line-height: 27px;
  font-weight: bold;
  color: $text_pink;
}
#designable_details {
  .tabs {
    .tab-title > a {
      padding: 0.5em 1em;
      margin: .03em;
      background-color: inherit;
      color: inherit;
      border: .1em solid;
    }
    .tab-title.active > a {
      background-color: #FFFFFF;
      color: #222222;
    }
  }
  ul.no-bullet {
    label b {
      text-decoration: underline;
    }
    label {
      color: white;
    }
  }
  table {
    background: inherit;
    border: none;
    tr {
      &:nth-of-type(even) {
        background: inherit;
      }
      td {
        color: $text_light_gray;
        vertical-align: baseline;
      }
    }
  }
}

.listing_panel_block{
  background: $snow_white;
  box-shadow: $card_box_shadow;
  margin-bottom: 16px;
  color: $text_black;

  li, label{
    cursor: auto;
  }
  .variant-price-text{
    font-size: 12px;
  }

  p{
    text-align: left;
    /*background: rgba(96, 96, 96, 0.9) none repeat scroll 0% 0%;*/
    color: $text_black;
    margin: -7px 0px 0px;
    border-radius: 2px 2px 0px 0px;
    padding: 6px;
  }

  ul{
    font-size: 14px;
    padding: 4px;
    list-style-type: circle;
    color: $text_light_gray;
    margin-left: 1.5rem;
  }
  
  td{
    vertical-align: baseline;
  }
}

.accordion{
  table, ul.sub-specs-table {
    display:none;
  }

  table.sub-specs-line{
    display: table;
  }
  
  a, .sub-grp{
    outline: none;
  }  

  .expand{
    display: none;
    float: right;
  }

  .collapse{
    float: right;
  }

  .spec-collapse{
    display: block;
  }

  .sub-grp{
    p{
      font-size: 0.9rem;
      margin-left: 0.6rem;
      .button-icon{
        transition: all .5s ease-in-out;
        display: inline-block;
        font-weight: 700;
        font-size: 1.1rem;
        line-height: 1.8;
      }
      .button-toggle{
        transform: rotate(90deg);
      }
    }
  }
  table.sub-specs-table, .sub-specs-line{
    margin: 0 0 0.5rem 0.6rem;
    td.spec{
      padding: 0.2rem 0.4rem;
    }
    td.spec.first{
      padding-right: 0px;
    }
  }
  table.sub-specs-table{
    padding-left: 0.7rem;
  }
}

#designable_details ul{
 margin-left: 1rem;
} 

.pre-order{
  padding: 7px 20px;
  #pre-order-check{
    vertical-align: middle;
    margin: 0px -2px 0px 0px;
  }
  label{
    color: $text_white;
  }
}


//Image Modal box
#myImg {
  cursor: pointer;
  transition: 0.3s;
}

/* The Modal (background) */
.modal {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 999;
  -webkit-transform: translate3d(0,0,0);
  transform: translate3d(0,0,0);
  left: 0;
  top: 0;
  width: 100%;
  height: 800px;
  background-color: $body_white;
}

/* Modal Content (Image) */
.modal-content {
  margin: auto;
  display: block;
  width: 80%;
  max-width: 100%;
}

#loadingImage{
  position: fixed;
  left: 45%;
  top: 30%;
  border: 4px dotted #4e4e4e;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  margin: 0 auto;
  animation: spin 2s ease-in-out infinite;
}

/* Add Animation - Zoom in the Modal */
.modal-content { 
  -webkit-animation-name: zoom;
  -webkit-animation-duration: 0.6s;
  animation-name: zoom;
  animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}

@keyframes zoom {
  from {transform:scale(0)} 
  to {transform:scale(1)}
}
/* shake effect */
.shake-effect {
  -webkit-animation-duration: 0.75s; 
  animation-duration: 0.75s; 
  -webkit-animation-fill-mode: none; 
  animation-fill-mode: none;
  -webkit-animation-name: shake; 
  animation-name: shake; 
  }
 @keyframes shake { 
    0%, 100% {transform: translateX(0);color: #ed8f03;} 
    20%, 60% {transform: translateX(-5px);color: #ed8f03;} 
    40%, 80% {transform: translateX(5px);color: #ed8f03;} 
  }
@-webkit-keyframes shake {
  0%, 100% {-webkit-transform: translateX(0);color: #ed8f03;} 
  20%, 60% {-webkit-transform: translateX(-5px);color: #ed8f03;} 
  40%, 80% {-webkit-transform: translateX(5px);color: #ed8f03;} 
}
/* The Close Button */
.close {
  font-size: 2em;
  padding: 1% 3% 1% 5%;
  right: 0.3em;
  display: block;
  text-align: right;
}

.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}


/* 100% Image Width on Smaller Screens */
@media only screen and (max-width: 700px){
  .modal-content {
    width: 100%;
  }
}

@media only screen and (min-width: 1200px){
  .modal-content {
    width: auto;
    height: auto;
  }
}

#branch-banner-iframe{
  z-index: 1 !important;
}

.notice_class {
  color:orange;
  font-size:14px;
  line-height:15px;
}

.product_design_price{
  margin: 0;
}

.font_greyed{
  color: $text_black;
  &.delivery_day{
    margin-top: 5px;
    font-size: 15px;
  }
}
#cod_available{
  font-size: 15px;
}
.rating_message{
  font-size: 14px;
  padding-left: 8px;
}
.reviews_ratings{
  display: inline;
  .small_rating{
    font-size: 12px;
    background-color: #16be48;
    color: white;
    padding: 5px;
    border-radius: 21%;
    font-weight: bold;
  }
  .green-rating{
    background-color: #16be48;
  }
  .red-rating{
    background-color: #FF5722;
  }
  .orange-rating{
    background-color: #FFA000;
  }
}
.review-opinion{
  font-size: 12px;
  font-weight: 600;
  margin-top: 5px;
}
.size-text{
    margin: 8px 0px 8px 16px;
    color: $text_black;
    font-weight: 700;
    font-size: 14px;
  }
  button.btn-view-size{
    background: transparent;
    color: $link_blue;
    padding: 4px 2px;
    font-size: 13px;
    margin: 0px;
  }
  button.btn-view-size:hover{
    background: #009688;
    border: 1px solid #088074;
    color: #1c1b1c;
  }
  .size-chart-div{

    margin-bottom: 30px;
    margin: 0px 0px 10px 10px;
    padding: 0px;
    height: 50px;
    .size-chart{
      
      a.size{
        width: 30px;
        background-color: $snow_white;
        border: $border_black;
        color: $text_black;
        font-weight: 700;
        margin-left: 6px;
        margin-top: 2px;
        float: left;
        border-radius: 1%;
        padding: 5px;
        cursor: pointer;
        display: inline-table;
      }
      a.size.selected, a.size:hover{
        background-color: $variant_background;
        color: $variant_color;
        border-color: $variant_border;
      }
    }
  }
  span.size-left,span.size-right{
    position: absolute;
    padding: 4px 8px;
    font-size: 18px;
    font-weight: 700;
    background-color: #d2d2d2;
    top: 38px;
    color: #2d2b2b;
    z-index: 2;
    cursor: pointer;
    border-radius: 50%;
  }
  span.size-left{
    left: -2px;
    display: none;
  }
  span.size-right{
    right: -28px;
    display: block;
  }
  span.size-left:hover, span.size-right:hover{
    background-color: #afafaf;
  }

  #modal-size-chart{
    -webkit-overflow-scrolling: touch;
    height: 100%;
    .btn_close_top{
      float: right;
      padding: 0px 7px;
      margin: 10px 4px 0px 0px;
      font-size: 24px;
      border-radius: 50%;
    }
    .btn_close{
      padding: 8px 30px;
      margin: 20px 0px 80px;
      font-size: 20px;
      border-radius: 5px;
    }
    .modal-sm-size{
      .modal-content{
        width: 900px;
        background: #efeded;
        color: white;
        .modal-header{
          h4{
            color: #383434;
          }
        }
        .modal-body{
          .size-label{
            margin: 10px 0px;
            color: $text_black;
            text-align: center;
          }
          .table-size-bordered{
            border: 1px solid black;
            th, td{
              border: 1px solid black;
            }
          }
        }
      }
    }
    .head_style{
      text-align: center;
      padding: 3px;
      font-size: 12px;
    }
  }
.stitching_offer{
  color: #EE9209;
  font-size: 15px;
  margin-top: 10px
}
.b1g1_label{
  display: inline;
  padding: .2em .6em .3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
}

// stitching conformation popup modal css
#stitchingModal{
  background-color:rgba(146, 140, 140, 0.84);
  height: 100%;
  overflow: auto;
  .modal-dialog{
    margin-top: 85px;
  }
  .modal-header{
    font-size: 13px;
    display: inline-flex;
    background-color: $text_black;
    width:100%;
    #stitching-confirmation-message{
      color: $text_white;
      padding: 11px;
    }
    a{
      padding-left: 45%;
    }
  }
  .modal-content{
    width: 280px;
    height: 100%;
    background-color: $text_white;
    color: $text_black;
    font-size: 1.25em;
  }
  .closeStitchingModal{
    color: white;
    font-size: 1.8em;
    padding: 1% 3% 1% 5%;
    right: 0.3em;
  }
  .modal-footer{
    text-align: center;
    padding: 2% 0 2%;
    .btn{
      padding: 7px !important;
      width: 46%;
      font-size: 12px !important;
      border-radius:2px;
    }
    .stitch{
      background: $dark_green;
    }
    #add-cart{
      background: $text_black;
      color: $text_white !important;
    }
  }
  .modal-body{
    text-align: center;
    padding:20px;
    font-size: 14px;
    .modal-message{
      padding-top:20px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.price_match_guarantee {
  .price_match_description {
    display: none;
    background: white;
    color: black;
    padding: 10px;
    z-index: 1;
    position: absolute;
    margin-top: 10px;
  }
}

#design_images_zoom {
  height: 100% !important;
}

#myModal {
  height: 100%;
  @mixin button-color {
    background-color: white;
    border-radius: 25px;
    width: 40px;
    height: 40px;
    opacity: 0.6;
    background-image: none;
  }
  @mixin arrow-style {
    content: '';
    border-right: 5px solid #a99b9b;
    border-top: 5px solid #a99b9b;
    width: 15px;
    height: 15px;
    position: absolute;
  }
  .lSAction > .lSNext {
    @include button-color;
    &:before {
      @include arrow-style;
      transform: rotate(45deg);
      bottom: 13px;
      left: 12px;
    }
  }
  .lSAction > .lSPrev {
    @include button-color;
    &:before {
      @include arrow-style;
      transform: rotate(-135deg);
      bottom: 13px;
      left: 14px;
    }
  }
}

.label_for_image_box {
  position: absolute;
  display: none;
  top: -4px;
  right: 0px;
  height: 125px;
  overflow: hidden;
  z-index: 1;
  width: 128px;
}

.label_text {
  display: none;
  top: 35px;
  width: 170px;
  padding: 5px 29px 5px;
  font-size: 15px;
  text-align: center;
  margin-left: -24px;
  color: #fff;
  z-index: 1;
  background-color: #e92d4c;
  position: absolute;
  left: 19px;
  -webkit-transform: rotate(45deg) translate3d(0, 0, 0);
  -moz-transform: rotate(45deg) translate3d(0, 0, 0);
  -ms-transform: rotate(45deg) translate3d(0, 0, 0);
  transform: rotate(45deg);
}
#design_group{
  .design-group-by-color{
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 23%;
    column-gap: 0.2rem;
    overflow-x: auto;
    overscroll-behavior-inline: contain;
    .sibling-design-container{
      display: grid;
      grid-template-rows: min-content;
      text-align: center;
    }
    .sibling-design{
      padding: 0;
      overflow: hidden;
      margin: 10px 11px 5px 11px;
      // box-shadow: 20px 5px rgba(0,0,0,0.7)
    }
    .sibling-design img{
      display: block;
      width:100%;
      height: 100%;
      clip-path: circle(50%);

    }
    .sibling-design-color-title{
      font-size: 12px;
      padding: 3px;
    }
    .current{
      border: 2px solid #303030;
    }
  }
}

#more_like_category{
  max-width: 76rem;
  margin: auto;
  .title-block{
    display: flex;
    justify-content: space-between;
    .view_all{
      color:#b11f2b !important;
      font-weight: bolder;
      font-size: small;
      min-width: 50px;
    }
  }
  .item-container{
    display: grid;
    .list-item{
      display: grid;
      padding-bottom: 1.2rem;
      grid-template-rows: min-content;
      text-align: center;
      grid-auto-flow: column;
      grid-auto-columns: 45%;
      column-gap: 1.6rem;
      overflow-x: auto;
      .product-container{
        padding: 0;
        box-shadow: 0 3px 9px 0 rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 10px 11px 5px 11px;
        width: 100%;
        height: auto;
        align-self: center;
        .price-tag{
          display: flex;
          text-align: center;
          justify-content: center;
          .product_price_wo_discount{
            text-decoration: line-through;
            padding-left: 2%;
            color: $text_black;
            font-size: 0.8em;
          }
          .details_block{
            font-size: 0.7em; 
            padding-left: 2%;
            
          }
          .discount_price{
            font-size: 0.8em;
            color: $text_black;
            padding-left: 2%;
            font-weight: bolder;
            padding: 0;
          }
        }
        
      }
    }
  }

}

.recommendations_modal_btn {
  box-shadow: 1px 2px 12px #0000005c;
  aspect-ratio: 1; 
  border-radius: 50%; 
  display: grid; 
  place-items: center; 
  background-color: white; 
  position: absolute; 
  z-index: 4; 
  padding: 10px;  
  right: 4%; 
  bottom: 0;
  @media only screen and (max-width: 50px) {
    img {
      width: 100%;
    }
  }
}
img.image_modal{
  height: 25px;
  opacity: 85%;
}
@import 'unbxd_recommendations';
@import 'reviews';

/* Desktop Image Zoom Modal Styles */
@media (min-width: 769px) {
  #master {
    cursor: zoom-in !important;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.9;
    }
  }

  #design_gallery a {
    cursor: zoom-in;

    img {
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  /* Remove any elevateZoom related styles on desktop */
  .zoomContainer {
    display: none !important;
  }

  .zoomLens {
    display: none !important;
  }

  .zoomWindow {
    display: none !important;
  }

  /* Hide mobile tap text on desktop */
  .tap_text {
    display: none;
  }

  /* Enhanced PhotoSwipe Modal Styling for Desktop */
  .pswp {
    z-index: 1500 !important;

    .pswp__bg {
      background-color: rgba(0, 0, 0, 0.85) !important;
      backdrop-filter: blur(3px);
    }

    .pswp__scroll-wrap {
      padding: 40px 60px !important;
      box-sizing: border-box;
    }

    .pswp__container {
      max-width: 90vw !important;
      max-height: 85vh !important;
      margin: auto;
    }

    .pswp__item {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      box-sizing: border-box;
    }

    .pswp__img {
      max-width: 100% !important;
      max-height: 100% !important;
      width: auto !important;
      height: auto !important;
      object-fit: contain;
      border-radius: 8px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
    }

    .pswp__ui {
      .pswp__top-bar {
        background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent);
        height: 60px;

        .pswp__counter {
          color: #fff;
          font-size: 14px;
          font-weight: 500;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
          top: 20px;
          left: 20px;
        }

        .pswp__button {
          width: 44px;
          height: 44px;
          top: 8px;
          background-color: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
          }

          &--close {
            right: 20px;

            &:before {
              color: #fff;
              font-size: 18px;
              font-weight: bold;
            }
          }

          &--zoom {
            right: 80px;
          }

          &--fs {
            right: 140px;
          }
        }
      }

      .pswp__button--arrow--left,
      .pswp__button--arrow--right {
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        margin-top: -25px;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(255, 255, 255, 0.2) !important;
          transform: scale(1.1);
        }

        &:before {
          color: #fff;
          font-size: 20px;
          font-weight: bold;
        }
      }

      .pswp__button--arrow--left {
        left: 20px;
      }

      .pswp__button--arrow--right {
        right: 20px;
      }

      .pswp__caption {
        background: linear-gradient(transparent, rgba(0,0,0,0.3));
        padding: 20px 40px;

        .pswp__caption__center {
          color: #fff;
          font-size: 14px;
          text-align: center;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
          max-width: 600px;
          margin: 0 auto;
        }
      }

      .pswp__preloader {
        .pswp__preloader__icn {
          .pswp__preloader__cut {
            .pswp__preloader__donut {
              border: 2px solid rgba(255, 255, 255, 0.3);
              border-top: 2px solid #fff;
              border-radius: 50%;
              width: 30px;
              height: 30px;
              animation: spin 1s linear infinite;
            }
          }
        }
      }
    }
  }

  /* Prevent body scroll when modal is open */
  body.pswp-open {
    overflow: hidden !important;
  }

  /* Loading animation */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
}

/* Mobile - keep existing functionality */
@media (max-width: 768px) {
  #master {
    cursor: default;
  }

  .tap_text {
    display: block;
  }
}