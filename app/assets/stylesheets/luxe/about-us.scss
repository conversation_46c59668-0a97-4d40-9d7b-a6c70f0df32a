body {
  color: #01443b;
}

.mobile-header-component-container {
  display: none;
}

.section-one {
  background-color: #efe7df;
  padding: 4rem 0;

  .main-content {
    display: flex;
    justify-content: space-between;
    align-items: end;
    gap: 2rem;
    max-width: 1840px;
    margin: 0 auto;

    .image-one {
      position: relative;
      max-width: 400px;

      .brand-name {
        position: absolute;
        top: -180px;
        right: 0;
        font-size: 80px;
      }
    }

    .brand-tagline {
      font-size: 24px;
      margin-bottom: 11rem;
    }
  }

  .image-two {
    overflow: hidden;
    border-radius: 100% 0% 100% 0% / 70% 100% 0% 0%;
    max-width: 400px;
  }
}

.section-two {
  background-color: #01443b;
  padding: 5rem;

  .main-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6rem;
    max-width: 1840px;
    margin: 0 auto;

    .left-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 2rem;
      max-width: 900px;
      color: #d4ad8e;

      .content-title {
        font-size: 55px;
        text-transform: capitalize;
      }

      .content {
        font-size: 20px;
        text-align: justify;
      }
    }

    .right-content {
      .image-three {
        width: 480px;
        border-radius: 250px 250px 0 0;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

.section-three {
  background-color: #efe7df;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 5rem;
  gap: 3rem;

  .content-title {
    font-size: 55px;
    text-transform: capitalize;
  }

  .main-content {
    .image-container {
      display: flex;
      gap: 5rem;

      .category {
        text-align: center;
        text-transform: capitalize;

        .image {
          border-radius: 200px;
          overflow: hidden;
          max-width: 400px;
        }

        .image-title {
          font-size: 30px;
          padding: 1rem;
        }
      }
    }
  }
}

@media only screen and (max-width: 1400px) {
  .section-one {
    .main-content {
      .image-one {
        max-width: 300px;

        .brand-name {
          font-size: 65px;
        }
      }

      .brand-tagline {
        font-size: 20px;
      }
    }

    .image-two {
      max-width: 300px;
    }
  }

  .section-two {
    padding: 3rem;

    .main-content {
      gap: 4rem;
    }

    .left-content {
      .content-title {
        font-size: 45px;
      }

      .content {
        font-size: 18px;
      }
    }

    .right-content {
      .image-three {
        max-width: 380px;
      }
    }
  }

  .section-three {
    padding: 3rem;

    .content-title {
      font-size: 45px;
    }

    .main-content {
      .image-container {
        gap: 3rem;

        .category {
          .image-title {
            font-size: 25px;
          }
        }
      }
    }
  }

}

@media only screen and (max-width: 1000px) {
  .section-one {
    margin-top: 40px;
    padding: 1rem;

    .main-content {
      flex-direction: column;
      align-items: center;
      margin-top: 10rem;

      .image-one {
        .brand-name {
          right: 40px;
          top: -130px;
        }
      }

      .brand-tagline {
        margin-bottom: 1rem;
      }
    }
  }

  .section-two {
    .main-content {
      flex-direction: column;

      .right-content {
        .image-three {
          width: 100%;
        }
      }
    }
  }

  .section-three {
    .main-content {
      .image-container {
        flex-wrap: wrap;
        justify-content: center;
        gap: 6rem;

        .category {
          .image {
            max-width: 240px;
          }
        }
      }
    }
  }

  .mobile-header-component-container {
    display: block;
  }
}

@media only screen and (max-width: 800px) {

  .section-two .main-content,
  .section-three {
    gap: 2rem;
  }

  .section-three {
    .main-content {
      .image-container {
        gap: 4rem;

        .category {
          .image {
            max-width: 200px;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 600px) {
  .section-one {
    padding: 1rem;
  }

  .section-two {
    padding: 1.3rem;

    .left-content {
      .content-title {
        font-size: 35px;
      }
    }
  }

  .section-three {
    padding: 1.3rem;

    .content-title {
      font-size: 35px;
    }

    .main-content {
      .image-container {
        gap: 2rem;

        .category {
          .image {
            max-width: 165px;
          }

          .image-title {
            font-size: 22px;
          }
        }
      }
    }
  }
}

@media only screen and (max-width: 410px) {
  .section-three {
    .main-content {
      .image-container {
        gap: 1rem;

        .category {
          .image {
            max-width: 140px;
          }
        }
      }
    }
  }
}
