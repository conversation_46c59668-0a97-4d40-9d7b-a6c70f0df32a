.home-page-container {
  background-color: #ffffff;
  text-transform: uppercase;
}
.home-page-container .container-layout-max {
  max-width: 100% !important;
}
.home-page-container .container-layout-max-home {
  max-width: 2350px;
  margin-left: auto;
  margin-right: auto;
}
p.common-heading.four-cards-title {
  padding: 0.8rem 0 0.2rem 0;
  text-transform: uppercase;
  color: #00443d;
  font-weight: 700;
  letter-spacing: 1px;
  font-size: 40px;
}
.four-cards-container.celebrity-closet p.common-heading.four-cards-title {
  color: #00443d;
}
p.common-heading.three-cards-title {
  padding: 0.8rem 0 0.2rem 0;
  text-transform: uppercase;
  color: #d4ad8e;
  font-weight: 700;
  letter-spacing: 1px;
  font-size: 40px;
}
.home-page-container .horizontal-padding {
  padding: 0 15px;
}
.home-page-container .main-banner {
  margin-bottom: 20px;
}
.home-page-container .main-banner .slider-box .banner-img-box {
  margin-bottom: 2.5rem;
}

.home-page-container .main-banner .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  display: inline-block;
  margin: 0 7px;
  background-color: #808080;
  position: relative;
  vertical-align: middle;
  text-align: center;
  -webkit-transition: all 1s ease-out;
  -o-transition: all 1s ease-out;
  transition: all 1s ease-out;
}
.home-page-container
  .main-banner
  .swiper-pagination-bullet.swiper-pagination-bullet-active {
  opacity: 1;
  height: 12px;
  width: 12px;
  background-color: #00443d;
}

.home-page-container .main-banner .swiper-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
}
.home-page-container .offer-three-layout-card-wrapper {
  margin-top: -25px;
  margin-bottom: -25px;
}
.home-page-container .offer-three-layout-card-wrapper .shadow-overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  /*  box-shadow: inset 0px -200px 100px -100px rgba(0, 0, 0, 0.5);*/
}
.home-page-container .offer-three-layout-card-wrapper .b-card-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .offer-three-layout-card-wrapper .b-card-link {
  width: 100%;
  height: 100%;
  display: block;
  position: relative;
}
.home-page-container .offer-three-layout-card-wrapper .b-card-overlay-txt {
  left: 0;
  right: 0;
  bottom: 20px;
  color: #ffffff;
  font-size: 32px;
  position: absolute;
  text-align: center;

  font-weight: 600;
}
.home-page-container .exclusive-brands-carousel-card-wrapper {
  padding-top: 50px;
  padding-right: 30px;
  padding-left: 30px;
}
.home-page-container
  .exclusive-brands-carousel-card-wrapper
  .exclusive-brands-swiper {
  padding: 25px 0px 0px;
}
.home-page-container .exclusive-brands-carousel-card-wrapper .eb-carousel-item {
  width: 100%;
  margin-right: 32px;
}
.home-page-container .exclusive-brands-carousel-card-wrapper .eb-carousel-card {
  display: block;
  text-align: center;
}
.home-page-container
  .exclusive-brands-carousel-card-wrapper
  .eb-carousel-card
  .eb-card-title {
  color: #1f1f1f;
  font-size: 24px;
  margin-top: 20px;
  margin-bottom: 10px;
  text-transform: capitalize;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  font-weight: 500;
}
.home-page-container
  .exclusive-brands-carousel-card-wrapper
  .eb-carousel-card
  .eb-img-box {
  width: 100%;
  height: 100%;
}
.home-page-container
  .exclusive-brands-carousel-card-wrapper
  .eb-carousel-card
  .eb-card-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container
  .exclusive-brands-carousel-card-wrapper
  .eb-carousel-card
  .eb-card-btn {
  width: auto;
  font-size: 14px;
  color: #1f1f1f;
  padding: 3px 11px;
  border-radius: 6px;
  background: #ffffff;
  text-transform: uppercase;
  border: 1px solid #1f1f1f;

  font-weight: bold;
}
.home-page-container .single-img-banner-wrapper .banner-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}

.home-page-container .full-img-banner-wrapper .banner-img {
  height: auto;
  object-fit: cover;
}

.home-page-container .single-img-banner-wrapper .full-page-banner {
  width: 100%;
}

.home-page-container .best-sellers-card-wrapper .main-flex-container {
  padding: 15px 0 10px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
  -webkit-box-pack: normal;
  -ms-flex-pack: normal;
  justify-content: normal;
}
.home-page-container
  .best-sellers-card-wrapper
  .main-flex-container
  .flex-item-1 {
  width: 60%;
}
.home-page-container
  .best-sellers-card-wrapper
  .main-flex-container
  .flex-item-2 {
  width: 40%;
}
.home-page-container .best-sellers-card-wrapper .child-flex-container {
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
  -webkit-box-pack: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}
.home-page-container .best-sellers-card-wrapper .shadow-overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  /*  box-shadow: inset 0px -100px 100px 0px rgba(0, 0, 0, 0.8);*/
}
.home-page-container .best-sellers-card-wrapper .bs-img-txt-card {
  height: 100%;
  position: relative;
}
.home-page-container .best-sellers-card-wrapper .bs-img {
  -o-object-fit: fill;
  object-fit: fill;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .best-sellers-card-wrapper .bs-txt {
  position: absolute;
  left: 0;
  text-align: center;
  right: 0;
  bottom: 15px;
  font-size: 32px;
  color: #ffffff;
  padding: 5px 10px;

  font-weight: 600;
}
.home-page-container .best-sellers-card-wrapper .space {
  padding: 10px;
}
.home-page-container .best-sellers-card-wrapper .flex-item-1 .child-flex-item {
  width: 50%;
  display: block;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-1
  .child-flex-item:nth-child(1) {
  height: 282px;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-1
  .child-flex-item:nth-child(2) {
  height: 282px;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-1
  .child-flex-item:nth-child(3) {
  height: 555px;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-1
  .child-flex-item:last-child {
  width: 100%;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-1
  .child-flex-item:nth-child(odd) {
  padding-left: 0;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-2
  .child-flex-container {
  flex-flow: column;
}
.home-page-container .best-sellers-card-wrapper .flex-item-2 .child-flex-item {
  padding-right: 0;
  display: block;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-2
  .child-flex-item:nth-child(1) {
  height: 405px;
}
.home-page-container
  .best-sellers-card-wrapper
  .flex-item-2
  .child-flex-item:nth-child(2) {
  flex: 1 1 auto;
}

.home-page-container .recently-viewed-card-wrapper .recently-viewed-swiper {
  padding: 20px 27px 70px;
}
.home-page-container .recently-viewed-card-wrapper .rv-carousel-item {
  width: 320px;
  margin-right: 32px;
}
.home-page-container .recently-viewed-card-wrapper .see-all-link-box {
  display: none;
}
.home-page-container .recently-viewed-card-wrapper .rv-carousel-card {
  display: block;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-card-name {
  color: #1f1f1f;
  font-size: 24px;
  margin-top: 25px;
  margin-bottom: 5px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  font-weight: 600;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-card-des {
  font-size: 16px;
  color: #636466;
  margin-bottom: 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;

  font-weight: normal;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-amount-box {
  display: block;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-amount-box
  .price {
  font-size: 20px;
  color: #1f1f1f;
  margin-bottom: 10px;
  margin-right: 5px;

  font-weight: 600;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-amount-box
  .mrp {
  color: #636466;
  font-size: 20px;
  margin-right: 5px;

  font-weight: 500;
  position: relative;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-amount-box
  .mrp::after {
  content: "";
  width: 100%;
  height: 1px;
  position: absolute;
  background-color: #636466;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-amount-box
  .discount {
  font-size: 15px;
  color: #1daf3a;
  text-transform: uppercase;

  font-weight: 600;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-img-box {
  width: 100%;
  height: 465px;
}
.home-page-container
  .recently-viewed-card-wrapper
  .rv-carousel-card
  .rv-card-btn {
  width: 100%;
  font-size: 16px;
  color: #1f1f1f;
  padding: 10px 15px;
  border-radius: 6px;
  background: #ffffff;
  text-transform: uppercase;
  border: 1px solid #1f1f1f;

  font-weight: bold;
}
.home-page-container .categories-view-card-wrapper .c-flex-container {
  margin-bottom: 20px;
  width: 115%;
}
.home-page-container
  .categories-view-card-wrapper
  .c-flex-container:nth-of-type(odd)
  .c-card-img-box {
  border-radius: 0px;
}
.home-page-container
  .categories-view-card-wrapper
  .c-flex-container:nth-of-type(odd)
  .c-card-img {
  border-radius: 0px;
}
.home-page-container .categories-view-card-wrapper .c-card-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .categories-view-card-wrapper .c-card-txt-box {
  text-align: center;
  padding: 3px 10px 3px;
}
.home-page-container .categories-view-card-wrapper .c-card-txt {
  color: #1f1f1f;
  font-size: 24px;
  display: block;

  font-weight: 500;
}
@media only screen and (min-width: 1000px) {
  .home-page-container .categories-view-card-wrapper .c-flex-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: normal;
    -ms-flex-align: normal;
    align-items: normal;
  }
  .home-page-container .categories-view-card-wrapper .c-flex-item {
    width: 21%;
    display: block;
    text-align: center;
    margin-right: 20px;
    height: auto;
  }
}

@media only screen and (max-width: 999px) {
  .home-page-container .categories-view-card-wrapper .c-flex-container {
    flex-direction: row;
  }
  .home-page-container .categories-view-card-wrapper .c-flex-item {
    height: auto;
    margin-top: 0 !important;
  }
  .home-page-container .categories-view-card-wrapper .swiper-pagination-bullet {
    width: 10px;
    height: 7px;
    display: inline-block;
    margin: 0 2px;
    border-radius: 12px;
    background-color: #636466;
    position: relative;
    vertical-align: middle;
    text-align: center;
    -webkit-transition: all 0.5s ease-out;
    -o-transition: all 0.5s ease-out;
    transition: all 0.5s ease-out;
  }
  .home-page-container
    .categories-view-card-wrapper
    .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    width: 25px;
  }
}
.home-page-container .mobile-app-banner-wrapper .banner-box {
  position: relative;
}
.home-page-container .mobile-app-banner-wrapper .app-btn-grp {
  bottom: 80px;
  left: 50px;
  position: absolute;
}
.home-page-container .mobile-app-banner-wrapper .link-btn {
  max-width: 170px;
  border: none;
  outline: none;
  box-shadow: none;
  background-color: transparent;
}
.home-page-container .mirraw-picks-slider-wrapper .swiper-container {
  width: 750px;
  padding-top: 50px;
  padding-bottom: 50px;
  margin: 0 auto;
}
.home-page-container .mirraw-picks-slider-wrapper .swiper-slide {
  width: 300px;
}

.home-page-container .four-cards-container .heading-box {
  display: inline-block;
  padding-top: 250px;
  text-align: center;
  padding-left: 12px;
  padding-right: 12px;
}

.home-page-container .four-cards-container.celebrity-closet .slider-box {
  padding-left: 7rem;
  padding-right: 7rem;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box.static-four {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);
}

.home-page-container .three-cards-container {
  padding-bottom: 5rem;
  background-color: #00443d;
}

.home-page-container .three-cards-container .slider-box {
  width: 100%;
  padding-left: 14.25rem;
  padding-right: 14.25rem;
  max-width: 2350px;
}

.home-page-container .four-cards-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-bottom: 3rem;
}

.home-page-container .four-cards-container .slider-box {
  padding-left: 7rem;
  padding-right: 7rem;
  max-width: 2350px;
  width: 100%;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-img-box {
  width: 100%;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-img-title
  .shop-ocasion-img-box {
  width: 100%;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-img-box
  img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.home-page-container .four-cards-container .slider-box .shop-ocasion-card {
  color: #00443d;
  font-size: 24px;
  line-height: 30px;
  padding: 0;
  text-align: center;
  font-weight: 600;
  letter-spacing: 1px;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-title {
  padding: 1rem 0;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .shop-ocasion-card
  .shop-ocasion-img-title
  .shop-ocasion-title {
  padding: 1rem 0.5rem 0.2rem 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.home-page-container .four-cards-container.celebrity-closet {
  padding-bottom: 3rem;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-description {
  color: #00443d;
  text-transform: capitalize;
  font-weight: 400;
  padding: 1rem 0.5rem 0.2rem 0.5rem;
  font-size: 20px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-price
  .mrp {
  text-decoration: line-through;
  color: #63646687;
  font-size: 18px;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-wrapper
  .swiper-slide
  .shop-ocasion-card
  .shop-ocasion-price
  .discount {
  font-size: 18px;
  color: #1daf3a;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper
  .swiper-pagination {
  padding-bottom: 2rem;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper
  .swiper-pagination
  .swiper-pagination-bullet {
  height: 10px;
  width: 10px;
  background: #808080;
}

.home-page-container
  .four-cards-container
  .slider-box
  .swiper
  .swiper-pagination
  .swiper-pagination-bullet-active {
  height: 12px;
  width: 12px;
  background: #00443d;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper
  .swiper-pagination
  .swiper-pagination-bullet-active {
  height: 12px;
  width: 12px;
  background: #ffffff;
}

.home-page-container .four-cards-container .heading {
  color: #000000;
  font-size: 48px;
  max-width: 280px;
  text-align: center;
  line-height: 80px;
  font-family: "Cormorant Garamond" !important;
  font-weight: 500;
}

.home-page-container .four-cards-container .slider-slide-btn {
  border: none;
  outline: none;
  box-shadow: none;
  margin-top: 0px;
  background-color: transparent;
}

.home-page-container .four-cards-container .cp-carousel-item .cp-img-box {
  width: 390px;
  height: 100%;
}

.home-page-container .four-cards-container .cp-carousel-item .cp-card-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}

.home-page-container .four-cards-container .slider-box .swiper-next-btn,
.home-page-container .four-cards-container .slider-box .swiper-prev-btn,
.home-page-container .three-cards-container .slider-box .swiper-next-btn,
.home-page-container .three-cards-container .slider-box .swiper-prev-btn {
  background: #5454548c;
  opacity: 1;
}

.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-next-btn,
.home-page-container
  .four-cards-container.celebrity-closet
  .slider-box
  .swiper-prev-btn {
  top: 310px;
}

.home-page-container .three-cards-container .cp-carousel-item .cp-card-title,
.home-page-container .four-cards-container .cp-carousel-item .cp-card-title {
  color: #00443d;
  font-size: 24px;
  line-height: 30px;
  padding: 20px 0;
  text-align: center;
  font-weight: bold;
}
.home-page-container .mirraw-picks-wrapper .header-img-box {
  padding: 50px;
}
.home-page-container .mirraw-picks-wrapper .header-img-box .header-img {
  max-width: 360px;
  margin: 0 auto;
  display: block;
}
.home-page-container .mirraw-picks-wrapper .mp-swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item {
  width: 360px;
  text-align: center;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-img-box {
  width: 360px;
  height: 470px;
  overflow: hidden;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-img {
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-txt-box {
  padding: 20px 10px;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-name {
  color: black;
  font-size: 24px;
  line-height: 30px;
  padding-bottom: 10px;

  font-weight: 600;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-des {
  color: black;
  font-size: 20px;
  line-height: 30px;
  padding-bottom: 10px;

  font-weight: normal;
}
.home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-amount {
  color: black;
  font-size: 32px;

  font-weight: 600;
}
.home-page-container .circular-categories-wrapper {
  display: none;
}
.home-page-container .circular-categories-wrapper .flex-container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 16px;
  justify-content: space-between;
}
.home-page-container .circular-categories-wrapper .flex-container .flex-item {
  margin-right: 16px;
  text-align: center;
  flex: 0 0 auto;
}
.home-page-container
  .circular-categories-wrapper
  .flex-container
  .flex-item:last-child {
  margin-right: 0;
}
.home-page-container .circular-categories-wrapper .a-link {
  display: block;
}
.home-page-container .circular-categories-wrapper .circular-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .circular-categories-wrapper .c-lable {
  display: block;
  font-size: 10px;
  color: #000000;
  padding-top: 5px;
  text-transform: uppercase;

  font-weight: 500;
}
.home-page-container .single-img-banner-wrapper {
  position: relative;
  z-index: 2;
  width: 100%;
  overflow: hidden;
}
img.d-block {
  width: 100%;
}
.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
  padding: 10px 80px !important;
  margin: 15px 0 !important;
}
.flash-message {
  display: flex;
  justify-content: center;
  width: 100%;
}

.best-sellers-card-wrapper .grid-container {
  display: grid;
  grid-template-columns: repeat(3, auto);
  grid-template-rows: repeat(2, auto);
  gap: 20px;
  width: 100%;
  padding: 0 2rem 2rem 2rem;
}

.best-sellers-card-wrapper .grid-container .grid-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.best-sellers-card-wrapper .grid-container .item1 {
  grid-column: 1 / 3;
  grid-row: 1 / 2;
}

.best-sellers-card-wrapper .grid-container .item2 {
  grid-column: 3 / 4;
  grid-row: 1 / 2;
}

.best-sellers-card-wrapper .grid-container .item3 {
  grid-column: 1 / 2;
  grid-row: 2 / 3;
}

.best-sellers-card-wrapper .grid-container .item4 {
  grid-column: 2 / 3;
  grid-row: 2 / 3;
}

.best-sellers-card-wrapper .grid-container .item5 {
  grid-column: 3 / 4;
  grid-row: 2 / 3;
}

.home-page-container .four-cards-container,
.home-page-container .four-cards-container.celebrity-closet {
  padding-bottom: 1rem;
}

.home-page-container .four-cards-container.celebrity-closet.last-container {
  padding-bottom: 3rem;
}


#mainContentContainer.main-content-container .home-page-container .default-image {
  max-height: 200px;
  object-fit: contain;
}

@media only screen and (max-width: 999px) {
  .home-page-container {
    padding-top: 72px;
  }
  .home-page-container .vertical-margin {
    margin: 0;
  }
  .home-page-container .horizontal-padding {
    padding: 0;
  }
  .home-page-container .circular-categories-wrapper {
    display: block;
  }
  .home-page-container .offer-three-layout-card-wrapper {
    padding: 0 7px;
  }
  .home-page-container .offer-three-layout-card-wrapper .f-item {
    padding-right: calc(var(--bs-gutter-x) * 0.2);
    padding-left: calc(var(--bs-gutter-x) * 0.2);
  }
  .home-page-container .offer-three-layout-card-wrapper .b-card-link {
    height: 130px;
  }
  .home-page-container .offer-three-layout-card-wrapper .b-card-overlay-txt {
    bottom: 10px;
    font-size: 12px;
    line-height: 15px;
  }
  .home-page-container .single-img-banner-wrapper {
    position: relative;
    z-index: 2;
  }

  .home-page-container .full-img-banner-wrapper .banner-img {
    padding: 0 0 16px 0px;
  }

  .home-page-container .exclusive-brands-carousel-card-wrapper {
    padding-top: 30px;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .exclusive-brands-swiper {
    padding: 20px 0px 40px 0px;
  }
  .home-page-container .exclusive-brands-carousel-card-wrapper .common-heading {
    font-size: 20px;
    font-family: "Cormorant Garamond" !important;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .eb-carousel-item {
    width: 155px;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .eb-carousel-card
    .eb-img-box {
    width: 130px;
    height: 155px;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .eb-txt-box
    .eb-card-title {
    font-size: 16px;
    margin-top: 10px;
    margin-bottom: 5px;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .eb-txt-box
    .eb-card-btn {
    font-size: 12px;

    font-weight: 600;
  }
  .home-page-container .exclusive-brands-carousel-card-wrapper .swiper-prev-btn,
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .swiper-next-btn {
    display: none;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    display: inline-block;
    margin: 0 2px;
    border-radius: 12px;
    background-color: #636466;
    position: relative;
    vertical-align: middle;
    text-align: center;
    -webkit-transition: all 0.8s ease-out;
    -o-transition: all 0.8s ease-out;
    transition: all 0.8s ease-out;
  }
  .home-page-container
    .exclusive-brands-carousel-card-wrapper
    .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    background: #1f1f1f;
    width: 12px;
  }
  .home-page-container .best-sellers-card-wrapper .main-flex-container {
    padding: 10px 0;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .main-flex-container
    .flex-item-1,
  .home-page-container
    .best-sellers-card-wrapper
    .main-flex-container
    .flex-item-2 {
    width: 100%;
  }
  .home-page-container .best-sellers-card-wrapper .bs-txt {
    bottom: 5px;
    font-size: 14px;

    font-weight: 600;
  }
  .home-page-container .best-sellers-card-wrapper .space {
    padding: 10px;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-1
    .child-flex-item:nth-child(1),
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-1
    .child-flex-item:nth-child(2) {
    height: 120px;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-1
    .child-flex-item:nth-child(2) {
    padding-right: 0;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-1
    .child-flex-item:nth-child(3) {
    height: 245px;
    padding: 0;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-1
    .child-flex-item:nth-child(odd) {
    padding-right: 0;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-2
    .child-flex-container {
    flex-flow: row;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-2
    .child-flex-item {
    width: 50%;
    height: 120px;
  }
  .home-page-container
    .best-sellers-card-wrapper
    .flex-item-2
    .child-flex-item:nth-child(1) {
    height: 120px;
    padding-left: 0;
  }
  .home-page-container .recently-viewed-card-wrapper {
    padding-top: 10px;
  }
  .home-page-container .recently-viewed-card-wrapper .common-heading {
    font-size: 17px;
    font-family: "Cormorant Garamond" !important;
  }
  .home-page-container .recently-viewed-card-wrapper .swiper-prev-btn,
  .home-page-container .recently-viewed-card-wrapper .swiper-next-btn {
    display: none;
  }
  .home-page-container .recently-viewed-card-wrapper .recently-viewed-swiper {
    padding: 20px 12px;
  }
  .home-page-container .recently-viewed-card-wrapper .rv-carousel-item {
    width: 160px;
    margin-right: 0;
  }
  .home-page-container .recently-viewed-card-wrapper .see-all-link-box {
    display: block;
  }
  .home-page-container .recently-viewed-card-wrapper .see-all-link-box .a-link {
    text-transform: uppercase;

    font-weight: 600;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-card-name {
    font-size: 16px;
    margin-top: 10px;
    margin-bottom: 0;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-card-des {
    font-size: 14px;
    overflow: hidden;
    margin-bottom: 0;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-amount-box
    .price {
    font-size: 13px;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-amount-box
    .mrp {
    font-size: 13px;
    margin-right: 1rem;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-amount-box
    .discount {
    font-size: 13px;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-img-box {
    width: 100%;
    height: 240px;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-card-btn {
    display: none;
  }
  .home-page-container
    .recently-viewed-card-wrapper
    .rv-carousel-card
    .rv-txt-box {
    padding: 0 10px;
  }
  .home-page-container .categories-view-card-wrapper .c-card {
    display: block;
  }
  .home-page-container .categories-view-card-wrapper .c-card-txt-box {
    padding: 10px 10px 5px;
  }
  .home-page-container .categories-view-card-wrapper .c-card-txt {
    font-size: 21px;
    line-height: 25px;
  }
  .flash-message {
    position: fixed;
    z-index: 9;
    bottom: 130px;
  }
  p.common-heading.ourExclusiveBrandsPosition {
    font-size: 20px;
    position: relative;
    top: 9px;
  }
  p.common-heading.ourExclusiveBrandsPositionNew {
    padding-top: 0.8rem;
    position: relative;
    font-size: 20px;
    bottom: 40px;
  }
}
@media only screen and (max-width: 999px) and (max-width: 999px) {
  .home-page-container
    .categories-view-card-wrapper
    .swiper-pagination-bullets {
    bottom: 0;
  }

  .home-page-container .categories-view-card-wrapper .swiper-pagination-bullet {
    width: 6px;
  }
  .home-page-container
    .categories-view-card-wrapper
    .swiper-pagination-bullet.swiper-pagination-bullet-active {
    opacity: 1;
    width: 12px;
    background-color: #1f1f1f;
  }
}
@media only screen and (max-width: 999px) {
  .home-page-container .mobile-app-banner-wrapper .app-btn-grp {
    bottom: 10px;
    left: 10px;
  }
  .home-page-container .mobile-app-banner-wrapper .link-btn {
    max-width: 70px;
  }
  .home-page-container .four-cards-container.celebrity-closet {
    margin-top: 0;
  }
  .home-page-container .four-cards-container .heading-box {
    padding: 75px 12px 0 12px;
  }
  .home-page-container .four-cards-container .slider-box {
    top: 10%;
  }
  .home-page-container .four-cards-container .heading {
    font-size: 17px;
    max-width: 100px;
    line-height: 15px;
    margin-top: 1rem;
    color: #000000;
  }
  .home-page-container .four-cards-container .slider-slide-btn {
    margin-top: 10px;
  }
  .home-page-container .four-cards-container .slider-slide-btn svg {
    width: 24px;
    height: 24px;
  }
  .home-page-container .four-cards-container .cp-carousel-item {
    width: 115px;
  }
  .home-page-container .four-cards-container .cp-carousel-item .cp-img-box {
    width: 114%;
    height: 222px;
  }
  .home-page-container .four-cards-container .cp-carousel-item .cp-card-title {
    font-size: 14px;
    line-height: 20px;
    padding: 15px 0;
  }
  .home-page-container .mirraw-picks-wrapper {
    min-height: 400px;
    margin: -25px 0px -25px 0px;
  }
  .home-page-container .mirraw-picks-wrapper .header-img-box {
    padding: 30px;
  }
  .home-page-container .mirraw-picks-wrapper .header-img-box .header-img {
    max-width: 165px;
  }
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item {
    width: 210px;
  }
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-img-box {
    width: 210px;
    height: 290px;
  }
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-txt-box {
    padding: 5px;
  }
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-name,
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-amount {
    color: black;
    line-height: 16px;
    font-size: 14px;
  }
  .home-page-container .mirraw-picks-wrapper .mp-carousel-item .mp-card-des {
    color: black;
    line-height: 16px;
    font-size: 12px;
  }
}

@media only screen and (max-width: 1870px) {
  .home-page-container .four-cards-container .slider-box,
  .home-page-container .three-cards-container .slider-box {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .home-page-container .four-cards-container.celebrity-closet .slider-box {
    padding-left: 5rem;
    padding-right: 5rem;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-next-btn,
  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-prev-btn {
    top: 50%;
  }
}
@media screen and (max-width: 1500px) {
  .home-page-container
    .four-cards-container
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-card
    .shop-ocasion-img-box {
    position: relative;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-card {
    position: relative;
  }

  .home-page-container
    .four-cards-container
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-title {
    font-size: 18px;
    padding: 0.2rem 1.2rem;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-img-title {
    position: relative;
  }

  .home-page-container
    .four-cards-container
    .slider-box
    .swiper
    .swiper-pagination {
    padding-bottom: 2rem;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-description {
    padding: 0.5rem 0.5rem 0.2rem 0.5rem;
    font-size: 16px;
    line-height: 20px;
  }

  .best-sellers-card-wrapper .grid-container {
    gap: 12px;
    padding: 0 1rem 1rem 1rem;
  }

  .home-page-container .categories-view-card-wrapper .c-flex-container {
    width: 114%;
  }
}

@media only screen and (max-width: 1200px) {
  .home-page-container .four-cards-container .slider-box,
  .home-page-container .three-cards-container .slider-box {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .home-page-container .four-cards-container.celebrity-closet .slider-box {
    padding-left: 6rem;
    padding-right: 6rem;
  }

  .home-page-container .categories-view-card-wrapper .c-flex-container {
    width: 111%;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box.static-four {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

@media screen and (max-width: 1000px) {
  .home-page-container .main-banner .swiper-pagination-bullet {
    height: 7px;
    width: 7px;
  }
  .home-page-container
    .main-banner
    .swiper-pagination-bullet.swiper-pagination-bullet-active {
    height: 10px;
    width: 10px;
  }
  .best-sellers-card-wrapper .grid-container {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(3, auto);
  }

  .best-sellers-card-wrapper .grid-container .item1,
  .best-sellers-card-wrapper .grid-container .item2,
  .best-sellers-card-wrapper .grid-container .item3,
  .best-sellers-card-wrapper .grid-container .item4,
  .best-sellers-card-wrapper .grid-container .item5 {
    grid-column: auto;
    grid-row: auto;
  }

  .best-sellers-card-wrapper .grid-container .item1 {
    grid-column: 1 / 3;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box.static-four {
    padding: 0 2rem;
  }

  .home-page-container .three-cards-container {
    padding-bottom: 4rem;
  }

  .home-page-container
    .four-cards-container
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-title {
    font-size: 16px;
  }
}

@media only screen and (max-width: 800px) {
  .home-page-container .four-cards-container .slider-box,
  .home-page-container .three-cards-container .slider-box {
    padding-left: 0;
    padding-right: 0;
  }

  .home-page-container .categories-view-card-wrapper .four-cards-title,
  .home-page-container .four-cards-container .four-cards-title,
  .home-page-container .three-cards-container .three-cards-title,
  .home-page-container .four-cards-title.grid-title {
    font-size: 24px;
  }

  .home-page-container .four-cards-container.celebrity-closet .slider-box {
    padding-left: 0;
    padding-right: 0;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box.static-four {
    padding: 0 1rem;
  }

  .home-page-container .four-cards-container .slider-box .shop-ocasion-card {
    font-size: 18px;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .shop-ocasion-card
    .shop-ocasion-img-title
    .shop-ocasion-title {
    padding: 0;
    font-size: 14px;
  }
}
@media only screen and (max-width: 500px) {
  .home-page-container .main-banner .swiper-pagination-bullet {
    height: 5px;
    width: 5px;
  }
  .home-page-container
    .main-banner
    .swiper-pagination-bullet.swiper-pagination-bullet-active {
    height: 8px;
    width: 8px;
  }
  .home-page-container
    .four-cards-container
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-img-box,
  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-img-title
    .shop-ocasion-img-box,
  .home-page-container .three-cards-container .cp-img-box {
    height: auto;
    width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-img-title
    .shop-ocasion-img-box {
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .home-page-container .three-cards-container .cp-img-box {
    object-fit: cover;
  }

  .home-page-container
    .four-cards-container.celebrity-closet
    .slider-box
    .swiper-wrapper
    .swiper-slide
    .shop-ocasion-card
    .shop-ocasion-description {
    font-size: 14px;
  }

  .home-page-container
    .four-cards-container
    .slider-box
    .swiper
    .swiper-pagination {
    padding-bottom: 1rem;
  }

  .home-page-container .three-cards-container {
    padding-bottom: 3rem;
  }
}
