//= require 'swiper-bundle.min'
//= require 'photoswipe'
//= require 'photoswipe-ui-default'
//= require jquery.elevatezoom
//= require fancyzoom.min
//= require jquery.fancybox
//= require jquery.fancybox-buttons
//= require jquery.fancybox-thumbs

# for sticky BUY NOW button
adjustActionButton = ->
  if ($('#place_holder_for_action_btns').length > 0)
    scrollBottom = $(window).scrollTop() + $(window).height()
    if scrollBottom > $('#place_holder_for_action_btns').offset().top
      $('#action_buttons').removeClass 'fixed_cart_buttons'
    if scrollBottom < $('#place_holder_for_action_btns').offset().top
      $('#action_buttons').addClass 'fixed_cart_buttons'
  return

afterWindowOrTrubolinksLoad ->
  if $('#add_to_cart').length or $('.buy_now_sticky').length
    $('#line_items_count').css 'bottom', '8%'
    adjustActionButton()
    $(document).on 'scroll', ->
      adjustActionButton()
      return
  return


afterWindowOrTrubolinksLoad ->
  addElevateZoom()
  changeColorbox = (tag) ->
    $('#master').attr('data-id', $(tag).attr('data-image-id'))
    $('#master').attr('src', $(tag).attr('data-image-id'))

  $('#design_gallery a').click ->
    $('#design_gallery a').removeClass 'active'
    $(this).addClass 'active'
    changeColorbox(this)
    
addElevateZoom = () ->
  #image magnification and fancybox
  if $("#master").length > 0
    $("#master").elevateZoom
      gallery: "gal1"
      cursor: "pointer"
      galleryActiveClass: "active"
      gallery: "gal1"
      zoomWindowWidth: 200
      zoomWindowHeight: 200
      zoomWindowOffetx: 10


  #pass the images to Fancybox
  $("#master").bind "click", (e) ->
    ez = $("#master").data("elevateZoom")
    $.fancybox ez.getGalleryList(),
      openEffect: "none"
      closeEffect: "none"
      prevEffect: "fade"
      nextEffect: "fade"
      prevSpeed: "fast"
      nextSpeed: "fast"
      helpers:
        thumbs:
          width: 50
          height: 50
      afterLoad: ->
        if this.group.length < 2
          this.helpers.thumbs = true


$(document).on 'click', '.design-info-blocks a.main', (e) ->
  e.preventDefault()
  collapse = $(this).attr('href')
  $(this).find('span').toggle()
  $(collapse).slideToggle 'fast'
  # if $('#add_to_cart').length or $('.buy_now_sticky').length
  #  adjustActionButton()
  return

$(document).on 'click', '.design-info-sub-blocks a', (event) ->
  event.preventDefault()
  is_visible = $($(this).attr('href')).is(':visible')
  $('.sub-specs-table').hide()
  $('.sub-grp span.button-icon').removeClass 'button-toggle'
  if !is_visible
    $(this).find('span.button-icon').addClass 'button-toggle'
    $($(this).attr('href')).show()
  return

createReview = (design_id, rating, review) ->
  $.ajax
    url: '/save_review'
    type: 'Post'
    data:
      design_id: design_id
      rating: rating
      review: review
    datatype: 'JSON'
    success: (data) ->
      if data['status'] == 200
        if review == 'false'
          $('#save-alert-message').show().css('color', '#21a95a').text 'Rating Saved Successfully'
          $('#save-alert-message').data 'rating-given', rating
        else
          $('#save-alert-message').show().css('color', '#21a95a').text data['message']
          window.location.reload()
      else
        $('#save-alert-message').show().text data['message']
      return
    error: ->
  return

$(document).on 'click', '#review-submit-btn', ->
  rating = $('#form-rating-star input[name=\'score\']').val()
  review = $('textarea#review-text').val()
  design_id = $(this).data('design-id')
  $('#save-alert-message').hide()
  if review.trim().length > 30
    $('textarea#review-text').removeClass 'alert-border'
    $('#rating-alert-message, #review-alert-message').hide()
    createReview design_id, rating, review
  else
    if rating.length == 0
      $('#rating-alert-message').show()
    else if $('#save-alert-message').data('rating-given') != 0
      $('#rating-alert-message').hide()
      $('textarea#review-text').addClass 'alert-border'
      $('#review-alert-message').show()
    else
      $('#rating-alert-message').hide()
      $('textarea#review-text').addClass 'alert-border'
      $('#review-alert-message').show()
      createReview design_id, rating, 'false'
  return

# Preorder Rakhi delivery time
$(document).on 'change', '#pre-order-check', (e) ->
  estimated_rakhi = if @checked then $(this).val() else $('.delivery_day').data('date')
  $('.delivery_day').html "#{estimated_rakhi}"

$(document).on 'change', '.addon_types', ->
  $('.plus_size_message').hide();
  estd_delivery = []
  selected_addon = $(this).find(':selected')
  select_val = selected_addon.val()

  div_name = '#' + selected_addon.text().split("-")[0].trim().toLowerCase().replace(/ /g,'_')
  $('.info_message').hide()
  $(div_name).css('display','block')
  if $("#unstitch").val() == select_val && $('#stitchingModal').length > 0
    unstitched()
  if $('.custom').val() == select_val || $('.petticoat_stitching').val() == select_val || $("#unstitch").val() || $('.pre_stitch').val() == select_val
    $(this).next().addClass 'hide'
  else 
    $(this).next().removeClass 'hide'

  if selected_addon.hasClass('standard-stitch-variant')
    change_displayed_design_price($('.standard-stitch-variant').attr('selected-variant-oldprice'), $('.standard-stitch-variant').attr('selected-variant-price'))
  else if selected_addon.hasClass('custom') && selected_addon.attr('data-variant-id') != undefined
    change_displayed_design_price(selected_addon.attr('data-old-price'),selected_addon.attr('data-price'))
  date_to_select = 'delivery-date'
  $('.addon_types').each ->
    other_selected_addon = $(this).find(':selected')
    if other_selected_addon.hasClass('standard') && $('a.button.size.selected').length > 0
      size_selected = $('a.button.size.selected')
      if parseInt(size_selected.data('prodtime')) == 0
        date_to_select = 'rts-date'
    else
      size_selected = other_selected_addon
    if size_selected.data(date_to_select) != undefined && size_selected.data(date_to_select).length > 0
      delivery_time = size_selected.data(date_to_select)
    else
      delivery_time = if ($('#pre-order-check').length > 0 && $('#pre-order-check').is(':checked')) then $('#pre-order-check').val() else size_selected.attr('data-delivery-date')
    estd_delivery.push([parseInt(size_selected.data('prodtime')),delivery_time,size_selected.data('ready-to-ship')])
  max_delivery_date = estd_delivery.sort().pop()
  $('.delivery_day').html max_delivery_date[1]
  if max_delivery_date[2] == 'true'
    $('#ready_to_ship, del.strike_old_date').show()
  else
    $('#ready_to_ship, del.strike_old_date').hide()
  # if (prod_time != "0") {
  #  $('.stitching_delay_msg').html("Note : Due to Festival Rush, estimated time to deliver stitched product is 25 days from date of order.");
  # }
  # else {
  #  $('.stitching_delay_msg').html("");
  # }
  atv_element = '#atv_' + select_val
  atv_children_length = $(atv_element).find('.columns').children().length
  #got any children?
  if atv_children_length > 0 || $(atv_element).find('.size-chart-div').length > 0
    if $('.custom').val() == select_val  && $(".plus_size_blouse_custom").length > 0 
      $(".plus_size_blouse_custom").show()
      $(".plus_size_regular").hide()
    else if selected_addon.hasClass('standard') && $(".plus_size_regular").length > 0
      $(".plus_size_regular").show()
      $(".plus_size_blouse_custom").hide()
    $(atv_element).show()
    $(this).next().removeClass 'hide'
    if $('.active-plus-size').length >0 || $('.active-plus-regular').length > 0 & $('.regular_plus_options').is(':visible')
      $('.plus_size_message').show();
    if $('.regular_plus_options').is(':visible')
      $('.plus_size_message').show();

  if $('.petticoat_stitching_div').val() == select_val
    $('.addon_name_shapewear').hide()   
  else if $('.shapewear').val() == select_val 
    $('.addon_name_petticoat_stitching').hide()
  return

$(document).on 'change', '.addon_types', ->
  if (custom_selection = $('.addon_types').find(':selected').hasClass('custom')) && !$('.stitching_note').is(':visible')
    $('.stitching_note').removeClass('hide')
  else if !custom_selection
    $('.stitching_note').addClass('hide') 
change_displayed_design_price = (old_price,new_price,delivery_date=undefined,ready_to_ship=false,design_id=undefined) ->
  if design_id != undefined
    change_discounted_price = $('.product_discount_price_'+design_id)
    change_wo_discounted_price = $('.product_price_wo_discount_'+design_id)
  else
    change_discounted_price = $('.product_discount_price')
    change_wo_discounted_price = $('.product_price_wo_discount')
  if new_price != undefined && change_discounted_price[1].innerText != new_price
    change_discounted_price.text(new_price)
    change_discounted_price.addClass "shake-effect"
    $('.changeInPriceNote').fadeIn().css 'display' , 'inline-flex'
    setTimeout (->
      change_discounted_price.removeClass 'shake-effect'
      $('.changeInPriceNote').fadeOut()
    ), 750
    change_wo_discounted_price.text(old_price)
  if delivery_date != undefined
    if ready_to_ship == 'true'
      $('#ready_to_ship, del.strike_old_date').show()
    else
      $('#ready_to_ship, del.strike_old_date').hide()
    $('.delivery_day').html delivery_date


unstitched = ->
  if $("#unstitch").attr('popup') == 'true'
    $('.addon_types').first().next().addClass 'hide'
  if $("#unstitch").val() == $('.addon_types').find(':selected').val() && $("#unstitch").attr('popup') == 'false'
      $('.addon_types').first().next().addClass 'hide'
      $('#main-section').css({"position":"fixed","overflow":"hidden"})
      $(".fixed").css("z-index",'0')
      $("#stitchingModal").show()
      $("#unstitch").attr('popup','true')
  
checkRakhiPre = ->
  status = false
  if $('#pre-order-check').length > 0
    if $('#pre-order-check').is(':checked')
      status = true
    else
      status = false
  status

$(document).on 'click', '.render_fabric_option', ->
  $('.render_fabric_option').removeClass('fabric_color_selected')
  $(this).addClass('fabric_color_selected')

$(document).on 'click', '.render_shapewear_fabric_option', ->
  $('.render_shapewear_fabric_option').removeClass('fabric_color_selected fabric_color_shapewear_selected')
  $(this).addClass('fabric_color_selected fabric_color_shapewear_selected')

checkAddons = -> 
  status = true
  if $('.addon_types').is(':visible') 
    $('.addon_types').each ->
      dataName = $(this).data('name')
      if $(this).val() ==''
        alert('please select' + " " + dataName)
        return status = false
  if $('.standard_size').is(':visible') && $('.standard_size').length > 0 && $('.standard_size.selected').length != 1 && (!$('.regular_plus_options').is(':visible') && !$('.custom_hide_option_type').is('.visible'))
    alert('Please Select a Standard Size')
    return status = false
  $('.dropdown-div').each ->
    if $(this).is(':visible') && $('.addon_types').is(':visible')
      dataName = $(this).data('name')
      selectedValue = $(this).find('select').val()
      if selectedValue == '0' 
        alert('Please' + " " + dataName)
        return status = false
      else if $('.render_fabric_option').is(':visible') && !$('.render_fabric_option').hasClass('fabric_color_selected')
        dataName = $('.plus_size_color_pallete').data('name')
        alert('please' + " " + dataName)
        return status = false
      else if $('.render_shapewear_fabric_option').is(':visible') && !$('.render_shapewear_fabric_option').hasClass('fabric_color_shapewear_selected')
        dataName = $('.shapewear_color_pallete').data('name')
        alert('please' + " " + dataName)
        return status = false     
  return status

checkVariantSelection = ->
  status = true
  $('.select-size').removeClass 'shake-effect'
  if $('.variant, .variant_stitch').is(':visible')
    status = false
    if $('.variant.selected, .variant_stitch.selected').length == 1 || $('.variant.selected').length > 1
      status = true
  if $('.variant').length < 1 and $('.variant_modal').is(':visible') && $('.variant_modal.selected').length > 1
    status = true
  if status == false
    id = $('.add_to_buy_bow').attr('design_id')
    size_modal = $('#variant_select_'+ id)
    if size_modal.length > 0
      openSizeModal(size_modal)
    $('.select-size').addClass 'shake-effect'
  status

sendAddToCartGa = (multi_ga_hash, on_same_page, country_code, ads_data) ->
  window.dataLayer = window.dataLayer || [];
  design_ids = []
  for index,ga_hash of multi_ga_hash
    design_ids.push(ga_hash.item_id)
    # ga 'ec:addProduct', ga_hash
    # ga 'ec:setAction', 'add'
    # ga 'send', 'event', 'UX', 'click', 'add to cart'
    
    total = multi_ga_hash[0].price + multi_ga_hash[0].item_customization
    multi_ga_hash[0].price = multi_ga_hash[0].price + multi_ga_hash[0].item_customization
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
    event: "ga4_add_to_cart",
    ecommerce: {
      currency: "INR",
      country_code: country_code,
      value: total,
      items: multi_ga_hash,
      contents: ads_data[0].contents,
      content_ids: ads_data[0].content_ids,
      item_ids: ads_data[0].item_ids
      }
    });
    
    window._pq = window._pq or []
    _pq.push [
      'track'
      'add_to_cart'
    ]
    # if typeof fbq != 'undefined'
      # fbq 'track', 'AddToCart',
        # value: ga_hash.price
        # currency: 'INR'
        # content_ids: ga_hash.item_id
        # content_type: 'product'
        # content_category: ga_hash.item_category
  if design_ids.length > 1
    ga 'send', 'event', 'complete_the_look', 'add_to_cart', design_ids.join('-')
  return

addToCart = (url, on_same_page, fast_mode_browser) ->
  items = []
  design_hash = {quantity: 1}
  design_hash['design_id'] = $('#product_id').html().split(' ').pop()
  design_hash['rakhi_note'] = checkRakhiPre()
  line_item_addons_attributes = []
  if $('.addon_types option.custom').is(':selected') && $('.variant_stitch').length > 0
    variant_id = $('.addon_types option.custom').attr('data-variant-id')
  else if $('.variant_stitch').length > 0
    variant_id = $('.variant_stitch.selected').attr('id')
  else
    variant_id = $('.variant.selected').attr('id')
  design_hash['variant_id'] = variant_id

  if !$('.variant_stitch').is(':visible')
    $('.addon_types').each (index) ->
      notes = ''
      size = ''
      atv_id = $(this).val()
      atv_block = $('#atv_' + atv_id)
      addon_attributes = {}
      addon_option_type_id = []
      size = $('.size.selected').attr('id')
      if atv_block.length > 0
        option_value_select = $('select', atv_block)
        if option_value_select.length > 0
          option_value_select.each ->
            if $('option:first', this).text() != $('option:selected', this).text()
              notes = notes + $('option:first', this).text() + ' : ' + $('option:selected', this).text() + ', '
            return
        $('input:checked', atv_block).each ->
          notes = notes + this.name + ', '
          addon_option_type_id.push(this.value)
          return
        if $('.render_fabric_option', atv_block).hasClass('fabric_color_selected')
          fabric_color = $('.fabric_color_selected')
          notes = notes + fabric_color.data('optionType') + ' : ' + fabric_color.data('color') + ', '
        if $('.render_shapewear_fabric_option', atv_block).hasClass('fabric_color_selected')
          fabric_color = $('.fabric_color_shapewear_selected')
          notes = notes + fabric_color.data('optionType') + ' : ' + fabric_color.data('color') + ', '
        if $('.common_addons', atv_block).size() > 0 and $('.size.selected').length > 0 and $('.size-chart').is(':visible')
          notes = notes + ' Standard Stitching Size : size-' + size
        addon_attributes['addon_option_type_id'] = addon_option_type_id
      addon_attributes['addon_type_value_id'] = atv_id
      #trim the newlines and append to addon_attributes
      addon_attributes['notes'] = notes.replace(RegExp('  ', 'g'), '')
      line_item_addons_attributes.push addon_attributes
      design_hash['line_item_addons_attributes'] = line_item_addons_attributes
      return
  items.push(design_hash)
  $('.addon_product:checked').each ->
    design_hash = {quantity: 1}
    design_hash['design_id']  = this.value
    design_hash['pair_product']  = true
    if $('#variant_select_'+this.value).length > 0
      design_hash['variant_id'] = $('#variant_select_' + this.value + ' .variant_modal.selected').attr('id')
    items.push(design_hash)
  post_data =
    line_items: items
    design_page: true
  $.ajax
    type: 'POST'
    data: post_data
    url: url
    dataType: 'JSON'
    success: (response, status, jqxhr) ->
      sendAddToCartGa response.ga_hash, on_same_page, response.country_code, response.ads_data
      if !on_same_page
        window.location.assign(response.redirect_url)
      else
        $('.cart_count').html response.cart_count
        $('.buy_now').hide()
        $('.go_to_cart').show()
        $('#add_to_cart_message').fadeIn 'slow'
        setTimeout (->
          $('#add_to_cart_message').fadeOut 'slow'
          return
        ), 2000
      return
    beforeSend: ->
      if !fast_mode_browser
        $('.progress_img').show()
        $('#design_image_block').css 'opacity', '0.4'
      return
    complete: ->
      if !fast_mode_browser
        $('.progress_img').hide()
        $('#design_image_block').css 'opacity', '1'
      return
  return

stitchingPopupChanges = ->
  $('#main-section').css({"position":"inherit"})
  $(".fixed").css("z-index",'99')

$(document).on 'click', '.variant, .variant_stitch', (e) ->
  $this = $(this)
  $(".variant-price-text").html 'Price shown is for the size selected'
  $('.go_to_cart').hide()
  $('.buy_now').show()
  if $this.hasClass('disabled') == false
    $('.variant, .variant_stitch').removeClass 'selected alert'
    $this.addClass 'selected alert'
    if $this[0].className.indexOf('variant') > 0
      id = $this.attr('id')
      $('#'+id).addClass 'selected alert'
    change_displayed_design_price($this.attr('data-old-price'),$this.attr('data-price'),$this.attr('data-delivery-date'), $this.attr('data-ready-to-ship'))
    if $('.variant_stitch').length > 0
      $('.standard-stitch-variant').attr('selected-variant-price', $('.product_discount_price').text())
      $('.standard-stitch-variant').attr('selected-variant-oldprice', $('.product_price_wo_discount').text())
  return

openSizeModal = (size_modal) ->
  size_modal.addClass 'animate'
  innerHeight = window.innerHeight
  contentHeight = size_modal.children('.content').height()
  translate = window.innerHeight - size_modal.children('.content').height()
  transform = "translate3d(0px,"+(translate-60)+"px, 0px)"
  size_modal.css "transform", transform
  size_modal.siblings('.variant-bg').css({'pointer-events':'auto', 'opacity': '1'})

closeSizeModal = ->
  $('.addon_product:checked').each ->
    size_modal = $('#variant_select_'+ this.value)
    if size_modal.length > 0 && !size_modal.find('.variant_modal').hasClass('selected') || size_modal.find('.variant_modal').hasClass('variant-selected')
      $(this).siblings('label').show()
      $(this).prop 'checked' , false
  $('.variant_selection').css 'transform', 'translate3d(0px, 110%, 0px)'
  $('.variant-bg').css({'pointer-events':'none', 'opacity':'0'})

$(document).on 'click', '.plus_size_blouse_regular', (e) ->
  element = $('.size.standard_size.button.selected.alert')
  if element.length>0
    element.removeClass('selected alert')
  $('.standard_option_values, .regular_plus_options').show()
  $('.plus_size_message').show();
  $('.plus_size_blouse_regular').addClass('plus_size_regular_btn')
  $('.plus_size_blouse_regular').addClass('.active-plus-regular')
  $('.size').removeClass 'selected'

$(document).on 'click', '.plus_size_custom', (e) ->
  $('.custom_hide_option_type').show()
  $('.plus_size_message').show();
  $('.plus_size_custom').addClass('selected_custom_plus_size')
  $('.plus_size_custom_regular').removeClass('selected_custom_plus_size')
  $('.plus_size_custom_regular').addClass('other_custom_plus_size')
  $('.plus_size_custom_regular').addClass('active-plus-size')
  $('.plus_size_custom').removeClass('other_custom_plus_size')

$(document).on 'click', '.plus_size_custom_regular', (e) ->
  $('.custom_hide_option_type').hide()
  $('.plus_size_custom_regular').removeClass('active-plus-size')
  $('.plus_size_message').hide();
  hide_options_plus_size = $('select', '.custom_hide_option_type')
  $('.render_fabric_option').removeClass('fabric_color_selected')
  if hide_options_plus_size.length > 0
    hide_options_plus_size.each ->
      $(this).find('option:eq(0)').prop('selected', true)
  $('.plus_size_custom_regular').addClass('selected_custom_plus_size')
  $('.plus_size_custom').addClass('other_custom_plus_size')
  $('.plus_size_custom').removeClass('selected_custom_plus_size')
  $('.plus_size_custom_regular').removeClass('other_custom_plus_size')

$(document).on 'click', '.addon_product', (e) ->
  $this = $(this)
  $this.siblings('label').toggle()
  size_modal = $('#variant_select_'+this.value)
  if size_modal.length > 0
    if $this.is(':checked')
      openSizeModal(size_modal)
  else
    ga 'send', 'event', 'complete_the_look', 'addon_product_view', this.value

$(document).on 'click', '.variant_modal', (e) ->
  $this = $(this)
  $design_id = $this.attr('value')
  $parent = $this.parents('#variant_select_'+ $design_id)
  if $parent.find(this).hasClass('disabled') == false
    $parent.find('.variant_modal').removeClass 'variant-selected alert'
    $parent.find(this).addClass 'variant-selected alert'
  change_displayed_design_price($this.attr('data-old-price'),$this.attr('data-price'),$this.attr('data-delivery-date'), $this.attr('data-ready-to-ship'), $design_id)

$(document).on 'click', '.select-addon-variant', (e) ->
  $this = $(this)
  $this_design_id = $this.attr('value')
  $parent = $this.parents('#variant_select_'+ $this_design_id)
  $parent.find('.variant_modal').each ->
    if $(this).hasClass('variant-selected')
      $parent.find('.variant_modal').removeClass 'selected alert variant-selected'
      $(this).addClass 'selected alert'
      ga 'send', 'event', 'complete_the_look', 'addon_product_view', $this_design_id
  closeSizeModal()

$(document).on 'click', '.variant-bg, .close-variant-select', (e) ->
  closeSizeModal()


$(document).on 'click', '.size', (e) ->

  $('.regular_plus_options').hide()
  $('.plus_size_message').hide()
  hide_options_plus_size = $('select', '.regular_plus_options')
  $('.plus_size_blouse_regular').removeClass('plus_size_regular_btn')
  $('.plus_size_blouse_regular').removeClass('active-plus-regular')
  $('.render_fabric_option').removeClass('fabric_color_selected')
  if hide_options_plus_size.length > 0
    hide_options_plus_size.each ->
      $(this).find('option:eq(0)').prop('selected', true)
  if (message = $('#salwar_kameez_specific'))
    selected_value = parseInt($(this).text(),10)
    message.find('var:first').text(selected_value + 2)
    message.find('var:last').text(selected_value + 3)
    message.show()
    $('#salwar_kameez_default').hide()
  if $(this).data('delivery-date').length > 0
    estd_delivery = []
    prod_time = parseInt($(this).data('prodtime'))
    if prod_time == 0
      date_to_select = 'rts-date'
    else
      date_to_select = 'delivery-date'
    estd_delivery.push([prod_time,$(this).data('delivery-date'),$(this).data('ready-to-ship')])
    $('.addon_types').each ->
      if $(this).is(':checked') && !$(this).hasClass('standard')
        estd_delivery.push([parseInt($(this).data('prodtime')),$(this).data(date_to_select),$(this).data('ready-to-ship')])
    max_delivery_date = estd_delivery.sort().pop()
    if max_delivery_date[2] == true
      $('#ready_to_ship, del.strike_old_date').show()
    else
      $('#ready_to_ship, del.strike_old_date').hide()
    $('.delivery_day').html(max_delivery_date[1])
  if $(this).hasClass('disabled') == false
    $('.size').removeClass 'selected alert'
    $(this).addClass 'selected alert'
    $('.standard_option_values').show()
    $('.std_size_chart tr').removeClass('highlight')
    $(".std_size_chart td:first-child:contains('"+$(this).text()+"')").parent('tr').addClass('highlight')
  return

$(document).on 'change', '#show_rts_products', (e) ->
  if $(this).is(':checked')
    $.each $(this).data('rts-sizes'), (index, size_button_id) ->
      $('#'+size_button_id).css('border','2px solid #7b0e1d')
  else
    $('a.button.size').css('border','1px solid #aba9a4')
    
$(document).on 'click', '.add_to_buy_bow, #buy_now', (e) ->
  stitchingPopupChanges()
  $('#stitchingModal').hide()
  if checkVariantSelection() and checkAddons() 
    if(typeof Unbxd != 'undefined')
      unbxdTrack 'addToCart', 'pid': $('#product_id').html().split(' ').pop()
    addToCart $(this).attr('data-targeturl'), false, $(this).hasClass('add_to_buy_bow')
  return
  
$(document).on 'click', '.add_to_cart, #add_to_cart', (e) ->
  stitchingPopupChanges()
  $('#stitchingModal').hide()
  if checkVariantSelection() and checkAddons() 
    if(typeof Unbxd != 'undefined')
      unbxdTrack 'addToCart', 'pid': $('#product_id').html().split(' ').pop()
    addToCart $(this).attr('data-targeturl'), true, $(this).hasClass('add_to_cart')
  return

$(document).on 'click', '.line_item_close', ->
  $('#line_items_count').fadeOut(1000)

$(document).on 'click', '.stitch', ->
  stitchingPopupChanges()
  $('#stitchingModal').hide()
  $(".standard").prop('selected', true)
  select_val = $('.addon_types').find(':selected').val()
  if $('.standard').val() == select_val
    $('.addon_types').first().next().removeClass 'hide'

$(document).on 'click', '.closeStitchingModal', ->
  stitchingPopupChanges()
  $('#stitchingModal').hide()

$(document).on 'click', '#size-chart-btn, #dynamic-size-chart-btn, #add_review, .view-more-btn', ->
  $('body').addClass('modal-open').css('margin-top', '0px')

$(document).on 'click', '.close-review-more', -> 
  $('body').removeClass('modal-open')

$(document).foundation 'reveal', animation: false

variantDefaultText = () ->
  if $('#variants_block').length > 0
    $('.variant').each ->
      if $('.product_discount_price').text().trim() == $(this).attr('data-price')
        $('.variant-price-text').html "The price shown is for size : "+ $(this).text()
        return false
variantDefaultText()

$(document).on 'turbolinks:render', ->
  applyOrbitSlider()
  variantDefaultText()
  
$ ->
  $('.aov_select').on "change", ->
    if $('#standard_height_notice').length > 0
      if (selected_val = $('#'+ this.id + ' option:selected').text().match(/\d+/g)) != null
        height = selected_val.map(Number).slice(0,2)
        height_inch = height[0] * 12
        if height.length > 1
          height_inch += height[1]
        img = $('#standard_height_notice').data('default')
        $.each $('#standard_height_notice').data(), (k,v) ->
          if k <= height_inch
            img = v
        $('.std_height_img').hide()
        $('#std-'+ img).show()
        if $('#standard_info_icon span').length > 0
          top_style_element = $('#standard_info_icon span')
        else
          top_style_element = $("<span style= 'font-size:11px;'></span>")
        status = top_style_element.text('For the selected size, kameez will fall ' + img.replace(/_/g,' ') + ' area')
        $('#standard_info_icon').prepend(status)
        $('#standard_height_notice').show()
        setTimeout (->
          $('#standard_height_notice').fadeOut();
        ), 3000
        $('#standard_info_icon').show()
      else
        $('#standard_height_notice').hide()
        $('#standard_info_icon').hide()

$(document).on 'click', '#standard_info_icon a', (e) ->
  $('#standard_height_notice').toggle()
  setTimeout (->
    $('#standard_height_notice').fadeOut();
  ), 3000

$(document).on 'tap', ->
  if $('#standard_height_notice').is(':visible')
    $('#standard_height_notice').hide()

applyOrbitSlider = ->
  if $('.design_image').length > 1 || $('.design_image').length == 1 && $('.design_video').length == 1
    if $('.ProductSwiperContainer').length
      swiper = new Swiper('.image-slider',
        slidesPerView: 1
        noSwiping: false
        noSwipingClass: 'swiper-slide'
        pagination:
          el: '.swiper-pagination'
          clickable: true)

  return

applyOrbitSlider()
$(document).on 'turbolinks:load', ->
  if $('.label_for_image_box').length > 0
    $('.label_for_image_box, .label_text').css 'display', 'inline-block'
applyPhotoSwipe = (currentSlide) ->
  $(window).scrollTop(100)
  # ios fix
  $('.fixed').css 'z-index', '0'
  $('#branch-banner-iframe').css 'display', 'none'
  pswpElement = document.querySelectorAll('.pswp')[0]
  items = []
  # build items array
  $('.design_image').each ->
    $this = $(this)
    $this_id = $this[0].id
    $this_width = $this[0].width
    $this_height = $this[0].height
    width = if $this_width > 2 then $this_width*1.5 else $this_width*400
    height = if $this_height > 2 then $this_height*1.5 else $this_height*400
    if $this_id == 'product_video_src'
      items.push 
       html: '<video class="design_video" id="product_video_zoom" controls muted playsinline loop user-scalable="no"><source src="'+$this.data('src')+'" type="video/mp4"></video>'
    else 
      items.push
        src: $this.data('src')
        w: width
        h: height
        title: 'Disclaimer: Slight variation in actual color vs. image is possible due to the screen resolution.'  
  
  # define options
  options =
    index: currentSlide.data('index')

  # Initializes and opens PhotoSwipe
  gallery = new PhotoSwipe(pswpElement, PhotoSwipeUI_Default, items, options)

  gallery.listen 'gettingData', (index, item) ->
    img = new Image
    img.src = item.src
    if img.width != 0
      item.w = img.width
      item.h = img.height
    return

  gallery.listen 'afterChange', (item) ->
    $current_element = gallery.currItem.html
    if $current_element != undefined
        $('#product_video_zoom')[0].play()
    else if $('#product_video_zoom')[0]
         $('#product_video_zoom')[0].pause()
         $('#product_video_zoom')[0].muted = true
    return
  
  gallery.listen 'destroy', ->
    $('.fixed').css 'z-index', '99'
    $('#branch-banner-iframe').fadeIn()
  gallery.init()
  return

$(document).on 'click', '.design_image', (e) ->
  applyPhotoSwipe($(this))

$(document).on 'click', '#share-now-button', (e) ->
  shareEarnURL($(this))
  ga 'send', 'event', 'UX', 'click', 'share and earn'

shareEarnURL = (evnt) ->
  $this = evnt
  if signedIn()
    text = 'check this out'
    url = $this.attr('data-d-link') + "?utm_medium=mirraw_mobile_web&referral_campaign=share_and_earn_user_" + $this.attr('data-u-id')
    message = encodeURIComponent(text) + " - " + encodeURIComponent(url)
    whatsapp_url = "whatsapp://send?text=" + message
    window.location.href = whatsapp_url
  else
    window.location = '/accounts/sign_in'

$ ->
  $(document).scroll ->
    if $('.buy-now').length > 0
      checkout_button = $('#action_buttons')
      scroll = $(this).scrollTop()
      stop = $('.buy-now').offset().top - window.innerHeight + 15
      if $('.line_items_count_text').text() == ""
        $('#line_items_count').hide()
      else
        $('#line_items_count').show()
      if stop < scroll
        checkout_button.removeClass('fixed_button')  
      else
        # $('#line_items_count').show()
        # $('#line_items_count').hide()
        checkout_button.addClass('fixed_button')

$ -> 
  $(document).on 'click', '#check_for_pdd', (e) ->
    product_id = $('#pdd_product_id').val()
    pincode = $('#pin_code').val()
    if pincode != ''
      checkPddOnItemPage(product_id, pincode)

checkPddOnItemPage = (product_id, pincode) ->
  regexp = /(^\d{6}$)/;
  return unless product_id? && pincode?
  $.ajax
    type: 'GET'
    data:
      pincode: pincode
      id: product_id
    url: '/designs/pdd_design'
    success: (data, status, jqhxr) ->
      if data.error == false
        tdHTML = data.eta
        $('.delivery_block').show()
        $('.delivery_day').html(tdHTML).css("color","#303030")
        $('.pdd_error_message').hide()
      else if(!regexp.test(pincode))
        $('.delivery_block').hide()
        $('.pdd_error_message').show()
        $('.pdd_error_message').html("<strong>Invalid pincode.</strong> Please enter a valid pincode").css("color","red")
      else
        $('.delivery_block').hide()
        $('.pdd_error_message').show()
        $('.pdd_error_message').html('Unable to calculate, Please try another pincode').css("color","red")
$ -> 
  $(document).on 'click', '.category-banners', (e) ->
    ga_data = $(this).data('ga-data')
    window.dataLayer = window.dataLayer || [];
    dataLayer.push({ bannerDetails: null });
    dataLayer.push({
    event: "banner_click",
    bannerDetails: ga_data
    });
$(document).on 'turbolinks:load', ->
  $('.image_modal').click ->
    $('html, body').animate
      scrollTop: $('#unbxd-data-div').offset().top - 70
    , 1000

