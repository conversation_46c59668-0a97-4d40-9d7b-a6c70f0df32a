$ ->
  mirrawJsVersion = '4.0'
  if 'scrollRestoration' of history
    history.scrollRestoration = 'manual'
  $('a.left-off-canvas-toggle,li.has-submenu, .next, .previous, .add_to_cart_link').on 'click', -> {}
  $('img').error ->
    $(this)
      .attr('src', "<%= asset_path('default_image.jpg') %>")
      .addClass('default-image')

  $(document).on 'close.fndtn.reveal', '[data-reveal]', ->
    $('body').removeClass 'modal-open'
    if ($(this).attr('id') == 'mobile-subscribe-window' && !getCookie('subscribe').length)
      $('body').removeClass 'modal-top'
      $('.reveal-modal-bg').removeClass 'modal-tr-bg'
    $('.reveal-modal-bg').removeClass 'transparent_bg'
    return

  $(document).on 'open.fndtn.reveal', '[data-reveal]', ->
    $('body').addClass 'modal-open'
    if $(this).attr('id') == 'mobile-subscribe-window'
      $('body').addClass 'modal-top'
    return

  $(document).on 'opened.fndtn.reveal', '[data-reveal]', ->
    if $(this).attr('id') == 'mobile-subscribe-window'
      $('.reveal-modal-bg').addClass 'modal-tr-bg'

  window.getCookie = (cname) ->
    name = cname + '='
    ca = document.cookie.split(';')
    i = 0
    while i < ca.length
      c = ca[i]
      while c.charAt(0) == ' '
        c = c.substring(1)
      if c.indexOf(name) == 0
        return c.substring(name.length, c.length)
      i++
    ''

window.setCookie = (cname, cvalue, exdays) ->
  d = new Date
  d.setTime d.getTime() + exdays * 24 * 60 * 60 * 1000
  expires = 'expires=' + d.toUTCString()
  document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/'
  return

$(document).on 'click', 'a#view-more-top-content', ->
  if $('#top_content').hasClass('read-more')
    $(this).text 'Read Less'
    $('#top_content').removeClass 'read-more'
  else
    $(this).text 'Read More'
    $('#top_content').addClass 'read-more'
  return

$(document).on 'click', 'a#view-more-seo-post', ->
  if $('#seo_post').hasClass('read-more')
    $(this).text 'Read Less'
    $('#seo_post').removeClass 'read-more'
  else
    $(this).text 'Read More'
    $('#seo_post').addClass 'read-more'
  return

show_sticky_coupon_banner = ->
  if !getCookieValue('sticky_coupon_banner').length
    $wrapper = $('.sticky-coupon-banner .wrapper')
    setTimeout (->
      $('.sticky-coupon-banner').fadeIn()
      $('.wrapper').css 'transform', 'translate3d(0, 0, 0)'
      ), 500

$(document).on 'click', '.close-sticky-coupon-banner, .sticky-coupon-image', ->
  setCookie 'sticky_coupon_banner' , 'closed' , 7
  $('.sticky-coupon-banner').fadeOut 500

window.static_header_scroll = () ->
  if $(window).scrollTop() > 75
    $('.fixed-header').css({transform: 'translate3d(0, 0, 0)', display: 'block'})
    $('.unbxd-as-wrapper').addClass 'unbxd_as_wrapper_scroll'
  else
    $('.fixed-header').css({transform: 'translate3d(0, -50px, 0)', display: 'none'})
    $('.unbxd-as-wrapper').removeClass 'unbxd_as_wrapper_scroll'

loadCountdownScript = ->
  loadScript "<%= asset_url('countdown.min.js') %>", ->
    applyGlobalTimer()
    return
  return

applyGlobalTimer = () ->
  val_utc_cal = $('#offer_message_timer').val()
  val_cal = new Date(val_utc_cal + ' UTC')
  $('#offer_message_clock').countdown(val_cal)
    .on 'update.countdown', (event) ->
      totalDays = event.offset.totalDays
      format = if totalDays > 0
                 "<span class='deal_timer'>#{totalDays}</span>:<span class='deal_timer'>%H</span>:<span class='deal_timer'>%M</span>:<span class='deal_timer'>%S</span>"
               else
                 "<span class='deal_timer'>%H</span>:<span class='deal_timer'>%M</span>:<span class='deal_timer'>%S</span>"
      $(this).html event.strftime(format)
    .on 'finish.countdown', (event) ->
      $('#offer_message_clock').html('Offer on product has expired!').addClass 'disabled'
      setTimeout (->
        $('#offer_message_countdown').remove()
      ), 5000
loadSlick = ->
  loadScript "<%= asset_url('slick.js') %>", ->
    createSlick()
    return
  return
createSlick = () ->
  if $("#homepage_cart").length > 0
    $('.cart-slider').not('.slick-initialized').slick
      slidesToShow: 1
      slidesToShow: 1
      dots: false
      arrows: false
      autoplay: true
      autoplaySpeed: 2000
      touchThreshold: 500
  # if $(".homepage_design_blocks").length > 0
  #   $('.design-slider').not('.slick-initialized').slick
  #     slidesToShow: 2
  #     slidesToScroll: 2
  #     dots: false
  #     arrows: false
  #     infinite: false
  #     touchThreshold: 500

  if $('.banner-slider-box').length > 0
    $('.auto_scrollable_banner').not('.slick-initialized').slick
      slidesToShow: 1
      slidesToScroll: 1
      dots: true
      arrows: false
      autoplay: true
      autoplaySpeed: 2000
      touchThreshold: 500

document.addEventListener "turbolinks:before-cache", ->
  if typeof $(window).slick == 'function'
    $('.auto_scrollable_banner, .cart-slider').slick('unslick')

lazyLoadImage = ->
  $('.lazy').each ->
    img = $(this)
    if (img.is('picture'))
      img.children().each ->
        $self = $(this)
        if ($self.is('source') && !!$self.data('srcset'))
          $self.attr('srcset', $self.data('srcset'))
        else if (!!$self.data('src'))
          $self.attr('src', $self.data('src'))
          $self.show()
    else if (!!img.data('src'))
      img.attr('src', img.data('src'))
      img.show()
      
afterWindowOrTrubolinksLoad ->
  loadCallback()

loadCallback = () ->
  if $("#offer_message_timer").length > 0
    if typeof $(window).countdown == 'function'
      applyGlobalTimer()
    else
      loadCountdownScript()
  if $('#branch-banner-iframe').contents().find('body').find('#branch-banner').length <= 0
    $('#container').css 'margin-top', '0'
    $('#branch-banner-iframe').hide()
  if $('.menu-icon').length > 0
    $('.menu-icon').on 'click', ->
      $('body').addClass 'no-scroll-background'
      $('#menu-side-nav').css 'background-image', "url(<%= image_url('theme_bg_luxe.png') %>)"
      $('#menu-side-nav').css({overflowX: 'visible', width: '250px', transition: '0.3s'})
      $('.menu-accordion').show()
      $('.off-canvas-wrap').addClass('move-right')

    $('.page, .close-menu').on 'click', ->
      if $('#menu-side-nav').css('width') != '0px'
        $('body').removeClass 'no-scroll-background'
        $('#menu-side-nav').css({overflowX: 'hidden',width: '0px', transition: '0.3s'})
        $('.off-canvas-wrap').removeClass('move-right')
        $('.menu-content').removeClass('active')
        $('.accordion-navigation').removeClass('active')
        $('.menu-accordion').hide()


  $('input.search-trending').on "click", ->
    $result_box = $(this).parents('.search_margin').find('.trending-results-box')
    enable_trending_search = false
    if enable_trending_search && !$result_box.data('visited') && $('#trending-results').length == 0
      $.ajax
        url: '/trending_searches'
        type: 'GET'
        datatype: JSON
        caches: true
        success: (data) ->
          if !data.empty_response
            list = '<ul id="trending-results">'
            count = Math.min(7, data.length)
            i = 1
            while i < count
              element = data[i]
              list += '<li><a href="/search?utf8=✓&q=' + element['query'] + '&clk_src=trends">' + element['query'] + '</a></li>'
              i++
            list += '</ul>'
            $('.trending-results-text').after list
            $result_box.data 'visited', 'true'
          else
            $result_box.remove()
        error: (error) ->
          $result_box.remove()

  if $('.designs_show.page').length > 0
    design_id = $('#line_items_count').attr('pid')
    design_quantity = parseInt($('#line_items_count').data('quantity'))
    path = window.location.pathname + '/get_line_items_count?id=' + design_id
    $.ajax
      url: path
      type: 'GET'
      datatype: 'script'
      success: (data) ->
        number = parseInt(data['count'])
        if number > 0
          if design_quantity <= 2 and number > 2
            $('.line_items_count_text').html 'This product will most likely be <span>Sold Out</span> in a few hours'
          else if number > 5
            $('.line_items_count_text').html 'Fast Mover: <span>' + number + ' people </span>added this to cart today! '
          else if data['count'] == 1
            $('.line_items_count_text').html '<span>' + number + ' person </span>added this to cart today!'
          else
            $('.line_items_count_text').html '<span>' + number + ' people </span>added this to cart today!'
          $('#line_items_count').slideDown 500, ->
            setTimeout (->
              $('#line_items_count').slideUp 500
              return
            ), 5000
            return
        return
      error: (error) ->
    return

  $('input.search-trending').on
    focus: ->
      $result_box = $(this).parents('.search_margin').find('.trending-results-box')
      if !$result_box.data('visited')
        $result_box.slideDown()
    blur: ->
      $result_box = $(this).parents('.search_margin').find('.trending-results-box')
      $result_box.slideUp()
    keyup: ->
      $result_box = $(this).parents('.search_margin').find('.trending-results-box')
      $result_box.slideUp()

  $(document).on 'click','span#scroll-btn-left', ->
    $('header.scroll').animate {scrollLeft: '+=' + 175}
    $('span#scroll-btn-right').fadeIn 300

  $(document).on 'click', '#branch-banner-close', ->
    branch.closeBanner();

  $(document).on 'click','span#scroll-btn-right', ->
    $('header.scroll').animate {scrollLeft: '-=' + 175}, complete: ->
      if $(this).scrollLeft() == 0
        $('span#scroll-btn-right').fadeOut 300

  return

nextPageUrl = ->
  nextPage = $('.next-page').attr('id').split('_')[1]
  if window.location.href.indexOf('?') == -1
    nextUrl = window.location.href + '?more_items=true&page=' + nextPage
  else
    nextUrl = window.location.href.replace(/&?((pid=)|(page=))[^\&]*/, '') + '&more_items=true&page=' + nextPage
  nextUrl

window.getMoreItems = ->
  $(window).on 'scroll', ->
    static_header_scroll()
    buttonTop = $('.navigate_home_page').position().top if $('.navigate_home_page').length > 0 
    if $('.pages_home').length && $('.next-page').length > 0 and (buttonTop > $(window).scrollTop())
      nextUrl = nextPageUrl()
      if nextUrl and (buttonTop - $(window).scrollTop() < 1100)
        getNextPageData nextUrl
     getFooterPng()   
    return

getNextPageData = (nextUrl) ->
  $.ajax
    type: 'GET'
    url: nextUrl
    dataType: 'script'
    beforeSend: ->
      $('.next-page, .previous-page').hide()
      $('#more-items-loader').show()
      static_header_scroll()
      $(window).off 'scroll'
      return
    complete: ->
      $('.next-page, .previous-page').show()
      $('#more-items-loader').hide()
      getMoreItems()
      lazyLoad()
      lazyLoadImage()
      createSlick()
      applyCountdownTimer()
      return
    success: (response) ->
      response
      return
    error: (xhr, status, error) ->
  return

window.getFooterPng = ->
  if !$('#mobile_footer').data('bg_loaded') && $(document).height() - $(window).height() - $(window).scrollTop() < 1000
      $('#mobile_footer').attr('data-bg_loaded', true)
      $('#mobile_footer').css 'background-image', "url(<%= image_url('theme_bg_luxe.png') %>)" 

$ ->
  if $('.pages_home .previous-page').length
    prevPage = $('.previous-page').attr('id').split('_')[1]
    prevUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + prevPage
    $('.previous-page').attr 'href', prevUrl
  if $('.pages_home .next-page').length
    nextPage = $('.next-page').attr('id').split('_')[1]
    if window.location.href.indexOf('?') == -1
      nextUrl = window.location.href + '?page=' + nextPage
    else
      nextUrl = window.location.href.replace(/&?page=[^\&]*/, '') + '&page=' + nextPage
    $('.next-page').attr 'href', nextUrl
  getMoreItems()
  return


window.signedIn = ()->
  getCookieValue('signed_in') == '1'

$ ->

  <%# fbqRecordAddToCart = (data) -> %>
    <%# if typeof fbq != 'undefined' %>
      <%# fbq 'track', 'AddToCart', %>
        <%# value: data.price %>
        <%# currency: 'INR' %>
        <%# content_ids: data.id %>
        <%# content_type: 'product' %>
        <%# content_category: data.category %>

  <%# gaRecordAddToCart = (data) -> %>
    <%# ga 'ec:addProduct', data %>
    <%# ga 'ec:setAction', 'add' %>
    <%# ga 'send', 'event', 'UX', 'click', 'add to cart catalog' %>

  $(document).on 'click', '.add_to_cart_link', (e) ->
    dData = $(this).data()
    createLineItem dData.id

  createLineItem = (design_id) ->
    $.ajax
      type: 'POST'
      dataType: 'json'
      url: '/line_items'
      data: line_items: [{design_id: design_id, quantity: 1}]
      success: (response) ->
        <%# if response.ga_hash %>
          <%# $.each response.ga_hash, (i, ga_hash) -> %>
            <%# fbqRecordAddToCart(ga_hash) %>
            <%# gaRecordAddToCart(ga_hash) %>
        if response.url == undefined
          $('.cart_count').html response.cart_count
          return window.location.assign '/cart'
        else if response.cart_count == undefined
          if Turbolinks.supported
            return Turbolinks.visit response.url
          else
            return window.location.assign response.url
    return

  if $(document).height() <= $(window).height()
    getFooterPng()

  window.stickyButton = (id,nearid,d) ->
    start = $(this).scrollTop()
    stop = $(nearid).offset().top - window.innerHeight + (d * $(nearid).height())
    if stop < start
      $(id).removeClass('sticky-button')
    if stop > start
      $(id).addClass('sticky-button')

  acc = $('#footer-1 .accordion')
  i = undefined
  i = 0
  while i < acc.length
    acc[i].addEventListener 'click', ->
      @classList.toggle 'active'
      panel = @nextElementSibling
      if panel.style.maxHeight
        panel.style.maxHeight = null
      else
        panel.style.maxHeight = panel.scrollHeight + 'px'
      return
    i++
