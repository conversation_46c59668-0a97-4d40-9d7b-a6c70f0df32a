var MR = MR || {};
var 
MR = (function(window, document, Mirraw){

  Mirraw.product ={
    
    initVariantSelect: function(){
      $("#variantselect").on('change', function(){
       if ($(this).find("option:selected").text() == '--select--') {
        $('.var-title').addClass('shake-effect')
        $(".add_to_buy_bow").removeAttr("data-targeturl")
        }
       else {
        $(".add_to_buy_bow").attr('data-targeturl', '/line_items')
       } 
        MR.product.clearVariantSelections()
        if ($(this).find("option.selected").length == 0) {
          $(this).find("option:selected").addClass("selected alert")
          MR.product.changeDesignPrice($('.selected').attr('data-old-price'), $('.selected').attr('data-price'));
        }
      })
    },
    changeDesignPrice: function(old_price, new_price) {
      if (new_price !== old_price) {
        $('.product_discount_price').text(new_price).removeClass('shake-effect');
        $('.product_discount_price').text(new_price).addClass('shake-effect');
      }
    },  
    selectVariant: function(category_id) {
      if ($('option').find("option.selected").length == 0 && category_id == "127") {
        $(".add_to_buy_bow").removeAttr("data-targeturl")
        $('#action_buttons').on('click', function(){
        $('.var-title').addClass('shake-effect')
        MR.product.initVariantSelect();
        })
      }
    },
   
    clearVariantSelections: function() {
      return $('.variant').removeClass('selected alert');
    },
  unbxdSwiperRecommendation: function() {
    if ($("#recommendations").length) {
      $("#recommendations").each(function(index, element) {
        blaze_container = $(element)
        totalSlides = blaze_container.find('.blaze-item').length;

            // Check if the total number of slides is less than 6
        if (totalSlides < 6) {
            blaze_container.find('.blaze-prev, .blaze-next').hide(); // Hide prev and next buttons
        }
        var blazeSlider = new BlazeSlider(element[0], {
          all: {
            loop: false,
            transitionDuration: 500,
            slidesToShow: 6,
            slidesToScroll: 6,
            prevArrow: element.find('.blaze-prev')[0],
            nextArrow: element.find('.blaze-next')[0]
          },
          "(max-width: 1200px)": {
            slidesToShow: 5,
            slidesToScroll: 5,
          },
          "(max-width: 768)": {
            slidesToShow: 3.7,
            slidesToScroll: 3.7,
          },
          "(max-width: 640px)": {
            slidesToShow: 2.7,
            slidesToScroll: 2.7,
          },
          "(max-width: 480px)": {
            slidesToShow: 1.7,
            slidesToScroll: 1.7,
          },
          "(max-width: 320px)": {
            slidesToShow: 1,
            slidesToScroll: 1,
          }
        });
        return blazeSlider;
      });
    }
  },
    
  swiperRecommendation: function() {
    if ($("#pdp-container").length) {
      $("#pdp-container").each(function(index, element) {
        element = $(element)
        var blazeSlider = new BlazeSlider(
            element[0],
            {
                all: {
                    loop: false,
                    transitionDuration: 500,
                    slidesToShow: 6,
                    slidesToScroll: 6,
                    prevArrow: element.find('.blaze-prev')[0],
                    nextArrow: element.find('.blaze-next')[0]
                },
                "(max-width: 1200px)": {
                    slidesToShow: 5,
                    slidesToScroll: 5,
                },
                "(max-width: 768)": {
                  slidesToShow: 3.7,
                  slidesToScroll: 3.7,
                },
                "(max-width: 640px)": {
                    slidesToShow: 2.7,
                    slidesToScroll: 2.7,
                },
                "(max-width: 480px)": {
                    slidesToShow: 1.7,
                    slidesToScroll: 1.7,
                },
                "(max-width: 320px)": {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                }
        });
            }
        );
    }
},

    ajaxRecommendations: function(product_id) {
      if ($('#more_from_category_container').length) {
        $.ajax({
          url: '/designs/more_from_category',
          type: 'GET',
          data: {
            id: product_id,
          },
          dataType: 'html',
          success: function(html) {
            $('#more_from_category_container').html(html);
          },
          error: function(xhr, status, error) {
            console.error('AJAX Error: ' + status + ' - ' + error);
            console.log(xhr.responseText);
          }
        });
      }
    },
    
    getDesignId: function() {
      design_id = $('.add_to_buy_bow').attr('design_id');
      MR.product.ajaxRecommendations(design_id);
    },
    init: function(){
        MR.product.initVariantSelect();
        MR.product.getDesignId();
        MR.product.selectVariant($('.variant').attr('data-category-id'));
        MR.product.unbxdSwiperRecommendation();
    },
  }
  return Mirraw; 
})(this, this.document, MR);