document.addEventListener('turbolinks:load', function() {
  const ww = document.documentElement.clientWidth;
  // ------------------------------------------------------------------
  // Set Body Top Padding For Fixed Header
  if (ww >= 1000) {
    const headerHeight = $(".desktop-header-component-container:visible").css(
      "height"
    );
    $("#bodyTagContainer").css("padding-top", headerHeight);
  }
  // ------------------------------------------------------------------
  // Mobile Menu
  $(".openSideNav").click(function () {
    $("#mobileSideNav").css("transform", "translate(0, 0)");
    $("#bodyTagContainer").addClass("noScroll");
    $("#bodyTagContainer").addClass("setRelative");
    $("#mobileMenuOverlay").addClass("toggleOverlay");
  });
  $(".closeSideNav, #mobileMenuOverlay").click(function () {
    $("#mobileSideNav").css("transform", "translate(-400px, 0)");
    $("#bodyTagContainer").removeClass("noScroll");
    $("#bodyTagContainer").removeClass("setRelative");
    $("#mobileMenuOverlay").removeClass("toggleOverlay");
  });
  // ------------------------------------------------------------------
  // Cart Sidenav Drawer
  $(".cartSidenavDrawerOpen").click(function () {
    $("#cartSidenavDrawer").css("transform", "translate(0, 0)");
    $("#bodyTagContainer").addClass("noScroll");
    $("#bodyTagContainer").addClass("setRelative");
    $("#mobileMenuOverlay").addClass("toggleOverlayForCartSidenavDrawer");
  });
  $(".cartSidenavDrawerClose, #mobileMenuOverlay").click(function () {
    $("#cartSidenavDrawer").css("transform", "translate(500px, 0)");
    $("#bodyTagContainer").removeClass("noScroll");
    $("#bodyTagContainer").removeClass("setRelative");
    $("#mobileMenuOverlay").removeClass("toggleOverlayForCartSidenavDrawer");
  });
  // ------------------------------------------------------------------
  // stopPropagation on click on card
  $(".nestedClickStopPropagation").click(function (event) {
    event.stopPropagation();
    event.preventDefault();
  });
  // ------------------------------------------------------------------
  // Catalog Page Accordion Scrollbar
  $(".filterCustomScrollbar").mCustomScrollbar({
    theme: "dark",
    scrollInertia: 600,
  });
  // ------------------------------------------------------------------
  // Catalog Page Set Dropdown Value on select
  $("#sortByDropdownMenuBox .dropdown-item").click(function () {
    $("#sortByDropdownMenuButton").text($(this).text());
  });
  // ------------------------------------------------------------------
  // Qucik View Modal Slider
  if ($('.quickViewModalSwiperSlider').length) {
    var quickViewModalSwiperSlider = new Swiper('.quickViewModalSwiperSlider', {
      pagination: {
        el: '.swiper-pagination',
        clickable: true,
      },
    });
  }
  // ------------------------------------------------------------------
  // Mobile Sort By Options
  $(".showSortByOptions").click(function () {
    $("#bodyTagContainer").addClass("noScroll");
    $(".sortByOptionsBox").animate({ height: "290px" }, 300);
    $(".sortByPopUpCoverBlack")
      .delay(200)
      .queue(function (next) {
        $(this).css("opacity", "1");
        $(this).css("height", "100vh");
        next();
      });
  });
  $(".hideSortByOptions, .sortByPopUpCoverBlack").click(function () {
    $("#bodyTagContainer").removeClass("noScroll");
    $(".sortByOptionsBox").delay(300).animate({ height: "0px" }, 300);
    $(".sortByPopUpCoverBlack")
      .delay(100)
      .queue(function (next) {
        $(this).css("opacity", "0");
        next();
      });
    $(".sortByPopUpCoverBlack")
      .delay(200)
      .queue(function (next) {
        $(this).css("height", "0");
        next();
      });
  });
  // ------------------------------------------------------------------
  // Mobile Product Filter
  $(".showMobileProductFilter").click(function () {
    $("#bodyTagContainer").addClass("noScroll");
    $("#mobileProductFilter").css("display", "block");
  });
  $(".hideMobileProductFilter").click(function () {
    $("#mobileProductFilter").css("display", "none");
    $("#bodyTagContainer").removeClass("noScroll");
  });
  // ------------------------------------------------------------------
  // Product Detail Page - Add to cart Toast
  // if ($('#itemAddedCartShowToast').length) {
  //   var toastTrigger = document.getElementById('itemAddedCartShowToast');
  //   var toastLiveExample = document.getElementById('itemAddedCartToastContent');
  //   if (toastTrigger) {
  //     toastTrigger.addEventListener('click', function () {
  //       var toast = new bootstrap.Toast(toastLiveExample);
  //       toast.show();
  //     });
  //   }
  // }
  // ------------------------------------------------------------------

  // ------------------------------------------------------------------
  // Default Scrollbar
  $(".defaultCustomScrollbar").mCustomScrollbar({
    theme: "dark",
    scrollInertia: 600,
  });
  // ------------------------------------------------------------------
  // Product V2 Slider
  const productV2SwiperBreakpoints = {
    0: { slidesPerView: 2.2 },
    700: { slidesPerView: 3.2 },
    1000: { slidesPerView: 3.1 },
    1240: { slidesPerView: 4.1 },
    1300: { slidesPerView: 4.1 },
    1600: { slidesPerView: 5.1 },
    1800: { slidesPerView: 6.1 },
  };
  if ($(".commonProductV2SwiperContainer").length) {
    var commonProductV2SwiperContainer = new Swiper(
      ".commonProductV2SwiperContainer",
      {
        slidesPerView: 4.3,
        spaceBetween: 30,
        freeMode: ww <= 999 ? true : false,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: productV2SwiperBreakpoints,
      }
    );
  }
  // ------------------------------------------------------------------
  // Light Gallery
  // Desktop
  // if ($('#desktopLightboxGalleryContainer').length) {
  //   const desktopContainer = document.getElementById('desktopLightboxGalleryContainer');
  //   lightGallery(desktopContainer, {
  //     speed: 500,
  //     download: false,
  //     counter: false,
  //     loop: false,
  //     thumbnail: true,
  //     exThumbImage: 'data-thumb',
  //     selector: '.desktopLightboxSelector',
  //     plugins: [lgZoom, lgThumbnail],
  //   });
  // }
  // // Mobile
  // if ($('#mobileLightboxGalleryContainer').length) {
  //   const mobileContainer = document.getElementById('mobileLightboxGalleryContainer');
  //   const plugin = lightGallery(mobileContainer, {
  //     speed: 300,
  //     download: false,
  //     counter: false,
  //     loop: false,
  //     thumbnail: true,
  //     exThumbImage: 'data-thumb',
  //     mobileSettings: {
  //       showCloseIcon: true,
  //       controls: false,
  //     },
  //     selector: '.mobileLightboxSelector',
  //     plugins: [lgZoom, lgThumbnail],
  //   });
  //   // mobileContainer.addEventListener('lgAfterOpen', () => (location.hash = '#lighGalleryPopup'));
  //   // mobileContainer.addEventListener('lgAfterClose', () => (location.hash = ''));
  //   $(window).on('hashchange', function() {
  //     if (location.hash == '') plugin.closeGallery();
  //   });
  // }

  // jQuery Zoom
  // if ($(".product-detail-page-container").length) {
  //   $(".zoomImgSelectorContainer").zoom();
  // }
  // ------------------------------------------------------------------
  // Home Page - Start

  // Main Banner
  if ($("#home_mainBannerContainer").length) {
    var home_mainBannerSwiper = new Swiper(
      "#home_mainBannerContainer",
      {
        autoplay: {
          delay: 4000
        },
        loop: true,
        speed: 1000,
        effect: "fade",
        slidesPerView: 1,
        slidesPerGroup: 1,
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
        },
      }
    );
  }
  if ($("#home_exclusiveBrandsSwiperContainer").length) {
    var home_exclusiveBrandsSwiper = new Swiper(
      "#home_exclusiveBrandsSwiperContainer",
      {
        slidesPerView: 3,
        spaceBetween: 30,
        slidesPerGroup: 2,
        speed: 1000,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          0: {
            slidesPerView: 2.5,
            pagination: {
              el: ".swiper-pagination",
              clickable: true,
            },
          },
          800: { slidesPerView: 2 },
          1100: { slidesPerView: 3.1 },
          1320: { slidesPerView: 4.1 },
          1800: { slidesPerView: 5.1 },
        },
      }
    );
  }
  if ($("#home_categoriesSwiperContainer").length && ww <= 999) {
    var home_categoriesSwiper = new Swiper("#home_categoriesSwiperContainer", {
      slidesPerView: 2,
      grid: {
        rows: 2,
      },
      slidesPerGroup: 2,
      spaceBetween: 10,
      speed: 1000,
    });
  }
  if ($("#home_shopByOccasionContainer").length) {
    var home_shopByOccasionSwiper = new Swiper(
      "#home_shopByOccasionContainer",
      {
        autoplay: false,
        slidesPerView: 1.2,
        slidesPerGroup: 1,
        loop: false,
        spaceBetween: 12,
        centeredSlides: true,
        breakpoints: {
          500: {
            slidesPerView: 1.2 
          },
          600: {
            slidesPerView: 1.5 , centeredSlides: true
          },
          700: {
            slidesPerView: 1.7 , centeredSlides: true
          },
          800: {
            slidesPerView: 2.1 , centeredSlides: false
          },
          1000: {
            slidesPerView: 2.3, centeredSlides: false, spaceBetween: 16
          },
          1200: {
            slidesPerView: 2.7 , centeredSlides: false
          },
          1500: {
            slidesPerView: 3 , centeredSlides: false, spaceBetween: 20
          }
        }
      }
    );
  }
  if ($("#home_shopByCategoryContainer").length) {
    var home_shopByCategorySwiper = new Swiper(
      "#home_shopByCategoryContainer",
      {
        autoplay: false,
        loop: true,
        speed: 2000,
        slidesPerView: 1.9,
        spaceBetween: 12,
        slidesPerGroup: 1,
        centeredSlides: true,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          500: {
            slidesPerView: 2 , centeredSlides: true
          },
          600: {
            slidesPerView: 2 , centeredSlides: true
          },
          700: {
            slidesPerView: 2.3 , centeredSlides: true
          },
          800: {
            slidesPerView: 3 , centeredSlides: true
          },
          1000: {
            slidesPerView: 3 , centeredSlides: false, spaceBetween: 12
          },
          1200: {
            slidesPerView: 4 , centeredSlides: false
          },
          1500: {
            slidesPerView: 4, centeredSlides: false , spaceBetween: 20,
          },
          2000: {
            slidesPerView: 5, centeredSlides: false , spaceBetween: 20,
          }
        }
      }
    );
  }
  if ($("#home_celebrityClosetContainer").length) {
    var home_celebrityClosetSwiper = new Swiper(
      "#home_celebrityClosetContainer",
      {
        autoplay: false,
        loop: false,
        speed: 2000,
        slidesPerView: 1.9,
        spaceBetween: 12,
        slidesPerGroup: 1,
        centeredSlides: true,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          500: {
            slidesPerView: 2 , centeredSlides: true
          },
          600: {
            slidesPerView: 2 , centeredSlides: true
          },
          700: {
            slidesPerView: 2.3 , centeredSlides: true
          },
          800: {
            slidesPerView: 3 , centeredSlides: false
          },
          1000: {
            slidesPerView: 3 , centeredSlides: false, spaceBetween: 12
          },
          1200: {
            slidesPerView: 3 , centeredSlides: false
          },
          1500: {
            slidesPerView: 3, centeredSlides: false , spaceBetween: 20,
          }
        }
      }
    );
  }
  if ($("#home_designerInFocusContainer").length) {
    var home_designerInFocusSwiper = new Swiper(
      "#home_designerInFocusContainer",
      {
        autoplay: false,
        loop: true,
        speed: 2000,
        slidesPerView: 1.9,
        spaceBetween: 12,
        slidesPerGroup: 1,
        centeredSlides: true,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        breakpoints: {
          500: {
            slidesPerView: 2 , centeredSlides: true
          },
          600: {
            slidesPerView: 2 , centeredSlides: true
          },
          700: {
            slidesPerView: 2.3 , centeredSlides: true
          },
          800: {
            slidesPerView: 3 , centeredSlides: true
          },
          1000: {
            slidesPerView: 3 , centeredSlides: false, spaceBetween: 12
          },
          1200: {
            slidesPerView: 4 , centeredSlides: false
          },
          1500: {
            slidesPerView: 4, centeredSlides: false , spaceBetween: 20,
          },
          2000: {
            slidesPerView: 5, centeredSlides: false , spaceBetween: 20,
          }
        }
      }
    );
  }
  if ($("#home_trendingNowContainer").length) {
    var home_trendingNowSwiper = new Swiper(
      "#home_trendingNowContainer",
      {
        autoplay: false,
        loop: true,
        speed: 2000,
        slidesPerView: 1.9,
        spaceBetween: 12,
        slidesPerGroup: 1,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        centeredSlides: true,
        breakpoints: {
          500: {
            slidesPerView: 2 , centeredSlides: true
          },
          600: {
            slidesPerView: 2 , centeredSlides: true
          },
          700: {
            slidesPerView: 2.3 , centeredSlides: true
          },
          800: {
            slidesPerView: 3 , centeredSlides: true
          },
          1000: {
            slidesPerView: 3 , centeredSlides: false, spaceBetween: 12
          },
          1200: {
            slidesPerView: 4 , centeredSlides: false
          },
          1500: {
            slidesPerView: 4, centeredSlides: false , spaceBetween: 20,
          },
          2000: {
            slidesPerView: 5, centeredSlides: false , spaceBetween: 20,
          }
        }
      }
    );
  }
  // Home Page - End
  // ------------------------------------------------------------------
  // User Image Upload File Preview
  if ($("#userImageUploadFile").length) {
    $("#userImageUploadFile").change(function () {
      var curElement = $("#userPortraitImage");
      console.log(curElement);
      var reader = new FileReader();
      reader.onload = function (e) {
        curElement.attr("src", e.target.result);
      };
      reader.readAsDataURL(this.files[0]);
    });
  }
  // ------------------------------------------------------------------
  // Set Gender radio btn styling
  $("input.genderRadionBtn:radio").click(function () {
    $("input.genderRadionBtn:radio[name=" + $(this).attr("name") + "]")
      .parent()
      .removeClass("genderChecked");
    $(this).parent().addClass("genderChecked");
  });
  // ------------------------------------------------------------------
  // Hide Fixed bottom button when mobile keyboard is open
  if (ww <= 999) {
    $("input[type=text], input[type=password], input[type=tel]").focus(
      function () {
        $(".mobilePositionFixedToggle").css("visibility", "visible");
      }
    );
    $("input[type=text], input[type=password], input[type=tel]").focusout(
      function () {
        $(".mobilePositionFixedToggle").css("visibility", "visible");
      }
    );
  }
  // ------------------------------------------------------------------
  // Set Dropdown Value on select
  $(".dropdownMenuBox .dropdown-item").click(function () {
    $(this)
      .parents(".dropdownMenuBox")
      .find(".dropdownMenuButton")
      .text($(this).text());
  });
  // ------------------------------------------------------------------
  // Feedback Star
  /* 1. Visualizing things on Hover - See next part for action on click */
  $("#stars li")
    .on("mouseover", function () {
      var onStar = parseInt($(this).data("value"), 10); // The star currently mouse on
      // Now highlight all the stars that's not after the current hovered star
      $(this)
        .parent()
        .children("li.star")
        .each(function (e) {
          if (e < onStar) {
            $(this).addClass("hover");
          } else {
            $(this).removeClass("hover");
          }
        });
    })
    .on("mouseout", function () {
      $(this)
        .parent()
        .children("li.star")
        .each(function (e) {
          $(this).removeClass("hover");
        });
    });
  /* 2. Action to perform on click */
  $("#stars li").on("click", function () {
    var onStar = parseInt($(this).data("value"), 10); // The star currently selected
    var stars = $(this).parent().children("li.star");
    for (i = 0; i < stars.length; i++) {
      $(stars[i]).removeClass("selected");
    }
    for (i = 0; i < onStar; i++) {
      $(stars[i]).addClass("selected");
    }
  });
  // ------------------------------------------------------------------
  // Edit Address or Add Address Toast
  var toastLiveExample = document.getElementById("addressAddedToastContent");
  var toast = new bootstrap.Toast(toastLiveExample);
  // toast.show(); // Call this to show toast
  // ------------------------------------------------------------------
  // Order Detail Page - Select Reason & Cancel Order Confiramtion Modal
  if ($("#orderCancelSelectReasonModal").length) {
    var OCSRM = new bootstrap.Modal(
      document.getElementById("orderCancelSelectReasonModal")
    );
  }
  if ($("#cancelOrderConfirmationModal").length) {
    var COCM = new bootstrap.Modal(
      document.getElementById("cancelOrderConfirmationModal")
    );
  }
  $(".onShowOrderCancelSelectReasonModal").click(function () {
    window.location.hash = "OCSRM_hash";
    OCSRM.show();
  });
  $(".onShowCancelOrderConfirmationModal").click(function () {
    OCSRM.hide();
    COCM.show();
    window.location.hash = "COCM_hash";
  });
  $(".onCloseCancelOrderConfirmationModal").click(function () {
    COCM.hide();
  });
  if ($("#orderCancelSelectReasonModal").length) {
    document
      .getElementById("orderCancelSelectReasonModal")
      .addEventListener("hide.bs.modal", function () {
        $('input[name="cancelSelectReason"]').prop("checked", false);
        var url = window.location.href;
        url = url.split("#")[0];
        window.history.replaceState({}, document.title, url);
      });
  }
  if ($("#cancelOrderConfirmationModal").length) {
    document
      .getElementById("cancelOrderConfirmationModal")
      .addEventListener("hide.bs.modal", function () {
        var url = window.location.href;
        url = url.split("#")[0];
        window.history.replaceState({}, document.title, url);
      });
  }
  $(window).on("hashchange", function (event) {
    if (window.location.hash != "#OCSRM_hash") {
      $("#orderCancelSelectReasonModal").modal("hide");
    }
  });
  $(window).on("hashchange", function (event) {
    if (window.location.hash != "#COCM_hash") {
      $("#cancelOrderConfirmationModal").modal("hide");
    }
  });
  // ------------------------------------------------------------------
  // ------------------------------------------------------------------
  // Multi Step Form
  if ($("#personalDetailsMultiStepForm").length) {
    var current_fs, next_fs, previous_fs;
    var opacity;
    $(".stepGoNext").click(function () {
      current_fs = $(this).closest("fieldset");
      next_fs = $(this).closest("fieldset").next();
      console.log({ current_fs });
      if (current_fs[0].className === "stepOneFieldset") {
        $("#stepOne").removeClass("stepActive").addClass("stepComplete");
        $("#stepTwo").addClass("stepActive");
      } else if (current_fs[0].className === "stepTwoFieldset") {
        $("#stepOne").addClass("stepComplete");
        $("#stepTwo").removeClass("stepActive").addClass("stepComplete");
        $("#stepThree").addClass("stepActive");
      }
      next_fs.show();
      current_fs.animate(
        { opacity: 0 },
        {
          step: function (now) {
            opacity = 1 - now;
            current_fs.css({
              display: "none",
              position: "relative",
            });
            next_fs.css({ opacity: opacity });
          },
          duration: 500,
        }
      );
    });
    $(".stepGoPrevious").click(function () {
      current_fs = $(this).closest("fieldset");
      previous_fs = $(this).closest("fieldset").prev();
      if (current_fs[0].className === "stepTwoFieldset") {
        $("#stepOne").removeClass("stepComplete").addClass("stepActive");
        $("#stepTwo").removeClass("stepComplete").removeClass("stepActive");
        $("#stepThree").removeClass("stepComplete").removeClass("stepActive");
      } else if (current_fs[0].className === "stepThreeFieldset") {
        $("#stepThree").removeClass("stepComplete").removeClass("stepActive");
        $("#stepTwo").removeClass("stepComplete").addClass("stepActive");
      }
      previous_fs.show();
      current_fs.animate(
        { opacity: 0 },
        {
          step: function (now) {
            opacity = 1 - now;
            current_fs.css({
              display: "none",
              position: "relative",
            });
            previous_fs.css({ opacity: opacity });
          },
          duration: 500,
        }
      );
    });
    $(".submitPersonalDetailsForm").click(function () {
      return false;
    });
  }
  // ------------------------------------------------------------------
  // Change Password Modal OTP Box
  var verificationOTPCode = [];
  $("#changePasswordModal .otp-code-inputs input[type=text]").keyup(function (
    e
  ) {
    $("#changePasswordModal .otp-code-inputs input[type=text]").each(function (
      i
    ) {
      verificationOTPCode[i] = $(
        "#changePasswordModal .otp-code-inputs input[type=text]"
      )[i].value;
    });
    const enteredOTP = Number(verificationOTPCode.join(""));
    if ($(this).val() > 0) {
      if (
        event.key == 1 ||
        event.key == 2 ||
        event.key == 3 ||
        event.key == 4 ||
        event.key == 5 ||
        event.key == 6 ||
        event.key == 7 ||
        event.key == 8 ||
        event.key == 9 ||
        event.key == 0
      ) {
        $(this).next().focus();
      }
    } else {
      if (event.key == "Backspace") {
        $(this).prev().focus();
      }
    }
  });
  // ------------------------------------------------------------------
  // Error Toast
  var errorToast = new bootstrap.Toast(
    document.getElementById("errorToastContent")
  );
  // Call this on trigger event
  // errorToast.show(); // Call this to show toast
  // ------------------------------------------------------------------
  // intl-tel-input
  const mobileNumberInput = document.querySelectorAll(".mobileNumberTelInput");
  for (var i = 0; i < mobileNumberInput.length; i++) {
    window.intlTelInput(mobileNumberInput[i], {
      preferredCountries: ["in"],
      customContainer: "intlTelInputContainer",
      initialCountry: "in",
      separateDialCode: true,
    });
  }
  $('.track_btn').click(function(event) {
    event.preventDefault();
    if ($(this).hasClass('signed-in')) {
      var orderNo = $('#product_no').val();
      window.location.href = "/orders/" + orderNo;
    }
  });
  // ------------------------------------------------------------------
});
