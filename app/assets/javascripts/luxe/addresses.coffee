# # Place all the behaviors and hooks related to the matching controller here.
# # All this logic will automatically be available in application.js.
# # You can use CoffeeScript in this file: http://coffeescript.org/
# //= require 'fuse.min.js'

# $ ->
#   # gtag 'event', 'set_checkout_option',
#     # 'checkout_step': 1
#     # 'checkout_option': 'address-collect'
#     # 'value': 1
#   validatePhoneNumber("#address_country")

# $ ->

#   zipCountryCode = undefined
#   zipRegex = undefined
#   stateListFuse = undefined
  
#   toogleDialCode = (dial_code) ->
#     if !(dial_code?) || dial_code == ''
#       $('.dial_code_block').hide()
#       $('#dial_code_text').attr('value', '')
#       $('#address_phone_conatainer').removeClass('small-10 medium-10 large-10')
#     else
#       $('.dial_code_block').show()
#       $('#dial_code_text').val('+' + dial_code)
#       $('#address_phone_conatainer').addClass('small-10 medium-10 large-10')

#   country_code = $('#country_code').val()
#   if (country_code != undefined)
#     $('#address_country').val(country_code).prop('selected', true);
#     validatePhoneNumber("#address_country")
#   $(document).on 'change', '.mark_as_default', (e) -> $(this).parent().submit()
#   if $('#address_country').length > 0
#     createStateDropDown = (state_list) ->
#       options = ''
#       $.each state_list, (index, state) ->
#         options += "<option value = '" + state + "'>" + state + "</option>"
#       options

#     paramsGetStates = (country_code, id, selected_value = $('#address_state').val()) ->
#       type: 'GET'
#       url: '/country/get_states_and_dial_code'
#       data:
#         country: country_code
#       success: (data, status, jqhxr) ->
#         options = createStateDropDown(data.state_list)
#         if options == ''
#           html_data = '<input id="address_state" name="address[state]" size="30" required=true placeholder="Enter Your State" type="text">'
#         else
#           options = "<option value = ''>Select State</option>" + options
#           html_data = "<select id=address_state name =address[state] required=true>" + options + "</select>"
#         $('#address_state').replaceWith(html_data)
#         if options != ''
#           options = {shouldSort: true, threshold: 0.85, location: 0, distance: 100, maxPatternLength: 32, minMatchCharLength: 2, keys: ["value"]};
#           stateListFuse = new Fuse($('#address_state option').slice(1), options);
#         if selected_value? && $.inArray(selected_value, data.state_list)
#           $('#address_state').val(selected_value)
#         if $('.dial_code_block').length > 0
#           toogleDialCode(data.dial_code)
#     showPincodeFormat = (country_code, id)  ->
#       type: 'GET'
#       url: '/country/' + country_code + '/get_pincode_format'
#       success: (data, status, jqhxr) ->
#         pincode_format = data[0][0]
#         zipCountryCode = data[1][0]
#         zipRegex = new RegExp(data[1][1],'i')
#         if pincode_format != "" && pincode_format != null
#           $("#showpincodefields").show()
#           $("#pincode_format_notice").text "Example : " +pincode_format
#           $('#state-container').addClass('small-6 medium-6 large-6')
#         else
#           $("#address_pincode").val("None")
#           $("#showpincodefields").hide()
#           $('#state-container').removeClass('small-6 medium-6 large-6')

#     $(document).on 'change', '#address_country', (e) ->
#       if $(this).val() != ''
#         $('#address_city').val("")
#         $('#address_pincode').val("")
#         $.ajax(paramsGetStates($(this).val(), $(this)[0].id))
#         $.ajax(showPincodeFormat($(this).val(), $(this)[0].id))

#     if $('#address_country').val() != ''
#       $.ajax(showPincodeFormat($('#address_country').val(), $('#address_country')[0].id))
#       $.ajax(paramsGetStates($('#address_country').val(), $('#address_country')[0].id))

#   $(document).on 'click', '#shipping_address', (e) ->
#     ship_value = 0
#     if $('#shipping_address:checked').length > 0
#       ship_value = 1
#     $('#ship_to_same_address').attr('value', ship_value)

#   paramInternationalFetchCityStateOptions = (country_code, pincode) ->
#     type: 'GET'
#     url: '//api.zippopotam.us/'+country_code+'/'+pincode
#     datatype: 'JSON'
#     success: (data, status, jqhxr) -> 
#       if data['places'] != undefined && data['places'][0] !=undefined
#         $("#address_city").val(data['places'][0]['place name'])
#         state_name = data['places'][0]['state']
#         if state_name == '' || state_name == undefined
#           state_code = data['places'][0]['state abbreviation']
#           if state_code == '' || state_code == undefined
#             state_name = data['places'][0]['place name']
#           else
#             state_name = state_code
#         if stateListFuse != undefined && (state_option = stateListFuse.search(state_name)[0]) != undefined
#           state_name = state_option.value
#         if state_name != undefined
#           $("#address_state").val(state_name).prop('selected', true)

#   paramsGetCityState = (pincode) ->
#     type: 'GET'
#     data:
#       pincode: pincode
#     url: '/api/v1/addresses/pincode_info'
#     datatype: 'JSON'
#     success: (data, status, jqhxr) ->
#       if data
#         $('#address_city').val(data.city_name)
#         $('#address_state').val(data.state).prop('selected', true);

#   CityStateAutoFill = () ->
#     pincode = $('#address_pincode').val()
#     country = $('#address_country').val()
#     if country == 'India'
#       if pincode.length == 6
#         $.ajax(paramsGetCityState(pincode))
#     else if (zipCountryCode?) && (zipRegex?) && zipRegex.test(pincode)
#       $.ajax(paramInternationalFetchCityStateOptions(zipCountryCode, pincode))

#   CityStateAutoFill()

#   $(document).on 'keyup paste', '#address_pincode', (e) ->
#       if e.target.id == 'address_pincode'
#         CityStateAutoFill()

#   $(document).on 'change', '#address_country', (e) ->
#     if $(this).val() == 'India'
#       $('#address_pincode').attr({type:'tel', maxlength:'6' , pattern:"^[1-9][0-9]{5}$", placeholder: 'Pincode', title: 'Please enter 6 digit pincode'})
#     else
#       $('#address_pincode').removeAttr('pattern title')
#       $('#address_pincode').attr({type:'text', maxlength:'15', placeholder: 'Zipcode'})
#     validatePhoneNumber("#address_country")

#   # $(document).scroll ->
#   #   stickyButton(address_collect_submit,address_phone,4.8)

#   $(document).on 'change', '#address_phone', (e) ->
#     if $('#address_country').val() == 'India'
#       if $('#address_phone').val().match(/^[6-9][0-9]{9}$/) == null
#         $('#address_phone').attr({title: 'Please check your Mobile Number'})
#       else
#         $('#address_phone').attr({title: 'Please enter 10 digit Mobile Number'})

# validatePhoneNumber = (country) ->
#   if $(country).val() == 'India'
#     $('#address_phone').attr({minlength:'10', maxlength: '10', pattern: "^[6-9][0-9]{9}$", placeholder: 'Mobile Number', title: 'Please enter 10 digit Mobile Number' })
#   else
#     $('#address_phone').attr({minlength:'8', maxlength:'20', placeholder: 'Mobile Number'})
#     $('#address_phone').removeAttr('pattern title')

# Commenting out this code to prevent issues on the guest checkout address page.
# Keep this commented, as it may reappear when pulling updates in the future.
