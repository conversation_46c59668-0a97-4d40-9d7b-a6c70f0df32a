module <PERSON>ts<PERSON>elper

  def get_cart_total_information(country_code, rate)
    totals = []
    item_total = @cart.items_total_without_addons(rate)
    bmgn_discounts_inr = @cart.bmgnx_discounts.round
    coupon_discounts = get_price_in_currency(@cart.discount)
    # Cart additional discounts contain BmGn discounts
    cart_additional_discounts = get_price_in_currency(@cart.additional_discounts(country_code) - bmgn_discounts_inr)
    discounts = cart_additional_discounts + coupon_discounts + get_price_in_currency(bmgn_discounts_inr)
    wallet_discounts = @cart.wallet_details(country_code)[:referral_amount]
    totals << {amount: item_total - get_price_in_currency(bmgn_discounts_inr), title: 'Item Total'}
    if coupon_discounts > 0
      totals << {amount: coupon_discounts, title: 'Coupon Discounts'}
    end
    if cart_additional_discounts > 0
      totals << {amount: cart_additional_discounts, title: 'Cart Discounts'}
    end
    totals << {amount: item_total - discounts, title: 'Sub Total', bold: true}
    final_price = item_total - discounts - wallet_discounts
    shipping = 0
    if country_code == 'IN' && @actual_country == 'India'
      essential_shipping, essential_total, all_essential = Order.get_essential_shipping(@cart.line_items) 
      if all_essential 
        shipping += essential_shipping
      else
        shipping += @cart.shipping_cost_currency(@actual_country, rate, final_price - essential_total) + essential_shipping
      end
    else
      shipping += @cart.shipping_cost_currency(@actual_country, rate, final_price)
    end
    totals << {amount: shipping, title: 'Shipping'}
    addons = @cart.addons_total(rate)
    totals << {amount: addons, title: 'Add-On Charges'} if addons > 0
    gift_wrap_price = session[:gift_wrap] && country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
    if gift_wrap_price > 0
      totals << {amount: gift_wrap_price, title: 'Gift Wrap Charge'}
    end
    if wallet_discounts > 0 && @current_user.present? && @current_user.wallet.present? && @cart.coupon_id.nil? &&
       @current_user.wallet.referral_amount > 0 && @symbol == @current_user.wallet.currency_symbol
      totals << {amount: wallet_discounts, title: 'Wallet Discounts', id: 'wallet_discount_price'}
    end
    grandtotal = final_price + shipping + gift_wrap_price + addons
    totals << { amount: grandtotal, title: 'Amount Payable', id: 'grand_total_price_cart', bold: true}
    [totals, grandtotal, shipping]
  end

  def get_wallet_referral_amount(country_code)
    wallet_discounts = @cart.wallet_details(country_code)[:referral_amount]
    if wallet_discounts > 0 && @current_user.present? && @current_user.wallet.present? && @cart.coupon_id.nil? &&
      @current_user.wallet.referral_amount > 0 && @symbol == @current_user.wallet.currency_symbol
     return wallet_discounts
    else
      return 0
    end
  end

  def add_more_items_value_for(grandtotal, shipping)
    (DOMESTIC_SHIPPING_CHARGES.keys.last.to_i - grandtotal + shipping).round(2)
  end

  # Returns the closest offer according to difference between sub total (item total - discounts)
  # and the offer minimum amount
  def get_best_offer_for_cart_text(cart, conversion_rate, country, country_code, symbol)
    text_message, best_offers = nil, []
    discount_perc, discount_rates = Promotion.additional_discount_percent(country_code)
    free_stitching_available, free_shipping_available = cart.free_stitching_on_country?(country_code), Promotion.free_shipping_on_country?(country)
    offers_present_for_country = free_shipping_available || free_stitching_available || discount_perc[0] != 0 || @prepaid_payment_promotion
    if BEST_OFFER_THRESHOLD.to_i >= 0 && offers_present_for_country
      item_total = cart.items_total_without_addons(conversion_rate)
      bmgn_discounts = (cart.bmgnx_discounts/conversion_rate).round(2)
      coupon_discounts = get_price_in_currency(@cart.discount)
      item_total -= bmgn_discounts
      sub_total = item_total - (cart.total_discounts/conversion_rate).round(2)
      free_shipping_rate =  free_shipping_available ? (Promotion.get_free_shipping(country).first/conversion_rate).round(2) : 0
      free_stitching_rate =  free_stitching_available ? (Promotion.free_stitching_at(country_code)/conversion_rate).round(2) : 0
      ship_diff, stitch_diff = (free_shipping_rate - sub_total).round(2), (free_stitching_rate - (cart.items_total_without_addons(conversion_rate) - bmgn_discounts).to_i).round(2)
      best_offers << { type: :shipping, diff: ship_diff } if ship_diff > 0
      best_offers << { type: :stitching, diff: stitch_diff } if stitch_diff > 0
      discount_rates.each_with_index do |min_discount_amount, i|
        discount_diff = ((min_discount_amount.to_i/conversion_rate) - (item_total - coupon_discounts)).round(2)
        best_offers << { type: :discount, diff: discount_diff, index: i } if discount_diff > 0
      end
      if best_offers.present?
        if ship_diff == stitch_diff && ship_diff.between?(0, (BEST_OFFER_THRESHOLD.to_i * free_shipping_rate) / 100) && stitch_diff.between?(0, (BEST_OFFER_THRESHOLD.to_i * free_stitching_rate) / 100) && (show_stitching_offer = cart.show_stitching_offer?)
          text_message = "Add items worth #{symbol} #{stitch_diff} to get FREE SHIPPING and FREE STITCHING"
        else
          best_offer = best_offers.min { |a, b| a[:diff] <=> b[:diff] }
          if best_offer.present?
            text_message = "Add items worth #{symbol} #{best_offer[:diff]} to get "
            if best_offer[:type] == :shipping && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * free_shipping_rate) / 100)
              text_message += "FREE SHIPPING"
            elsif best_offer[:type] == :stitching && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * free_stitching_rate) / 100)
              if show_stitching_offer || cart.show_stitching_offer?
                text_message += "FREE STITCHING"
              else
                text_message = nil
              end
            elsif best_offer[:type] == :discount && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * (discount_rates[best_offer[:index]].to_i / conversion_rate)) / 100)
              text_message += "extra #{discount_perc[best_offer[:index]]}% off"
            else
              text_message = nil
            end
          end
        end
      end
    end
    return text_message, discount_perc, discount_rates, offers_present_for_country
  end

end