module OrdersHelper
  def currency_params(order)
    currency = if order.paid_currency_code.present?
                  CurrencyConvert.currency_convert_memcached.find{|cc| cc.symbol == order.paid_currency_code}
                else
                  CurrencyConvert.currency_convert_cache_by_country_code(order.country_code || @country_code)
                end
    values = {}
    values[:rate] = order.paid_currency_rate || order.currency_rate || @rate
    values[:currency_symbol] = order.paid_currency_code || order.currency_code || @symbol
    values[:hex_symbol] = currency.try(:hex_symbol) || @hex_symbol
    values[:round_to] = currency.try(:round_to) || 2
    values[:market_rate] = currency.try(:market_rate) || 1
    values
  end

  def is_response_signature_valid?(params)
    signature = params[:signature]
    params.slice!('order_id', 'status', 'status_id')
    key = ENV['JUSPAY_RESPONSE_KEY']
    encoded_sorted = []
    params.keys.sort.each { |k| encoded_sorted << URI.encode(k) + "=" + URI.encode(params[k]) }
    encoded_string = CGI.escape(encoded_sorted.join("&"))
    hash_string = CGI.escape(Base64.encode64(OpenSSL::HMAC.digest(OpenSSL::Digest.new('sha256'), key, encoded_string)).strip())
    URI.decode(hash_string) == signature
  end

  def set_wallet_pay_type
    shipping_address = Address.find params["order"]["shipping_address"]
    grandtotal = @order.cart.total_currency(@rate,@country_code,shipping_address.country)
    if grandtotal <= 0 
      @order.pay_type = "Mirraw Wallet"
    else
      redirect_to_order_new('Your payment failed. Invalid Wallet Amount') and return
    end
  end



  def calculate_payu_params(order)
    # Adding Mandatory info
    params = order.payu_web_create_params
    params.merge!(key: Order::PAYU_MERCHANT_KEY)

    # Adding urls for payu
    response_url = 'https://' +
      ENV.fetch('MIRRAW_DOMAIN') +
      payu_response_orders_path
    params.merge!(curl: response_url, surl: response_url, furl: response_url)
  end

  def payu_money_info(order)
    params = calculate_payu_params(order)
    {url: "#{Order::PAYU_URL}_payment", params: params}
  end

  def payu_redirect_url(order)
    params = calculate_payu_params(order)
    response = HTTParty.post(
      "#{Order::PAYU_URL}/_payment?type=merchant_txn",
      :verify => false,
      :body => params,
      :headers => browser.opera_mini? ? {} : {'User-Agent' => request.env["HTTP_USER_AGENT"]
    })
    "#{Order::PAYU_URL}_payment_options?mihpayid=#{response.body.strip}" if response.code == 200
  end

  def order_cancelled_handler
    if cookies[:payment_failure_count].present?
      cookies[:payment_failure_count] = cookies[:payment_failure_count].to_i + 1
    else
      cookies[:payment_failure_count] = { value: 1, expires: 12.hours.from_now }
    end
    new_order_url
  end
  
  def trigger_ga4_purchase_event(order)
    return false unless ['followup', 'sane', 'new'].include?(order.state)
    return false if order.other_details['purchase_event_triggered']

    date_time = if order.sane?
                  (order.confirmed_at + 2.minutes)
                elsif order.new? && order.cod? 
                  (order.created_at + 5.minutes)
                else
                  (order.created_at + 5.minutes)
                end
    if DateTime.now <= date_time
      order.update(other_details: order.other_details.merge('purchase_event_triggered' => true))
      return true
    else
      return false
    end
  end


  def set_wallet_pay_type
    shipping_address = Address.find params["order"]["shipping_address"]
    grandtotal = @order.cart.total_currency(@rate,@country_code,shipping_address.country)
    if grandtotal <= 0 
      @order.pay_type = "Mirraw Wallet"
    else
      redirect_to_order_new('payment failed. Invalid Wallet Amount') and return
    end
  end

  def trigger_ga4_purchase_event(order)
    return false unless ['followup', 'sane', 'new'].include?(order.state)
    date_time = if order.sane?
                  (order.confirmed_at + 2.minutes)
                elsif order.new? && order.cod?
                  (order.created_at + 5.minutes)
                else
                  (order.created_at + 5.minutes)
                end
    return DateTime.now <= date_time
  end

end
