%footer.footer-component-wrapper
  .footer-container
    .footer-section-one
      .container-fluid
        .horizontal-padding-footer
          .container.accordion-wrappper
            .row-footer
              .footer-links
                .accordion-item
                  %b#headingOne.accordion-header
                    %button.accordion-button.collapsed.mobile-accordion-btn.f-link{"aria-controls" => "collapseOne", "data-bs-target" => "#collapseOne", "data-bs-toggle" => "collapse", :type => "button"}
                      company
                    %button.btn.desktop-accordion-btn.f-link{:type => "button"} company
                  #collapseOne.accordion-collapse.collapse{"aria-labelledby" => "headingOne"}
                    .accordion-body
                      %ul.link-list-wrap
                        %li.list-item
                          %a.a-link{:href => "/pages/about"} About us
                        %li.list-item
                          %a.a-link{:href => "https://blog.shopmuzai.com/", :target => "_blank"} Blog
                        %li.list-item
                          %a.a-link{:href => "/pages/faq"} FAQs
                        %li.list-item
                          %a.a-link{:href => "/pages/terms"} Terms
                        %li.list-item
                          %a.a-link{:href => "/pages/privacy"} Privacy
              .footer-links
                .accordion-item
                  %b#headingTwo.accordion-header
                    %button.accordion-button.collapsed.mobile-accordion-btn.f-link{"aria-controls" => "collapseTwo", "data-bs-target" => "#collapseTwo", "data-bs-toggle" => "collapse", :type => "button"}
                      help
                    %button.btn.desktop-accordion-btn.f-link{:type => "button"} help
                  #collapseTwo.accordion-collapse.collapse{"aria-labelledby" => "headingTwo"}
                    .accordion-body
                      %ul.link-list-wrap
                        %li.list-item
                          %a.a-link{:href => "/pages/track"} Track your order
                        %li.list-item
                          %a.a-link{:href => "/pages/faq?question=22"} How to buy
              .footer-links
                .accordion-item
                  %b#headingThree.accordion-header
                    %button.accordion-button.collapsed.mobile-accordion-btn.f-link{"aria-controls" => "collapseThree", "data-bs-target" => "#collapseThree", "data-bs-toggle" => "collapse", :type => "button"}
                      business
                    %button.btn.desktop-accordion-btn.f-link{:type => "button"} business
                  #collapseThree.accordion-collapse.collapse{"aria-labelledby" => "headingThree"}
                    .accordion-body
                      %ul.link-list-wrap
                        %li.list-item
                          %a.a-link{:href => "/pages/faq?question=73"} Buyers
              .footer-links
                .accordion-item
                  %b#headingFour.accordion-header
                    %button.accordion-button.mobile-accordion-btn.f-link{"aria-controls" => "collapseFour", "data-bs-target" => "#collapseFour", "data-bs-toggle" => "collapse", :type => "button", "aria-expanded"=> true}
                      contact us
                    %button.btn.desktop-accordion-btn.f-link{:type => "button"} contact us
                  #collapseFour.accordion-collapse.collapse.show{"aria-labelledby" => "headingFour"}
                    .accordion-body
                      %ul.link-list-wrap
                        %li.list-item
                          %a.a-link{ href: "tel:+919137407527" }
                            %span.icon-box
                              / prettier-ignore
                              %svg{:height => "14", :viewbox => "0 0 14 14", :width => "14", :xmlns => "http://www.w3.org/2000/svg"}
                                %path#Stroke-1{:d => "M6,7.449c4.576,4.81,4.646-.138,6.488,1.7,1.776,1.775,2.25,1.528,0,3.777-.282.226-2.072,2.951-8.364-3.339S.552,1.507.779,1.225c2.255-2.255,1.977-1.775,3.752,0C6.373,3.066,1.422,2.639,6,7.449Z", :fill => "#fff" ,:transform => "translate(0 0.291)"}
                            +91 91374 07527
                        %li.list-item
                          = link_to 'https://api.whatsapp.com/send?phone=9137407527&text=Hello', target: '_blank', class:'a-link' do
                            %span.icon-box
                              / prettier-ignore
                              %svg{:height => "16.073", :viewbox => "0 0 16 16.073", :width => "16", :xmlns => "http://www.w3.org/2000/svg", "xmlns:xlink" => "http://www.w3.org/1999/xlink"}
                                %defs
                                  %lineargradient#linear-gradient{:gradientunits => "objectBoundingBox", :x1 => "0.5", :x2 => "0.5", :y1 => "1"}
                                    %stop{:offset => "0", "stop-color" => "#20b038"}
                                    %stop{:offset => "1", "stop-color" => "#60d66a"}
                                %g#Group_148617{"data-name" => "Group 148617", :transform => "translate(0 0.002)"}
                                  %path#Path_41{:d => "M13.669,2.34A7.967,7.967,0,0,0,1.13,11.949L0,16.076l4.223-1.107a7.961,7.961,0,0,0,3.807.969h0a7.967,7.967,0,0,0,5.635-13.6ZM8.033,14.593h0a6.614,6.614,0,0,1-3.37-.923l-.242-.143-2.506.657.669-2.443-.158-.25a6.622,6.622,0,1,1,5.609,3.1Z", "data-name" => "Path 41", :fill => "#e0e0e0", :transform => "translate(0 -0.005)"}
                                  %path#Path_42{:d => "M27.875,36.411l1.079-3.939a7.6,7.6,0,1,1,6.588,3.806h0a7.6,7.6,0,0,1-3.631-.924L27.876,36.41Z", "data-name" => "Path 42", :fill => "url(#linear-gradient)", :transform => "translate(-27.509 -20.798)"}
                                  %rect#Rectangle_1{"data-name" => "Rectangle 1", :fill => "none", :height => "8.383", :opacity => "0.08", :transform => "translate(3.555 3.822)", :width => "8.948"}
                                  %path#Path_43{:d => "M309.321,323.373c-.147-.328-.3-.334-.443-.34-.115-.005-.246,0-.377,0a.723.723,0,0,0-.525.246,2.206,2.206,0,0,0-.689,1.641,3.827,3.827,0,0,0,.8,2.035,8.076,8.076,0,0,0,3.361,2.97c1.662.655,2,.525,2.361.492a1.987,1.987,0,0,0,1.328-.935,1.643,1.643,0,0,0,.115-.936c-.049-.082-.18-.131-.377-.23s-1.164-.575-1.345-.64-.312-.1-.443.1-.508.64-.623.771-.23.148-.426.049a5.382,5.382,0,0,1-1.582-.977,5.927,5.927,0,0,1-1.095-1.363c-.115-.2-.012-.3.086-.4s.2-.23.3-.345a1.35,1.35,0,0,0,.2-.328.362.362,0,0,0-.016-.345C309.878,324.735,309.5,323.762,309.321,323.373Z", "data-name" => "Path 43", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-303.256 -318.791)"}
                                  %path#Path_44{:d => "M20.512,2.414a7.875,7.875,0,0,0-12.395,9.5L7,15.993,11.175,14.9a7.87,7.87,0,0,0,3.763.958h0A7.875,7.875,0,0,0,20.512,2.414ZM14.941,14.527h0a6.538,6.538,0,0,1-3.332-.913l-.239-.141-2.478.649.661-2.415L9.4,11.46a6.546,6.546,0,1,1,5.545,3.066Z", "data-name" => "Path 44", :fill => "#fff", :transform => "translate(-6.908 -0.105)"}
                            +91 91374 07527
                        %li.list-item
                          %a.a-link{ href: "mailto:<EMAIL>" }
                            %span.icon-box
                              / prettier-ignore
                              %svg{:height => "14.4", :viewbox => "0 0 16 14.4", :width => "16", :xmlns => "http://www.w3.org/2000/svg"}
                                %path#Path_220136{:d => "M11.951,0A4.045,4.045,0,0,1,16,4.04h0v6.32a4.046,4.046,0,0,1-4.049,4.04h-7.9A4.045,4.045,0,0,1,0,10.36H0V4.04A4.04,4.04,0,0,1,4.048,0h7.9Zm.9,4.16a.608.608,0,0,0-.449.16h0L8.8,7.2a1.253,1.253,0,0,1-1.6,0h0L3.6,4.32a.612.612,0,0,0-.857.856h0l.1.1,3.64,2.84a2.519,2.519,0,0,0,3.127,0h0l3.609-2.888.064-.064a.619.619,0,0,0-.009-.8A.673.673,0,0,0,12.856,4.16Z", :fill => "#fff","data-name" => "Path 220136"}
                            <EMAIL>
              .footer-links.app-store-links
                .accordion-item
                  %b#headingFourF.accordion-header
                    %button.accordion-button.mobile-accordion-btn.f-link{"aria-controls" => "collapseFourF", "data-bs-target" => "#collapseFourF", "data-bs-toggle" => "collapse", :type => "button", "aria-expanded"=> true}
                      EXPERIENCE ON MOBILE
                    %button.btn.desktop-accordion-btn.f-link{:type => "button"} EXPERIENCE ON MOBILE
                  #collapseFourF.accordion-collapse.collapse.show{"aria-labelledby" => "headingFourF"}
                    .accordion-body
                    .accordion-body
                      %ul.link-list-wrap.mobile
                        %li.list-item
                          %a{:href => "https://play.google.com/store/apps/details?id=com.mirraw.android&pli=1",:target=> "_blank"}
                            = image_tag('footer/get-it-on-google-play.svg', alt: 'footer_google_play', class: 'btn-img')
                        %li.list-item
                          %a{ :href => " https://apps.apple.com/in/app/mirraw-online-shopping-app/id1112569519",:target=>"_blank"}
                            = image_tag('footer/download-on-app-store.svg', alt: 'footer_apple_store', class: 'btn-img')
            .row.second-part-footer    
              .col-12.col-xs-12.col-sm-12.col-md-12.col-lg-12.second-col              
                .footer-part          
                  .main-flex-one-container
                    .flex-item
                      %span.t-layer
                        = image_tag('IconOntimedelivery.svg', alt: 'delivery', class: 'f-img', width: '85px')
                        %span.f-txt On time delivery
                    .flex-item
                      %span.t-layer
                        = image_tag('IconShipsWorldwide.svg', alt: 'shipping', class: 'f-img', width: '85px')
                        %span.f-txt Ships Worldwide
                    .flex-item
                      %span.t-layer
                        = image_tag('Icon24_7support.svg', alt: 'support', class: 'f-img', width: '85px')
                        %span.f-txt 24/7 Customer support
              .col-12.col-xs-12.col-sm-12.col-md-12.col-lg-12.second-coll          
                .footer-part-2            
                  .keep-in-touch
                    %ul.link-list-wrap.social-icon-wrap
                      %li.list-item.whatsapp  
                        %a{:href => "https://api.whatsapp.com/send?phone=919137407527&text=Hello", :target=>"_blank"}
                          %img{ src: asset_path('icons8-whatsapp.svg',  alt: "Whatsapp-icon")}
                      %li.list-item
                        %a{:href => "https://www.instagram.com/muzai.co/", :target=>"_blank"}
                          %img{ src: asset_path('icons8-instagram.svg',  alt: "Instagram-icon")} 
                      %li.list-item.pintrest
                        %a{:href => "https://in.pinterest.com/shopmuzai", :target=>"_blank"}
                          %img{ src: asset_path('icons8-pinterest.svg',  alt: "Pintrest-icon")}
      .footer-sectin-two.m-view
        .footer-background-image 
          %img.design_image{ src: asset_path('Footer.png') }         
    -if controller.controller_name != 'orders' && controller.controller_name != 'addresses' && controller.controller_name != 'sessions'
      -if true
        .mobile-share-whatsapp-wrapper
          = link_to 'https://api.whatsapp.com/send?phone=9137407527&text=Hello', target: '_blank' do
            = image_tag('wlivechat.png', alt: 'Muzai whatsapp live chat', class: 'btn-share-whatsapp')
%script{:crossorigin => "anonymous", :src => "https://kit.fontawesome.com/ad05349c4e.js"}
- content_for :page_specific_js_body do
  = javascript_include_tag 'app', :defer => true, 'data-turbolinks-track' => 'reload'
:javascript
  afterWindowOrTrubolinksLoad(function(){
    loadScript('#{asset_url("app.js")}');
  })
  document.addEventListener('turbolinks:load', function() {
    loadScript('#{asset_url("app.js")}');
  });
  if (typeof(Turbolinks) != 'undefined') {
    loadScript('#{asset_url("app.js")}');
  }