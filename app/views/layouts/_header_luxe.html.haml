= javascript_include_tag 'luxe/login.js'
= stylesheet_link_tag 'luxe/login_luxe'
%header.desktop-header-component-container
  - if !is_mobile_view?
    .offer-message.alert-info.offer-deal-message-wrappper
      .offer-message-block.message-block
        %p.message-block.offer-txt
          -if @offer_message.present?
            =raw @offer_message
          - else
            =raw "For Personal Assistance, you can WhatsApp us on"
            = link_to "+91 9137407527.  ",'https://api.whatsapp.com/send?phone=919137407527&text=Hello', target: '_blank', style: 'color:#f56a6a;;'
        - if !is_mobile_view? && (controller_name == 'pages' || controller_name == 'store' || controller_name == 'designs')
          - if @timer_promotion.present?
            #offer_message_countdown.offer-countdown-block
              %span.deal-end-txt Deal ends in
              %span#offer_message_clock.offer-timer
              %input{id:'offer_message_timer', type: 'hidden', value: @timer_promotion.end_date.getlocal.utc.strftime("%Y/%m/%d %H:%M:%S")}

      / %a.close{"data-dismiss" => "alert-info"} ×
  / .offer-deal-message-wrappper
  /   .message-block
  /     %p.offer-txt
  /       Avail FLAT
  /       %span ₹500
  /       OFF on orders above
  /       %span ₹2999
  /     .offer-countdown-block
  /       %p.deal-end-txt
  /         Deal ends in
  /         %span#offerTimer.offer-timer 01:22:59
  .header-with-searchbar-wrapper
    .main-flex-container
      .empty-container
      .flex-item-1.logo-box
        %a{href: '/'}
          =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img')
      .right-navbar-content
        .search-box
          = render partial:  'layouts/search_form_luxe'
        .actions-box
          .dropdown
            #profile-btn.profile-action-wrap.m-pointer.dropbtn{class: account_signed_in? ? 'signed-in' : 'not-signed-in'}
              / prettier-ignore
              %svg#Profile_in-active_W_O_Img{"data-name" => "Profile in-active/ W O Img", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                %g#Rectangle_24115{"data-name" => "Rectangle 24115", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
                  %rect{:height => "24", :stroke => "none", :width => "24"}
                  %rect{:fill => "none", :height => "23", :width => "23", :x => "0.5", :y => "0.5"}
                %g#Iconly_Bold_Profile{"data-name" => "Iconly/Bold/Profile", :transform => "translate(0.8 1)"}
                  %g#Profile{:transform => "translate(4 2)"}
                    %path#Path_220122{:d => "M7.2,16.5a14.812,14.812,0,0,0,4.562-.528c1.138-.4,1.138-.827,1.138-1.033s0-.643-1.137-1.045A14.612,14.612,0,0,0,7.2,13.357a14.812,14.812,0,0,0-4.562.528c-1.138.4-1.138.827-1.138,1.033s0,.643,1.137,1.045A14.611,14.611,0,0,0,7.2,16.5m0-8.475a3.247,3.247,0,1,0-2.313-.951A3.248,3.248,0,0,0,7.2,8.025M7.2,18C3.3,18,0,17.365,0,14.917s3.317-3.061,7.2-3.061c3.9,0,7.2.634,7.2,3.083S11.083,18,7.2,18Zm0-8.475a4.762,4.762,0,1,1,4.765-4.763A4.747,4.747,0,0,1,7.2,9.525Z", "data-name" => "Path 220122", :transform => "translate(0 0)"}
            .dropdown-content
              - if account_signed_in?
                = link_to 'Wallet', user_wallet_path
                = link_to 'Profile' , user_profile_path
                = link_to 'Order Details' , '/orders'
                = link_to 'Address', user_addresses_path
                = link_to 'Logout', destroy_account_session_path, method: :delete
            - if !is_mobile_view?
              #loginModal.modal.fade{aria_labelledby: "loginModalLabel", aria_hidden: "true", tabindex: "-1"}
                .modal-dialog
                  .modal-content
                    .modal-header
                      %a{href: '/'}
                        =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img-in-modal')
                      %button.btn-close{"aria-label": "Close", "data-bs-dismiss": "modal", type: "button"}
                    .modal-body
                      = render partial: 'luxe/shared/login_page' , locals: { modalId: '#loginModal'}
              #registerModal.modal.fade{aria_labelledby: "registerModalLabel", aria_hidden: "true", tabindex: "-1"}
                .modal-dialog
                  .modal-content
                    .modal-header
                      %a{href: '/'}
                        =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img-in-modal')
                      %button.btn-close{"aria-label": "Close", "data-bs-dismiss": "modal", type: "button"}
                    .modal-body
                      = render partial: 'luxe/shared/registration_page' , locals: { modalId: '#registerModal'}

          = link_to user_wishlists_path do
            .wishlist-action-wrap.m-pointer{id: 'desktop-wishlist-btn', class: account_signed_in? ? 'signed-in' : 'not-signed-in'}
              / prettier-ignore
              %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
          = link_to cart_path do
            .cart-action-wrap.m-pointer
              / prettier-ignore
              %span.cart-count= "#{session[:cart_count]}"
              %svg#icon_orders{"data-name" => "icon/orders", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                %rect#Rectangle_23418{"data-name" => "Rectangle 23418", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                %path#XMLID_1732_{:d => "M17.961,14.189l-2.093,6.593a1.109,1.109,0,0,1-1.057.783H6.73a1.154,1.154,0,0,1-1.076-.724L2.563,12.565H.783A.783.783,0,1,1,.783,11H3.111a.8.8,0,0,1,.743.528L7.043,20h7.435l1.663-5.283H6.926a.783.783,0,1,1,0-1.565H17.217a.778.778,0,0,1,.626.333.759.759,0,0,1,.117.7ZM7.122,22.6a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,7.122,22.6Zm7.024,0a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,14.146,22.6Z", :transform => "translate(3 -6.073)"}
  .header-mega-menu-wrapper
    %nav.container-layout-max
      %ul.nav-link-wrap
        - @menu_list.each do |menu|
          - menu_columns = menu.menu_columns
          - menu_columns = menu_columns.sort_by(&:position)
  
          %li.nav-link-list
            %a.action-link{:href => menu.link}= menu.title
            .mega-menu-wrapper
              .main-flex-container.container-layout-max
                - menu_list = []
                
                - # Identify main columns (exclude submenu-like columns)
                - main_columns = menu_columns.reject { |col| col.menu_items.present? && col.title.blank? } 
  
                - menu_columns.each do |menu_column|
                  - menu_c = menu_column.as_json
                  - menu_c['type'] = 'column'
                  - menu_list << menu_c
  
                  - menu_column.menu_items.each do |menu_item|
                    - menu_list << menu_item.as_json
  
                - category_flex_count = 0
  
                - menu_list.each_slice(15) do |batch|
                  .flex-item
                    - category_flex_count += 1
                    %ul.category-box
                      - batch.each_with_index do |item, i|
                        - if item['type'] == 'column'
                          - unless i == 0
                            %hr
                          %li
                            %a.parent-category-name{}= item['title']
                        - else
                          %li
                            %a.child-category-name{:href => item['link']}= item['title']
  
                - # Adjust number of images dynamically based on category flex items
                - num_images = case category_flex_count
                  - when 1 then 4
                  - when 2 then 3
                  - when 3 then 2
                  - when 4 then 1
                  - else 0 # 5 or more columns → No images
  
                - if menu.design_ids.present? && num_images > 0
                  - design_ids = menu.design_ids.split(',').first(num_images) # Select required number of images
                  - designs = Design.premium.by_ids(design_ids).index_by(&:id)
                  - design_ids.each do |design_id|
                    .flex-item
                      - design = designs[design_id.strip.to_i]
                      - if design
                        %a.p-card{:href => "/#{design.designer.cached_slug}/buy/#{design.title.parameterize}/#{design.id}", :target => "_blank"}
                          .header-p-img-box
                            - image_url = design.master_image.photo(:long_webp) || design.master_image.photo(:long)
                            = image_tag(IMAGE_PROTOCOL + image_url, alt: design.title, alt: '', class: 'lazyload p-card-img')
                            %p.header-p-title=design.designer.name.titleize
                            %p.header-p-desc=design.title.titleize
                            %span.discount_price= "#{get_symbol_from(@hex_symbol)}  #{number_with_delimiter(design.effective_price_currency(@rate).to_s)}"
                            - if (discount = design.effective_discount(PromotionPipeLine.active_promotions)) && discount.to_f > 0
                              %span.header_product_price_wo_discount= "#{get_symbol_from(@hex_symbol)} #{number_with_delimiter(design.price_currency(@rate).to_s)}"
                              %br
                              %span.header_percent_disc= "(#{discount}% OFF)"



:javascript
  $(document).ready(function() {
      MR.login.headerIconClickEvent('#profile-btn','/accounts/sign_in','#loginModal');
      MR.login.headerIconClickEvent('#desktop-wishlist-btn','/accounts/sign_in','#loginModal');
      MR.login.closeModalEvent('#loginModal');
      MR.login.closeModalEvent('#registerModal');
    });