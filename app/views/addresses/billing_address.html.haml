- content_for :page_specific_css do
  -# = stylesheet_link_tag 'addresses'
- content_for :page_specific_js do
  = javascript_include_tag 'addresses'
.row
  .large-12.medium-8.small-12.columns.small-centered.address-heading.delivery
    %h1 Select Address
    .option-button          
      .button-custom
        = link_to 'Back', new_order_path , class: 'button tiny expand back address-bottom-buttons', style: 'margin: 0;'
      .button-custom
        = link_to ' + Add New Address', new_user_address_path , class: 'success button tiny expand add-new address-bottom-buttons', style: 'margin: 20px 0;'
    .top-address
      - @addresses.each do |address|
        - default_address = address.default == 0 ? '' : 'default-address'
        .billing_address.bordered_block{class: default_address}
          = link_to '', user_address_path(address), method: 'DELETE', class: 'right close-icon', data: { confirm: 'Are you sure you want to delete this address?'}
          %table{style: 'width: 100%;'}
            %tr
              %td
                Name :
              %td
                = address.name
            %tr
              %td
                Street Address :
              %td
                = address.street_address
            %tr
              %td
                Landmark :
              %td
                = address.landmark
            %tr
              %td
                City :
              %td
                = address.city
            %tr
              %td
                State :
              %td
                = address.state
            %tr
              %td
                Country :
              %td
                = address.country
            %tr
              %td
                Pincode :
              %td
                = address.pincode
            %tr
              %td{colspan: 2}
                - if address.default == 0
                  = form_tag [@user, address], method: 'PUT' do
                    = radio_button_tag 'address[default]', '1', false, class: 'mark_as_default', id: "default_address_#{address.id}"
                    = label_tag :mark_as_default, 'Mark as default billing address', for: "default_address_#{address.id}"
                    = hidden_field_tag :address_id, address.id
                    = submit_tag :save, class: 'hide'
                - else 
                  %span.default-address-text Default Address
          %ul.small-block-grid-2
            %li{style: 'padding-bottom: 0em;'}= link_to 'BILL HERE', billing_address_orders_path(billing_id: address.id), class: 'button tiny expand secondary ship-here-button'
            %li{style: 'padding-bottom: 0em;'}= link_to 'EDIT ADDRESS', edit_user_address_path(address, type: 'billing_address'), class: 'button tiny expand secondary back-button'
-if @cart.present?
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - coupon_discount_amount = pro_totals.find { |item| item[:title] == "Coupon Discounts" }&.dig(:amount) || 0
  - tax_amount = pro_totals.find { |item| item[:title] == "Tax" }&.dig(:amount) || 0
  - total_amount = pro_totals.find { |item| item[:title] == "Item Total" }&.dig(:amount) || 0
  - gift_wrap = session[:gift_wrap] && @country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
  - market_rate = CurrencyConvert.countries_marketrate[@country_code]
  - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - shipping_currency = (shipping_currency *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  -if coupon_discount_amount != 0
    -discount_percent = (coupon_discount_amount*100.0/@totalvalue)
  -else
    -discount_percent = 0
  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '.ship-here-button', function(e)  {
      var total =  (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount}
      var ga4_shipping_params = #{@ga_hash_new.to_json};
      var customizationTotal = 0;
  
      function subtractCoupon(item) {
        var discountedPrice = item.price - (item.price * #{discount_percent} / 100);
        var finalPrice = (discountedPrice + item.item_customization).toFixed(2);
        return parseFloat(finalPrice);
      }
      ga4_shipping_params.items.forEach(function (item) {
        item.price = subtractCoupon(item); 
        customizationTotal += item.item_customization;
        
      });
      ga4_shipping_params.customization = customizationTotal
      ga4_shipping_params.value = total + customizationTotal
      ga4_shipping_params.gift_wrap = #{gift_wrap}
      ga4_shipping_params.shipping = #{shipping_currency}
      ga4_shipping_params.coupon_discount = #{coupon_discount_amount}
      ga4_shipping_params.tax = #{tax_amount}
      dataLayer.push({ ecommerce: null });
      dataLayer.push({
        event: "ga4_add_shipping_info",
        ecommerce: ga4_shipping_params
      });
    });