:css
  #container{
    margin-top: 0.5em !important;
  }
  .flex-item-2.header-logo {
    padding-left: 16px;
  }
- content_for :page_specific_css do
  = stylesheet_link_tag 'luxe/sessions_luxe', 'luxe/addresses_luxe'
- content_for :page_specific_js do
  //= javascript_include_tag 'guest_login'
  = javascript_include_tag 'luxe/addresses'
.error_message_box.alert-box.alert{style: 'text-align: center;display: none;'}
/ = render partial: 'layouts/payment_steps'
= render 'luxe/shared/mobile_header'

= render partial: 'luxe/addresses/address_collect_form_domestic'

- if false # ajax login + address form not required now, because it is now merge into one page
  .email_field_div
    .row
      .large-12.medium-12.small-12.columns.small-centered.guest_login_form
        %h4.text-center
          %u Enter Your Email
        .checkout_login
          = form_tag(accounts_create_guest_path, {id: 'guest_login_form', method: 'POST'}) do
            .row
              .small-12.columns
                = text_field_tag 'email', '', placeholder: '<EMAIL>', type: 'email', required: true,
                  pattern: '^((?:[-a-zA-Z0-9_\.]+)+[a-zA-Z0-9]*)@((?:[-a-zA-Z0-9]+\.)+[a-zA-Z]{2,})$'
            .row
              .small-12.columns
                = submit_tag 'Continue As Guest', class: 'button postfix secondary order_flow_submit_button'



        = link_to 'I am an existing user?', 'javascript:void(0)', class: :toggle_loggin_forms
      .large-12.medium-12.small-12.columns.small-centered.hide.existing_user_login_form
        - session["account_return_to"] = params[:account_return_to]
        = form_for(resource, as: resource_name, url: session_path(resource_name)) do |f|
          = f.email_field :email, autofocus: true, label: false, placeholder: "Email", value: params[:email]
          = f.password_field :password, autocomplete: "off", label: false, placeholder: "Password"
          - session["account_return_to"] = new_order_path
          = f.submit "Log in", class: 'button postfix secondary order_flow_submit_button'
        %br
        = link_to 'Continue As Guest...', 'javascript:void(0)', class: :toggle_loggin_forms
      %hr
    = render "devise/shared/social_links"


  - unless check_for_alternate_tab?
    .address_field_div
      .row
        .text-center Add New Address
        .billing_address.large-6.medium-8.small-12.columns.small-centered.bordered_block
          = render partial: 'luxe/addresses/address_collect_form'

:javascript
  $(document).ready(function () {
    var ww = document.documentElement.clientWidth;
    // ------------------------------------------------------------------
    // Set Body Top Padding For Fixed Header
    if (ww >= 1000) {
      var headerHeight = $('.desktop-header-component-container:visible').css('height');
      $('#bodyTagContainer').css('padding-top', headerHeight);
    }
  });
-# %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=AW-10987265357"}
-# :javascript
  -# window.dataLayer = window.dataLayer || [];
  -# function gtag(){dataLayer.push(arguments);}
  -# gtag('js', new Date());
  -# gtag('config', 'AW-10987265357');
-if @cart.present?
  - market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - shipping = (shipping_currency.to_f * market_rate).round(2)
  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '#address_collect_submit', function(e)  {
      if (isFormFilled()) {
        var shipping_params = #{@ga_hash_new.to_json};
        shipping_params.ecommerce.shipping = #{shipping}
        dataLayer.push({ ecommerce: null });
        dataLayer.push(shipping_params);
      }
    });
    function isFormFilled() {
      var form = $('#new_address');
      var requiredFields = form.find('input[required], textarea[required]');
      for (var i = 0; i < requiredFields.length; i++) {
        if (!requiredFields[i].value.trim()) {
          return false;
        }
      }
      return true;
    }