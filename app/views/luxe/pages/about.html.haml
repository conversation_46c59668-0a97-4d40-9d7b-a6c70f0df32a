%head
  %title About us
  = stylesheet_link_tag 'luxe/about-us', media: 'all'
%header.mobile-header-component-container
  .mobile-fixed-header-wrapper
    -if is_mobile_view?
      .mobile-message-block.offer-txt
        - if @offer_message.present?
          =raw @offer_message
        - else
          =raw "For Personal Assistance, you can WhatsApp us on"
          = link_to "+91 9137407527. ",'https://api.whatsapp.com/send?phone=919137407527&text=Hello', target: '_blank', style: 'color:#f56a6a'
        = render partial: 'luxe/pages/deal_timer_mobile'
    .flex-container
      .flex-item-1
        %button.openSideNav.action-btn.action-hamburger-btn
          %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
            %g#Rectangle_21663{"data-name" => "Rectangle 21663", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
              %rect{:height => "24", :stroke => "none", :width => "24"}
              %rect{:fill => "none", :height => "23", :width => "23", :x => "0.5", :y => "0.5"}
            %g#Iconly_Light-Outline_Document{"data-name" => "Iconly/Light-Outline/Document", :transform => "translate(-2.589 -2.382)"}
              %g#Document{:transform => "translate(6.746 7.11)"}
                %path#Combined-Shape{:d => "M18.531,17.513a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,0,1,0-2.224Zm0-6.209a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,0,1,0-2.224ZM11.909,5.11a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,1,1,0-2.224Z", "fill-rule" => "evenodd", :transform => "translate(-3.745 -5.11)"}
      .flex-item-2.header-logo
        %a{href: '/'}
          =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img')
      .flex-item-3
        .dropdown
          %button.action-btn.user-btn.dropbtn{id: 'mobile-profile-btn', class: account_signed_in? ? 'signed-in' : 'not-signed-in'}
            / prettier-ignore
            %svg#Profile_in-active_W_O_Img{"data-name" => "Profile in-active/ W O Img", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %g#Rectangle_24115{"data-name" => "Rectangle 24115", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
                %rect{:height => "24", :stroke => "none", :width => "24"}
                %rect{:fill => "none", :height => "23", :width => "23", :x => "0.5", :y => "0.5"}
              %g#Iconly_Bold_Profile{"data-name" => "Iconly/Bold/Profile", :transform => "translate(0.8 1)"}
                %g#Profile{:transform => "translate(4 2)"}
                  %path#Path_220122{:d => "M7.2,16.5a14.812,14.812,0,0,0,4.562-.528c1.138-.4,1.138-.827,1.138-1.033s0-.643-1.137-1.045A14.612,14.612,0,0,0,7.2,13.357a14.812,14.812,0,0,0-4.562.528c-1.138.4-1.138.827-1.138,1.033s0,.643,1.137,1.045A14.611,14.611,0,0,0,7.2,16.5m0-8.475a3.247,3.247,0,1,0-2.313-.951A3.248,3.248,0,0,0,7.2,8.025M7.2,18C3.3,18,0,17.365,0,14.917s3.317-3.061,7.2-3.061c3.9,0,7.2.634,7.2,3.083S11.083,18,7.2,18Zm0-8.475a4.762,4.762,0,1,1,4.765-4.763A4.747,4.747,0,0,1,7.2,9.525Z", "data-name" => "Path 220122", :transform => "translate(0 0)"}
            .dropdown-content
              - if account_signed_in?
                = link_to 'Wallet', user_wallet_path
                = link_to 'Profile' , user_profile_path
                = link_to 'Order Details' , '/orders'
                = link_to 'Address', 'user/addresses'
                = link_to 'Logout', destroy_account_session_path, method: :delete

        = link_to user_wishlists_path do
          %button.action-btn.wishlist-btn{id: 'mobile-wishlist-btn', class: account_signed_in? ? 'signed-in' : 'not-signed-in'}
            / prettier-ignore
            %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
              %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
        = link_to cart_path do
          %button.action-btn.cart-btn
            %span.cart-count= "#{session[:cart_count]}"
            / prettier-ignore
            %svg#icon_orders{"data-name" => "icon/orders", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_23418{"data-name" => "Rectangle 23418", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
              %path#XMLID_1732_{:d => "M17.961,14.189l-2.093,6.593a1.109,1.109,0,0,1-1.057.783H6.73a1.154,1.154,0,0,1-1.076-.724L2.563,12.565H.783A.783.783,0,1,1,.783,11H3.111a.8.8,0,0,1,.743.528L7.043,20h7.435l1.663-5.283H6.926a.783.783,0,1,1,0-1.565H17.217a.778.778,0,0,1,.626.333.759.759,0,0,1,.117.7ZM7.122,22.6a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,7.122,22.6Zm7.024,0a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,14.146,22.6Z", :transform => "translate(3 -6.073)"}
    .header-searchbar-wrapper
      = render partial:  'layouts/search_form_luxe'
.mobile-side-navbar-component-container
  #mobileSideNav.mobile-sidenav-wrapper
    %button.closeSideNav.sidebars-close-btn
      %i.fa-solid.fa-xmark
    .flex-shrink-0.bg-white.menu-list-wrapper
      .user-action-btn-wrap
        .flex-container
          .flex-item-1
            =image_tag('mobile_nav_placeholder.png', alt: 'mobile_nav_placeholder', class: 'user-img')
          .flex-item-2
            - if account_signed_in?
              -if current_account.present? && current_account.email.present?
                %span.txt= current_account.email.split('@').first.try(:titleize)
              %div
                = link_to destroy_account_session_path, method: :delete do
                  %button.act-btn.login-btn logout
            - else
              %span.txt Login to continue
              %div
                = link_to '/accounts/sign_in' do
                  %button.act-btn.login-btn login
                = link_to '/accounts/sign_up' do
                  %button.act-btn.transparent.register-btn register

      %ul.list-unstyled

        -@menu_list.each do |menu|
          - menu_columns = menu.menu_columns
          - menu_columns = menu_columns.sort_by(&:position)

          %li.menu-list-item
            %button.btn.btn-toggle.m-toggle-btn.collapsed{"aria-expanded" => "false", "data-bs-target" => "#menu_#{menu.id}", "data-bs-toggle" => "collapse"}
              = menu.title

          .collapse{id: "menu_#{menu.id}"}
            - menu_columns.each do |menu_column|
              - menu_items = menu_column.menu_items
              %ul.btn-toggle-nav.list-unstyled
                %li
                  %a.a-link{:href => menu_column.link}
                    %h6= menu_column.title
                    - menu_items.each do |menu_item|
                      %li
                        %a.menu-submenu.a-link{:href=> menu_item.link}
                          = menu_item.title
#mainContentContainer
  %span#mobileMenuOverlay
  %section.section-one
    .main-content
      .image-one
        .brand-name
          MUZAI
        =image_tag('luxe/about_us/couple_dress.jpg', alt: 'couple', class: 'lazyload img-fluid')
      .brand-tagline
        Indulge in the Opulence of Ethnic Wear
      .image-two
        =image_tag('luxe/about_us/wedding_outfit.jpg', alt: 'group', class: 'lazyload img-fluid')
  
  %section.section-two
    .main-content
      .left-content
        .content-title
          Who we are ?
        .content
          MUZAI is a curated platform showcasing the finest in Indian ethnic and fusion wear, with a special focus on occasion dressing. From timeless sarees to statement lehengas and contemporary silhouettes, we bring together established and emerging designers who blend heritage with modern style. Whether it’s a wedding, festive celebration, or a special event, our handpicked collections are crafted to make you stand out. Thoughtfully selected for design, quality, and value—this is luxury made effortless.
      .right-content
        .image-three
          =image_tag('luxe/about_us/Who_we_are.jpg', alt: 'group', class: 'lazyload img-fluid')
  %section.section-three
    .content-title
      What we offer
    .main-content
      .image-container
        .category.category-one
          .image-four.image
            =image_tag('luxe/about_us/women.jpg', alt: 'women category', class: 'lazyload img-fluid')
          .image-title
            women
        .category.category-two
          .image-seven.image
            =image_tag('luxe/about_us/men.jpg', alt: 'men category', class: 'lazyload img-fluid')
          .image-title
            men
        .category.category-three
          .image-six.image
            =image_tag('luxe/about_us/kids.jpg', alt: 'kids category', class: 'lazyload img-fluid')
          .image-title
            kids
        .category.category-four
          .image-five.image
            =image_tag('luxe/about_us/jewllery.jpg', alt: 'jewellery category', class: 'lazyload img-fluid')
          .image-title
            jewellery
