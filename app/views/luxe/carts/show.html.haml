- content_for :page_specific_css do
  = stylesheet_link_tag 'luxe/cart'
/ Global site tag (gtag.js) - Google Ads: 10987265357
-# %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=AW-10987265357"}
-# :javascript
  -# window.dataLayer = window.dataLayer || [];
  -# function gtag(){dataLayer.push(arguments);}
  -# gtag('js', new Date());
-# 
  -# gtag('config', 'AW-10987265357');
/ Event snippet for Add to Cart Mirraw Luxe conversion page
-# :javascript
  -# gtag('event', 'conversion', {'send_to': 'AW-10987265357/U64XCMqZ594DEM26kfco'});
%header.mobile-header-component-container
  .mobile-fixed-header-wrapper
    .flex-container
      .flex-item-1
        = link_to "javascript:history.go(-1)" do
          %button.action-btn.action-back-btn
            / prettier-ignore
            %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_21663{"data-name" => "Rectangle 21663", :fill => "none", :height => "24", :width => "24"}
              %path#Path_102951{:d => "M1070.769,1253.048a1,1,0,0,0-1.41,0l-5.313,5.332h0a1.993,1.993,0,0,0,0,2.82h0l5.359,5.342a1,1,0,0,0,1.41-1.41l-4.919-4.9a.619.619,0,0,1,0-.877l4.875-4.9A1,1,0,0,0,1070.769,1253.048Z", "data-name" => "Path 102951", :fill => "#1f1f1f", :transform => "translate(-1055.462 -1247.758)"}
      .flex-item-2.header-logo
        %a{href: '/'}
          =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img')
      .flex-item-3
        / %button.action-btn.search-btn
        /   / prettier-ignore
        /   %svg#icon_search{"data-name" => "icon/search", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
        /     %rect#Rectangle_24335{"data-name" => "Rectangle 24335", :fill => "none", :height => "24", :width => "24"}
        /     %path#Union_21{:d => "M-15183-15921l3.123,3.432Zm-13-5.5a7.5,7.5,0,0,1,7.5-7.5,7.5,7.5,0,0,1,7.5,7.5,7.5,7.5,0,0,1-7.5,7.5A7.5,7.5,0,0,1-15196-15926.5Z", "data-name" => "Union 21", :fill => "none", :stroke => "#1b2437", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-miterlimit" => "10", "stroke-width" => "1.5", :transform => "translate(15200 15938)"}
        = link_to user_wishlists_path do
          %button.action-btn.wishlist-btn
            / prettier-ignore
            %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
              %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
- if @cart.line_items.present?
  - best_offer_text, discount_perc, discount_rates, offers_present_for_country = get_best_offer_for_cart_text(@cart, @rate, @actual_country, @country_code, @symbol)
  - item_total = @cart.item_total(@rate)
  - bmgnx_hash = PromotionPipeLine.bmgnx_hash
  - bmgnx_discounts = get_price_in_currency(@cart.bmgnx_discounts(bmgnx_hash))
  - recent_line_item = @cart.line_items.first
  - if recent_line_item
    - catgory = recent_line_item.design.categories.preload(:parent).first
    - category_name = catgory.parent.present? ? catgory.parent.name : catgory.name
  - recent_url = category_name.present? ? "/store/#{category_name}" : '/'
  %main#mainContentContainer.main-content-container
    %span#mobileMenuOverlay
    .cart-page-container
      .container-layout-max
        .horizontal-padding
          .container-fluid
            .row
              .col-12.d-no-padding
                .container-fluid.xs-no-padding.xs-no-margin
                  .row.xs-no-padding.xs-no-margin
                    .col-12.xs-no-padding
                      %h1.main-heading
                        Cart
                        - no_of_items = 0
                        - for line_item in @cart.line_items
                          - no_of_items += line_item.quantity
                        %span.item-count="(#{no_of_items} items)"
          .container-fluid.xs-no-padding.xs-no-margin
            .row.xs-no-padding.xs-no-margin
              .col-12.d-no-padding.d-width-70.xs-no-padding
                .orders-list-wrapper
                  .container-fluid.xs-no-padding.xs-no-margin
                    .row.xs-no-padding.xs-no-margin
                      .col-12.xs-no-padding
                        .order-item-wrap
                          - items = @cart.line_items - @cart.cart_addon_item(@country_code)
                          - for line_item in items
                            - out_stock_design = line_item.available_quantity(@country_code) < line_item.quantity
                            .o-card-step-item
                              .o-card-item
                                .flex-item-one.pd-around
                                  = link_to "/#{line_item.design.designer.cached_slug}/buy/#{line_item.design.title.parameterize}/#{line_item.design.id}" do
                                    - if line_item.design.master_image.present?
                                      = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:small), alt: line_item.design.title, class: 'lazyload cmn-img-fluid o-img')
                                    - else
                                      = image_tag('default_image.jpg', alt: line_item.design.title)
                                .flex-item-two.pd-around
                                  .common-product-info-box
                                    %h1.o-heading=line_item.design.designer.name.titleize
                                    %span.o-des=line_item.design.title.titleize
                                    .amount-box
                                      - price = get_price_with_symbol(line_item.snapshot_price_currency(@rate), @hex_symbol)
                                      %span.price=price
                                      / %span.mrp ₹11,822
                                      / %span.discount (25% OFF)
                                    - if line_item.variant.present?
                                      .o-variant-size
                                        - option_type_value = line_item.variant.option_type_values.first
                                        = "Size : #{option_type_value.p_name}"
                                    %p.o-sku-txt
                                      SKU Code -
                                      %span.o-sku-code=line_item.design.id
                                    - if false
                                      .o-delivers-box
                                      / prettier-ignore
                                      %svg#Group_149274{"data-name" => "Group 149274", :height => "19", :viewbox => "0 0 19 19", :width => "19", :xmlns => "http://www.w3.org/2000/svg"}
                                        %path#Path_220752{:d => "M16.531,10h-3v1.371h2.433l3.674,3.71V20l-.281.284h-.4V20l-1.755-1.772H15.291L13.536,20v.284H9.464V20L7.709,18.223H5.791L4.036,20v.284h-.4L3.357,20V16.167H2v4.4L3.076,21.65h.96v.284l1.755,1.772H7.709l1.755-1.772V21.65h4.071v.284l1.755,1.772H17.21l1.755-1.772V21.65h.959L21,20.563v-6.05ZM8.107,21.366l-.96.969h-.8l-.96-.969v-.8l.96-.969h.8l.96.969Zm9.5,0-.959.969h-.8l-.959-.969v-.8l.959-.969h.8l.959.969Z", "data-name" => "Path 220752", :fill => "#333", :transform => "translate(-2 -4.705)"}
                                        %path#Path_220753{:d => "M11.54,12.589l1.05-1.05V3.05L11.54,2H3.05L2,3.05v8.49l1.05,1.05ZM6.633,3.324H7.956V5.309H6.633ZM3.324,3.6,3.6,3.324H5.309V6.633H9.28V3.324h1.711l.274.274v7.394l-.274.274H3.6l-.274-.274Z", "data-name" => "Path 220753", :fill => "#af1f2b", :transform => "translate(-2 -2)"}
                                      %span.o-delivers Delivers by 21 Feb
                                  .o-select-flex-container.common-size-qty-select
                                    -if false
                                      .o-select-box
                                      %span.o-select-label Size -
                                      #sizeDropdownMenuBox.dropdown.order-duration-dropdown-box.dropdown-select-box.dropdownMenuBox
                                        %button#sizeDropdownMenuButton.btn.btn-secondary.dropdown-toggle.dropdown-select-btn.size-dropdown-btn.dropdownMenuButton{"aria-expanded" => "false", "data-bs-toggle" => "dropdown", :type => "button"}
                                          xs
                                        %ul.dropdown-menu{"aria-labelledby" => "sizeDropdownMenuButton"}
                                          %li
                                            %span.dropdown-item xs
                                          %li
                                            %span.dropdown-item sm
                                          %li
                                            %span.dropdown-item l
                                          %li
                                            %span.dropdown-item xxl
                                    .o-select-box
                                      %span.o-select-label Qty -
                                      / #quantityDropdownMenuBox.dropdown.order-duration-dropdown-box.dropdown-select-box.dropdownMenuBox
                                      /   %button#quantityDropdownMenuButton.btn.btn-secondary.dropdown-toggle.dropdown-select-btn.quantity-dropdown-btn.dropdownMenuButton{"aria-expanded" => "false", "data-bs-toggle" => "dropdown", :type => "button"}
                                      /     1
                                      /   %ul.dropdown-menu{"aria-labelledby" => "quantityDropdownMenuButton"}
                                      /     %li
                                      /       %span.dropdown-item 1
                                      /     %li
                                      /       %span.dropdown-item 2
                                      /     %li
                                      /       %span.dropdown-item 3
                                      - design_quantity = line_item.max_quantity_on_cart
                                      - design_quantity = 10 if design_quantity > 10
                                      = select_tag "quantity_#{line_item.id}", options_for_select((1..design_quantity).to_a, line_item.quantity), class: 'quantity_list'
                                    -if out_stock_design
                                      %span.out_of_stock Out of Stock
                                  /
                                    <div class="out-of-stock-box">
                                    <span class="msg">
                                    <img
                                    src="./../assets/img/out-of-stock.png"
                                    class="cmn-img-fluid out-stock-img"
                                    alt="Mirraw" />
                                    out of stock</span
                                    >
                                    </div>
                                .flex-item-three.action-btn-box.pd-around
                                  .i-flex
                                    / Use 'wishlisted' class to show active-icon
                                    = link_to line_item_path(line_item, move_to_wishlist: true), method: :delete, remote: true do
                                      %button.action-btn.wishlist-action-btn.wishlist-icon-btn{:type => "button", :class =>"wishlist-icon #{account_signed_in? ? 'signed-in' : 'not-signed-in'}"}
                                        .inactive-icon.d-view
                                          / prettier-ignore
                                          %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                                            %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                                            %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
                                        .active-icon.d-view
                                          / prettier-ignore
                                          %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                                            %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                                            %path#Path_197893{:d => "M2.2,9.366a6.653,6.653,0,0,0,2.062,5.068c1.649,1.59,7.113,5.168,7.32,5.366a1.5,1.5,0,0,0,.619.2,1.5,1.5,0,0,0,.619-.2c.206-.2,5.67-3.677,7.32-5.366A6.653,6.653,0,0,0,22.2,9.366,5.447,5.447,0,0,0,16.633,4,5.544,5.544,0,0,0,12.3,6.286,5.631,5.631,0,0,0,7.767,4,5.513,5.513,0,0,0,2.2,9.366Z", "data-name" => "Path 197893", :fill => "#1f1f1f", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "2", :transform => "translate(-0.2)"}
                                        %span.m-view.btn-txt wishlist
                                  .i-flex
                                    %button.action-btn.remove-action-btn{"data-bs-target" => "#cartRemoveItemModal_#{line_item.id}", "data-bs-toggle" => "modal", :type => "button"}
                                      .inactive-icon.d-view
                                        / prettier-ignore
                                        %svg{xmlns: "http://www.w3.org/2000/svg", width: "10", height: "10", viewBox: "0 0 10 10"}
                                          %line{x1: "0", y1: "0", x2: "10", y2: "10", stroke: "black", "stroke-width": "2"}
                                          %line{x1: "10", y1: "0", x2: "0", y2: "10", stroke: "black", "stroke-width": "2"}
                                      %span.m-view.btn-txt.red remove
                                / ************************* CART REMOVE ITEM MODAL *************************
                                .modal.fade.drawer.cart-remove-item-modal-wrapper{"aria-hidden" => "true", "aria-labelledby" => "cartRemoveItemModalLabel", :tabindex => "-1", id: "cartRemoveItemModal_#{line_item.id}"}
                                  .modal-dialog.modal-dialog-centered
                                    .modal-content
                                      .modal-body
                                        %h1.heading Do you really want to remove this item from the cart?
                                        .footer-btn-wrapper
                                          .flex-container
                                            .flex-item
                                              = link_to line_item_path(line_item, move_to_wishlist: true), method: :delete, remote: true , title: 'Move To Wishlist' do
                                                %button.action-btn.common-white-btn.wishlist-btn.wishlist-action-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal", :type => "button", :class =>"wishlist-icon #{account_signed_in? ? 'signed-in' : 'not-signed-in'}"} add to wishlist
                                            .flex-item
                                              = link_to line_item_path(line_item), method: 'DELETE', remote: true, title: 'Remove From Cart',  unbxdattr: "RemoveFromCart", unbxdparam_sku: line_item.design.id, unbxdparam_requestId: session[:unbxdparam_requestId], unbxdparam_price: line_item.snapshot_price, class: 'remove_from_cart Remove' do
                                                %button.action-btn.common-black-btn.remove-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal"} yes, remove
                          / Use this class 'outOfStockLayer' to show Out Of Stock styling
                          / .o-card-step-item.outOfStockLayer
                          /   .o-card-item
                          /     .flex-item-one.pd-around
                          /       %img.lazyload.cmn-img-fluid.o-img{:alt => "Mirraw", "data-src" => "./../assets/img/card-img-1.png", :src => "../../assets/img/product-placeholder.png"}/
                          /     .flex-item-two.pd-around
                          /       .common-product-info-box
                          /         %h1.o-heading adidas
                          /         %span.o-des Blue floral print cotton salwar semi stitched
                          /         .amount-box
                          /           %span.price ₹15,070
                          /           %span.mrp ₹11,822
                          /           %span.discount (25% OFF)
                          /         %p.o-sku-txt
                          /           SKU Code -
                          /           %span.o-sku-code 123564778
                          /         .o-delivers-box
                          /           / prettier-ignore
                          /           %svg#Group_149274{"data-name" => "Group 149274", :height => "19", :viewbox => "0 0 19 19", :width => "19", :xmlns => "http://www.w3.org/2000/svg"}
                          /             %path#Path_220752{:d => "M16.531,10h-3v1.371h2.433l3.674,3.71V20l-.281.284h-.4V20l-1.755-1.772H15.291L13.536,20v.284H9.464V20L7.709,18.223H5.791L4.036,20v.284h-.4L3.357,20V16.167H2v4.4L3.076,21.65h.96v.284l1.755,1.772H7.709l1.755-1.772V21.65h4.071v.284l1.755,1.772H17.21l1.755-1.772V21.65h.959L21,20.563v-6.05ZM8.107,21.366l-.96.969h-.8l-.96-.969v-.8l.96-.969h.8l.96.969Zm9.5,0-.959.969h-.8l-.959-.969v-.8l.959-.969h.8l.959.969Z", "data-name" => "Path 220752", :fill => "#333", :transform => "translate(-2 -4.705)"}
                          /             %path#Path_220753{:d => "M11.54,12.589l1.05-1.05V3.05L11.54,2H3.05L2,3.05v8.49l1.05,1.05ZM6.633,3.324H7.956V5.309H6.633ZM3.324,3.6,3.6,3.324H5.309V6.633H9.28V3.324h1.711l.274.274v7.394l-.274.274H3.6l-.274-.274Z", "data-name" => "Path 220753", :fill => "#af1f2b", :transform => "translate(-2 -2)"}
                          /           %span.o-delivers Delivers by 21 Feb
                          /       .o-select-flex-container.common-size-qty-select
                          /         .o-select-box
                          /           %span.o-select-label Size -
                          /           #sizeDropdownMenuBox.dropdown.order-duration-dropdown-box.dropdown-select-box.dropdownMenuBox
                          /             %button#sizeDropdownMenuButton.btn.btn-secondary.dropdown-toggle.dropdown-select-btn.size-dropdown-btn.dropdownMenuButton{"aria-expanded" => "false", "data-bs-toggle" => "dropdown", :type => "button"}
                          /               xs
                          /             %ul.dropdown-menu{"aria-labelledby" => "sizeDropdownMenuButton"}
                          /               %li
                          /                 %span.dropdown-item xs
                          /               %li
                          /                 %span.dropdown-item sm
                          /               %li
                          /                 %span.dropdown-item l
                          /               %li
                          /                 %span.dropdown-item xxl
                          /         .o-select-box
                          /           %span.o-select-label Qty -
                          /           #quantityDropdownMenuBox.dropdown.order-duration-dropdown-box.dropdown-select-box.dropdownMenuBox
                          /             %button#quantityDropdownMenuButton.btn.btn-secondary.dropdown-toggle.dropdown-select-btn.quantity-dropdown-btn.dropdownMenuButton{"aria-expanded" => "false", "data-bs-toggle" => "dropdown", :type => "button"}
                          /               1
                          /             %ul.dropdown-menu{"aria-labelledby" => "quantityDropdownMenuButton"}
                          /               %li
                          /                 %span.dropdown-item 1
                          /               %li
                          /                 %span.dropdown-item 2
                          /               %li
                          /                 %span.dropdown-item 3
                          /       .out-of-stock-box
                          /         %span.msg
                          /           %img.cmn-img-fluid.out-stock-img{:alt => "Mirraw", :src => "./../assets/img/out-of-stock.png"}/
                          /           out of stock
                          /     .flex-item-three.action-btn-box.pd-around
                          /       .i-flex
                          /         / Use 'wishlisted' class to show active-icon
                          /         %button.action-btn.wishlist-action-btn{:type => "button"}
                          /           .inactive-icon.d-view
                          /             / prettier-ignore
                          /             %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                          /               %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                          /               %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
                          /           .active-icon.d-view
                          /             / prettier-ignore
                          /             %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                          /               %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                          /               %path#Path_197893{:d => "M2.2,9.366a6.653,6.653,0,0,0,2.062,5.068c1.649,1.59,7.113,5.168,7.32,5.366a1.5,1.5,0,0,0,.619.2,1.5,1.5,0,0,0,.619-.2c.206-.2,5.67-3.677,7.32-5.366A6.653,6.653,0,0,0,22.2,9.366,5.447,5.447,0,0,0,16.633,4,5.544,5.544,0,0,0,12.3,6.286,5.631,5.631,0,0,0,7.767,4,5.513,5.513,0,0,0,2.2,9.366Z", "data-name" => "Path 197893", :fill => "#1f1f1f", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "2", :transform => "translate(-0.2)"}
                          /           %span.m-view.btn-txt wishlist
                          /       .i-flex
                          /         %button.action-btn.remove-action-btn{"data-bs-target" => "#cartRemoveItemModal", "data-bs-toggle" => "modal", :type => "button"}
                          /           .inactive-icon.d-view
                          /             / prettier-ignore
                          /             %svg{:height => "20", :viewbox => "0 0 18.458 20", :width => "18.458", :xmlns => "http://www.w3.org/2000/svg"}
                          /               %path#Combined-Shape{:d => "M16.385,6.72a.751.751,0,0,1,.688.808c-.006.068-.548,6.779-.86,9.594a2.976,2.976,0,0,1-3.09,2.842C11.79,19.987,10.5,20,9.247,20c-1.355,0-2.676-.015-3.983-.042a2.967,2.967,0,0,1-3.018-2.829c-.315-2.84-.854-9.534-.859-9.6a.749.749,0,0,1,.687-.808.77.77,0,0,1,.808.687c0,.043.224,2.777.464,5.482l.048.54c.121,1.344.244,2.636.343,3.536a1.472,1.472,0,0,0,1.558,1.494c2.5.053,5.051.056,7.8.006a1.5,1.5,0,0,0,1.626-1.507c.31-2.794.85-9.482.856-9.55A.766.766,0,0,1,16.385,6.72ZM11.345,0a2.033,2.033,0,0,1,1.962,1.506l.254,1.261a.9.9,0,0,0,.865.722h3.282a.75.75,0,1,1,0,1.5H.75a.75.75,0,1,1,0-1.5H4.031l.1-.006A.9.9,0,0,0,4.9,2.767L5.14,1.551A2.043,2.043,0,0,1,7.112,0Zm0,1.5H7.112a.529.529,0,0,0-.512.392l-.233,1.17a2.379,2.379,0,0,1-.128.427h5.979a2.386,2.386,0,0,1-.128-.427l-.243-1.216A.524.524,0,0,0,11.345,1.5Z", "fill-rule" => "evenodd", :transform => "translate(0 0)"}
                          /           %span.m-view.btn-txt.red remove
                        .bank-card-img-box
                          =image_tag('bank_card_type.png', alt: 'bank_card_type', class: 'cmn-img-fluid bank-card-img')
              - grandtotal = @cart.total_currency(@rate, @country_code, @actual_country)
              - grandtotal_without_shipping = @cart.total_currency(@rate, @country_code, nil, false, false)
              - grandtotal += (GIFT_WRAP_PRICE.to_f/@rate).round(2) if session[:gift_wrap]
              - amount_to_credit = Wallet.cashback_for_amount(grandtotal)
              - totals, grandtotal, shipping = get_cart_total_information(@country_code, @rate)
              - referral_amount = get_wallet_referral_amount(@country_code)
              .col-12.d-no-padding.d-width-30.xs-no-padding
                .order-payment-detail-wrapper
                  .price-details-box
                    %h1.heading Price details
                    .op-price-box
                      .op-flex-container
                        .flex-item-1
                          %span.op-label Items Total
                        .flex-item-2
                          %span.op-amount= "#{get_price_with_symbol(totals.first[:amount].to_f, @hex_symbol)}"
                      / .op-flex-container
                      /   .flex-item-1
                      /     %span.op-label.green Total Savings
                      /   .flex-item-2
                      /     %span.op-amount.green ₹47.0
                      - if ['Coupon Discounts', 'Cart Discounts'].include?(totals[1][:title])
                        .op-flex-container
                          .flex-item-1
                            %span.op-label.green Coupon Discount
                          .flex-item-2
                            %span.op-amount.green="#{get_price_with_symbol(totals[1][:amount].to_f, @hex_symbol)}"
                      .op-flex-container
                        .flex-item-1
                          - market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
                          %span.op-label.shipping_cost{data: {shipping:shipping.to_f ,market_rate: market_rate}} Delivery Charges
                        .flex-item-2
                          %span.op-amount= "#{get_price_with_symbol(shipping.to_f, @hex_symbol)}"
                      - if referral_amount > 0
                        .op-flex-container
                          .flex-item-1
                            %span.op-label.green Wallet Discount
                          .flex-item-2
                            %span.op-amount.green= "#{get_price_with_symbol(referral_amount.to_f, @hex_symbol)}"   
                      .op-flex-container
                        .flex-item-1
                          %span.op-label.big Total Amount
                        .flex-item-2
                          %span.op-amount.big= "#{get_price_with_symbol(grandtotal.to_f, @hex_symbol)}"
                      / .gift-checkbox
                      /   .checkbox-item
                      /     %label.common-checkbox-grp
                      /       Are you gifting to someone ?
                      /       %input{:checked => "checked", :type => "checkbox", :value => "gifting"}/
                      /       .control-indicator
                  .row.flash-message
                    - zurbs_flash_classes = {'notice' => 'success', 'alert' => 'warning', 'error' => 'alert'}
                    - flash.each do |key, msg|
                      .warning-message.alert-box.radius{data: {alert: ''}, class: zurbs_flash_classes[key]}
                        = msg
                        %a{href: '#', class: 'close'} &times;
                  = render partial: 'luxe/carts/cart_coupon' ,locals: { available_coupons: @available_coupons }
                  - if @cart.coupon.try(:code).present?
                    %div.remove-coupon-container
                      %div.coupon-info
                        .code-info
                          - coupon_code_text = "Coupon Applied: #{@cart.coupon.code}"
                          = coupon_code_text
                        .saved-amount You saved additional #{get_price_with_symbol(get_price_in_currency(@cart.discount), @hex_symbol)}
                      %div.remove-button
                        = link_to "Remove", remove_coupon_path(:coupon => @cart.coupon), method: :post, class: "button remove-coupon-button-css"
                  - if offers_present_for_country.present? || (item_total < @cart_minimum_value.to_f) || amount_to_credit.to_i > 0 || is_domestic?
                    .offer-box
                      .columns.cart-discount-message
                        %h1.heading Available Offers :
                        .offer_messages_block
                          %ul{style:'font-size:14px;'}
                            - if amount_to_credit.to_i > 0
                              .info-text
                                You will get cashback worth #{@symbol}#{amount_to_credit.round(2)} in your mirraw wallet.
                                - if is_domestic?
                                  %span (Only if you choose to opt for payment during the order).
                              %i.info-text (Cash back you will receive is #{Wallet.cashback_percent}% of the amount payable)
                              = link_to '| T&C', offer_tnc_url(anchor: 'loyalty_tnc'), :target => "_blank", class: 'click_wallet'
                            - if @prepaid_payment_promotion && (offer = @prepaid_payment_promotion.cart_message).present?
                              .info-text= offer.html_safe
                            - if is_domestic?
                              - msg = DOMESTIC_PREPAID_SHIPPING_PROMOTION ? 'Cash on Delivery' : ''
                              - if DOMESTIC_PREPAID_SHIPPING_PROMOTION
                                .info-text= "Get <span style='color:RGB(56,118,29);font-weight:bold'>FREE</span> Shipping on Online Payment".html_safe
                              - if DOMESTIC_SHIPPING_CHARGES.keys.last.to_i > 0
                                .info-text Free Shipping on #{msg} Orders above ₹#{DOMESTIC_SHIPPING_CHARGES.keys.last.to_i}
                              - else
                                .info-text Free Shipping on all Orders
                            - if !is_domestic?
                              - if @country_code != 'IN'
                                - if Promotion.free_shipping_on_country?
                                  = render partial: 'luxe/carts/promotion_offers', locals: { offers: @offers }
                              - if @free_stitching_text.present?
                                .info-text= @free_stitching_text
                            - max_discount = 0
                            - if !@cart.coupon.present?
                              - discount_rates.each_with_index do |amount,index|
                                - amount_on = get_price_in_currency(discount_rates[index].to_i, @rate)
                                - if item_total - bmgnx_discounts <= amount_on
                                  .info-text= "Extra #{discount_perc[index]}% off above #{get_price_with_currency(amount_on, @symbol)}"
                                - elsif max_discount < discount_perc[index].to_i && BEST_OFFER_THRESHOLD.to_i >= 0
                                  - max_discount = discount_perc[index].to_i
                              - if max_discount > 0
                                .info-text= "You got extra #{max_discount}% off"
                  .shipping-details-box
                    %h1.heading Shipping details
                    -delivery_time, show_rts_message, available_in_warehouse = @cart.get_delivery_time(@actual_country)
                    %span.info-text="Your Order will be delivered within #{delivery_time} days"
                    - discounted_item_total = get_price_in_currency(@cart.item_total(1) - @cart.additional_discounts(@actual_country), @rate).round(2)
                    - if false #is_domestic? && discounted_item_total < 1000
                      %br
                      .minimum_cart_value_message
                        = "Minimum order amount is #{get_price_with_symbol(1000, @hex_symbol)}"
                    - elsif false #!is_domestic? && discounted_item_total < @cart_minimum_value.to_f
                      %br
                      .minimum_cart_value_message
                        = "Minimum order amount is #{get_price_with_symbol(@cart_minimum_value, @hex_symbol)}"

                  - if account_signed_in?
                    - checkout_url = new_order_path
                  - elsif guest_logged_in?
                    - if session[:guest_shipping_address].present?
                      - checkout_url = new_order_url(protocol: Rails.application.config.partial_protocol)
                    - else
                      - checkout_url = collect_addresses_path
                  - else
                    - checkout_url = accounts_guest_login_url(protocol: Rails.application.config.partial_protocol)
                  .checkout-action-btn-box.d-view{"data-turbolinks" => "false"}
                    = link_to checkout_url do
                      %button.common-black-btn.action-btn.checkout-btn{:type => "button"} checkout
                    = link_to recent_url do
                      %button.common-white-btn.action-btn.continue-btn{:type => "button"} continue shopping
                  .m-bottom-fixed.mobilePositionFixedToggle.m-view
                    .checkout-action-btn-box
                      .item-1
                        %span.c-price= "#{get_price_with_symbol(grandtotal.to_f, @hex_symbol)}"
                        -# %button.c-view-detail-btn{:type => "button"} View details
                      .item-2{"data-turbolinks" => "false"}
                        = link_to checkout_url do
                          %button.common-black-btn.action-btn.checkout-btn{:type => "button"} place order
                .help-info-box.d-view
                  %p.info-1
                    %a{:href => "https://api.whatsapp.com/send?phone=9137407527&text=Hello"} Facing issues with this order?
                  %p.info-2
                    %a{:href => "https://api.whatsapp.com/send?phone=9137407527&text=Hello"} NEED HELP?
      -if false
        .product-slider-wrapper.common-product-v2-slider-wrapper.border-top-white
          .container-layout-max
            .horizontal-padding
              .container-fluid
                .row
                  .col-7
                    %h1.heading what's trending!
                  .col-5.text-end.see-all-link-box
                    %a.a-lnik{:href => "#", :target => "_blank"}
                      see all
                      %i.fa-solid.fa-angle-right
          .swiper.product-swiper.commonProductV2SwiperContainer
            .swiper-wrapper
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
            .swiper-button-next.product-next-btn
              %i.fa-solid.fa-angle-right
            .swiper-button-prev.product-prev-btn
              %i.fa-solid.fa-angle-left
        .product-slider-wrapper.common-product-v2-slider-wrapper.hide-btn.border-top-white
          .container-layout-max
            .horizontal-padding
              .container-fluid
                .row
                  .col-7
                    %h1.heading complete the look
                  .col-5.text-end.see-all-link-box
                    %a.a-lnik{:href => "#", :target => "_blank"}
                      see all
                      %i.fa-solid.fa-angle-right
          .swiper.product-swiper.commonProductV2SwiperContainer
            .swiper-wrapper
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price ₹15,070
                      %span.mrp ₹11,822
                      %span.discount (25% OFF)
            .swiper-button-next.product-next-btn
              %i.fa-solid.fa-angle-right
            .swiper-button-prev.product-prev-btn
              %i.fa-solid.fa-angle-left


- else
  = render partial: 'luxe/carts/no_items_cart'

- content_for :page_specific_js do
  = javascript_include_tag 'carts_luxe'
  = javascript_include_tag 'luxe/wallet_referral.js'
  
- if @cart.line_items.where(buy_get_free: 1).sum(:quantity) ==1
  :javascript
    title = $('.b1g1_text').attr('title');
    $('.b1g1_info').append('<a href="/buy1get1-offers">' + $('.b1g1_text').attr("title") + '</a>');

:javascript
    var ww = document.documentElement.clientWidth;
    // ------------------------------------------------------------------
    // Set Body Top Padding For Fixed Header
    if (ww >= 1000) {
      var headerHeight = $('.desktop-header-component-container:visible').css('height');
      $('#bodyTagContainer').css('padding-top', headerHeight);
    }

    $(document).ready(function() {
      MR.login.headerIconClickEvent('.wishlist-icon','/accounts/sign_in','#loginModal'); 
    });

= javascript_include_tag 'luxe/carts', :defer => true, 'data-turbolinks-track' => 'reload'

- if @ga_hash_new.present? && @ga_hash_new[:ecommerce][:items].present?
  :javascript
    window.dataLayer = window.dataLayer || [];
    var view_cart_params = #{@ga_hash_new.to_json};
    var shipping = $('.shipping_cost').attr('data-shipping')
    var market_rate = $('.shipping_cost').attr('data-market-rate')
    view_cart_params.ecommerce.shipping = parseFloat((parseFloat(shipping) * parseFloat(market_rate)).toFixed(2));
    dataLayer.push({ ecommerce: null });
    dataLayer.push(view_cart_params);

    $('.checkout-btn').on('click', function(e) {
      if ($('#sign_in_form input[name="redirect_to_checkout"]').length === 0) {
        $('<input>').attr({
          type: 'hidden',
          id: 'redirect_to_checkout',
          name: 'redirect_to_checkout',
          value: 'true'
        }).appendTo('#sign_in_form');
      }
      var checkout_params = #{@ga_hash_new.to_json};
      checkout_params.event = "begin_checkout"
      var shipping = $('.shipping_cost').attr('data-shipping')
      var market_rate = $('.shipping_cost').attr('data-market-rate')
      checkout_params.ecommerce.shipping = parseFloat((parseFloat(shipping) * parseFloat(market_rate)).toFixed(2));
      dataLayer.push({ ecommerce: null });
      dataLayer.push(checkout_params);
      gads_items_id = #{@googe_add_hash_new.to_json};
    });

