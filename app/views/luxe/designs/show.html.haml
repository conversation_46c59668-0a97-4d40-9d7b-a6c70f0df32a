- designable_type = @design['designable_type']
- design_specs = DESIGN_SPEC_SUB_GROUP[designable_type].to_a
- design_spec_hash = get_spec_data(@design['specifications'],@design['type'],design_specs)
- package_details = @design['package_details']
= render partial: 'luxe/designs/seo_og'
= render partial: 'luxe/designs/microdata', locals: {design: @design, seo_design_spec: design_spec_hash[:seo_keys], package_details: package_details}

/ = stylesheet_link_tag 'lightgallery-bundle.min.css', media: 'all', 'data-turbolinks-track' => 'reload'
= stylesheet_link_tag 'luxe/product-detail', media: 'all', 'data-turbolinks-track' => 'reload'
:javascript
  afterWindowOrTrubolinksLoad(function(){
    loadScript('#{asset_url("social-share.js")}');
  })
/ Global site tag (gtag.js) - Google Ads: ***********
-# %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=AW-***********"}
-# :javascript
  -# window.dataLayer = window.dataLayer || [];
  -# function gtag(){dataLayer.push(arguments);}
  -# gtag('js', new Date());
-# 
  -# gtag('config', 'AW-***********');

/ Event snippet for Product Page Mirraw Luxe conversion page
-# :javascript
  -# gtag('event', 'conversion', {'send_to': 'AW-***********/8_AtCOyR594DEM26kfco'});
-# :javascript
  -# gtag('event', 'page_view', {
    -# 'send_to': 'AW-***********',
    -# 'value': "#{@design['discount_price']}",
    -# 'items': [{
      -# 'id': "#{@design['id']}",
      -# 'google_business_vertical': 'retail'
    -# }]
  -# });
%header.mobile-header-component-container
  .mobile-fixed-header-wrapper
    -if !is_mobile_view?
      .mobile-message-block.offer-txt.destop
        - if @offer_message.present?
          =raw @offer_message
        - else
          =raw "For Personal Assistance, you can WhatsApp us on"
          = link_to "+91 9137407527. ",'https://api.whatsapp.com/send?phone=************&text=Hello', target: '_blank', style: 'color:#f56a6a;'
        = render partial: 'luxe/pages/deal_timer_mobile'

    .flex-container
      .flex-item-1
        %button.action-btn.action-back-btn
          / prettier-ignore
          %svg#Back_arrow.back-arrow-button{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
            %rect#Rectangle_21663{"data-name" => "Rectangle 21663", :fill => "none", :height => "24", :width => "24"}
            %path#Path_102951{:d => "M1070.769,1253.048a1,1,0,0,0-1.41,0l-5.313,5.332h0a1.993,1.993,0,0,0,0,2.82h0l5.359,5.342a1,1,0,0,0,1.41-1.41l-4.919-4.9a.619.619,0,0,1,0-.877l4.875-4.9A1,1,0,0,0,1070.769,1253.048Z", "data-name" => "Path 102951", :fill => "#1f1f1f", :transform => "translate(-1055.462 -1247.758)"}
      .flex-item-2.header-logo
        //.d-none
        %a{href: '/'}
          =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img')
      / .flex-item-2.header-txt
      /   %span=@design['designer']['name'].try(:titleize)
      .flex-item-3
        .social-mshare-content
          = social_share_button_tag(@design['title'], :url => "#{request.original_url}")
        = link_to user_wishlists_path do
          %button.action-btn.wishlist-btn
            / prettier-ignore
            %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
              %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#f56a6a", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
        = link_to cart_path do
          %button.action-btn.cart-btn
            %span.cart-count= "#{session[:cart_count]}"
            / prettier-ignore
            %svg#icon_orders{"data-name" => "icon/orders", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %rect#Rectangle_23418{"data-name" => "Rectangle 23418", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
              %path#XMLID_1732_{:d => "M17.961,14.189l-2.093,6.593a1.109,1.109,0,0,1-1.057.783H6.73a1.154,1.154,0,0,1-1.076-.724L2.563,12.565H.783A.783.783,0,1,1,.783,11H3.111a.8.8,0,0,1,.743.528L7.043,20h7.435l1.663-5.283H6.926a.783.783,0,1,1,0-1.565H17.217a.778.778,0,0,1,.626.333.759.759,0,0,1,.117.7ZM7.122,22.6a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,7.122,22.6Zm7.024,0a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,14.146,22.6Z", :transform => "translate(3 -6.073)"}
    .mobile-message-block.offer-txt.mobile.plp
      - if @offer_message.present?
        =raw @offer_message
      - else
        =raw "For Personal Assistance, you can WhatsApp us on"
        = link_to "+91 9137407527. ",'https://api.whatsapp.com/send?phone=************&text=Hello', target: '_blank', style: 'color:#f56a6a;'
      = render partial: 'luxe/pages/deal_timer_mobile'

%main#mainContentContainer.main-content-container
  %span#mobileMenuOverlay
  .product-detail-page-container
    .container-fluid
      .horizontal-padding
        .container-flui
          .row
            .col-12
              .common-breadcrumb-wrapper
                - if @breadcrumbs.present?
                  - final = @breadcrumbs.pop
                  %ul.breadcrumb-list{itemscope: '', itemtype: 'http://schema.org/BreadcrumbList'}
                    - @breadcrumbs.each_with_index do |crumb, i|
                      %li.b-item{itemscope: '', itemprop: 'itemListElement', itemtype: 'http://schema.org/ListItem'}
                        = link_to crumb[:url], itemprop: 'item', 'aria-label' => 'Breadcrumb url', class: 'b-link' do
                          %span.bcrumb{itemprop: 'name'}= crumb[:title]
                        %meta{itemprop: 'position', content: i+1}
                    %li.b-item{itemscope: '', itemprop: 'itemListElement', itemtype: 'http://schema.org/ListItem'}
                      - url = request.url.split('?').first
                      %a.b-link{href: url, itemprop: 'item', 'aria-label' => 'Breadcrumb url'}
                      %span.final.bcrumb{itemprop: 'name'}= final[:title]
                      %meta{itemprop: 'position', content: @breadcrumbs.length+1}
        .main-flex-container
          .main-flex-item-1
            #desktopLightboxGalleryContainer.desktop-view-product-slider-wrapper
              .container-fluid
                .row
                  -@design['images'].each_with_index do  |designImage, index|
                    - if designImage['sizes']
                      - original =  designImage['sizes']['luxe_webp'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                      - large = designImage['sizes']['luxe_webp'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                      -original_img = designImage['sizes']['luxe_webp'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                    -if index==0
                      .col-12
                        .product-img-box.big
                          %span.zoomImgWrap.zoomImgSelectorContainer
                            %img.lazyload.cmn-img-fluid.p-img.p-medium-img{id: 'zoom_01', src: IMAGE_PROTOCOL + original, data: {src: IMAGE_PROTOCOL + original_img, index: index}}
                    -else
                      .col-6
                        .product-img-box.medium
                          %span.zoomImgWrap.zoomImgSelectorContainer
                            %img.lazyload.cmn-img-fluid.p-img.p-medium-img{src: IMAGE_PROTOCOL + large}
            #mobileLightboxGalleryContainer.mobile-view-product-slider-wrapper
              .container-fluid.xs-no-padding
                .row.xs-no-padding.xs-no-margin
                  .col-12.xs-no-padding
                    .swiper.mpv-swiper.mobileViewProductSwiperContainer
                      .swiper-wrapper
                        -@design['images'].each_with_index do  |designImage, index|
                          - if designImage['sizes']
                            - original =  designImage['sizes']['luxe_webp'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                            - thumb = designImage['sizes']['thumb'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['thumb']
                            - large = designImage['sizes']['luxe_webp'].empty? ? asset_path('default_image.jpg') : designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                            -original_img = designImage['sizes']['luxe_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                            .swiper-slide.mpv-slide-item
                              .mpv-img-box
                                %span.zoomImgWrap.zoomImgSelectorContainer
                                  %img.design_image.lazyload.cmn-img-fluid.mpv-img{:alt => "Mirraw", :src => IMAGE_PROTOCOL + original, data: {src: IMAGE_PROTOCOL + original_img, index: index}}
                      .swiper-pagination
                      -# .swiper-button-next
                        %i.fa-solid.fa-angle-right.navigation-icon
                      -# .swiper-button-prev
                        %i.fa-solid.fa-angle-left.navigation-icon
                    =render partial: 'photoswipe_slider'
                  .similar_designs
                    = image_tag('similar_product.png', alt: '',id: 'similar_design-2', class: 'similar_design-2', style: 'width: 25px; height: 25px;margin-left:-6px;margin-top:-6px')
          .main-flex-item-2
            .product-detail-content-wrapper
              .container-fluid
                .row
                  .col-12
                    .xs-space.product-name-box
                      %a.vendor-detail{:href =>  "/#{@design['designer_cached_slug']}"}
                        %h1.heading=@design['designer']['name'].try(:titleize)
                      %h2.sub-heading=@design['title'].titleize
                  .col-12
                    .xs-space.amount-cash-box
                      .amount-box
                        %span.price
                          - if @country_code == 'IN' && @design['discount_percent'] == 0
                            %span.mrp-tag.single MRP
                            = get_price_with_symbol(@design['discount_price'], @design['hex_symbol'])
                          - else
                            = get_price_with_symbol(@design['discount_price'], @design['hex_symbol'])
                          -if @country_code == 'IN' && @design['discount_percent'].to_f > 0
                            %span.mrp-tag.full-price MRP
                            %span.mrp.variant-price
                              /%i.currency-icon.fa-solid
                              = get_price_with_symbol(@design['price'], @design['hex_symbol'])
                            %span.discount= "#{@design['discount_percent']} % OFF"  
                          -elsif @design['discount_percent'].to_f > 0 
                            %span.mrp.variant-price
                              /%i.currency-icon.fa-solid
                              = get_price_with_symbol(@design['price'], @design['hex_symbol'])   
                            %span.discount= "#{@design['discount_percent']} % OFF"
                      -if @country_code == 'IN'    
                        %h5.inclusive-tax (Inclusive of all taxes.)    
                      / .cash-box
                      /   .green-layer
                      /     / prettier-ignore
                      /     %svg#Iconly_Bold_Wallet.wallet-icon{"data-name" => "Iconly/Bold/Wallet", :height => "12.6", :viewbox => "0 0 14 12.6", :width => "14", :xmlns => "http://www.w3.org/2000/svg"}
                      /       %g#Wallet{:transform => "translate(0 0)"}
                      /         %path#Path_220734{:d => "M10.161,0A3.481,3.481,0,0,1,14,3.767H11.038v.024A2.458,2.458,0,0,0,8.549,6.218a2.458,2.458,0,0,0,2.489,2.427H14v.218A3.473,3.473,0,0,1,10.161,12.6H3.839A3.473,3.473,0,0,1,0,8.863H0V3.737A3.473,3.473,0,0,1,3.839,0h6.322Zm3.316,4.811A.516.516,0,0,1,14,5.32h0V7.092a.522.522,0,0,1-.523.51H11.094A1.474,1.474,0,0,1,9.632,6.485a1.388,1.388,0,0,1,.3-1.156,1.464,1.464,0,0,1,1.1-.518h2.439Zm-2.1.819h-.23a.536.536,0,0,0-.378.149.509.509,0,0,0-.157.367.534.534,0,0,0,.535.528h.23a.522.522,0,1,0,0-1.043ZM7.268,2.724H3.316a.529.529,0,0,0-.535.516.535.535,0,0,0,.535.528H7.268a.522.522,0,1,0,0-1.043Z", "data-name" => "Path 220734", :fill => "#46bf5e", :transform => "translate(0 0)"}
                      /     %span.txt
                      /       Earn
                      /       %i.currency-icon.fa-solid
                      /       %b.amount 244
                      /       Mirraw cash
                      - end_date = @design['promotion_offer_end_time'] if @design['promotion_offer_end_time'].present?
                      -if false #end_date.present?
                        .deal-end-box
                          .grey-layer
                            %p.timer
                              Deal ends in
                              %span 01:23:45
                  .col-12
                    - if @design['variants'].present? && (@design['addon_types'].blank? || [nil,'stitching'].exclude?(@design['addon_types'][0]['type_of_addon']))
                      .select-size-options                        
                        -if FOOTWEAR_CATEGORY_PARENT_IDS.include?(@category.parent_id) || FOOTWEAR_CATEGORY_IDS.include?(@category.id)
                          .xs-space.select-size-box.pd-top#variants_block
                            %span.ss-txt Select Size
                            %span.uk-text (UK Size)
                            .select-size-text Please select a size
                        -else 
                          .xs-space.select-size-box.pd-top#variants_block
                            %span.ss-txt Select Size
                            .select-size-text Please select a size
                        .select-size-box
                          .chart-scale-box
                            %button.size-chart-btn{"data-bs-target" => "#sizeMeasureChartModal", "data-bs-toggle" => "modal"}
                              / prettier-ignore
                              %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                                %rect#Rectangle_24255{"data-name" => "Rectangle 24255", :fill => "none", :height => "24", :width => "24"}
                                %g#ruler{:transform => "translate(2.563 -2.112)"}
                                  %path#Path_220146{:d => "M2.359,9A2.359,2.359,0,0,0,0,11.359v5.5a2.359,2.359,0,0,0,2.359,2.359H16.514a2.359,2.359,0,0,0,2.359-2.359v-5.5A2.359,2.359,0,0,0,16.514,9Zm4.718,1.573H5.5V14.5a.786.786,0,0,1-1.573,0V10.573H2.359a.786.786,0,0,0-.786.786v5.5a.786.786,0,0,0,.786.786H16.514a.786.786,0,0,0,.786-.786v-5.5a.786.786,0,0,0-.786-.786H14.941v2.359a.786.786,0,0,1-1.573,0V10.573H11.8V14.5a.786.786,0,0,1-1.573,0V10.573H8.65v2.359a.786.786,0,0,1-1.573,0Z", "data-name" => "Path 220146", "fill-rule" => "evenodd", :transform => "translate(0)"}
                              %span.c-txt CHART
                      #variants_block.xs-space.select-size-box.select-size
                        %div.s-size-box-wrap
                          / %span.ss-txt Select size
                          / %span.var-title Select Size
                          / - if @design['variant_size_chart'].present?
                          /   %span.size-chart-text
                          /     %span
                          /       For Help Check
                          /       %button#dynamic-size-chart-btn.btn-view-size{"data-reveal-id" => "dynamicSizeChartModal"} Size Chart
                          /       #dynamicSizeChartModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;overflow: scroll; height: 100%; top: 0'}
                          /         = render partial: '/designs/design_size_chart', locals: { size_chart: @design['variant_size_chart'].merge('is_variant' => true) }

                          - change_delivery_date = @design['variants'].collect{|v| v['id'] if v['in_warehouse']}.uniq.compact
                          - @design['variants'].each do |design|
                            - variant_quantity = design['quantity']
                            - if variant_quantity <= 0
                              - #%a.variant.button.secondary.round.tiny.line_through_text.disabled{:id => "#{design['id']}"}
                              - #design['option_type_values'].first['name']
                            - else
                              -if change_delivery_date.present?
                                - if change_delivery_date.include?(design['id'])
                                  - delivery_date = Time.now.advance(days: design['variant_estimated_delivery_days']).strftime('%d %b %Y')
                                  - ready_to_ship = 'true'
                                - else
                                  - delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
                                  - ready_to_ship = 'false'
                                %a.variant.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: get_price_with_symbol(design['price'], @design['hex_symbol']),price: get_price_with_symbol(design['discount_price'], @design['hex_symbol']), delivery_date: delivery_date, ready_to_ship: ready_to_ship }}
                                  = design['option_type_values'].first['name']
                                  - if variant_quantity < 4
                                    .variant-qty-left-msg= variant_quantity.to_s + 'left'
                              -else
                                %a.button.secondary.s-size-box.variant{:id => "#{design['id']}",data: {old_price: get_price_with_symbol(design['price'], @design['hex_symbol']),price: get_price_with_symbol(design['discount_price'], @design['hex_symbol']) }}
                                  %span.s-txt= design['option_type_values'].first['name']
                                - if false #variant_quantity < 4
                                  .variant-qty-left-msg= variant_quantity.to_s + ' left'
                      / .xs-space.select-size-box
                      /   .s-size-box-wrap
                      /     .s-size-box.s-selected
                      /       .lightning-icon-box.show
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt xs
                      /     .s-size-box
                      /       .lightning-icon-box.show
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt s
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt m
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt l
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt xl
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt xxl
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt 3xl
                      /     .s-size-box
                      /       .lightning-icon-box
                      /         / prettier-ignore
                      /         %svg.s-lightning-icon{:height => "8", :viewbox => "0 0 4.206 7.01", :width => "5", :xmlns => "http://www.w3.org/2000/svg"}
                      /           %path#bolt{:d => "M13.206,3H10.4L9,6.856h1.4L9.7,10.01l3.155-4.557H11.105Z", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-9 -3)"}
                      /       %span.s-txt 4xl
                      .xs-space.select-size-box.pd-bottom
                        %div
                          %span.s-info-error-txt  Please select a size
                        %div
                          %span.s-info-vary-txt *Prices may vary based on selected size
                          / .s-body-box
                          /   %span.s-info-txt
                          /     Bust
                          /     %b.s-size-info 43
                          /   %span.s-info-txt
                          /     Waist
                          /     %b.s-size-info 36
                          /   %span.s-info-txt
                          /     Hips
                          /     %b.s-size-info 39
                    -if false
                      .xs-space.select-size-box.pd-bottom
                        %div
                          %span.s-info-item-left-txt Only 2 items left, Hurry up!
                  .col-12
                    .xs-space.add-cart-share-box
                      - if @design['state'] == 'in_stock'
                        %button#itemAddedCartShowToast.p-add-cart-btn.add_to_buy_bow{'data-targeturl': line_items_path, design_id: @design['id'], unbxdattr: "AddToCart", unbxdparam_sku: @design['id'], unbxdparam_requestId: session[:unbxdparam_requestId]} add to cart
                      - else
                        .disabled.btn-bg.p-oos-btn Out Of Stock
                      %button.p-view-cart-green-btn.d-none view cart
                      .wishlist-forms.hide{data: {design_id: @design['id']}}
                        / .wishlist-error
                        - if account_signed_in? && @current_user.wishlists.exists?(design_id: @design['id'])
                          = form_for :wishlist, url: user_wishlists_path(design_id: @design['id']), html: {class: 'delete_wishlist', id: "design_#{@design['id']}_delete_wishlist"}, method: :delete do |f|
                            = f.button 'aria-label' => 'submit', type: :submit, class: 'wishlist-heart-button p-wishlist-btn p-wishlisted' do
                              / prettier-ignore
                              %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "22", :viewbox => "0 0 18 18", :width => "22", :xmlns => "http://www.w3.org/2000/svg"}
                                %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "22", :opacity => "0", :width => "22"}
                                %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "#f56a6a", :stroke => "#f56a6a", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
                        - else
                          = form_for Wishlist.new(design_id: @design['id']), url: user_wishlists_path, namespace: "design_#{@design['id']}" do |f|
                            = f.hidden_field :design_id
                            = f.button 'aria-label' => 'submit', type: :submit,id: 'pdp-wishlist-btn', class: "wishlist-heart-button empty-heart p-wishlist-btn p-wishlisted #{account_signed_in? ? 'signed-in' : 'not-signed-in'}" do
                              / prettier-ignore
                              %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "22", :viewbox => "0 0 18 18", :width => "22", :xmlns => "http://www.w3.org/2000/svg"}
                                %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "22", :opacity => "0", :width => "22"}
                                %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#f56a6a", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
                  .col-12
                    - if @design['product_offer'].present?
                      - is_flash_deals = @design['product_offer']['type'] == 'flash_deals'
                      - is_bmgn_product = @design['product_offer']['type'] == 'bmgnx'
                      - is_qpm_active = @design['product_offer']['type'] == 'qpm'
                    - is_prepaid_offer = (prepaid_message = PrepaidPaymentPromotion.design_message(@country_code)).present?
                    - prepaid_shipping_offer = @country_code == 'IN' && DOMESTIC_PREPAID_SHIPPING_PROMOTION
                    - if is_bmgn_product || is_qpm_active || is_prepaid_offer || prepaid_shipping_offer
                      .xs-space.best-offer-code-box.btm-hr-line.side-space
                        %h2.heading Best offers
                        .offer-flex-container
                          - if is_prepaid_offer
                            .offers-box
                              %p.code-txt=prepaid_message
                          - if prepaid_shipping_offer
                            .offers-box
                              %p.code-txt Free Shipping on Online Payment
                          - if is_bmgn_product
                            .offers-box
                              - bmgnx_msg = @design['product_offer']['msg']
                              %a.code-txt.b1g1{href:"/buy-m-get-n-free", title: "View More - #{bmgnx_msg} Products", data: {toggle:'tooltip', placement: 'right'}}=bmgnx_msg
                          -if @design['free_shipping_eligible'] == true && !is_domestic?
                            .offers-box
                              .code-txt
                                %ul{style: 'list-style-type: none;'}
                                  %li
                                    Get 10% Off with Coupon Code <span style='font-weight: bold;'>'LUXE10'</span>
                                  %li 
                                    <span style='font-weight: bold;'>Free Shipping</span> on Orders above $199
                          -if @design['ready_to_ship']
                            .offers-box
                              %p.code-txt Ready To Ship
                          - if false #Wallet.cashback_percent > 0 && !is_domestic?
                            .offers-box
                              .code-txt.loyalty_cashback #{Wallet.cashback_percent}% cashback
                              / &nbsp;
                              / %a.loyalty_tnc.right{href:"#", 'data-reveal-id' => "loyaltyTncModal"} T&C
                  .col-12
                    .xs-space.delivery-details-box.accordion.side-space
                      .accordion-item
                        %h2#accordionDeliveryDetails.accordion-header
                          Delivery details
                      #accordionDeliveryDetailsCollapse.accordion-collapse.collapse.show{"aria-labelledby" => "accordionDeliveryDetails", "data-bs-parent" => "#accordionExample"}
                        .accordion-body-pin
                          .pincode-box
                            - if @country_code == 'IN'
                              %form#pincode-form
                                .common-float-input-grp.floating-label-group
                                  / To Show Error Msg Use This Class 'show-error-block'
                                  / <div class="common-float-input-grp show-error-block">
                                  .float-input-wrap
                                    = hidden_field_tag 'pdd-product-id', @design['id']
                                    %span.location
                                      = image_tag('location.png', alt: 'location', class: 'btn-img')
                                    %input.cmn-form-input{:name => "pincode", placeholder: " Enter Pincode", type: "text", id: 'pin_code', required: true, maxlength: 6, pattern: "[0-9]{6}"}/
                                    %label.cmn-form-label.floating-label{:for => "pincode"} Enter Pincode
                                  .check_btn{:type => "submit", :id => 'check-for-pdd'} Check 
                                  .error-wrap
                                    %span.err-msg Delivery is not available for your location
                            .d-info.delivery-block           
                              %span.d-txt1 Estimated Delivery :
                              - if @country_code != 'IN' && (@design['sor_available'] || @design['variant_sor_available'])#rts_available_country?(@actual_country)
                                -hidden_class = @country_code != 'IN' && (@design['addon_types'].blank? || @design['unstitched_in_warehouse'] || @design['variant_sor_available']) ? '' : 'display: none;'
                                &nbsp;
                                -if (@design['addon_types'].blank? || @design['unstitched_in_warehouse'] || @design['variant_sor_available'])
                                  -delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
                                -else
                                  -delivery_date = Time.now.advance(days: @design['non_rts_time']).in_time_zone.strftime('%d %b %Y')
                              - else
                                -if @eta > 0
                                  -delivery_date = @eta
                                -else
                                  -delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s                      
                              %span.d-date.delivery-day=delivery_date
                            .pdd-error-message
                  .col-12
                    .xs-space.accordion.btm-hr-line.side-space
                      .accordion-item 
                        .need-help
                          %h2#accordionDisclaimer.accordion-header
                            Need Help ?
                          %ul.social-icon_share
                            %li
                              = link_to 'https://api.whatsapp.com/send?phone=9137407527&text=Hello', target: '_blank' do
                                %span.icon-box
                                  / prettier-ignore
                                  %svg{:height => "16.073", :viewbox => "0 0 16 16.073", :width => "16", :xmlns => "http://www.w3.org/2000/svg", "xmlns:xlink" => "http://www.w3.org/1999/xlink"}
                                    %defs
                                      %lineargradient#linear-gradient{:gradientunits => "objectBoundingBox", :x1 => "0.5", :x2 => "0.5", :y1 => "1"}
                                        %stop{:offset => "0", "stop-color" => "#20b038"}
                                        %stop{:offset => "1", "stop-color" => "#60d66a"}
                                    %g#Group_148617{"data-name" => "Group 148617", :transform => "translate(0 0.002)"}
                                      %path#Path_41{:d => "M13.669,2.34A7.967,7.967,0,0,0,1.13,11.949L0,16.076l4.223-1.107a7.961,7.961,0,0,0,3.807.969h0a7.967,7.967,0,0,0,5.635-13.6ZM8.033,14.593h0a6.614,6.614,0,0,1-3.37-.923l-.242-.143-2.506.657.669-2.443-.158-.25a6.622,6.622,0,1,1,5.609,3.1Z", "data-name" => "Path 41", :fill => "#e0e0e0", :transform => "translate(0 -0.005)"}
                                      %path#Path_42{:d => "M27.875,36.411l1.079-3.939a7.6,7.6,0,1,1,6.588,3.806h0a7.6,7.6,0,0,1-3.631-.924L27.876,36.41Z", "data-name" => "Path 42", :fill => "url(#linear-gradient)", :transform => "translate(-27.509 -20.798)"}
                                      %rect#Rectangle_1{"data-name" => "Rectangle 1", :fill => "none", :height => "8.383", :opacity => "0.08", :transform => "translate(3.555 3.822)", :width => "8.948"}
                                      %path#Path_43{:d => "M309.321,323.373c-.147-.328-.3-.334-.443-.34-.115-.005-.246,0-.377,0a.723.723,0,0,0-.525.246,2.206,2.206,0,0,0-.689,1.641,3.827,3.827,0,0,0,.8,2.035,8.076,8.076,0,0,0,3.361,2.97c1.662.655,2,.525,2.361.492a1.987,1.987,0,0,0,1.328-.935,1.643,1.643,0,0,0,.115-.936c-.049-.082-.18-.131-.377-.23s-1.164-.575-1.345-.64-.312-.1-.443.1-.508.64-.623.771-.23.148-.426.049a5.382,5.382,0,0,1-1.582-.977,5.927,5.927,0,0,1-1.095-1.363c-.115-.2-.012-.3.086-.4s.2-.23.3-.345a1.35,1.35,0,0,0,.2-.328.362.362,0,0,0-.016-.345C309.878,324.735,309.5,323.762,309.321,323.373Z", "data-name" => "Path 43", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-303.256 -318.791)"}
                                      %path#Path_44{:d => "M20.512,2.414a7.875,7.875,0,0,0-12.395,9.5L7,15.993,11.175,14.9a7.87,7.87,0,0,0,3.763.958h0A7.875,7.875,0,0,0,20.512,2.414ZM14.941,14.527h0a6.538,6.538,0,0,1-3.332-.913l-.239-.141-2.478.649.661-2.415L9.4,11.46a6.546,6.546,0,1,1,5.545,3.066Z", "data-name" => "Path 44", :fill => "#fff", :transform => "translate(-6.908 -0.105)"}
                            %li
                              %a{href: "tel:+************"}
                                %span.icon-box
                                  %svg{height: "14", viewBox: "0 0 14 14", width: "14", xmlns: "http://www.w3.org/2000/svg"}
                                    %path#Stroke-1{d: "M6,7.449c4.576,4.81,4.646-.138,6.488,1.7,1.776,1.775,2.25,1.528,0,3.777-.282.226-2.072,2.951-8.364-3.339S.552,1.507.779,1.225c2.255-2.255,1.977-1.775,3.752,0C6.373,3.066,1.422,2.639,6,7.449Z", "fill-rule": "evenodd", transform: "translate(0 0.291)"}
                            %li
                              %a{href: "mailto:<EMAIL>"}
                                %span.icon-box
                                  %svg{:height => "14.4", :viewbox => "0 0 16 14.4", :width => "16", :xmlns => "http://www.w3.org/2000/svg"}
                                    %path#Path_220136{:d => "M11.951,0A4.045,4.045,0,0,1,16,4.04h0v6.32a4.046,4.046,0,0,1-4.049,4.04h-7.9A4.045,4.045,0,0,1,0,10.36H0V4.04A4.04,4.04,0,0,1,4.048,0h7.9Zm.9,4.16a.608.608,0,0,0-.449.16h0L8.8,7.2a1.253,1.253,0,0,1-1.6,0h0L3.6,4.32a.612.612,0,0,0-.857.856h0l.1.1,3.64,2.84a2.519,2.519,0,0,0,3.127,0h0l3.609-2.888.064-.064a.619.619,0,0,0-.009-.8A.673.673,0,0,0,12.856,4.16Z", "data-name" => "Path 220136"}
                        #accordionDisclaimerCollapse
                          .accordion-body
                            %ul.help-list
                              %li 
                                %img.design_image{ src: asset_path('early-delivery.svg') }
                                %span.d-txt.a-description Early Delivery
                              %li 
                                %img.design_image{ src: asset_path('set-break.svg') }
                                %span.d-txt.a-description Don’t want the whole set?    
                              %li 
                                %img.design_image{ src: asset_path('customisation.svg') }
                                %span.d-txt.a-description Customisations 
                  .product-detail-care
                    .col-12
                      .xs-space.accordion.side-space
                        .product-detail
                          - if @design['description'].present?
                            .accordion-item.product-des
                              %h2#accordionProductDetails.accordion-header
                                Product details
                              #accordionProductDetailsCollapse
                                .accordion-body
                                  %span.a-description
                                    = simple_format(@design['description'].to_s.titleize)
                            .xs-space.sku-code-box
                              %span.sk-txt SKU Code
                              %span.code=@design['id']
                  
                    #product_id.hide{style: 'display:none'}= "Product ID : #{@design['id']}"
                  
                    .col-12
                      .xs-space.accordion.btm-hr-line.side-space.care
                        .product-detail
                          .accordion-item
                            %h2#accordionCompositionCare.accordion-header
                              Care
                            #accordionCompositionCareCollapse
                              .accordion-body
                                %span.a-description.care-apparels Dry Clean Only
                                - CARE_DESC.each do |care|
                                  - if care.values.first.include?(@category.parent_id) || care.values.first.include?(@category.id)
                                    %span.a-description.care-jewellery #{care.keys.first}
                    
                          - if @design['description'].blank?
                            .xs-space.sku-code-box.no-description
                              %span.sk-txt SKU Code
                              %span.code=@design['id']
                  
                  -if false
                    .col-12
                      .xs-space.specifications-box.accordion.btm-hr-line.side-space
                        .accordion-item
                          %h2#accordionSpecifications.accordion-header
                            Specifications
                          #accordionSpecificationsCollapse
                            .accordion-body
                              .spec-flex-container
                                .spec-flex-item
                                  =image_tag('pd_stitching_type.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Stitching type
                                  %span.spec-subtitle Semi-stitched
                                .spec-flex-item
                                  =image_tag('pd_top_fabric.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Top Fabric
                                  %span.spec-subtitle Santoon
                                .spec-flex-item
                                  =image_tag('pd_pattern_type.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Pattern type
                                  %span.spec-subtitle Printed
                                .spec-flex-item
                                  =image_tag('pd_weight.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Weight
                                  %span.spec-subtitle 450 gms
                                .spec-flex-item
                                  =image_tag('pd_bottom_material.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Bottom material
                                  %span.spec-subtitle Santoon
                                .spec-flex-item
                                  =image_tag('pd_sleeve_length.png', alt: '', class: 'cmn-img-fluid spec-icon')
                                  %span.spec-title Sleeve Length
                                  %span.spec-subtitle 3/4 th sleeves
                  .col-12
                    .xs-space.accordion.btm-hr-line.side-space
                      .accordion-item
                        %h2#accordionDisclaimer.accordion-header
                          Price Match Promise
                        #accordionDisclaimerCollapse
                          .accordion-body
                            .price-match-text
                              %span.d-txt.a-description If you find the product for less we'll match it!
                              %a.know-more{:href =>  "/pages/price_match"} Know More
                  .col-12
                    .xs-space.accordion.btm-hr-line.side-space
                      .accordion-item
                        %h2#accordionDisclaimer.accordion-header
                          Disclaimer
                        #accordionDisclaimerCollapse
                          .accordion-body
                            -if @design["designer"]["id"] == 15896
                              %span.d-txt.a-description Please note: Since we are a made-to-measure brand and our dyeing process and embroidery are handcrafted, there may be slight variations in the color and embroidery of the actual product. The color of the outfit might also slightly vary from what you see on your device's screen. The print placement may also vary from what is represented in the images shown on the product page.
                            -else
                              %span.d-txt.a-description Color of product may slightly vary due to digital photography or your monitor/mobile settings.
                            %span.d-txt.a-description All orders are not returnable.          
                  .col-12
                    .xs-space.accordion.btm-hr-line.side-space
                      .accordion-item.share-icons-box
                        %h2#accordionDisclaimer.accordion-header
                          Share
                        #accordionDisclaimerCollapse
                          .accordion-body
                            %ul.product-page-social-icon_share
                              %li
                                = link_to 'https://api.whatsapp.com/send?phone=9137407527&text=Hello', target: '_blank' do
                                  %img.design_image{ src: asset_path('pdp-whatsapp.png') }
                              %li
                                = link_to 'https://www.instagram.com/muzai.co/', target: '_blank' do
                                  %img.design_image{ src: asset_path('pdp-insta.png') } 
    
                  
    .container-fluid.mobie-view-space
      .col-12
        .xs-space.accordion.btm-hr-line.side-space
          -if @similar_designs['designs'].present?
            =render partial: 'solr_recommendations', locals: {data: @similar_designs['designs'], title: "Similar Products", view_all: @category.name.parameterize}
      .col-12
        .xs-space.accordion.side-space
          -if @more_from_designer.present? && @more_from_designer["search"]["designs"].present?
            =render partial: 'solr_recommendations', locals: {data: @more_from_designer["search"]["designs"], title: "More from #{@more_from_designer["search"]["designs"][0]["designer"].split.map(&:capitalize).join(' ')}", view_all: @designer.name.parameterize}
      -if false
        .common-product-v2-slider-wrapper.border-top-white
        .container-fluid
          .row
            .col-12.col-sm-8
              %h1.heading similar items
            .col-12.col-sm-4.text-end.see-all-link-box
              %a.a-lnik{:href => "#", :target => "_blank"}
                see all
                %i.fa-solid.fa-angle-right
        .swiper.product-swiper.commonProductV2SwiperContainer
          .swiper-wrapper
            -[].each do |item|
              .swiper-slide.product-slide-item
                %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                  .p-img-box
                    %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                  .p-txt-box
                    %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                    %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                    .amount-box
                      %span.price
                        %i.currency-icon.fa-solid>
                        15,070
                      %span.mrp
                        %i.currency-icon.fa-solid>
                        11,822
                      %span.discount 25% OFF  
                    .action-btn-grp.nestedClickStopPropagation
                      %button.p-card-btn add to cart
                      %button.p-wishlist-btn.p-wishlisted
                        / prettier-ignore
                        %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                          %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                          %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}S
          .swiper-button-next.product-next-btn
            %i.fa-solid.fa-angle-right
          .swiper-button-prev.product-prev-btn
            %i.fa-solid.fa-angle-left
      -if false
        .common-product-v2-slider-wrapper.border-top-white
        .container-fluid.heading-box
          .row
            .col-8
              %h1.heading complete the look
            .col-4.text-end.see-all-link-box
              %a.a-lnik{:href => "#", :target => "_blank"}
                see all
                %i.fa-solid.fa-angle-right
        .swiper.product-swiper.commonProductV2SwiperContainer
          .swiper-wrapper
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn.p-wishlisted
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn.p-wishlisted
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
            .swiper-slide.product-slide-item
              %a.common-product-card-v2-wrapper{:href => "#", :target => "_blank"}
                .p-img-box
                  %img.lazyload.cmn-img-fluid.p-card-img{:alt => "Mirraw", "data-src" => "../../assets/img/slider_card_img1.png", :src => "../../assets/img/product-placeholder.png"}/
                .p-txt-box
                  %p.p-card-name Ps Pret By Payal Singhal
                  %p.p-card-des Ivory colour printed art crepe tunic
                  .amount-box
                    %span.price
                      %i.currency-icon.fa-solid>
                      15,070
                    %span.mrp
                      %i.currency-icon.fa-solid>
                      11,822
                    %span.discount 25% OFF
                  .action-btn-grp.nestedClickStopPropagation
                    %button.p-card-btn add to cart
                    %button.p-wishlist-btn.p-wishlisted
                      / prettier-ignore
                      %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                        %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                        %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
          .swiper-button-next.product-next-btn
            %i.fa-solid.fa-angle-right
          .swiper-button-prev.product-prev-btn
            %i.fa-solid.fa-angle-left
      -if false
        .mobile-share-whatsapp-wrapper
          = link_to 'https://api.whatsapp.com/send?phone=************&text=Hello', target: '_blank' do
            %button.btn-share-whatsapp
              / prettier-ignore
              %svg.whatsapp-svg-icon{:height => "66", :viewbox => "0 0 66 66", :width => "66", :xmlns => "http://www.w3.org/2000/svg", "xmlns:xlink" => "http://www.w3.org/1999/xlink"}
                %defs
                  %filter#Path_220740{:filterunits => "userSpaceOnUse", :height => "66", :width => "66", :x => "0", :y => "0"}
                    %feoffset{:dx => "-2", :dy => "3", :input => "SourceAlpha"}
                    %fegaussianblur{:result => "blur", :stddeviation => "3"}
                    %feflood{"flood-opacity" => "0.122"}
                    %fecomposite{:in2 => "blur", :operator => "in"}
                    %fecomposite{:in => "SourceGraphic"}
                  %lineargradient#linear-gradient{:gradientunits => "objectBoundingBox", :x1 => "0.5", :x2 => "0.5", :y1 => "1"}
                    %stop{:offset => "0", "stop-color" => "#20b038"}
                    %stop{:offset => "1", "stop-color" => "#60d66a"}
                %g#Group_149192{"data-name" => "Group 149192", :transform => "translate(-276 -751)"}
                  %g#chat_1_{"data-name" => "chat (1)", :transform => "translate(287 747.116)"}
                    %g{:filter => "url(#Path_220740)", :transform => "matrix(1, 0, 0, 1, -11, 3.88)"}
                      %path#Path_220740-2{:d => "M24.373,9.887A24.017,24.017,0,0,0,.563,39.074a43.529,43.529,0,0,1,1.084,9.372v6.1a1.692,1.692,0,0,0,1.692,1.692h6.1a43.531,43.531,0,0,1,9.372,1.084A24,24,0,1,0,24.373,9.887Z", "data-name" => "Path 220740", :fill => "#4ac958", :transform => "translate(11 -3.88)"}
                  %g#Whatsapp{:transform => "translate(299 768.946)"}
                    %g#Group_148306{"data-name" => "Group 148306"}
                      %path#Path_220118{:d => "M20.5,3.5A11.951,11.951,0,0,0,1.7,17.917L0,24.108l6.335-1.661a11.942,11.942,0,0,0,5.71,1.454h.005A11.951,11.951,0,0,0,20.5,3.5ZM12.05,21.883h0A9.92,9.92,0,0,1,6.991,20.5l-.363-.215-3.759.986,1-3.664-.236-.376a9.933,9.933,0,1,1,8.414,4.653Z", "data-name" => "Path 220118", :fill => "#e0e0e0"}
                      %path#Path_220119{:d => "M27.875,44.084l1.618-5.907a11.4,11.4,0,1,1,9.882,5.71h-.005A11.4,11.4,0,0,1,33.92,42.5Z", "data-name" => "Path 220119", :fill => "url(#linear-gradient)", :transform => "translate(-27.326 -20.663)"}
                      %path#Path_220120{:d => "M310.339,323.545c-.221-.492-.454-.5-.664-.51-.172-.007-.369-.007-.566-.007a1.084,1.084,0,0,0-.787.369,3.309,3.309,0,0,0-1.033,2.461,5.741,5.741,0,0,0,1.205,3.053,12.116,12.116,0,0,0,5.042,4.455c2.493.983,3,.788,3.542.738a2.981,2.981,0,0,0,1.992-1.4,2.465,2.465,0,0,0,.172-1.4c-.074-.123-.271-.2-.566-.344s-1.746-.862-2.017-.96-.467-.148-.664.148-.762.96-.934,1.157-.344.222-.64.074a8.073,8.073,0,0,1-2.374-1.465,8.887,8.887,0,0,1-1.642-2.044c-.172-.3-.018-.455.13-.6.133-.132.3-.345.443-.517a2.023,2.023,0,0,0,.295-.492.543.543,0,0,0-.025-.517C311.175,325.588,310.6,324.128,310.339,323.545Z", "data-name" => "Path 220120", :fill => "#fff", "fill-rule" => "evenodd", :transform => "translate(-301.242 -316.671)"}
                      %path#Path_220121{:d => "M27.268,3.577A11.814,11.814,0,0,0,8.676,17.826L7,23.945,13.262,22.3a11.8,11.8,0,0,0,5.645,1.438h.005A11.813,11.813,0,0,0,27.268,3.577ZM18.912,21.746h0a9.806,9.806,0,0,1-5-1.368l-.359-.213-3.716.974.992-3.622-.234-.371a9.819,9.819,0,1,1,8.317,4.6Z", "data-name" => "Path 220121", :fill => "#fff", :transform => "translate(-6.862 -0.112)"}
      .item-added-to-cart-toast-wrapper.p-3
        #itemAddedCartToastContent.toast.iac-toast-body{"aria-atomic" => "true", "aria-live" => "assertive"}
          .toast-body Item added to Cart
      #sizeMeasureChartModal.modal.fade.size-measure-chart-modal-wrapper{"aria-hidden" => "true", "aria-labelledby" => "sizeMeasureChartModalLabel", :tabindex => "-1"}
        .modal-dialog.modal-dialog-centered
          .modal-content
            .modal-body
              .cs-modal-header
                .mobile-header-component-container
                  .mobile-fixed-header-wrapper
                    .flex-container
                      .flex-item-1
                        %button.action-btn.action-back-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal"}
                          / prettier-ignore
                          %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                            %rect#Rectangle_21663{"data-name" => "Rectangle 21663", :fill => "none", :height => "24", :width => "24"}
                            %path#Path_102951{:d => "M1070.769,1253.048a1,1,0,0,0-1.41,0l-5.313,5.332h0a1.993,1.993,0,0,0,0,2.82h0l5.359,5.342a1,1,0,0,0,1.41-1.41l-4.919-4.9a.619.619,0,0,1,0-.877l4.875-4.9A1,1,0,0,0,1070.769,1253.048Z", "data-name" => "Path 102951", :fill => "#1f1f1f", :transform => "translate(-1055.462 -1247.758)"}
                      .flex-item-2.header-logo
                        =image_tag('mobile_header_logo.png', alt: 'mobile_header_logo')
              .s-flex-container
                .s-flex-item
                  - if @design['variant_size_chart'].present?  
                    .img-box
                      = render partial: 'luxe/designs/design_size_chart', locals: { size_chart: @design['variant_size_chart'].merge('is_variant' => true) }

/ = javascript_include_tag 'jquery.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'bootstrap.bundle.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'jquery.mCustomScrollbar.concat.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'swiper-bundle.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
= javascript_include_tag 'lazysizes.min.js', :defer => true, 'data-turbolinks-track' => 'reload'

/ = javascript_include_tag 'lightgallery.umd.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'lg-thumbnail.umd.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'lg-zoom.umd.min.js', :defer => true, 'data-turbolinks-track' => 'reload'


= javascript_include_tag 'jquery.zoom.min.js', :defer => true, 'data-turbolinks-track' => 'reload'
/ = javascript_include_tag 'app.js', :defer => true, 'data-turbolinks-track' => 'reload'
= javascript_include_tag 'jquery.elevatezoom.js', :defer => true, 'data-turbolinks-track' => 'reload'
-# = javascript_include_tag 'luxe/designs', :defer => true, 'data-turbolinks-track' => 'reload'

= javascript_include_tag 'luxe/design', :defer => true, 'data-turbolinks-track' => 'reload'

= javascript_include_tag 'luxe/solr_recommendation', :defer => true, 'data-turbolinks-track' => 'reload'

- if @ga_hash_new.present? || @googe_add_hash_new.present?
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{@googe_add_hash_new.to_json};
    var view_item_params = #{@ga_hash_new.to_json};
    dataLayer.push({ ecommerce: null });
    dataLayer.push(view_item_params);

:javascript
  $(".p-share-btn").click(function(){
    $(".social-share-content").toggle();
  });
  $(".share-btn").click(function(){
    $(".social-mshare-content").toggle();
  });

  // Product Detail Page - Mobile View Top Slider
   if ($(".mobileViewProductSwiperContainer").length) {
     var mobileViewProductSwiperContainer = new Swiper(
       ".mobileViewProductSwiperContainer",
       {
         slidesPerView: 1,
         noSwiping: false,
         noSwipingClass: "swiper-slide",
         loop: true,
         pagination: {
           el: ".swiper-pagination",
           clickable: true,
         },
         navigation: {
           nextEl: ".swiper-button-next",
           prevEl: ".swiper-button-prev",
         },
       }
     );
   }
:javascript
  afterWindowOrTrubolinksLoad(function() {
    MR.addCart.init()
    var variantsBlock = document.getElementById("variants_block");
    if (variantsBlock) {
      var mediaQuery = window.matchMedia("(min-width: 1000px)");
      if (!mediaQuery.matches) {
        var box = document.querySelector(".variant");
        if (!box.classList.contains(".selected .alert")) {
          var variantSection = document.querySelector(".price");
          function scrollVariant() {
            variantSection.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
          var addToCart = document.getElementById("itemAddedCartShowToast");
          addToCart.addEventListener("click", scrollVariant);
        }
      }
    }
  })
  
:javascript
  afterWindowOrTrubolinksLoad(function() {
    let hideCompositionCare = document.querySelector('.care-jewellery');
    if (hideCompositionCare.classList.contains('care-jewellery')){
    $('.care-apparels').hide()
    }
  });
  
:javascript
  if (typeof Turbolinks !== 'undefined') {
    MR.solrRecommendation.init()
    $('.variant').on('click', function(){
    var new_p = $(this).attr('data-price')
    var old_p = $('.price').text()
    changeDesignPrice(parseInt($(this).attr('data-price').replace(/[^0-9.]/g, "")),parseInt($('.price').text().replace(/[^0-9.]/g, "")));
    function changeDesignPrice(new_price, old_price){
      if (new_price !== old_price) {
        $('.price').text(new_p).removeClass('shake-effect');
        $('.price').text(new_p).addClass('shake-effect')
      }
    }
  })
  var variantsBlock = document.getElementById("variants_block");
    if (variantsBlock) {
      var mediaQuery = window.matchMedia("(min-width: 1000px)");
      if (!mediaQuery.matches) {
        var box = document.querySelector(".variant");
        if (!box.classList.contains(".selected .alert")) {
          var variantSection = document.querySelector(".price");
          function scrollVariant() {
            variantSection.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
          var addToCart = document.getElementById("itemAddedCartShowToast");
          addToCart.addEventListener("click", scrollVariant);
        }
      }
    }
  }
  document.addEventListener('turbolinks:load', function() {
    MR.solrRecommendation.init()
    $('.variant').on('click', function(){
    var new_p = $(this).attr('data-price')
    var old_p = $('.price').text()
    changeDesignPrice(parseInt($(this).attr('data-price').replace(/[^0-9.]/g, "")),parseInt($('.price').text().replace(/[^0-9.]/g, "")));
    function changeDesignPrice(new_price, old_price){
      if (new_price !== old_price) {
        $('.price').text(new_p).removeClass('shake-effect');
        $('.price').text(new_p).addClass('shake-effect')
      }
    }
  })
    var variantsBlock = document.getElementById("variants_block");
    if (variantsBlock) {
      var mediaQuery = window.matchMedia("(min-width: 1000px)");
      if (!mediaQuery.matches) {
        var box = document.querySelector(".variant");
        if (!box.classList.contains(".selected .alert")) {
          var variantSection = document.querySelector(".price");
          function scrollVariant() {
            variantSection.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
          var addToCart = document.getElementById("itemAddedCartShowToast");
          addToCart.addEventListener("click", scrollVariant);
        }
      }
    }
  });

:javascript
  var backArrowButton = document.querySelector('.back-arrow-button');
  backArrowButton.addEventListener('click', function(event) {
    event.preventDefault();
    if (window.history.length > 1) {
      window.history.go(-1);
    } else {
      window.location.href = '/';
    }
  });
  
  if (typeof Turbolinks !== 'undefined') {
    var ww = document.documentElement.clientWidth;
    if (ww >= 1000) {
      var headerHeight = $('.desktop-header-component-container:visible').css('height');
      $('#bodyTagContainer').css('padding-top', headerHeight);
    }
  }
  $(document).ready(function() {
    MR.login.headerIconClickEvent('#pdp-wishlist-btn','/accounts/sign_in','#loginModal');
  });

  document.addEventListener('turbolinks:load', function() {
    var ww = document.documentElement.clientWidth;
    if (ww >= 1000) {
      var headerHeight = $('.desktop-header-component-container:visible').css('height');
      $('#bodyTagContainer').css('padding-top', headerHeight);
    }
  });





